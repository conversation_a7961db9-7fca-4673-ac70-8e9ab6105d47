#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版失败案例检索脚本
专门用于命令行查询，避免交互式输入问题
"""

from sentence_transformers import SentenceTransformer
from pymilvus import connections, Collection
import sys


def search_failure_cases(query_text, top_k=5):
    """
    搜索相似的失败案例
    
    Args:
        query_text: 查询文本
        top_k: 返回最相似的前k个结果
    """
    try:
        print(f"正在搜索与 '{query_text}' 相似的失败案例...")
        
        # 加载模型
        print("加载text2vec-large-chinese模型...")
        model = SentenceTransformer("./text2vec-large-chinese")
        
        # 连接Milvus
        print("连接Milvus数据库...")
        connections.connect("default", host="************", port="19530")
        
        # 获取集合
        collection = Collection("failure_analysis")
        collection.load()
        
        # 生成查询向量
        query_embedding = model.encode([query_text]).tolist()
        
        # 搜索参数
        search_params = {"metric_type": "COSINE", "params": {"nprobe": 10}}
        
        # 执行搜索
        results = collection.search(
            data=query_embedding,
            anns_field="embedding",
            param=search_params,
            limit=top_k,
            output_fields=["task_id", "env_id", "branch_name", "timestamp", "failure_reason", "manual_operation", "reason_category"]
        )
        
        # 显示结果
        if not results or not results[0]:
            print("未找到相关结果")
            return
            
        print(f"\n查询: '{query_text}' 的搜索结果:")
        print("=" * 80)
        
        for i, hit in enumerate(results[0]):
            print(f"\n结果 {i+1} (相似度分数: {hit.score:.4f})")
            print("-" * 60)
            print(f"任务ID: {hit.entity.get('task_id')}")
            print(f"环境ID: {hit.entity.get('env_id')}")
            print(f"分支名称: {hit.entity.get('branch_name')}")
            print(f"时间戳: {hit.entity.get('timestamp')}")
            print(f"失败原因: {hit.entity.get('failure_reason')}")
            print(f"人工操作: {hit.entity.get('manual_operation')}")
            print(f"原因分类: {hit.entity.get('reason_category')}")
            
    except Exception as e:
        print(f"搜索过程中出现错误: {e}")
        return False
        
    return True


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python simple_search.py '查询内容'")
        print("示例:")
        print("  python simple_search.py 'AAU重启问题'")
        print("  python simple_search.py '核心网异常'")
        print("  python simple_search.py '业务不稳定'")
        return
        
    # 获取查询内容
    query = " ".join(sys.argv[1:])
    
    # 执行搜索
    search_failure_cases(query)


if __name__ == "__main__":
    main()
