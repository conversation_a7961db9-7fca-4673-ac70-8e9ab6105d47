#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理脚本 - 删除临时文件和测试文件
"""

import os
import glob

def cleanup_files():
    """清理临时文件和测试文件"""
    
    # 要清理的文件模式
    cleanup_patterns = [
        "*.pyc",
        "__pycache__/",
        "*.tmp",
        "*.log",
        ".DS_Store",
        "*.bak"
    ]

    # 要删除的测试文件
    test_files = [
        "test_qwen_embedding.py"
    ]
    
    # 要保留的重要文件
    keep_files = [
        "failure_analysis_to_milvus.py",
        "search_failure_cases.py", 
        "text_embedding.py",
        "六月失败原因及处理手段统计.xlsx",
        "README.md",
        "cleanup.py",
        "docker-compose.yml"
    ]
    
    print("开始清理临时文件...")
    
    cleaned_count = 0
    
    # 清理匹配模式的文件
    for pattern in cleanup_patterns:
        for file_path in glob.glob(pattern, recursive=True):
            if os.path.isfile(file_path):
                try:
                    os.remove(file_path)
                    print(f"删除文件: {file_path}")
                    cleaned_count += 1
                except Exception as e:
                    print(f"删除文件失败 {file_path}: {e}")
            elif os.path.isdir(file_path):
                try:
                    import shutil
                    shutil.rmtree(file_path)
                    print(f"删除目录: {file_path}")
                    cleaned_count += 1
                except Exception as e:
                    print(f"删除目录失败 {file_path}: {e}")
    
    # 删除测试文件
    for test_file in test_files:
        if os.path.exists(test_file):
            try:
                os.remove(test_file)
                print(f"删除测试文件: {test_file}")
                cleaned_count += 1
            except Exception as e:
                print(f"删除测试文件失败 {test_file}: {e}")

    print(f"\n清理完成！共清理了 {cleaned_count} 个文件/目录")
    
    # 显示当前目录内容
    print("\n当前目录文件列表:")
    for item in sorted(os.listdir(".")):
        if not item.startswith('.'):
            if os.path.isdir(item):
                print(f"📁 {item}/")
            else:
                print(f"📄 {item}")

if __name__ == "__main__":
    cleanup_files()
