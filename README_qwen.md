# Qwen3-Embedding-0.6B 失败案例分析系统

本文档介绍如何使用新创建的基于 Qwen3-Embedding-0.6B 模型的失败案例分析脚本。

## 新增文件说明

### 1. failure_analysis_to_milvus_qwen.py
基于原始 `failure_analysis_to_milvus.py` 创建的新版本，主要变更：
- 使用 Qwen3-Embedding-0.6B 模型替代 text2vec-large-chinese
- 集合名称改为 `failure_analysis_qwen` 以区分不同模型的数据
- 支持 query prompt 功能（如果模型支持）
- 保持相同的向量维度（1024）和其他配置

### 2. simple_search_qwen.py
基于原始 `simple_search.py` 创建的新版本，主要变更：
- 使用 Qwen3-Embedding-0.6B 模型进行检索
- 连接到 `failure_analysis_qwen` 集合
- 支持单模型搜索和双模型对比搜索
- 优化了查询向量生成（支持 query prompt）

### 3. test_qwen_embedding.py
Qwen3-Embedding-0.6B 模型测试脚本：
- 验证模型加载和基本功能
- 测试向量生成性能
- 比较不同编码方式的效果
- 提供模型性能基准测试

## 使用步骤

### 第一步：测试模型功能
```bash
# 测试 Qwen3-Embedding-0.6B 模型是否正常工作
python test_qwen_embedding.py
```

### 第二步：导入数据到 Milvus
```bash
# 使用 Qwen3-Embedding-0.6B 模型处理数据并存储到 Milvus
python failure_analysis_to_milvus_qwen.py
```

### 第三步：进行检索测试
```bash
# 使用 Qwen3-Embedding-0.6B 模型进行检索
python simple_search_qwen.py "AAU重启问题"

# 对比两个模型的检索效果
python simple_search_qwen.py "核心网异常" --compare
```

## 模型对比

| 特性 | text2vec-large-chinese | Qwen3-Embedding-0.6B |
|------|------------------------|----------------------|
| 向量维度 | 1024 | 1024 |
| 模型大小 | ~400MB | ~600MB |
| 语言支持 | 中文优化 | 多语言支持 |
| 特殊功能 | - | Query Prompt 支持 |
| 集合名称 | failure_analysis | failure_analysis_qwen |

## 配置说明

### 模型路径配置
```python
# Qwen3-Embedding-0.6B 模型路径
model_path = "/home/<USER>/.cache/modelscope/hub/models/Qwen/Qwen3-Embedding-0.6B"
```

### Milvus 连接配置
```python
# Milvus 服务器配置（与原版本相同）
milvus_host = "************"
milvus_port = "19530"
```

## 注意事项

1. **向量空间一致性**：两个模型生成的向量在不同的向量空间中，不能直接比较相似度
2. **集合分离**：使用不同的集合名称存储不同模型的数据
3. **模型依赖**：确保 Qwen3-Embedding-0.6B 模型已正确下载和配置
4. **性能差异**：新模型可能在加载时间和推理速度上有所不同

## 故障排除

### 模型加载失败
```bash
# 检查模型路径是否正确
ls -la /home/<USER>/.cache/modelscope/hub/models/Qwen/Qwen3-Embedding-0.6B

# 检查依赖包版本
pip list | grep sentence-transformers
pip list | grep transformers
```

### Milvus 连接问题
```bash
# 检查 Milvus 服务状态
ping ************

# 检查端口连通性
telnet ************ 19530
```

### 集合不存在错误
确保先运行数据导入脚本：
```bash
python failure_analysis_to_milvus_qwen.py
```

## 清理和维护

### 清理测试文件
```bash
# 运行清理脚本删除临时文件
python cleanup.py
```

### 删除 Milvus 集合
```python
from pymilvus import connections, utility

connections.connect("default", host="************", port="19530")
if utility.has_collection("failure_analysis_qwen"):
    utility.drop_collection("failure_analysis_qwen")
```

## 性能优化建议

1. **批量处理**：处理大量数据时使用批量编码
2. **内存管理**：大数据集处理时注意内存使用
3. **索引优化**：根据数据量调整 Milvus 索引参数
4. **并发控制**：避免同时运行多个模型加载操作

## 扩展功能

### 自定义 Query Prompt
```python
# 如果需要自定义查询提示词
custom_prompt = "查询相似的失败案例："
query_with_prompt = f"{custom_prompt}{query_text}"
```

### 混合检索
可以结合两个模型的结果进行混合检索，提高检索质量。

## 联系和支持

如有问题或需要技术支持，请联系开发团队。
