{"image": "mcr.microsoft.com/devcontainers/universal:2", "hostRequirements": {"cpus": 4}, "waitFor": "onCreateCommand", "updateContentCommand": "pip install -r .devcontainer/requirements.txt", "postCreateCommand": "", "customizations": {"codespaces": {"openFiles": ["notebooks/llms/langchain/lc_vectorstore_conv_mem.ipynb", "notebooks/llms/llamaindex/multi_doc_qa.ipynb", "notebooks/text/white_house_speeches.ipynb", "notebooks/vision/reverse_painting_search.ipynb"]}, "vscode": {"extensions": ["ms-toolsai.jupyter", "ms-python.python"]}}}