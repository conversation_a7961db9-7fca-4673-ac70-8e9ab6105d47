{"cells": [{"cell_type": "markdown", "source": ["# Use Ragas to evaluate the customized RAG pipeline based on milvus\n", "\n", "**Please note that this test requires a large amount of OpenAI api token consumption. Please read it carefully and Pay attention to the number of times you request access.**"], "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}}, {"cell_type": "markdown", "source": ["## 1. Prepare environment and data\n", "\n", "Before starting, you must set OPENAI_API_KEY in your environment variables."], "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}}, {"cell_type": "markdown", "source": ["You also need to install [milvus](https://milvus.io/) and start it. You can refer to the [official introduction](https://milvus.io/docs/install_standalone-docker.md) to start quickly."], "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}}, {"cell_type": "markdown", "source": ["Install pip dependencies"], "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["# ! python -m pip install openai beir pandas ragas==0.0.17 pymilvus"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "markdown", "source": ["Download [Financial Opinion Mining and Question Answering (fiqa) Dataset](https://sites.google.com/view/fiqa/) data if it not exists in your local space. We convert it into a ragas form that is easier to process, referring from this [script](https://github.com/explodinggradients/ragas/blob/main/experiments/baselines/fiqa/dataset-exploration-and-baseline.ipynb)."], "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}}, {"cell_type": "code", "execution_count": 1, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1706\n"]}], "source": ["import json\n", "import pandas as pd\n", "import os\n", "from tqdm import tqdm\n", "from datasets import Dataset\n", "from beir import util\n", "\n", "\n", "def prepare_fiqa_without_answer(knowledge_path):\n", "    dataset_name = \"fiqa\"\n", "\n", "    if not os.path.exists(os.path.join(knowledge_path, f'{dataset_name}.zip')):\n", "        url = (\n", "            \"https://public.ukp.informatik.tu-darmstadt.de/thakur/BEIR/datasets/{}.zip\".format(\n", "                dataset_name\n", "            )\n", "        )\n", "        util.download_and_unzip(url, knowledge_path)\n", "\n", "    data_path = os.path.join(knowledge_path, 'fiqa')\n", "    with open(os.path.join(data_path, \"corpus.jsonl\")) as f:\n", "        cs = [pd.Series(json.loads(l)) for l in f.readlines()]\n", "\n", "    corpus_df = pd.DataFrame(cs)\n", "\n", "    corpus_df = corpus_df.rename(columns={\"_id\": \"corpus-id\", \"text\": \"ground_truth\"})\n", "    corpus_df = corpus_df.drop(columns=[\"title\", \"metadata\"])\n", "    corpus_df[\"corpus-id\"] = corpus_df[\"corpus-id\"].astype(int)\n", "    corpus_df.head()\n", "\n", "    with open(os.path.join(data_path, \"queries.jsonl\")) as f:\n", "        qs = [pd.Series(json.loads(l)) for l in f.readlines()]\n", "\n", "    queries_df = pd.DataFrame(qs)\n", "    queries_df = queries_df.rename(columns={\"_id\": \"query-id\", \"text\": \"question\"})\n", "    queries_df = queries_df.drop(columns=[\"metadata\"])\n", "    queries_df[\"query-id\"] = queries_df[\"query-id\"].astype(int)\n", "    queries_df.head()\n", "\n", "    splits = [\"dev\", \"test\", \"train\"]\n", "    split_df = {}\n", "    for s in splits:\n", "        split_df[s] = pd.read_csv(os.path.join(data_path, f\"qrels/{s}.tsv\"), sep=\"\\t\").drop(\n", "            columns=[\"score\"]\n", "        )\n", "\n", "    final_split_df = {}\n", "    for split in split_df:\n", "        df = queries_df.merge(split_df[split], on=\"query-id\")\n", "        df = df.merge(corpus_df, on=\"corpus-id\")\n", "        df = df.drop(columns=[\"corpus-id\"])\n", "        grouped = df.groupby(\"query-id\").apply(\n", "            lambda x: pd.Series(\n", "                {\n", "                    \"question\": x[\"question\"].sample().values[0],\n", "                    \"ground_truths\": x[\"ground_truth\"].tolist(),\n", "                }\n", "            )\n", "        )\n", "\n", "        grouped = grouped.reset_index()\n", "        grouped = grouped.drop(columns=\"query-id\")\n", "        final_split_df[split] = grouped\n", "\n", "    return final_split_df\n", "\n", "\n", "knowledge_datas_path = './knowledge_datas'\n", "fiqa_path = os.path.join(knowledge_datas_path, 'fiqa_doc.txt')\n", "\n", "if not os.path.exists(knowledge_datas_path):\n", "    os.mkdir(knowledge_datas_path)\n", "contexts_list = []\n", "answer_list = []\n", "\n", "final_split_df = prepare_fiqa_without_answer(knowledge_datas_path)\n", "\n", "docs = []\n", "\n", "split = 'test'\n", "for ds in final_split_df[split][\"ground_truths\"]:\n", "    docs.extend([d for d in ds])\n", "print(len(docs))\n", "\n", "docs_str = '\\n'.join(docs)\n", "with open(fiqa_path, 'w') as f:\n", "    f.write(docs_str)\n", "\n", "split = 'test'\n", "question_list = final_split_df[split][\"question\"].to_list()\n", "ground_truth_list = final_split_df[split][\"ground_truths\"].to_list()"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "markdown", "source": ["Now we have the question list and the ground truth list. And the knowledge documents are prepared in `fiqa_path`."], "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}}, {"cell_type": "markdown", "source": ["## 2. Build RAG pipeline based on milvus and langchain\n", "Split the doc using langchain RecursiveCharacterTextSplitter."], "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}}, {"cell_type": "code", "execution_count": 2, "outputs": [], "source": ["from langchain.document_loaders import TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "\n", "loader = TextLoader(fiqa_path)\n", "documents = loader.load()\n", "text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=40)\n", "docs = text_splitter.split_documents(documents)"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "markdown", "source": ["Prepare embedding model and milvus settings."], "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}}, {"cell_type": "code", "execution_count": 3, "outputs": [{"data": {"text/plain": "Batches:   0%|          | 0/77 [00:00<?, ?it/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "d8eed54919ba4be49c14c2f8899d8276"}}, "metadata": {}, "output_type": "display_data"}], "source": ["from langchain.embeddings import HuggingFaceEmbeddings\n", "from langchain.vectorstores.milvus import Milvus\n", "\n", "embeddings = HuggingFaceEmbeddings(model_name=\"BAAI/bge-base-en\")\n", "\n", "vector_db = Milvus.from_documents(\n", "    docs,\n", "    embeddings,\n", "    connection_args={\"host\": \"127.0.0.1\", \"port\": \"19530\"},\n", "    drop_old=True\n", ")"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "markdown", "source": ["Build agent using langchain."], "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}}, {"cell_type": "code", "execution_count": 4, "outputs": [], "source": ["def search_milvus(question, top_k=5):\n", "    contexts = vector_db.similarity_search(question, k=top_k)\n", "    return contexts[:top_k]"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 5, "outputs": [], "source": ["from langchain.tools import Tool\n", "from langchain.memory import ConversationBufferMemory\n", "from langchain.chat_models import ChatOpenAI\n", "from langchain.agents import AgentExecutor, ConversationalChatAgent\n", "\n", "chat_llm = ChatOpenAI(model_name='gpt-4-1106-preview')\n", "tools = [\n", "    Tool(\n", "        name='Search',\n", "        func=search_milvus,\n", "        description='useful for search professional knowledge and information'\n", "    )\n", "]\n", "agent = ConversationalChatAgent.from_llm_and_tools(llm=chat_llm, tools=tools)\n", "\n", "memory = ConversationBufferMemory(memory_key=\"chat_history\", return_messages=True, output_key='output')\n", "agent_chain = AgentExecutor.from_agent_and_tools(\n", "    agent=agent,\n", "    tools=tools,\n", "    memory=memory,\n", "    return_intermediate_steps=True,\n", "    # verbose=True,\n", ")"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 8, "outputs": [], "source": ["import time\n", "\n", "\n", "def retry_agent_chain(retry_num=4, retry_interval=4):\n", "    answer = 'failed. please retry.'\n", "    contexts = ['failed. please retry.']\n", "    for _ in range(retry_num):\n", "        try:\n", "            agent_result = agent_chain(question)\n", "            contexts = [document.page_content for document in agent_result['intermediate_steps'][0][1]]\n", "            answer = agent_result['output']\n", "            break\n", "        except Exception as e:\n", "            time.sleep(retry_interval)\n", "            print(e)\n", "            print('failed, retry...')\n", "            continue\n", "    return answer, contexts"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "markdown", "source": ["## 3. Start Ragas Evaluation\n", "\n", "Note that a large amount of OpenAI api token is consumed. Every time you ask a question and every evaluation, you will ask the OpenAI service. Please pay attention to your token consumption. If you only want to run a small number of tests, you can modify the code to reduce the test size."], "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["contexts_list = []\n", "answer_list = []\n", "for question in tqdm(question_list):\n", "    memory.clear()\n", "    answer, contexts = retry_agent_chain()\n", "    # print(f'answer = {answer}')\n", "    # print(f'contexts = {contexts}')\n", "    # print('=' * 80)\n", "    answer_list.append(answer)\n", "    contexts_list.append(contexts)"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "markdown", "source": ["You can choose the indicators you care about to test."], "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["from ragas import evaluate\n", "from ragas.metrics import answer_relevancy, faithfulness, context_recall, context_precision, answer_similarity\n", "\n", "ds = Dataset.from_dict({\"question\": question_list,\n", "                        \"contexts\": contexts_list,\n", "                        \"answer\": answer_list,\n", "                        \"ground_truths\": ground_truth_list})\n", "\n", "result = evaluate(\n", "    ds,\n", "    metrics=[\n", "        context_precision,\n", "        # context_recall,\n", "        # faithfulness,\n", "        # answer_relevancy,\n", "        # answer_similarity,\n", "        # answer_correctness,\n", "    ],\n", "\n", ")\n", "print(result)"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 0}