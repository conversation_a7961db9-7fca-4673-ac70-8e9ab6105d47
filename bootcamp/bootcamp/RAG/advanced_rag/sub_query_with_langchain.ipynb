{"cells": [{"cell_type": "markdown", "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/bootcamp/RAG/advanced_rag/sub_query_with_langchain.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>\n", "\n", "# Sub query"], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "source": ["## Google Colab preparation[optional]\n", "This is an optional step, if you want to run this notebook on Google Colab."], "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["! git clone https://github.com/milvus-io/bootcamp.git"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["import shutil\n", "src_dir = \"./bootcamp/bootcamp/RAG/advanced_rag/rag_utils\"\n", "dst_dir = \"./rag_utils\"\n", "shutil.copytree(src_dir, dst_dir)"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["! pip install --upgrade langchain langchain-community langchain_milvus langchain-openai bs4"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "markdown", "source": ["Please prepare you [OPENAI_API_KEY](https://openai.com/index/openai-api/) in your environment variables.\n", "![](imgs/colab_api_key1.png)"], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "source": ["### If you are running this notebook on Google Colab, you have to restart this session by `Cmd/Ctrl + M`, then press `.` to make the environment take effect."], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["from google.colab import userdata\n", "import os\n", "\n", "os.environ['OPENAI_API_KEY'] = userdata.get('OPENAI_API_KEY')"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "markdown", "source": ["----\n", "## Get started\n", "![](imgs/sub_query.png)"], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "source": ["## Prepare the data\n", "\n", "We use the Langchain WebBaseLoader to load documents from [blog sources](https://lilianweng.github.io/posts/2023-06-23-agent/) and split them into chunks using the RecursiveCharacterTextSplitter."], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["import bs4\n", "from langchain_community.document_loaders import WebBaseLoader\n", "from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "\n", "from rag_utils.vanilla import vectorstore\n", "\n", "# Create a WebBaseLoader instance to load documents from web sources\n", "loader = WebBaseLoader(\n", "    web_paths=(\"https://lilianweng.github.io/posts/2023-06-23-agent/\",),\n", "    bs_kwargs=dict(\n", "        parse_only=bs4.SoupStrainer(\n", "            class_=(\"post-content\", \"post-title\", \"post-header\")\n", "        )\n", "    ),\n", ")\n", "# Load documents from web sources using the loader\n", "documents = loader.load()\n", "# Initialize a RecursiveCharacterTextSplitter for splitting text into chunks\n", "text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=0)\n", "\n", "# Split the documents into chunks using the text_splitter\n", "docs = text_splitter.split_documents(documents)"]}, {"cell_type": "markdown", "source": ["## Build the chain\n", "\n", "We load the docs into milvus vectorstore, and build a milvus retriever."], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["vectorstore.add_documents(docs)\n", "retriever = vectorstore.as_retriever()"]}, {"cell_type": "markdown", "source": ["Define the vanilla RAG chain."], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.runnables import RunnablePassthrough\n", "from rag_utils.vanilla import format_docs, rag_prompt, llm\n", "\n", "vanilla_rag_chain = (\n", "    {\"context\": retriever | format_docs, \"question\": RunnablePassthrough()}\n", "    | rag_prompt\n", "    | llm\n", "    | StrOutputParser()\n", ")"]}, {"cell_type": "markdown", "source": ["Define the sub query chain."], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.runnables import RunnablePassthrough\n", "from rag_utils.sub_query import SubQueryRetriever\n", "\n", "sub_query_retriever = SubQueryRetriever.from_vectorstore(vectorstore)\n", "\n", "sub_query_chain = (\n", "    {\"context\": sub_query_retriever | format_docs, \"question\": RunnablePassthrough()}\n", "    | rag_prompt\n", "    | llm\n", "    | StrOutputParser()\n", ")"]}, {"cell_type": "markdown", "source": ["## Test the chain\n"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sub_queries: ['What is MRKL?', 'What is HuggingGPT?']\n", "\n", "[vanilla_result]:\n", "I'm sorry, but the provided context does not contain any information about MRKL, so I cannot provide a comparison between MRKL and HuggingGPT.\n", "\n", "[sub_query_result]:\n", "MRKL (Modular Reasoning, Knowledge and Language) and HuggingGPT are both systems that utilize large language models (LLMs), but they differ in their structure and functionality. \n", "\n", "MRKL, proposed by <PERSON><PERSON><PERSON> et al. in 2022, is a neuro-symbolic architecture for autonomous agents. It contains a collection of \"expert\" modules, and the LLM works as a router to route inquiries to the most suitable expert module. These modules can be neural, like deep learning models, or symbolic, like a math calculator or weather API.\n", "\n", "On the other hand, HuggingGPT, proposed by <PERSON> et al. in 2023, is a framework that uses ChatGPT as a task planner to select models available in the HuggingFace platform according to the model descriptions. It then summarizes the response based on the execution results. \n", "\n", "In terms of differences, MRKL's structure is more modular, with the LLM directing inquiries to different expert modules, while HuggingGPT uses ChatGPT to plan tasks and select models from the HuggingFace platform. Furthermore, MRKL can incorporate both neural and symbolic modules, whereas HuggingGPT primarily interacts with other models on the HuggingFace platform.\n"]}], "source": ["query = \"What is the difference between MRKL and HuggingGPT?\"\n", "\n", "vanilla_result = vanilla_rag_chain.invoke(query)\n", "sub_query_result = sub_query_chain.invoke(query)\n", "print(\n", "    f\"\\n[vanilla_result]:\\n{vanilla_result}\\n\\n[sub_query_result]:\\n{sub_query_result}\"\n", ")"]}, {"cell_type": "markdown", "source": ["The vanilla chain cannot answer this question. This is because the retrieved documents are limited and do not contain enough information to answer the question. The method of using sub query to answer questions can increase the number of queries, thereby increasing the retrieved document information to answer the question."], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%%\n"}}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}}, "nbformat": 4, "nbformat_minor": 4}