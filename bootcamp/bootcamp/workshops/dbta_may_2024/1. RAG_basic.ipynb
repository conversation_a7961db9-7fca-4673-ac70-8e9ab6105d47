{"cells": [{"cell_type": "markdown", "id": "369c3444", "metadata": {}, "source": ["# ReadtheDocs Retrieval Augmented Generation (RAG)"]}, {"cell_type": "markdown", "id": "f6ffd11a", "metadata": {}, "source": ["In this notebook, we are going to use Milvus documentation pages to create a chatbot about our product.  The chatbot is going to follow RAG steps to retrieve chunks of data using Semantic Vector Search, then the Question + Context will be fed as a Prompt to a LLM to generate an answer.\n", "\n", "Many RAG demos use OpenAI for the Embedding Model and ChatGPT for the Generative AI model.  **In this notebook, we will demo a fully open source RAG stack.**\n", "\n", "Using open-source Q&A with retrieval saves money since we make free calls to our own data almost all the time - retrieval, evaluation, and development iterations.\n", "\n", "<div>\n", "<img src=\"../../../pics/rag_image.png\" width=\"80%\"/>\n", "</div>\n", "\n", "Let's get started!"]}, {"cell_type": "code", "execution_count": 1, "id": "b2509fe9", "metadata": {}, "outputs": [], "source": ["# For colab install these libraries in this order:\n", "# !python -m pip install torch transformers sentence-transformers langchain\n", "# !python -m pip install -<PERSON> pymilvus\n", "# !python -m pip install unstructured openai tqdm numpy ipykernel"]}, {"cell_type": "code", "execution_count": 2, "id": "d7570b2e", "metadata": {}, "outputs": [], "source": ["# Import common libraries.\n", "import sys, os, time, pprint\n", "import numpy as np\n", "\n", "# Import custom functions for splitting and search.\n", "sys.path.append(\"../..\")  # Adds higher directory to python modules path.\n", "import milvus_utilities as _utils"]}, {"cell_type": "code", "execution_count": 3, "id": "33647d02", "metadata": {}, "outputs": [], "source": ["# Function to remove newlines and double spaces from a string.\n", "def clean_text(text):\n", "    clean_text = text.replace(\"\\n\\n\", \" \")\\\n", "                     .replace(\"\\n\", \" \")\\\n", "                     .replace(\"<br><br>\", \" \")\\\n", "                     .replace(\"<br /><br />\", \" \")\n", "    \n", "    # Remove extra whitespace.\n", "    clean_text = ' '.join(clean_text.split())\n", "    return clean_text\n", "\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# Function to inspect chunk lengths in matplotlit.\n", "def plot_chunk_lengths(chunked_docs, title_keyword):\n", "    # Get chunk lengths.\n", "    lengths = [len(doc.page_content) for doc in chunked_docs]\n", "\n", "    # Mean, median lengths.\n", "    mean_length = np.mean(lengths)\n", "    median_length = np.median(lengths)\n", "\n", "    # Assemble the title.\n", "    title = f\"Chunk Lengths from {title_keyword} Chunking\"\n", "\n", "    # Plot the lengths.\n", "    plt.figure(figsize=(10, 6))  # Adjust figure size\n", "    plt.plot(lengths, marker='o')  # Plot lengths with circle markers\n", "    plt.title(title, fontsize=20, fontweight='bold')\n", "    plt.xlabel('Document Index')  # X-axis label\n", "    plt.ylabel('Length')  # Y-axis label\n", "    plt.grid(True)  # Show grid\n", "\n", "    # Add a horizontal line at mean and median length\n", "    plt.axhline(y=mean_length, color='g', linestyle='-')\n", "    plt.axhline(y=median_length, color='r', linestyle='-')\n", "    plt.text(len(lengths)-1, mean_length, f'mean = {mean_length:.0f}', va='center', ha='left', backgroundcolor='w', fontsize=12)\n", "    plt.text(0, median_length, f'median = {median_length:.0f}', va='center', ha='right', backgroundcolor='w', fontsize=12)\n", "\n", "    plt.show()  # Display the plot"]}, {"cell_type": "markdown", "id": "e059b674", "metadata": {}, "source": ["## Download Data\n", "\n", "The data used in this notebook is Milvus documentation web pages.\n", "\n", "The code block below downloads all the web pages into a local directory called `rtdocs`.  \n", "\n", "I've already uploaded the `rtdocs` data folder to github, so you should see it if you cloned my repo."]}, {"cell_type": "code", "execution_count": 4, "id": "25686cc7", "metadata": {}, "outputs": [], "source": ["# # UNCOMMENT TO DOWNLOAD THE DOCS.\n", "\n", "# # !pip install -U langchain\n", "# from langchain_community.document_loaders import RecursiveUrlLoader\n", "\n", "# DOCS_PAGE=\"https://milvus.io/docs/\"\n", "\n", "# loader = RecursiveUrlLoader(DOCS_PAGE)\n", "# docs = loader.load()\n", "\n", "# num_documents = len(docs)\n", "# print(f\"loaded {num_documents} documents\")\n", "\n", "# # Print the type of the docs.\n", "# print(type(docs))\n", "# print(type(docs[0]))\n", "# docs[0].page_content[:500]\n", "\n", "\n", "# # UNCOMMENT TO SAVE THE DOCS LOCALLY.\n", "# OUTPUT_DIR = \"../../RAG/rtdocs_new/\"\n", "# os.makedirs(OUTPUT_DIR, exist_ok=True)"]}, {"cell_type": "code", "execution_count": 5, "id": "075b55bc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loaded 22 documents\n", "Why Milvus Docs Tutorials Tools Blog Community Stars0 Try Managed Milvus FREE Search Home v2.4.x About Milvus Get StartedPrerequisitesInstall MilvusInstall SDKsQuickstart Concepts User Guide Models Administration Guide Tools Integrations Example Applications FAQs API reference Quickstart This guide explains how to connect to your Milvus cluster and performs CRUD operations in minutes Before you start You have installed Milvus standalone or Milvus cluster. You have installed preferred SDKs. You c\n", "{'source': 'https://milvus.io/docs/quickstart.md'}\n"]}], "source": ["# From raw docs\n", "\n", "# UNCOMMENT TO READ THE DOCS FROM A LOCAL DIRECTORY.\n", "\n", "# Read docs into Lang<PERSON><PERSON><PERSON>\n", "# !pip install -U langchain\n", "# !pip install unstructured\n", "from langchain.document_loaders import DirectoryLoader\n", "\n", "# Load HTML files already saved in a local directory.\n", "path = \"../../RAG/rtdocs_new/\"\n", "global_pattern = '*.html'\n", "loader = DirectoryLoader(path=path, glob=global_pattern)\n", "docs = loader.load()\n", "\n", "num_documents = len(docs)\n", "print(f\"loaded {num_documents} documents\")\n", "\n", "# Clean up \\n literals and source metdata.\n", "for doc in docs:\n", "    doc.page_content = clean_text(doc.page_content)\n", "    doc.metadata[\"source\"] = \\\n", "        doc.metadata[\"source\"]\\\n", "            .replace(\"../../RAG/rtdocs_new\", \"https://milvus.io/docs\")\\\n", "            .replace(\".html\", \".md\")\n", "    \n", "print(docs[0].page_content[:500])\n", "pprint.pprint(docs[0].metadata)"]}, {"cell_type": "markdown", "id": "f19236fe", "metadata": {}, "source": ["## Load the Embedding Model checkpoint and use it to create vector embeddings\n", "\n", "#### What are Embeddings?\n", "\n", "Check out [this blog](https://zilliz.com/glossary/vector-embeddings) for an introduction to embeddings.  \n", "\n", "An excellent place to start is by selecting an embedding model from the [HuggingFace MTEB Leaderboard](https://huggingface.co/spaces/mteb/leaderboard), sorted descending by the \"Retrieval Average'' column since this task is most relevant to RAG. Then, choose the smallest, highest-ranking embedding model. But, Beware!! some models listed are overfit to the training data, so they won't perform on your data as promised.  \n", "\n", "Milvus (and <PERSON><PERSON><PERSON>) only supports tested embedding models that are **not overfit**!"]}, {"cell_type": "markdown", "id": "9b6c23fc", "metadata": {}, "source": ["### Use open source Embedding Model from HuggingFace"]}, {"cell_type": "code", "execution_count": 6, "id": "f043c02e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/miniconda3/envs/py311-unum/lib/python3.11/site-packages/huggingface_hub/file_download.py:1132: FutureWarning: `resume_download` is deprecated and will be removed in version 1.0.0. Downloads always resume when possible. If you want to force a new download, use `force_download=True`.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["model_name: BAAI/bge-large-en-v1.5\n", "EMBEDDING_DIM: 1024\n", "MAX_SEQ_LENGTH: 512\n"]}], "source": ["import torch\n", "from sentence_transformers import SentenceTransformer\n", "\n", "# Initialize torch settings for device-agnostic code\n", "N_GPU = torch.cuda.device_count()\n", "DEVICE = torch.device('cuda:N_GPU' if torch.cuda.is_available() else 'cpu')\n", "\n", "# Download the model from huggingface model hub.\n", "model_name = \"BAAI/bge-large-en-v1.5\"\n", "encoder = SentenceTransformer(model_name, device=DEVICE)\n", "# print(encoder)\n", "\n", "# Get the model parameters and save for later.\n", "EMBEDDING_DIM = encoder.get_sentence_embedding_dimension()\n", "MAX_SEQ_LENGTH_IN_TOKENS = encoder.get_max_seq_length() \n", "# # Assume tokens are 3 characters long.\n", "# MAX_SEQ_LENGTH = MAX_SEQ_LENGTH_IN_TOKENS * 3\n", "# HF_EOS_TOKEN_LENGTH = 1 * 3\n", "# Test with 512 sequence length.\n", "MAX_SEQ_LENGTH = MAX_SEQ_LENGTH_IN_TOKENS\n", "HF_EOS_TOKEN_LENGTH = 1\n", "\n", "# Inspect model parameters.\n", "print(f\"model_name: {model_name}\")\n", "print(f\"EMBEDDING_DIM: {EMBEDDING_DIM}\")\n", "print(f\"MAX_SEQ_LENGTH: {MAX_SEQ_LENGTH}\")"]}, {"cell_type": "markdown", "id": "06a86a1e", "metadata": {}, "source": ["## Create Milvus Collection and Index"]}, {"cell_type": "code", "execution_count": 7, "id": "2a43c38d", "metadata": {}, "outputs": [], "source": ["# Connect a client to the Milvus Lite server.\n", "from pymilvus import MilvusClient\n", "mc = MilvusClient(\"milvus_demo.db\")"]}, {"cell_type": "code", "execution_count": 8, "id": "9fce83b1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Successfully dropped collection: `MilvusDocs`\n", "Successfully created collection: `MilvusDocs`\n"]}], "source": ["# Create vectorstore for vector index and retrieval.\n", "COLLECTION_NAME = \"MilvusDocs\"\n", "\n", "COLLECTION_NAME = \"MilvusDocs\"\n", "\n", "# Milvus Lite uses the MilvusClient object.\n", "if mc.has_collection(COLLECTION_NAME):\n", "    mc.drop_collection(COLLECTION_NAME)\n", "    print(f\"Successfully dropped collection: `{COLLECTION_NAME}`\")\n", "\n", "# Create a collection with flexible schema and AUTOINDEX.\n", "mc.create_collection(COLLECTION_NAME, \n", "        EMBEDDING_DIM,\n", "        consistency_level=\"Eventually\", \n", "        auto_id=True,  \n", "        overwrite=True,\n", "    )\n", "print(f\"Successfully created collection: `{COLLECTION_NAME}`\")"]}, {"cell_type": "markdown", "id": "0804d3ff", "metadata": {}, "source": ["## Fixed-length Chunking using Lang<PERSON>hain\n", "\n", "Before embedding, it is necessary to decide your chunk strategy, chunk size, and chunk overlap.  This section uses:\n", "- **Strategy** = Use markdown header hierarchies.  Keep markdown sections together unless they are too long.\n", "- **Chunk size** = Use the embedding model's parameter `MAX_SEQ_LENGTH`\n", "- **Overlap** = Rule-of-thumb 10-15%\n", "- **Function** = \n", "  - Langchain's `RecursiveCharacterTextSplitter` to split up long reviews recursively.\n", "\n", "Notice below, each chunk is grounded with the document source page.  <br>"]}, {"cell_type": "code", "execution_count": 9, "id": "f51def0d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["chunk_size: 512, chunk_overlap: 51.0\n", "22 docs split into 355 child documents.\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "\n", "CHUNK_SIZE = 512\n", "chunk_overlap = np.round(CHUNK_SIZE * 0.10, 0)\n", "print(f\"chunk_size: {CHUNK_SIZE}, chunk_overlap: {chunk_overlap}\")\n", "\n", "# The splitter to create smaller (child) chunks\n", "# Default splitters: https://github.com/langchain-ai/langchain/blob/9ef2feb6747f5a69d186bd623b569ad722829a5e/libs/langchain/langchain/text_splitter.py#L842\n", "child_splitter = RecursiveCharacterTextSplitter(\n", "    chunk_size=CHUNK_SIZE, \n", "    chunk_overlap=chunk_overlap,\n", "    # length_function = len,  # use built-in Python len function\n", "    # separators = [\"\\n\\n\", \"\\n\", \" \", \". \", \"\"], # these are defaults\n", ")\n", "\n", "# Child docs for inspection\n", "chunks = child_splitter.split_documents(docs)\n", "print(f\"{len(docs)} docs split into {len(chunks)} child documents.\")\n", "\n", "# Inspect chunk lengths\n", "plot_chunk_lengths(chunks, 'Recursive Character')"]}, {"cell_type": "markdown", "id": "d2bad5f5", "metadata": {}, "source": ["## Transform Chunks into Vectors"]}, {"cell_type": "code", "execution_count": 10, "id": "a10900de", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Embedding time for 355 chunks: 34.82 seconds\n"]}], "source": ["# Encoder input is doc.page_content as strings.\n", "list_of_strings = [doc.page_content for doc in chunks if hasattr(doc, 'page_content')]\n", "\n", "# Embedding inference using HuggingFace encoder.\n", "start_time = time.time()\n", "embeddings = torch.tensor(encoder.encode(list_of_strings))\n", "end_time = time.time()\n", "\n", "print(f\"Embedding time for {len(list_of_strings)} chunks: \", end=\"\")\n", "print(f\"{round(end_time - start_time, 2)} seconds\")"]}, {"cell_type": "code", "execution_count": 11, "id": "ea1b9782", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["type embeddings: <class 'list'> of <class 'numpy.ndarray'>\n", "of numbers: <class 'numpy.float32'>\n"]}], "source": ["# Normalize the embeddings.\n", "embeddings = np.array(embeddings / np.linalg.norm(embeddings))\n", "\n", "# <PERSON><PERSON><PERSON><PERSON> expects a list of `numpy.ndarray` of `numpy.float32` numbers.\n", "converted_values = list(map(np.float32, embeddings))\n", "\n", "# Inspect the embeddings.\n", "assert len(chunks[0].page_content) <= MAX_SEQ_LENGTH-1\n", "assert len(converted_values[0]) == EMBEDDING_DIM\n", "print(f\"type embeddings: {type(converted_values)} of {type(converted_values[0])}\")\n", "print(f\"of numbers: {type(converted_values[0][0])}\")"]}, {"cell_type": "markdown", "id": "c4cd5859", "metadata": {}, "source": ["## Insert data into Milvus\n", "\n", "For each original text chunk, we'll write the sextuplet (`chunk, h1, h2, source, dense_vector, sparse_vector`) into the database.\n", "\n", "<div>\n", "<img src=\"../../../pics/db_insert.png\" width=\"80%\"/>\n", "</div>\n", "\n", "**The Milvus Client wrapper can only handle loading data from a list of dictionaries.**\n", "\n", "Otherwise, in general, Milvus supports loading data from:\n", "- pandas dataframes \n", "- list of dictionaries\n", "\n", "Below, we use the embedding model provided by HuggingFace, download its checkpoint, and run it locally as the encoder."]}, {"cell_type": "code", "execution_count": 12, "id": "97f4a20a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Start inserting entities\n", "Milvus insert time for 355 vectors: 0.21 seconds\n"]}], "source": ["# Create dict_list for <PERSON>l<PERSON><PERSON> insertion.\n", "dict_list = []\n", "for chunk, vector in zip(chunks, converted_values):\n", "    # Assemble embedding vector, original text chunk, metadata.\n", "    chunk_dict = {\n", "        'chunk': chunk.page_content,\n", "        'source': chunk.metadata.get('source', \"\"),\n", "        'vector': vector,\n", "    }\n", "    dict_list.append(chunk_dict)\n", "\n", "# # TODO - Uncomment to inspect the first chunk and its metadata.\n", "# print(len(dict_list))\n", "# print(type(dict_list[0]), len(dict_list[0]))\n", "# pprint.pprint(dict_list[0])\n", "\n", "# Insert data into the Milvus collection.\n", "print(\"Start inserting entities\")\n", "start_time = time.time()\n", "mc.insert(\n", "    COLLECTION_NAME,\n", "    data=dict_list,\n", "    progress_bar=True)\n", "\n", "end_time = time.time()\n", "print(f\"<PERSON><PERSON><PERSON><PERSON> insert time for {len(dict_list)} vectors: \", end=\"\")\n", "print(f\"{round(end_time - start_time, 2)} seconds\")"]}, {"cell_type": "markdown", "id": "495bd7f4", "metadata": {}, "source": ["## Define 4 questions\n", "\n", "Search Milvus using [PyMilvus API](https://milvus.io/docs/search.md).\n", "\n", "💡 By their nature, vector searches are \"semantic\" searches.  For example, if you were to search for \"leaky faucet\": \n", "> **Traditional Key-word Search** - either or both words \"leaky\", \"faucet\" would have to match some text in order to return a web page or link text to the document.\n", "\n", "> **Semantic search** - results containing words \"drippy\" \"taps\" would be returned as well because these words mean the same thing even though they are different words.\n"]}, {"cell_type": "code", "execution_count": 13, "id": "da6eb9c8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["example query length: 75\n"]}], "source": ["# Define a sample question about your data.\n", "QUESTION1 = \"What do the parameters for HNSW mean?\"\n", "QUESTION2 = \"What are good default values for HNSW parameters with 25K vectors dim 1024?\"\n", "QUESTION3 = \"What does nlist vs nprobe mean in ivf_flat?\"\n", "QUESTION4 = \"What is the default AUTOINDEX index and vector field distance metric in Milvus?\"\n", "\n", "# In case you want to ask all the questions at once.\n", "QUERY = [QUESTION1, QUESTION2, QUESTION3, QUESTION4]\n", "\n", "# Inspect the length of one question.\n", "QUERY_LENGTH = len(QUESTION2)\n", "print(f\"example query length: {QUERY_LENGTH}\")"]}, {"cell_type": "code", "execution_count": 14, "id": "59452df4", "metadata": {}, "outputs": [], "source": ["SAMPLE_QUESTION = QUESTION1"]}, {"cell_type": "markdown", "id": "c8c91b86", "metadata": {}, "source": ["## Execute a vector search\n", "\n", "Search Milvus using [PyMilvus API](https://milvus.io/docs/search.md).\n", "\n", "💡 By their nature, vector searches are \"semantic\" searches.  For example, if you were to search for \"leaky faucet\": \n", "> **Traditional Key-word Search** - either or both words \"leaky\", \"faucet\" would have to match some text in order to return a web page or link text to the document.\n", "\n", "> **Semantic search** - results containing words \"drippy\" \"taps\" would be returned as well because these words mean the same thing even though they are different words."]}, {"cell_type": "code", "execution_count": 15, "id": "eb738b0f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["output fields: ['chunk', 'source']\n"]}], "source": ["# Define metadata fields you can filter on.\n", "OUTPUT_FIELDS = list(dict_list[0].keys())\n", "OUTPUT_FIELDS.remove('vector')\n", "print(f\"output fields: {OUTPUT_FIELDS}\")\n", "\n", "# Define how many top-k results you want to retrieve.\n", "TOP_K = 2\n", "\n", "# Define a convenience function for searching.\n", "def mc_run_search(question, filter_expression, top_k=2):\n", "    # Embed the question using the same encoder.\n", "    query_embeddings = _utils.embed_query(encoder, [question])\n", "\n", "    # Run semantic vector search using your query and the vector database.\n", "    results = mc.search(\n", "        COLLECTION_NAME,\n", "        data=query_embeddings,\n", "        output_fields=OUTPUT_FIELDS, \n", "        # Milvus can utilize metadata in boolean expressions to filter search.\n", "        filter=filter_expression,\n", "        limit=top_k,\n", "        consistency_level=\"Eventually\"\n", "    )\n", "\n", "    # Assemble retrieved context and context metadata.\n", "    # The search result is in the variable `results[0]`, which is type \n", "    # 'pymilvus.orm.search.SearchResult'. \n", "    METADATA_FIELDS = [f for f in OUTPUT_FIELDS if f != 'chunk']\n", "    formatted_results, context, context_metadata = _utils.client_assemble_retrieved_context(\n", "        results, metadata_fields=METADATA_FIELDS, num_shot_answers=top_k)\n", "    \n", "    return formatted_results, context, context_metadata"]}, {"cell_type": "code", "execution_count": 16, "id": "6be3c61f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["filter: \n", "Milvus Client search time for 355 vectors: 0.17427706718444824 seconds\n", "type: <class 'list'>, count: 2\n"]}], "source": ["# RET<PERSON>EVE ANSWERS FROM YOUR DOCUMENTS STORED IN MILVUS OR ZILLIZ.\n", "\n", "# Metadata filters for CSV dataset.\n", "# expression = 'film_year >= 2019'\n", "expression = \"\"\n", "print(f\"filter: {expression}\")\n", "\n", "start_time = time.time()\n", "formatted_results, contexts, context_metadata = \\\n", "    mc_run_search(SAMPLE_QUESTION, expression)\n", "elapsed_time = time.time() - start_time\n", "print(f\"Milvus Client search time for {len(dict_list)} vectors: {elapsed_time} seconds\")\n", "\n", "# Inspect search result.\n", "print(f\"type: {type(formatted_results)}, count: {len(formatted_results)}\")"]}, {"cell_type": "code", "execution_count": 17, "id": "32b003e9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Retrieved result #1\n", "distance = 0.7001987099647522\n", "('Chunk text: layer, finds the node closest to the target in this layer, and '\n", " 'then enters the next layer to begin another search. After multiple '\n", " 'iterations, it can quickly approach the target position. In order to improve '\n", " 'performance, HNSW limits the maximum degree of nodes on each layer of the '\n", " 'graph to M. In addition, you can use efConstruction (when building index) or '\n", " 'ef (when searching targets) to specify a search range. Index building '\n", " 'parameters Parameter Description Range M M defines tha maximum number of '\n", " 'outgoing')\n", "source: https://milvus.io/docs/index.md\n", "\n", "Retrieved result #2\n", "distance = 0.6953287124633789\n", "('Chunk text: this value can improve recall rate at the cost of increased '\n", " 'search time. [1, 65535] 2 HNSW HNSW (Hierarchical Navigable Small World '\n", " 'Graph) is a graph-based indexing algorithm. It builds a multi-layer '\n", " 'navigation structure for an image according to certain rules. In this '\n", " 'structure, the upper layers are more sparse and the distances between nodes '\n", " 'are farther; the lower layers are denser and the distances between nodes are '\n", " 'closer. The search starts from the uppermost layer, finds the node closest '\n", " 'to the target')\n", "source: https://milvus.io/docs/index.md\n", "\n"]}], "source": ["# Loop through search results, print metadata.\n", "sources = []\n", "for i in range(len(contexts)):\n", "    print(f\"Retrieved result #{i+1}\")\n", "    print(f\"distance = {formatted_results[i][0]}\")\n", "    pprint.pprint(f\"Chunk text: {contexts[i]}\")\n", "    for key, value in context_metadata[i].items():\n", "        if key == \"source\":\n", "            sources.append(value)\n", "        print(f\"{key}: {value}\")\n", "    print()"]}, {"cell_type": "markdown", "id": "9e0a0bdb", "metadata": {}, "source": ["## Get ready for G-Generation part of RAG"]}, {"cell_type": "code", "execution_count": 18, "id": "0cf40205", "metadata": {}, "outputs": [], "source": ["# Define temperature for the LLM and random seed.\n", "TEMPERATURE = 0.1\n", "TOP_P = 0.9\n", "RANDOM_SEED = 415\n", "MAX_TOKENS = 512\n", "FREQUENCY_PENALTY = 2"]}, {"cell_type": "code", "execution_count": 19, "id": "410393e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Length long text to summarize: 1021\n", "sources: https://milvus.io/docs/index.md\n"]}], "source": ["# Separate all the context together by space.\n", "contexts_combined = ' '.join(contexts)\n", "# <PERSON>, <PERSON><PERSON><PERSON><PERSON>, says put the best contexts at the end.\n", "contexts_combined = ' '.join(reversed(contexts))\n", "\n", "print(f\"Length long text to summarize: {len(contexts_combined)}\")\n", "\n", "# Separate all the unique sources together by comma.\n", "source_combined = ' '.join(reversed(list(dict.fromkeys(sources))))\n", "print(f\"sources: {source_combined}\")"]}, {"cell_type": "markdown", "id": "bd6060ce", "metadata": {}, "source": ["# Use an LLM to Generate a chat response to the user's question using the Retrieved Context.\n", "\n", "Many different generative LLMs exist these days.  Check out the lmsys [leaderboard](https://chat.lmsys.org/?leaderboard).\n", "\n", "In this notebook, we'll try these LLMs:\n", "- The newly released open-source Llama 3 from Meta.\n", "- The cheapest, paid model from Anthropic Claude3 Haiku.\n", "- The standard in its price cateogory, gpt-3.5-turbo, from Openai."]}, {"cell_type": "code", "execution_count": 20, "id": "47e5bd6a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Length prompt: 1432\n"]}], "source": ["SYSTEM_PROMPT = f\"\"\"First, check if the provided Context is relevant to \n", "the user's question.  Second, only if the provided Context is strongly relevant, \n", "answer the question using the Context.  Otherwise, if the Context is not \n", "strongly relevant, answer the question without using the Context.  \n", "Be clear, concise, relevant.  Answer clearly, in fewer than 2 sentences.\n", "Grounding sources: {source_combined}\n", "Context: {contexts_combined}\n", "\"\"\"\n", "print(f\"Length prompt: {len(SYSTEM_PROMPT)}\")\n", "\n", "# Inspect the prompt.\n", "# pprint.pprint(SYSTEM_PROMPT)"]}, {"cell_type": "markdown", "id": "11fb35aa", "metadata": {}, "source": ["### Try Meta Llama 3 with <PERSON><PERSON><PERSON> to generate a human-like chat response to the user's question\n", "\n", "Follow the instructions to install ollama and pull a model.<br>\n", "https://github.com/ollama/ollama\n", "\n", "View details about which models are supported by ollama. <br>\n", "https://ollama.com/library/llama3\n", "\n", "That page says `ollama run llama3` will by default pull the latest \"instruct\" model, which is fine-tuned for chat/dialogue use cases.\n", "\n", "The other kind of llama3 models are \"pre-trained\" base model. <br>\n", "Example: ollama run llama3:text ollama run llama3:70b-text\n", "\n", "**Format** `gguf` means the model runs on CPU.  gg = \"<PERSON><PERSON>\", creator of the C library model format ggml, which was recently changed to gguf.\n", "\n", "**Quantization** (think of it like vector compaction) can lead to higher throughput at the expense of lower accuracy.  For the curious, quantization meanings can be found on: <br>\n", "https://huggingface.co/TheBloke/Llama-2-13B-chat-GGML/tree/main.  \n", "\n", "Below just listing the main quantization types.\n", "- **q4_0**: Original quant method, 4-bit.\n", "- **q4_k_m**: Uses Q6_K for half of the attention.wv and feed_forward.w2 tensors, else Q4_K\n", "- **q5_0**: Higher accuracy, higher resource usage and slower inference.\n", "- **q5_k_m**: Uses Q6_K for half of the attention.wv and feed_forward.w2 tensors, else Q5_K\n", "- **q 6_k**: Uses Q8_K for all tensors\n", "- **q8_0**: Almost indistinguishable from float16. High resource use and slow. Not recommended for most users."]}, {"cell_type": "code", "execution_count": 21, "id": "0edc67e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MODEL:llama3.1:latest, FORMAT:gguf, PARAMETER_SIZE:8.0B, QUANTIZATION_LEVEL:Q4_0, \n", "\n"]}], "source": ["# !python -m pip install ollama\n", "# !ollama pull llama3.1\n", "import ollama\n", "\n", "# Verify details which model you are running.\n", "# ollama.list()['models'][0]\n", "\n", "# Print the model details.\n", "keys = ['format', 'parameter_size', 'quantization_level']\n", "print(f\"MODEL:{ollama.list()['models'][0]['name']}\", end=\", \")\n", "for key in keys:\n", "    print(f\"{str.upper(key)}:{ollama.list()['models'][0]['details'].get(key, 'Key not found in dictionary')}\", end=\", \")\n", "print(end=\"\\n\\n\")"]}, {"cell_type": "code", "execution_count": 22, "id": "394f31d0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('The parameter \"M\" in HNSW limits the maximum degree of nodes on each layer, '\n", " 'controlling recall rate and search time. A higher value can improve recall '\n", " 'at a cost of increased search time.')\n", "ollama_llama3_time: 10.65 seconds\n"]}], "source": ["# Send the question to llama 3 chat.\n", "start_time = time.time()\n", "response = ollama.chat(\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": SYSTEM_PROMPT,},\n", "        \n", "        {\"role\": \"user\", \"content\": f\"question: {SAMPLE_QUESTION}\",}\n", "    ],\n", "    model='llama3',\n", "    stream=False,\n", "    options={\"temperature\": TEMPERATURE, \"seed\": RANDOM_SEED,\n", "             \"top_p\": TOP_P, \n", "            #  \"max_tokens\": MAX_TOKENS,\n", "             \"frequency_penalty\": FREQUENCY_PENALTY}\n", ")\n", "\n", "ollama_llama3_time = time.time() - start_time\n", "pprint.pprint(response['message']['content'])\n", "print(f\"ollama_llama3_time: {format(ollama_llama3_time, '.2f')} seconds\")"]}, {"cell_type": "code", "execution_count": 23, "id": "309d7025", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('The parameters for HNSW (Hierarchical Navigable Small World Graph) are:  * '\n", " 'M: The maximum degree of nodes on each layer of the graph, which can improve '\n", " 'recall rate at the cost of increased search time. * efConstruction and ef: '\n", " 'These parameters specify a search range when building or searching an index.')\n", "llama3_octai_endpoints_time: 1.72 seconds\n"]}], "source": ["# Also try OctoAI\n", "# !python -m pip install octoai\n", "# Models supported: https://octo.ai/models/\n", "from octoai.text_gen import ChatMessage\n", "from octoai.client import OctoAI\n", "\n", "# LLM_NAME = \"meta-llama-3-8b-instruct\"\n", "LLM_NAME = \"meta-llama/meta-llama-3.1-8b-instruct\"\n", "\n", "octoai_client = OctoAI(\n", "    api_key=os.environ.get(\"OCTOAI_TOKEN\"),\n", ")\n", "\n", "# Generate response using OpenAI API.\n", "start_time = time.time()\n", "response = octoai_client.text_gen.create_chat_completion(\n", "\tmessages=[\n", "\t\tChatMessage(\n", "\t\t\tcontent=SYSTEM_PROMPT,\n", "\t\t\trole=\"system\"\n", "\t\t),\n", "\t\tChatMessage(\n", "\t\t\tcontent=SAMPLE_QUESTION,\n", "\t\t\trole=\"user\"\n", "\t\t)\n", "\t],\n", "\tmodel=LLM_NAME,\n", "    temperature=TEMPERATURE,\n", "    # seed=RANDOM_SEED,\n", "    frequency_penalty=FREQUENCY_PENALTY,\n", "    top_p=TOP_P, \n", "    max_tokens=MAX_TOKENS,\n", ")\n", "llama3_octai_endpoints_time = time.time() - start_time\n", "\n", "# Print the response.\n", "pprint.pprint(response.choices[0].message.content.replace('\\n', ' '))\n", "print(f\"llama3_octai_endpoints_time: {format(llama3_octai_endpoints_time, '.2f')} seconds\")"]}, {"cell_type": "code", "execution_count": 24, "id": "e6b94795", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('The parameters for HNSW (Hierarchical Navigable Small World Graph) are:  * '\n", " 'M: Maximum degree of nodes on each layer of the graph, which can be adjusted '\n", " 'to balance recall rate and search time. * efConstruction (or ef): Search '\n", " 'range when building the index, which can be used to control the trade-off '\n", " 'between recall rate and search time.')\n", "llama3_groq_endpoints_time: 0.54 seconds\n"]}], "source": ["# Also try <PERSON><PERSON><PERSON> endpoints\n", "# !python -m pip install groq\n", "# Models supported: https://console.groq.com/docs/models\n", "from groq import Groq\n", "\n", "# LLM_NAME = \"llama3-8b-8192\"\n", "LLM_NAME = \"llama-3.1-8b-instant\"\n", "\n", "groq_client = Groq(\n", "    api_key=os.environ.get(\"GROQ_API_KEY\"),\n", ")\n", "\n", "# Generate response using OpenAI API.\n", "start_time = time.time()\n", "response = groq_client.chat.completions.create(\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": SYSTEM_PROMPT,},\n", "        {\"role\": \"user\", \"content\": f\"question: {SAMPLE_QUESTION}\",}\n", "    ],\n", "    model=LLM_NAME,\n", "    temperature=TEMPERATURE,\n", "    seed=RANDOM_SEED,\n", "    frequency_penalty=FREQUENCY_PENALTY,\n", "    top_p=TOP_P, \n", "    max_tokens=MAX_TOKENS,\n", ")\n", "llama3_groq_endpoints_time = time.time() - start_time\n", "\n", "# Print the response.\n", "pprint.pprint(response.choices[0].message.content.replace('\\n', ' '))\n", "print(f\"llama3_groq_endpoints_time: {format(llama3_groq_endpoints_time, '.2f')} seconds\")"]}, {"cell_type": "markdown", "id": "3e172726", "metadata": {}, "source": ["## Also try OpenAI\n", "\n", "💡 Note: For use cases that need to always be factually grounded, use very low temperature values while more creative tasks can benefit from higher temperatures."]}, {"cell_type": "code", "execution_count": 25, "id": "493c5e82", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Length prompt: 1483\n"]}], "source": ["SYSTEM_PROMPT = f\"\"\"First, check if the provided Context is relevant to \n", "the user's question.  Second, only if the provided Context is strongly relevant, \n", "answer the question using the Context.  Otherwise, if the Context is not \n", "strongly relevant, answer the question without using the Context.\n", "Be clear, concise, relevant.  Answer clearly, easy to understand, \n", "fewer than 2 sentences, and cite unique grounding sources.\n", "Grounding sources: {source_combined}\n", "Context: {contexts_combined}\n", "\"\"\"\n", "print(f\"Length prompt: {len(SYSTEM_PROMPT)}\")\n", "\n", "# Inspect the prompt.\n", "# pprint.pprint(SYSTEM_PROMPT)"]}, {"cell_type": "code", "execution_count": 26, "id": "76a62feb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Question: What do the parameters for HNSW mean?\n", "('Answer: The parameters for HNSW (Hierarchical Navigable Small World Graph) '\n", " 'include M, which defines the maximum number of outgoing connections from '\n", " 'each node in the graph, and efConstruction or ef, which specify the search '\n", " 'range during index building and searching respectively. These parameters '\n", " 'help control the trade-off between recall rate and search time.')\n", "\n", "\n", "chatgpt_3.5_turbo_time: 1.57389\n"]}], "source": ["import openai, pprint\n", "from openai import OpenAI\n", "\n", "# 1. Define the generation llm model to use.\n", "# LLM_NAME = \"gpt-3.5-turbo\"\n", "LLM_NAME = \"gpt-4o-mini\"\n", "\n", "# 2. Get your API key: https://platform.openai.com/api-keys\n", "# 3. Save your api key in env variable.\n", "# https://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety\n", "openai_client = OpenAI(\n", "    # This is the default and can be omitted\n", "    api_key=os.environ.get(\"OPENAI_API_KEY\"),\n", ")\n", "\n", "# 4. Generate response using the OpenAI API.\n", "start_time = time.time()\n", "response = openai_client.chat.completions.create(\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": SYSTEM_PROMPT,},\n", "        {\"role\": \"user\", \"content\": f\"question: {SAMPLE_QUESTION}\",}\n", "    ],\n", "    model=LLM_NAME,\n", "    temperature=TEMPERATURE,\n", "    seed=RANDOM_SEED,\n", "    frequency_penalty=FREQUENCY_PENALTY,\n", ")\n", "chatgpt_35turbo_time = time.time() - start_time\n", "\n", "# Print the question and answer along with grounding sources and citations.\n", "print(f\"Question: {SAMPLE_QUESTION}\")\n", "\n", "# 5. Print all answers in the response.\n", "for i, choice in enumerate(response.choices, 1):\n", "    pprint.pprint(f\"Answer: {choice.message.content}\")\n", "    print(\"\\n\")\n", "print(f\"chatgpt_3.5_turbo_time: {format(chatgpt_35turbo_time, '.5f')}\")"]}, {"cell_type": "code", "execution_count": 27, "id": "d0e81e68", "metadata": {}, "outputs": [], "source": ["# # Drop the collection.\n", "# mc.drop_collection(COLLECTION_NAME)\n", "# print(f\"Successfully dropped collection: `{COLLECTION_NAME}`\")"]}, {"cell_type": "code", "execution_count": 28, "id": "c777937e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Author: <PERSON>\n", "\n", "Python implementation: CPython\n", "Python version       : 3.11.8\n", "IPython version      : 8.22.2\n", "\n", "unstructured: 0.14.4\n", "torch       : 2.3.0\n", "pymilvus    : 2.4.4\n", "langchain   : 0.2.6\n", "ollama      : 0.1.8\n", "groq        : 0.8.0\n", "octoai      : 1.0.2\n", "openai      : 1.35.0\n", "\n", "conda environment: py311-unum\n", "\n"]}], "source": ["# Props to <PERSON> for this handy watermark.\n", "# !pip install watermark\n", "%load_ext watermark\n", "\n", "%watermark -a '<PERSON>' -v -p unstructured,torch,pymilvus,langchain,ollama,groq,octoai,openai --conda"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}