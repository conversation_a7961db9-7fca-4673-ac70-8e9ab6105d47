{"cells": [{"cell_type": "markdown", "metadata": {"id": "YH2TplpJDIv6"}, "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/tutorials/quickstart/movie_recommendation_with_milvus.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/tutorials/quickstart/movie_recommendation_with_milvus.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>"]}, {"cell_type": "markdown", "metadata": {"id": "wg1bdO0lDIv6"}, "source": ["# Movie Recommendation with <PERSON><PERSON><PERSON><PERSON>\n", "In this notebook, we will explore how to generate embeddings of movie descriptions using OpenAI and leverage those embeddings within Milvus to recommend movies that match your preferences. To enhance our search results, we will utilize filtering to perform metadata searches. The dataset used in this example is sourced from HuggingFace datasets and contains over 8,000 movie entries, providing a rich pool of options for movie recommendations.\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "Gv0NJ31IDIv7"}, "source": ["## Dependencies and Environment\n", "You can install the dependencies by running the following command:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true, "id": "kdxHDsewDIv7", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "a38ce11b-f668-46ef-9ce2-38fd7d12ba1d"}, "outputs": [], "source": ["! pip install openai pymilvus datasets tqdm"]}, {"cell_type": "markdown", "metadata": {"id": "_sbPlC7tDIv7"}, "source": ["> If you are using Google Colab, to enable dependencies just installed, you may need to **restart the runtime** (click on the \"Runtime\" menu at the top of the screen, and select \"Restart session\" from the dropdown menu)."]}, {"cell_type": "markdown", "metadata": {"id": "08DQFod0DIv7"}, "source": ["We will use OpenAI as the LLM in this example. You should prepare the [api key](https://platform.openai.com/docs/quickstart) `OPENAI_API_KEY` as an environment variable."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "E8LsgeBzDIv8", "ExecuteTime": {"end_time": "2024-11-10T04:43:29.331948Z", "start_time": "2024-11-10T04:43:29.322790Z"}}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-***********\""]}, {"cell_type": "markdown", "metadata": {"id": "aJcCzQhODIv8"}, "source": ["## Initialize OpenAI client and Milvus\n", "Initialize the OpenAI client."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "aBOi2mvKDIv8", "ExecuteTime": {"end_time": "2024-11-10T04:43:29.739181Z", "start_time": "2024-11-10T04:43:29.333409Z"}}, "outputs": [], "source": ["from openai import OpenAI\n", "\n", "openai_client = OpenAI()"]}, {"cell_type": "markdown", "metadata": {"id": "9IO_ts7pDIv8"}, "source": ["Set the collection name and dimension for the embeddings."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "4rXlQ29MDIv8", "ExecuteTime": {"end_time": "2024-11-10T04:43:29.746569Z", "start_time": "2024-11-10T04:43:29.740806Z"}}, "outputs": [], "source": ["COLLECTION_NAME = \"movie_search\"\n", "DIMENSION = 1536\n", "\n", "BATCH_SIZE = 1000"]}, {"cell_type": "markdown", "metadata": {"id": "OHuz5DtVDIv8"}, "source": ["Connect to Milvus."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "VUXhaRtQDIv8", "outputId": "ac2c222b-9af3-4b64-d8dd-cf5b3fe2f376", "ExecuteTime": {"end_time": "2024-11-10T04:44:04.205921Z", "start_time": "2024-11-10T04:44:03.187020Z"}}, "outputs": [], "source": ["from pymilvus import MilvusClient\n", "\n", "# Connect to Milvus Database\n", "client = MilvusClient(\"./milvus_demo.db\")"]}, {"cell_type": "markdown", "metadata": {"id": "U8es09T2DIv8"}, "source": ["> As for the argument of `url` and `token`:\n", "> - Setting the `uri` as a local file, e.g.`./milvus.db`, is the most convenient method, as it automatically utilizes [Milvus Lite](https://milvus.io/docs/milvus_lite.md) to store all data in this file.\n", "> - If you have large scale of data, say more than a million vectors, you can set up a more performant Milvus server on [Docker or Kubernetes](https://milvus.io/docs/quickstart.md). In this setup, please use the server address and port as your uri, e.g.`http://localhost:19530`. If you enable the authentication feature on Milvus, use \"<your_username>:<your_password>\" as the token, otherwise don't set the token.\n", "> - If you want to use [Zilliz Cloud](https://zilliz.com/cloud), the fully managed cloud service for Milvus, adjust the `uri` and `token`, which correspond to the [Public Endpoint and Api key](https://docs.zilliz.com/docs/on-zilliz-cloud-console#free-cluster-details) in Zilliz Cloud."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "eaBFyWQPDIv8", "ExecuteTime": {"end_time": "2024-11-10T04:44:06.851427Z", "start_time": "2024-11-10T04:44:06.768643Z"}}, "outputs": [], "source": ["# Remove collection if it already exists\n", "if client.has_collection(COLLECTION_NAME):\n", "    client.drop_collection(COLLECTION_NAME)"]}, {"cell_type": "markdown", "metadata": {"id": "rmt5EIn8DIv8"}, "source": ["Define the fields for the collection, which include the id, title, type, release year, rating, description, and embedding."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "xf5YMsDmDIv8", "outputId": "b26cb009-8a82-4cfd-9d76-db6323d0df69", "ExecuteTime": {"end_time": "2024-11-10T04:44:08.634998Z", "start_time": "2024-11-10T04:44:08.631894Z"}}, "outputs": [], "source": ["from pymilvus import DataType\n", "\n", "# 1. Create schema\n", "schema = MilvusClient.create_schema(\n", "    auto_id=True,\n", "    enable_dynamic_field=False,\n", ")\n", "\n", "# 2. Add fields to schema\n", "schema.add_field(field_name=\"id\", datatype=DataType.INT64, is_primary=True)\n", "schema.add_field(field_name=\"title\", datatype=DataType.VARCHAR, max_length=64000)\n", "schema.add_field(field_name=\"type\", datatype=DataType.VARCHAR, max_length=64000)\n", "schema.add_field(field_name=\"release_year\", datatype=DataType.INT64)\n", "schema.add_field(field_name=\"rating\", datatype=DataType.VARCHAR, max_length=64000)\n", "schema.add_field(field_name=\"description\", datatype=DataType.VARCHAR, max_length=64000)\n", "schema.add_field(field_name=\"embedding\", datatype=DataType.FLOAT_VECTOR, dim=DIMENSION)\n", "\n", "# 3. Create collection with the schema\n", "client.create_collection(collection_name=COLLECTION_NAME, schema=schema)"]}, {"cell_type": "markdown", "metadata": {"id": "5Ch-2J5CDIv8"}, "source": ["Create the index on the collection and load it."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "K9fwi332DIv8", "outputId": "d9e1e36a-4830-491f-ce3b-be1974a363cf", "ExecuteTime": {"end_time": "2024-11-10T04:44:11.606543Z", "start_time": "2024-11-10T04:44:11.097780Z"}}, "outputs": [], "source": ["# 1. Prepare index parameters\n", "index_params = client.prepare_index_params()\n", "\n", "# 2. Add an index on the embedding field\n", "index_params.add_index(\n", "    field_name=\"embedding\", metric_type=\"IP\", index_type=\"AUTOINDEX\", params={}\n", ")\n", "\n", "# 3. Create index\n", "client.create_index(collection_name=COLLECTION_NAME, index_params=index_params)\n", "\n", "# 4. Load Collection\n", "client.load_collection(collection_name=COLLECTION_NAME)"]}, {"cell_type": "markdown", "metadata": {"id": "L-d-pR5DDIv8"}, "source": ["## Dataset\n", "With Milvus up and running we can begin grabbing our data. `Hugging Face Datasets` is a hub that holds many different user datasets, and for this example we are using HuggingLearners's netflix-shows dataset. This dataset contains movies and their metadata pairs for over 8 thousand movies. We are going to embed each description and store it within Milvus along with its title, type, release_year and rating."]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 220, "referenced_widgets": ["7364d39079fa40c5b5e5abccd2d006b6", "4f8b5d0ad9f942c59b386d6b5f07242e", "7db1a3f59362417586d8739cb791207a", "24517a7598c84e3dbc2864e702fccb86", "3b004c8bc2b147b48d2807262322de40", "d17551bb40fc4a2fbe2a1628e32cab11", "aabf31d74d7b425798c68b83d1c025d8", "d8f8541809964b2a9b2003092b1e1dad", "4ccca81c7b714e75929822ed57cab5d5", "2d941555097e4d49a01b4e240d0ab18f", "81c4aa500d6449b1ac6dcd5ea6365630", "074d1bb0263442c082a83e13cc77a264", "9beb8aa11477425498859e544100b109", "1608f9db979346d9bb294e564b507fce", "971e5d9d61c44905b1f1704178925db1", "7ade9ecc4c2c4bda8ca175dbffedbb12", "0c5a98cdee664622a50c92be5ef4631f", "e7069429122b48208c760be1ee5bcb9f", "4e41b396f14941508899975184e07a71", "a8796c17d961465fa141933ff82cf7d8", "7a231c8d89b844b295ccb656c17d9464", "6ea032bb9ca148b7885bc4cba9c17525", "0d2ccc863fad437f8138fa0b32938f84", "c8cef285e18f4b558ae6a6a4ad5aee92", "2cc3a2b72f154656b8e2c3acc20146b5", "620d6ed5d68f48b281b5f2200dfc7d0b", "537bb70db0814a43aff05c4b2f712322", "5860b6c65c1b4f5aa486e7815e2aecfe", "f326231ed25749f1b81fb0b6fca578f5", "1d992ac5fb2b43f4a9f791730bdea123", "de31aa5b5e4845be980f550a4b2bea91", "eb0436e824f94281af20d69dfcec3ea4", "45a3e27ec99f4685913f2f5f77b846ef"]}, "id": "7pyst3ZBDIv8", "outputId": "4f477b23-f190-4a36-905a-3884e40e70b9", "ExecuteTime": {"end_time": "2024-11-10T04:44:16.177399Z", "start_time": "2024-11-10T04:44:13.214578Z"}}, "outputs": [], "source": ["from datasets import load_dataset\n", "\n", "dataset = load_dataset(\"hugginglearners/netflix-shows\", split=\"train\")"]}, {"cell_type": "markdown", "metadata": {"id": "1-iOxwCMDIv8"}, "source": ["## Insert the Data\n", "Now that we have our data on our machine we can begin embedding it and inserting it into Milvus. The embedding function takes in text and returns the embeddings in a list format."]}, {"cell_type": "code", "execution_count": 10, "metadata": {"id": "YBi6ria2DIv8", "ExecuteTime": {"end_time": "2024-11-10T04:44:16.183133Z", "start_time": "2024-11-10T04:44:16.179165Z"}}, "outputs": [], "source": ["def emb_texts(texts):\n", "    res = openai_client.embeddings.create(input=texts, model=\"text-embedding-3-small\")\n", "    return [res_data.embedding for res_data in res.data]"]}, {"cell_type": "markdown", "metadata": {"id": "D-SJAeQXDIv8"}, "source": ["This next step does the actual inserting. We iterate through all the entries and create batches that we insert once we hit our set batch size. After the loop is over we insert the last remaning batch if it exists."]}, {"cell_type": "code", "source": ["from tqdm import tqdm\n", "\n", "# batch (data to be inserted) is a list of dictionaries\n", "batch = []\n", "\n", "# Embed and insert in batches\n", "for i in tqdm(range(0, len(dataset))):\n", "    batch.append(\n", "        {\n", "            \"title\": dataset[i][\"title\"] or \"\",\n", "            \"type\": dataset[i][\"type\"] or \"\",\n", "            \"release_year\": dataset[i][\"release_year\"] or -1,\n", "            \"rating\": dataset[i][\"rating\"] or \"\",\n", "            \"description\": dataset[i][\"description\"] or \"\",\n", "        }\n", "    )\n", "\n", "    if len(batch) % BATCH_SIZE == 0 or i == len(dataset) - 1:\n", "        embeddings = emb_texts([item[\"description\"] for item in batch])\n", "\n", "        for item, emb in zip(batch, embeddings):\n", "            item[\"embedding\"] = emb\n", "\n", "        client.insert(collection_name=COLLECTION_NAME, data=batch)\n", "        batch = []"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "hMIpsCJUDrO3", "outputId": "957f4502-dc50-49cc-c534-f447a7451445", "ExecuteTime": {"end_time": "2024-11-10T04:44:50.716217Z", "start_time": "2024-11-10T04:44:17.361030Z"}}, "execution_count": 11, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 8807/8807 [00:33<00:00, 264.06it/s]\n"]}]}, {"cell_type": "markdown", "metadata": {"id": "X3pSr28CDIv9"}, "source": ["## Query the Database\n", "With our data safely inserted into Milvus, we can now perform a query. The query takes in a tuple of the movie description you are searching for and the filter to use. More info about the filter can be found [here](https://milvus.io/docs/boolean.md). The search first prints out your description and filter expression. After that for each result we print the score, title, type, release year, rating and description of the result movies."]}, {"cell_type": "code", "execution_count": 12, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ohIMv8stDIv9", "outputId": "67186ad6-3522-4f3f-c20d-c67693c05b5e", "ExecuteTime": {"end_time": "2024-11-10T04:44:51.025459Z", "start_time": "2024-11-10T04:44:50.720202Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Description: movie about a fluffly animal Expression: release_year < 2019 and rating like \"PG%\"\n", "Results:\n", "\tRank: 1 Score: 0.4221227169036865 Title: The Adventures of Tintin\n", "\t\tType: Movie Release Year: 2011 Rating: PG\n", "This 3-D motion capture adapts <PERSON>'s classic comic strip about the adventures\n", "of fearless young journalist <PERSON><PERSON> and his trusty dog, <PERSON><PERSON>.\n", "\n", "\tRank: 2 Score: 0.4041031002998352 Title: Hedgehogs\n", "\t\tType: Movie Release Year: 2016 Rating: PG\n", "When a hedgehog suffering from memory loss forgets his identity, he ends up on a big\n", "city journey with a pigeon to save his habitat from a human threat.\n", "\n", "\tRank: 3 Score: 0.3980264365673065 Title: <PERSON><PERSON><PERSON>\n", "\t\tType: Movie Release Year: 2001 Rating: PG\n", "<PERSON> and <PERSON> outdo themselves with this partially animated tale about an\n", "out-of-shape 40-year-old man who's the host to various organisms.\n", "\n", "\tRank: 4 Score: 0.3947935104370117 Title: The Lamb\n", "\t\tType: Movie Release Year: 2017 Rating: PG\n", "A big-dreaming donkey escapes his menial existence and befriends some free-spirited\n", "animal pals in this imaginative retelling of the Nativity Story.\n", "\n", "\tRank: 5 Score: 0.39370331168174744 Title: Open Season 2\n", "\t\tType: Movie Release Year: 2008 Rating: PG\n", "<PERSON> the buck and his forest-dwelling cohorts must rescue their dachshund pal from\n", "some spoiled pets bent on returning him to domesticity.\n"]}], "source": ["import textwrap\n", "\n", "\n", "def query(query, top_k=5):\n", "    text, expr = query\n", "\n", "    res = client.search(\n", "        collection_name=COLLECTION_NAME,\n", "        data=emb_texts(text),\n", "        filter=expr,\n", "        limit=top_k,\n", "        output_fields=[\"title\", \"type\", \"release_year\", \"rating\", \"description\"],\n", "        search_params={\n", "            \"metric_type\": \"IP\",\n", "            \"params\": {},\n", "        },\n", "    )\n", "\n", "    print(\"Description:\", text, \"Expression:\", expr)\n", "\n", "    for hit_group in res:\n", "        print(\"Results:\")\n", "        for rank, hit in enumerate(hit_group, start=1):\n", "            entity = hit[\"entity\"]\n", "\n", "            print(\n", "                f\"\\tRank: {rank} Score: {hit['distance']:} Title: {entity.get('title', '')}\"\n", "            )\n", "            print(\n", "                f\"\\t\\tType: {entity.get('type', '')} \"\n", "                f\"Release Year: {entity.get('release_year', '')} \"\n", "                f\"Rating: {entity.get('rating', '')}\"\n", "            )\n", "            description = entity.get(\"description\", \"\")\n", "            print(textwrap.fill(description, width=88))\n", "            print()\n", "\n", "\n", "my_query = (\"movie about a fluffly animal\", 'release_year < 2019 and rating like \"PG%\"')\n", "\n", "query(my_query)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}, "colab": {"provenance": []}, "widgets": {"application/vnd.jupyter.widget-state+json": {"7364d39079fa40c5b5e5abccd2d006b6": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_4f8b5d0ad9f942c59b386d6b5f07242e", "IPY_MODEL_7db1a3f59362417586d8739cb791207a", "IPY_MODEL_24517a7598c84e3dbc2864e702fccb86"], "layout": "IPY_MODEL_3b004c8bc2b147b48d2807262322de40"}}, "4f8b5d0ad9f942c59b386d6b5f07242e": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d17551bb40fc4a2fbe2a1628e32cab11", "placeholder": "​", "style": "IPY_MODEL_aabf31d74d7b425798c68b83d1c025d8", "value": "README.md: 100%"}}, "7db1a3f59362417586d8739cb791207a": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d8f8541809964b2a9b2003092b1e1dad", "max": 2812, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_4ccca81c7b714e75929822ed57cab5d5", "value": 2812}}, "24517a7598c84e3dbc2864e702fccb86": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2d941555097e4d49a01b4e240d0ab18f", "placeholder": "​", "style": "IPY_MODEL_81c4aa500d6449b1ac6dcd5ea6365630", "value": " 2.81k/2.81k [00:00&lt;00:00, 186kB/s]"}}, "3b004c8bc2b147b48d2807262322de40": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d17551bb40fc4a2fbe2a1628e32cab11": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "aabf31d74d7b425798c68b83d1c025d8": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d8f8541809964b2a9b2003092b1e1dad": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4ccca81c7b714e75929822ed57cab5d5": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "2d941555097e4d49a01b4e240d0ab18f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "81c4aa500d6449b1ac6dcd5ea6365630": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "074d1bb0263442c082a83e13cc77a264": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_9beb8aa11477425498859e544100b109", "IPY_MODEL_1608f9db979346d9bb294e564b507fce", "IPY_MODEL_971e5d9d61c44905b1f1704178925db1"], "layout": "IPY_MODEL_7ade9ecc4c2c4bda8ca175dbffedbb12"}}, "9beb8aa11477425498859e544100b109": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0c5a98cdee664622a50c92be5ef4631f", "placeholder": "​", "style": "IPY_MODEL_e7069429122b48208c760be1ee5bcb9f", "value": "netflix_titles.csv: 100%"}}, "1608f9db979346d9bb294e564b507fce": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4e41b396f14941508899975184e07a71", "max": 3399670, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_a8796c17d961465fa141933ff82cf7d8", "value": 3399670}}, "971e5d9d61c44905b1f1704178925db1": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7a231c8d89b844b295ccb656c17d9464", "placeholder": "​", "style": "IPY_MODEL_6ea032bb9ca148b7885bc4cba9c17525", "value": " 3.40M/3.40M [00:00&lt;00:00, 25.7MB/s]"}}, "7ade9ecc4c2c4bda8ca175dbffedbb12": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0c5a98cdee664622a50c92be5ef4631f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e7069429122b48208c760be1ee5bcb9f": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4e41b396f14941508899975184e07a71": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a8796c17d961465fa141933ff82cf7d8": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "7a231c8d89b844b295ccb656c17d9464": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6ea032bb9ca148b7885bc4cba9c17525": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0d2ccc863fad437f8138fa0b32938f84": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_c8cef285e18f4b558ae6a6a4ad5aee92", "IPY_MODEL_2cc3a2b72f154656b8e2c3acc20146b5", "IPY_MODEL_620d6ed5d68f48b281b5f2200dfc7d0b"], "layout": "IPY_MODEL_537bb70db0814a43aff05c4b2f712322"}}, "c8cef285e18f4b558ae6a6a4ad5aee92": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5860b6c65c1b4f5aa486e7815e2aecfe", "placeholder": "​", "style": "IPY_MODEL_f326231ed25749f1b81fb0b6fca578f5", "value": "Generating train split: 100%"}}, "2cc3a2b72f154656b8e2c3acc20146b5": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1d992ac5fb2b43f4a9f791730bdea123", "max": 8807, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_de31aa5b5e4845be980f550a4b2bea91", "value": 8807}}, "620d6ed5d68f48b281b5f2200dfc7d0b": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_eb0436e824f94281af20d69dfcec3ea4", "placeholder": "​", "style": "IPY_MODEL_45a3e27ec99f4685913f2f5f77b846ef", "value": " 8807/8807 [00:00&lt;00:00, 47917.27 examples/s]"}}, "537bb70db0814a43aff05c4b2f712322": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5860b6c65c1b4f5aa486e7815e2aecfe": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f326231ed25749f1b81fb0b6fca578f5": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1d992ac5fb2b43f4a9f791730bdea123": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "de31aa5b5e4845be980f550a4b2bea91": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "eb0436e824f94281af20d69dfcec3ea4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "45a3e27ec99f4685913f2f5f77b846ef": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}}}}, "nbformat": 4, "nbformat_minor": 0}