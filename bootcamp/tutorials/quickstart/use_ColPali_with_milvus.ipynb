{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/tutorials/quickstart/use_ColPali_with_milvus.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/tutorials/quickstart/use_ColPali_with_milvus.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Use ColPali for Multi-Modal Retrieval with Milvus\n", "\n", "Modern retrieval models typically use a single embedding to represent text or images. ColBERT, however, is a neural model that utilizes a list of embeddings for each data instance and employs a \"MaxSim\" operation to calculate the similarity between two texts. Beyond textual data, figures, tables, and diagrams also contain rich information, which is often disregarded in text-based information retrieval.\n", "\n", "![](../../pics/colpali_formula.png)\n", "\n", "MaxSim function compares a query with a document (what you're searching in) by looking at their token embeddings. For each word in the query, it picks the most similar word from the document (using cosine similarity or squared L2 distance) and sums these maximum similarities across all words in the query\n", "\n", "ColPali is a method that combines ColBERT's multi-vector representation with PaliGemma (a multimodal large language model) to leverage its strong understanding capabilities. This approach enables a page with both text and images to be represented using a unified multi-vector embedding. The embeddings within this multi-vector representation can capture detailed information, improving the performance of retrieval-augmented generation (RAG) for multimodal data.\n", "\n", " In this notebook, we refer to this kind of multi-vector representation as \"ColBERT embeddings\" for generality. However, the actual model being used is the **ColPali model**. We will demonstrate how to use Milvus for multi-vector retrieval. Building on that, we will introduce how to use ColPali for retrieving pages based on a given query.\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Preparation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install pdf2image\n", "!pip install pymilvus\n", "!pip install colpali_engine\n", "!pip install tqdm\n", "!pip install pillow"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prepare the data\n", "We will use PDF RAG as our example. You can download [ColBERT](https://arxiv.org/pdf/2004.12832) paper and put it into `./pdf`. ColPali does not process text directly; instead, the entire page is rasterized into an image. The ColPali model excels at understanding the textual information contained within these images. Therefore, we will convert each PDF page into an image for processing."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from pdf2image import convert_from_path\n", "\n", "pdf_path = \"pdfs/2004.12832v2.pdf\"\n", "images = convert_from_path(pdf_path)\n", "\n", "for i, image in enumerate(images):\n", "    image.save(f\"pages/page_{i + 1}.png\", \"PNG\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next, we will initialize a database using Milvus Lite. You can easily switch to a full Milvus instance by setting the uri to the appropriate address where your Milvus service is hosted."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from pymilvus import MilvusClient, DataType\n", "import numpy as np\n", "import concurrent.futures\n", "\n", "client = MilvusClient(uri=\"milvus.db\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "> - If you only need a local vector database for small scale data or prototyping, setting the uri as a local file, e.g.`./milvus.db`, is the most convenient method, as it automatically utilizes [Milvus Lite](https://milvus.io/docs/milvus_lite.md) to store all data in this file.\n", "> - If you have large scale of data, say more than a million vectors, you can set up a more performant Milvus server on [Docker or Kubernetes](https://milvus.io/docs/quickstart.md). In this setup, please use the server address and port as your uri, e.g.`http://localhost:19530`. If you enable the authentication feature on Milvus, use \"<your_username>:<your_password>\" as the token, otherwise don't set the token.\n", "> - If you use [Zilliz Cloud](https://zilliz.com/cloud), the fully managed cloud service for Milvus, adjust the `uri` and `token`, which correspond to the [Public Endpoint and API key](https://docs.zilliz.com/docs/on-zilliz-cloud-console#cluster-details) in Zilliz Cloud."]}, {"cell_type": "markdown", "metadata": {}, "source": ["We will define a MilvusColbertRetriever class to wrap around the Milvus client for multi-vector data retrieval. The implementation flattens ColBERT embeddings and inserts them into a collection, where each row represents an individual embedding from the ColBERT embedding list. It also records the doc_id and seq_id to trace the origin of each embedding.\n", "\n", "When searching with a ColBERT embedding list, multiple searches will be conducted—one for each ColBERT embedding. The retrieved doc_ids will then be deduplicated. A reranking process will be performed, where the full embeddings for each doc_id are fetched, and the MaxSim score is calculated to produce the final ranked results.\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["class MilvusColbertRetriever:\n", "    def __init__(self, milvus_client, collection_name, dim=128):\n", "        # Initialize the retriever with a Milvus client, collection name, and dimensionality of the vector embeddings.\n", "        # If the collection exists, load it.\n", "        self.collection_name = collection_name\n", "        self.client = milvus_client\n", "        if self.client.has_collection(collection_name=self.collection_name):\n", "            self.client.load_collection(collection_name)\n", "        self.dim = dim\n", "\n", "    def create_collection(self):\n", "        # Create a new collection in Milvus for storing embeddings.\n", "        # Drop the existing collection if it already exists and define the schema for the collection.\n", "        if self.client.has_collection(collection_name=self.collection_name):\n", "            self.client.drop_collection(collection_name=self.collection_name)\n", "        schema = self.client.create_schema(\n", "            auto_id=True,\n", "            enable_dynamic_fields=True,\n", "        )\n", "        schema.add_field(field_name=\"pk\", datatype=DataType.INT64, is_primary=True)\n", "        schema.add_field(\n", "            field_name=\"vector\", datatype=DataType.FLOAT_VECTOR, dim=self.dim\n", "        )\n", "        schema.add_field(field_name=\"seq_id\", datatype=DataType.INT16)\n", "        schema.add_field(field_name=\"doc_id\", datatype=DataType.INT64)\n", "        schema.add_field(field_name=\"doc\", datatype=DataType.VARCHAR, max_length=65535)\n", "\n", "        self.client.create_collection(\n", "            collection_name=self.collection_name, schema=schema\n", "        )\n", "\n", "    def create_index(self):\n", "        # Create an index on the vector field to enable fast similarity search.\n", "        # Releases and drops any existing index before creating a new one with specified parameters.\n", "        self.client.release_collection(collection_name=self.collection_name)\n", "        self.client.drop_index(\n", "            collection_name=self.collection_name, index_name=\"vector\"\n", "        )\n", "        index_params = self.client.prepare_index_params()\n", "        index_params.add_index(\n", "            field_name=\"vector\",\n", "            index_name=\"vector_index\",\n", "            index_type=\"HNSW\",  # or any other index type you want\n", "            metric_type=\"IP\",  # or the appropriate metric type\n", "            params={\n", "                \"M\": 16,\n", "                \"efConstruction\": 500,\n", "            },  # adjust these parameters as needed\n", "        )\n", "\n", "        self.client.create_index(\n", "            collection_name=self.collection_name, index_params=index_params, sync=True\n", "        )\n", "\n", "    def create_scalar_index(self):\n", "        # Create a scalar index for the \"doc_id\" field to enable fast lookups by document ID.\n", "        self.client.release_collection(collection_name=self.collection_name)\n", "\n", "        index_params = self.client.prepare_index_params()\n", "        index_params.add_index(\n", "            field_name=\"doc_id\",\n", "            index_name=\"int32_index\",\n", "            index_type=\"INVERTED\",  # or any other index type you want\n", "        )\n", "\n", "        self.client.create_index(\n", "            collection_name=self.collection_name, index_params=index_params, sync=True\n", "        )\n", "\n", "    def search(self, data, topk):\n", "        # Perform a vector search on the collection to find the top-k most similar documents.\n", "        search_params = {\"metric_type\": \"IP\", \"params\": {}}\n", "        results = self.client.search(\n", "            self.collection_name,\n", "            data,\n", "            limit=int(50),\n", "            output_fields=[\"vector\", \"seq_id\", \"doc_id\"],\n", "            search_params=search_params,\n", "        )\n", "        doc_ids = set()\n", "        for r_id in range(len(results)):\n", "            for r in range(len(results[r_id])):\n", "                doc_ids.add(results[r_id][r][\"entity\"][\"doc_id\"])\n", "\n", "        scores = []\n", "\n", "        def rerank_single_doc(doc_id, data, client, collection_name):\n", "            # Rerank a single document by retrieving its embeddings and calculating the similarity with the query.\n", "            doc_colbert_vecs = client.query(\n", "                collection_name=collection_name,\n", "                filter=f\"doc_id in [{doc_id}]\",\n", "                output_fields=[\"seq_id\", \"vector\", \"doc\"],\n", "                limit=1000,\n", "            )\n", "            doc_vecs = np.vstack(\n", "                [doc_colbert_vecs[i][\"vector\"] for i in range(len(doc_colbert_vecs))]\n", "            )\n", "            score = np.dot(data, doc_vecs.T).max(1).sum()\n", "            return (score, doc_id)\n", "\n", "        with concurrent.futures.ThreadPoolExecutor(max_workers=300) as executor:\n", "            futures = {\n", "                executor.submit(\n", "                    rerank_single_doc, doc_id, data, client, self.collection_name\n", "                ): doc_id\n", "                for doc_id in doc_ids\n", "            }\n", "            for future in concurrent.futures.as_completed(futures):\n", "                score, doc_id = future.result()\n", "                scores.append((score, doc_id))\n", "\n", "        scores.sort(key=lambda x: x[0], reverse=True)\n", "        if len(scores) >= topk:\n", "            return scores[:topk]\n", "        else:\n", "            return scores\n", "\n", "    def insert(self, data):\n", "        # Insert ColBERT embeddings and metadata for a document into the collection.\n", "        colbert_vecs = [vec for vec in data[\"colbert_vecs\"]]\n", "        seq_length = len(colbert_vecs)\n", "        doc_ids = [data[\"doc_id\"] for i in range(seq_length)]\n", "        seq_ids = list(range(seq_length))\n", "        docs = [\"\"] * seq_length\n", "        docs[0] = data[\"filepath\"]\n", "\n", "        # Insert the data as multiple vectors (one for each sequence) along with the corresponding metadata.\n", "        self.client.insert(\n", "            self.collection_name,\n", "            [\n", "                {\n", "                    \"vector\": colbert_vecs[i],\n", "                    \"seq_id\": seq_ids[i],\n", "                    \"doc_id\": doc_ids[i],\n", "                    \"doc\": docs[i],\n", "                }\n", "                for i in range(seq_length)\n", "            ],\n", "        )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We will use the [colpali_engine](https://github.com/illuin-tech/colpali) to extract embedding lists for two queries and retrieve the relevant information from the PDF pages.\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from colpali_engine.models import ColPali\n", "from colpali_engine.models.paligemma.colpali.processing_colpali import ColPaliProcessor\n", "from colpali_engine.utils.processing_utils import BaseVisualRetrieverProcessor\n", "from colpali_engine.utils.torch_utils import ListDataset, get_torch_device\n", "from torch.utils.data import DataLoader\n", "import torch\n", "from typing import List, cast\n", "\n", "device = get_torch_device(\"cpu\")\n", "model_name = \"vidore/colpali-v1.2\"\n", "\n", "model = ColPali.from_pretrained(\n", "    model_name,\n", "    torch_dtype=torch.bfloat16,\n", "    device_map=device,\n", ").eval()\n", "\n", "queries = [\n", "    \"How to end-to-end retrieval with <PERSON><PERSON><PERSON>?\",\n", "    \"Where is ColBERT performance table?\",\n", "]\n", "\n", "processor = cast(ColPaliProcessor, ColPaliProcessor.from_pretrained(model_name))\n", "\n", "dataloader = DataLoader(\n", "    dataset=ListDataset[str](queries),\n", "    batch_size=1,\n", "    shuffle=False,\n", "    collate_fn=lambda x: processor.process_queries(x),\n", ")\n", "\n", "qs: List[torch.Tensor] = []\n", "for batch_query in dataloader:\n", "    with torch.no_grad():\n", "        batch_query = {k: v.to(model.device) for k, v in batch_query.items()}\n", "        embeddings_query = model(**batch_query)\n", "    qs.extend(list(torch.unbind(embeddings_query.to(\"cpu\"))))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Additionally, we will need to extract the embedding list for each page and it shows there are 1030 128-dimensional embeddings for each page."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 10/10 [01:22<00:00,  8.24s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([1030, 128])\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["from tqdm import tqdm\n", "from PIL import Image\n", "import os\n", "\n", "images = [Image.open(\"./pages/\" + name) for name in os.listdir(\"./pages\")]\n", "\n", "dataloader = DataLoader(\n", "    dataset=ListDataset[str](images),\n", "    batch_size=1,\n", "    shuffle=False,\n", "    collate_fn=lambda x: processor.process_images(x),\n", ")\n", "\n", "ds: List[torch.Tensor] = []\n", "for batch_doc in tqdm(dataloader):\n", "    with torch.no_grad():\n", "        batch_doc = {k: v.to(model.device) for k, v in batch_doc.items()}\n", "        embeddings_doc = model(**batch_doc)\n", "    ds.extend(list(torch.unbind(embeddings_doc.to(\"cpu\"))))\n", "\n", "print(ds[0].shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We will create a collection called \"colpali\" using MilvusColbertRetriever."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["retriever = MilvusColbertRetriever(collection_name=\"colpali\", milvus_client=client)\n", "retriever.create_collection()\n", "retriever.create_index()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We will insert embedding lists to the Milvus database."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["filepaths = [\"./pages/\" + name for name in os.listdir(\"./pages\")]\n", "for i in range(len(filepaths)):\n", "    data = {\n", "        \"colbert_vecs\": ds[i].float().numpy(),\n", "        \"doc_id\": i,\n", "        \"filepath\": filepaths[i],\n", "    }\n", "    retriever.insert(data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we can search the most relevant page using query embedding list."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["./pages/page_5.png\n", "./pages/page_7.png\n"]}], "source": ["for query in qs:\n", "    query = query.float().numpy()\n", "    result = retriever.search(query, topk=1)\n", "    print(filepaths[result[0][1]])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Finally, we retrieve the original page name. With Col<PERSON>ali, we can retrieve multimodal documents without the need for complex processing techniques to extract text and images from the documents. By leveraging large vision models, more information—such as tables and figures—can be analyzed without significant information loss."]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}