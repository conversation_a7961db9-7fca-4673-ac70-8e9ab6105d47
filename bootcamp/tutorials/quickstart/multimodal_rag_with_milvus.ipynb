{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/tutorials/quickstart/multimodal_rag_with_milvus.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/tutorials/quickstart/multimodal_rag_with_milvus.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Multimodal RAG with <PERSON><PERSON><PERSON><PERSON> 🖼️\n", "\n", "If you want to experience the final effect of this tutorial, you can go directly to [online demo](https://demos.milvus.io/multimodal-image-search/)\n", "\n", "<img src=\"https://raw.githubusercontent.com/milvus-io/bootcamp/master/tutorials/quickstart/apps/multimodal_rag_with_milvus/pics/step3.jpg\n", "\"/>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This tutorial showcases the multimodal RAG powered by Milvus, [Visualized BGE model](https://github.com/FlagOpen/FlagEmbedding/tree/master/FlagEmbedding/visual), and [GPT-4o](https://openai.com/index/hello-gpt-4o/). With this system, users are able to upload an image and edit text instructions, which are processed by BGE's composed retrieval model to search for candidate images. GPT-4o then acts as a reranker, selecting the most suitable image and providing the rationale behind the choice. This powerful combination enables a seamless and intuitive image search experience, leveraging Milvus for efficient retrieval, BGE model for precise image processing and matching, and GPT-4o for advanced reranking."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Preparation\n", "\n", "### Install Dependencies"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["!pip install --upgrade pymilvus openai datasets opencv-python timm einops ftfy peft tqdm"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["!git clone https://github.com/FlagOpen/FlagEmbedding.git\n", "!pip install -e FlagEmbedding"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> If you are using Google Colab, to enable dependencies just installed, you may need to **restart the runtime** (click on the \"Runtime\" menu at the top of the screen, and select \"Restart session\" from the dropdown menu)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Download Data\n", "\n", "The following command will download the example data and extract to a local folder \"./images_folder\" including:\n", "\n", "- **images**: A subset of [Amazon Reviews 2023](https://github.com/hyp1231/AmazonReviews2023) as containing approximately 900 images from the categories \"Appliance\", \"Cell_Phones_and_Accessories\", and \"Electronics\".\n", "- **leopard.jpg**: An example query image."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["!wget https://github.com/milvus-io/bootcamp/releases/download/data/amazon_reviews_2023_subset.tar.gz\n", "!tar -xzf amazon_reviews_2023_subset.tar.gz"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load Embedding Model\n", "\n", "We will use the Visualized BGE model \"bge-visualized-base-en-v1.5\" to generate embeddings for both images and text. \n", "\n", "**1. Download weight**"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["!wget https://huggingface.co/BAAI/bge-visualized/resolve/main/Visualized_base_en_v1.5.pth"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**2. Build encoder**"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": []}], "source": ["import torch\n", "from FlagEmbedding.visual.modeling import Visualized_BGE\n", "\n", "\n", "class Encoder:\n", "    def __init__(self, model_name: str, model_path: str):\n", "        self.model = Visualized_BGE(model_name_bge=model_name, model_weight=model_path)\n", "        self.model.eval()\n", "\n", "    def encode_query(self, image_path: str, text: str) -> list[float]:\n", "        with torch.no_grad():\n", "            query_emb = self.model.encode(image=image_path, text=text)\n", "        return query_emb.tolist()[0]\n", "\n", "    def encode_image(self, image_path: str) -> list[float]:\n", "        with torch.no_grad():\n", "            query_emb = self.model.encode(image=image_path)\n", "        return query_emb.tolist()[0]\n", "\n", "\n", "model_name = \"BAAI/bge-base-en-v1.5\"\n", "model_path = \"./Visualized_base_en_v1.5.pth\"  # Change to your own value if using a different model path\n", "encoder = Encoder(model_name, model_path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Data\n", "\n", "This section will load example images into the database with corresponding embeddings.\n", "\n", "### Generate embeddings\n", "\n", "Load all jpeg images from the data directory and apply the encoder to convert images to embeddings."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Generating image embeddings: 100%|██████████| 900/900 [00:20<00:00, 44.08it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of encoded images: 900\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["import os\n", "from tqdm import tqdm\n", "from glob import glob\n", "\n", "\n", "# Generate embeddings for the image dataset\n", "data_dir = (\n", "    \"./images_folder\"  # Change to your own value if using a different data directory\n", ")\n", "image_list = glob(\n", "    os.path.join(data_dir, \"images\", \"*.jpg\")\n", ")  # We will only use images ending with \".jpg\"\n", "image_dict = {}\n", "for image_path in tqdm(image_list, desc=\"Generating image embeddings: \"):\n", "    try:\n", "        image_dict[image_path] = encoder.encode_image(image_path)\n", "    except Exception as e:\n", "        print(f\"Failed to generate embedding for {image_path}. Skipped.\")\n", "        continue\n", "print(\"Number of encoded images:\", len(image_dict))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Insert into Milvus\n", "\n", "Insert images with corresponding paths and embeddings into Milvus collection.\n", "\n", "> As for the argument of `MilvusClient`:\n", "> - Setting the `uri` as a local file, e.g. `./milvus_demo.db`, is the most convenient method, as it automatically utilizes [Milvus Lite](https://milvus.io/docs/milvus_lite.md) to store all data in this file.\n", "> - If you have large scale of data, you can set up a more performant Milvus server on [docker or kubernetes](https://milvus.io/docs/quickstart.md). In this setup, please use the server uri, e.g.`http://localhost:19530`, as your `uri`.\n", "> - If you want to use [Zilliz Cloud](https://zilliz.com/cloud), the fully managed cloud service for Milvus, adjust the `uri` and `token`, which correspond to the [Public Endpoint and Api key](https://docs.zilliz.com/docs/on-zilliz-cloud-console#free-cluster-details) in Zilliz Cloud."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["DEBUG:pymilvus.milvus_client.milvus_client:Created new connection using: 7f33daeed99a4d8e8a5e28d47673ecc8\n", "DEBUG:pymilvus.milvus_client.milvus_client:Successfully created collection: multimodal_rag_demo\n", "DEBUG:pymilvus.milvus_client.milvus_client:Successfully created an index on collection: multimodal_rag_demo\n"]}, {"data": {"text/plain": ["{'insert_count': 900,\n", " 'ids': [451537887696781312, 451537887696781313, ..., 451537887696782211],\n", " 'cost': 0}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from pymilvus import MilvusClient\n", "\n", "\n", "dim = len(list(image_dict.values())[0])\n", "collection_name = \"multimodal_rag_demo\"\n", "\n", "# Connect to Milvus client given URI\n", "milvus_client = MilvusClient(uri=\"./milvus_demo.db\")\n", "\n", "# Create Milvus Collection\n", "# By default, vector field name is \"vector\"\n", "milvus_client.create_collection(\n", "    collection_name=collection_name,\n", "    auto_id=True,\n", "    dimension=dim,\n", "    enable_dynamic_field=True,\n", ")\n", "\n", "# Insert data into collection\n", "milvus_client.insert(\n", "    collection_name=collection_name,\n", "    data=[{\"image_path\": k, \"vector\": v} for k, v in image_dict.items()],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Multimodal Search with Generative Reranker\n", "\n", "In this section, we will firstly search for relevant images by a multimodal query and then use LLM service to rerank the results and find the best one with explanation.\n", "\n", "### Run search\n", "\n", "Now we are ready to perform the advanced image search with query data composed of both image and text instruction."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['./images_folder/images/518Gj1WQ-RL._AC_.jpg', './images_folder/images/41n00AOfWhL._AC_.jpg', './images_folder/images/51Wqge9HySL._AC_.jpg', './images_folder/images/51R2SZiywnL._AC_.jpg', './images_folder/images/516PebbMAcL._AC_.jpg', './images_folder/images/51RrgfYKUfL._AC_.jpg', './images_folder/images/515DzQVKKwL._AC_.jpg', './images_folder/images/51BsgVw6RhL._AC_.jpg', './images_folder/images/51INtcXu9FL._AC_.jpg']\n"]}], "source": ["query_image = os.path.join(\n", "    data_dir, \"leopard.jpg\"\n", ")  # Change to your own query image path\n", "query_text = \"phone case with this image theme\"\n", "\n", "# Generate query embedding given image and text instructions\n", "query_vec = encoder.encode_query(image_path=query_image, text=query_text)\n", "\n", "search_results = milvus_client.search(\n", "    collection_name=collection_name,\n", "    data=[query_vec],\n", "    output_fields=[\"image_path\"],\n", "    limit=9,  # Max number of search results to return\n", "    search_params={\"metric_type\": \"COSINE\", \"params\": {}},  # Search parameters\n", ")[0]\n", "\n", "retrieved_images = [hit.get(\"entity\").get(\"image_path\") for hit in search_results]\n", "print(retrieved_images)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Rerank with GPT-4o\n", "\n", "We will use an LLM to rank images and generate an explanation for the best result based on the user query and retrieved results.\n", "\n", "**1. Create a panoramic view**"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import cv2\n", "\n", "img_height = 300\n", "img_width = 300\n", "row_count = 3\n", "\n", "\n", "def create_panoramic_view(query_image_path: str, retrieved_images: list) -> np.ndarray:\n", "    \"\"\"\n", "    creates a 5x5 panoramic view image from a list of images\n", "\n", "    args:\n", "        images: list of images to be combined\n", "\n", "    returns:\n", "        np.ndarray: the panoramic view image\n", "    \"\"\"\n", "    panoramic_width = img_width * row_count\n", "    panoramic_height = img_height * row_count\n", "    panoramic_image = np.full(\n", "        (panoramic_height, panoramic_width, 3), 255, dtype=np.uint8\n", "    )\n", "\n", "    # create and resize the query image with a blue border\n", "    query_image_null = np.full((panoramic_height, img_width, 3), 255, dtype=np.uint8)\n", "    query_image = Image.open(query_image_path).convert(\"RGB\")\n", "    query_array = np.array(query_image)[:, :, ::-1]\n", "    resized_image = cv2.resize(query_array, (img_width, img_height))\n", "\n", "    border_size = 10\n", "    blue = (255, 0, 0)  # blue color in BGR\n", "    bordered_query_image = cv2.copyMakeBorder(\n", "        resized_image,\n", "        border_size,\n", "        border_size,\n", "        border_size,\n", "        border_size,\n", "        cv2.BORDER_CONSTANT,\n", "        value=blue,\n", "    )\n", "\n", "    query_image_null[img_height * 2 : img_height * 3, 0:img_width] = cv2.resize(\n", "        bordered_query_image, (img_width, img_height)\n", "    )\n", "\n", "    # add text \"query\" below the query image\n", "    text = \"query\"\n", "    font_scale = 1\n", "    font_thickness = 2\n", "    text_org = (10, img_height * 3 + 30)\n", "    cv2.putText(\n", "        query_image_null,\n", "        text,\n", "        text_org,\n", "        cv2.FONT_HERSHEY_SIMPLEX,\n", "        font_scale,\n", "        blue,\n", "        font_thickness,\n", "        cv2.LINE_AA,\n", "    )\n", "\n", "    # combine the rest of the images into the panoramic view\n", "    retrieved_imgs = [\n", "        np.array(Image.open(img).convert(\"RGB\"))[:, :, ::-1] for img in retrieved_images\n", "    ]\n", "    for i, image in enumerate(retrieved_imgs):\n", "        image = cv2.resize(image, (img_width - 4, img_height - 4))\n", "        row = i // row_count\n", "        col = i % row_count\n", "        start_row = row * img_height\n", "        start_col = col * img_width\n", "\n", "        border_size = 2\n", "        bordered_image = cv2.copyMakeBorder(\n", "            image,\n", "            border_size,\n", "            border_size,\n", "            border_size,\n", "            border_size,\n", "            cv2.BORDER_CONSTANT,\n", "            value=(0, 0, 0),\n", "        )\n", "        panoramic_image[\n", "            start_row : start_row + img_height, start_col : start_col + img_width\n", "        ] = bordered_image\n", "\n", "        # add red index numbers to each image\n", "        text = str(i)\n", "        org = (start_col + 50, start_row + 30)\n", "        (font_width, font_height), baseline = cv2.getTextSize(\n", "            text, cv2.FONT_HERSHEY_SIMPLEX, 1, 2\n", "        )\n", "\n", "        top_left = (org[0] - 48, start_row + 2)\n", "        bottom_right = (org[0] - 48 + font_width + 5, org[1] + baseline + 5)\n", "\n", "        cv2.rectangle(\n", "            panoramic_image, top_left, bottom_right, (255, 255, 255), cv2.FILLED\n", "        )\n", "        cv2.putText(\n", "            panoramic_image,\n", "            text,\n", "            (start_col + 10, start_row + 30),\n", "            cv2.FONT_HERSHEY_SIMPLEX,\n", "            1,\n", "            (0, 0, 255),\n", "            2,\n", "            cv2.LINE_AA,\n", "        )\n", "\n", "    # combine the query image with the panoramic view\n", "    panoramic_image = np.hstack([query_image_null, panoramic_image])\n", "    return panoramic_image"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Combine the query image and retrieved images with indices in a panoramic view."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"image/jpeg": "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", "image/png": "iVBORw0KGgoAAAANSUhEUgAAASwAAAEsCAIAAAD2HxkiAAEAAElEQVR4AeT9BYBeV5n4j7/uOu+4RyeujTRN29TdjRYKRRaKFV1YWGAXWGSxwgJLKVaHUi/VpGmSxt0zmUlmkkzG9XWX/+c5953JpC1Q2P3+/t9vuJnc98q5Rx8/z3mOvlAo6M7co7e3993vfnd1dbVOl9frDHnVUr1On8tn+Sd3enXS68f6Qf9nrtWnf/1EfqpDC5Kx3MhdoUCu6ls91/KEOhQTqsfFpDyV7wrF19RYx4fGbDYnyccdqpI6g8Gwe/duzgsWLJD3BZ0qVdIm0kn+VF5jn0kS+S+HNuicqc1YKu3laBItYbGqkjcPVLVHX7zNL2nk84IuP5qrfEGjtM/HfyGPtcLHP5V0kon0k05nNBkP7j3gsDrmzZ9HKrLO6w2G4lCpXnprvuNz+x9dU5o0mUOapNcbjIZCvpDNZpPJlN1ms9ntuRx3mQLjIEmLidUXf8Pp0KFDpr8h+f+DSZPJ5M233PzRuz/6prr/4fHHf/ebB61Wk4AI/SvdKOhhNpsVfAvS8G0kEtFGXEtAGu2tlvhNeWpvORsMeqfN4nE5PE6HUZ83m0zJVDqXz6thKlBYXp8zCHDqGFQylNx0XBVsJqvdZrVapA75Qs5gMbic/oOHjvcP9huNBqPRaLXaLBbqaOFWDqMxl03fdsed//71fyeT8cdwdLhrpMeopyzJX0qQElUz1a2+kAOkAR3qAOpqEGTQG8F5uZeWqnR8V8xB+kddahnK27ccOYpQn5MzhcnHCgHJ880wOtaTWiZjCdQF9aKUgt1h//aXv1lTWv2tb3/rLWX9f/ogncoEg0GTyTQcDq9b98am9Rtmz5t1xRWXTp3U9D+sx7333nuGIyEjnYONCAzqCtlc6uWX88PDjltu7u7q3rZzk91iByXoWRKMgRqwzTXncDg8MjKSzwOkgio8IRmJuc3lcjzR4IbnXJBMy0RS6vIlHqfP5Qj4PQ6rEXCPJ5PpPAAKjENPjTp9HoyCy/GJgLYgQ95sMdvBMbPRbAK5DPLQYGlr62lt6zSAefyz2uwWm9lqlRqqf9Sor6eH+kk2ZARvScRSza2O+fPSGcAmIwkFcVRBwmThSNqhEW4+ldaRRJ30eUlDd5FQcKCYdhxC8kTlNfbmtAt4gpI4CiahEcZCTjoKCUSnyxTTncpSZTR6K5UXKlE8VIukAtmMgTyyORlBjnw2E3/mucLwoP3mW4yBgLDXYtu09/9Hzv39A83NLZFw9D+/+92lZ5/9ic/cc/NNt3qc7hdWvnigreWTd39sTtMMqKmOYf+7DpjpGY6EY93CkIYe+G30O98ymW25PXsNExpMJoFsUAa8AnM4xhJreMWt1WoFjAAQgXt1CPIYDNpDEmivBILUwYWC33w2mzdZrPFU2mgw63OZTCYLf+RT3nIq5EAiHSyNLHloFHDNWiwWVQr4nIPXUa39B050dQ46HXYj+Gm3m0xmi8WGhEYmArXwToUrReClCqlk5Kv/Fl+/wbFxPe3SF+TPaNTxP5/TwYpBbFVN+Q5kU1kI5goCqPZpDZK343qDt0UMUT00ijhai0fPPFVv9SIw5vce2HX0yJGFCxbVN0wSCqMzqpxHP9V6evQTyUKaIp0o/+Xglht1MY4WhH/4w8j99xudjuiatZUPPwTdGq2RlqP69H/jRF/FY7F4PHnoUPOx9uPLlp+3cEH1jp27X1u92vfoYx+/++M3XHd9VV3Nn15f+fQLL06un+iw2oqj8HeV/nei799V1v9fP0omko/9vuxXv6t4Y31+7+5s8yGjyQSfoE4wMZBKwyvgkUMk/UxGY27ckkY9Frqu3QKj2oX2SgFt8QTEQhWTmXw4nsqim6XSeqPFZHGkUznUB7Q7s83i9ngsoLc+nylkgH67C5Zs0pv1OiNaqz5TMARjmR27Wrt6hmB+4B5kwkh1jSawVLE8CpGSc5ks4KIBIMXHDu6PvPySzW4VSVAAOW8y5nt7jm/dvK6n+7gJ9stDaid//Dfm9aaCzsifQW/S60y6gvpTTUI+VX8KE+gBDTko9M/9yVeCR2aTcd/efT//yX0vPr/q1798IBKMgN18TiUV6qte4lLqPy43De0kc62s0bNKV8SwTCb8+uryX/+uat06U1tbpv0YGYxxdpXd33eCMPEnZTPi6HuDIyOdPb0jwfCh5pb+geHrb7i5pqbeaLJwMWna9N7BwZ17dtCDixYuuvyiS0dC4V179qj+/vtKl6/+UThhbmTEbDBZFi7Qe72mpinx5uZ0QafIl+CXxujG8Ip+0fBNQyytdxkhwQGBE4Eg7Vp7NZaAC1ilZKjTRSJRVE6j1ZTUp4WJmS0MtgijRqPFakbwQ0RFbCvo88iNCHJGnTGbzxtN5mg41d5+PBZNm01W2K6wXpOZX64UOFI6ArASI6mL8Bk5AFvX3PmWR34//LWvQld4CoM/3t7681/8MpXMWi02dONly87O50go6QUlgOEigCsglBfFeyUQSkulB8ZMIfLdXzoKhkImm3/55dfj8bTJAKYcO3bi2Nz58/MYwZSMcKqAP5dNkQMD5IoZKtxQ9ZX2lP3gXvO0plxfDyq10eWSwZBjrA3a7fjzWIGMWTHrYm7qRz0yQHOF66WyoWC4+eD+I63tepO1tq7O47Zfc8N12TTNwpKXnzRl4syZM4YjI/ubD86ZMdvh9C5dcNbQwMAbGzecvWQxozS+4L/p+h8BCaW/88NB1DidRdprdnnznV3pZFJvhWkUhxBg45WGYFxrKAfCaPxwPAZqoCl5KnWxCKmjahXPgVpyIrtEPGExOmC4wJLdbkulEryNRaOC4rkcTM1qNjO6GcRWxFdjAZUwGk21tB5D5QRvQTxUSCpjNGBAQktEsiuyFPJRZGKs+oJUgKnR5VQWFwFhwGLblq1dHb1WqyNmSD380KNVVVUNEyYAc3yuqi2V5f+4A57AExBVnvNfflSh49L8ucsCskXrodbm5maYdjqegKYk6OQiWy5m+Oc+psuUMirvR/FElS86rTr0OvvsWVyN3Psj04KzDDU1kkxDVS3B25yLnJJUqiHj0kMEc/loJJKIxlKJDMLG2jc2PPLQwy1HjuqNNqfHbzLrq8vLu/uHbrrhWvR01FKTxXjOOUteXrUylky2th09a/5CFN7LLrzoaGvLwOBgZXn525T/zh79/ej7zvL/vyGVGkSnRWfI6ZWKj83CUlkBlNOJYxg1vqJgF5AKBmoPNTwkJReCEkot1JBWS8A1b7nmAkNCQcCpAHZhUVCZCCigVNrsNj4VO1ABk6nBYbMg+QLtsUQKSBgcjpw8OdjSeiJXMBrNVj26H5wPNIRdAkJYg4ShIBDDzMB+MQRpln8NZDWJipwV+qh6FXTRSCyXyqViyXQy1d/X//ratVpiVVVVYR0UgmI0jZc2UPmsJuTRFg6tge/oTOp8Yc2atelEimZJN2DQz2TeJKqR45v/QHLVe5SidaNGAlShao5GXcHK+I298mpk7XrvF77AjWrLWINUorec5Bslf8NbVQY6FI3e7t5DLe0bt+8eGI7o9KYSf9mv7vv1lz7/zy0HW5yl1Y3TZuQyKV0+k7ZZv/ffv/ryV74xHI5AEoGJ6U3TKsvK6OqOvm4ZgVwBa/bll1za3dn9lpL/hgf/CEgo3WEKVOhSuVzfsNwc7zDU16KiJRLxdDoNS5GHowewBCggbYI/HGOAqD3RzhqscM1bjjEMJA+eAYCZdAKO5PN50dDMFpPZbMpkUy6Xw+1yWC0mj8fldNjgl5lsBjMmpCCvM0RjiYHBYXRCwXFlRQVoAGTypxiQSzFeTtQXXRB85FERsKTcMWgULBQQ5TthoTL3IfZcYYxbtw/0D1LtIgCPNlmlpyzSabhMXlqbTqX4K1fCro3dnT27duyyWiwyeyaZiWXzTR8KVtC9tGoMG6UL5THH2IV2q+rESb3CxtrRMfyNr1T84IfmCY3CrMeaXEz91h+6SIzSoDJ/3b19q1evaTl8JBpLR+PZg4daa2vqSkrL7r33x7994AGHx2f1lZY1NPT2nDQkEumhaO/JkzPnzdvS3PbFf/9W3/AgNNlpdpy79Bwo2sDQUCQeQeTJZwv1tbXQGhmLv/f4RxBHVd+4Xfr5c4Y+9TFLfb0hnzQ0zcs9/0LOaiuk0zA9pt4whIJyWjdqbBAoGoMJ7ULgSiGB9oqHGm/kzKFhLPhnsRq8Pr8Tzc8sMw02mwU0gABjYjeD1LA4A0gBdhlTWH90erhhKBRFKxGswVgk0w+qIoo/CBzJbH3egI1RyWZ8SAWwJoBuUhMtrTYDASNOi7SpHbBU0JQcsI3CUYf6Bjdt3HTDTTfkMqfhBtXT0tOi0UaTKw8FfzXMOMVgRzM/9YtGWyhQ9bVr3wiNhOlJZoOkXI5s9k2oonXdqW//3JUeckAdwE4aJtXLxeMjd3/YHYqn9u+LNx/w3HKzqbSS50p01vrgLXkJITIMjYz84alnNzPpb3cgJJd5/e959+2PPfZESal/eKDnpz/71e8fedTu8RVMdkdtPT2diITcZWUmI4njx3cfKJ/Y0N3V+70f/eyrX/68x+6cMnnKzEnT9rUe7BkenFLvBSJcLidfMavMrOZbavCOHvyjICFj6fvil0P//tVUT2/Jd39YWLWqkM3rrUU4i8fjIJ7b7VZQKMBD53E9hpYCi+pAEBQOg0FFmIYkAi8ZacQScCeVSno9Hp/PYTGBKDpMhUzWkwADJ9YVOBAMMI0vS5LichYLfhfOeDo3MjwYT6SxvkhWwvZk4pGM5VKwQhAVYEO6NRjFu0CT3gA+IfDFekkKDkNZwH7HLTr0S9BTr7fabPKeT6RNgt2rV7629OylFVUVgteCZvIe45DAsqQU9Ff8lTIF9uWxMK43Mc9TQC9fidHE2HWyY+2adRaLFRYtsx/qU2iN5KoVo1JKnqOdqR7IadwTlVYwUMuYN5KTpGHas7wKbTn1/PM5q91x4SXmMkHCv3To9Tv27v/KN7+1e19zzYSJsVBUh4E6ntiwfqvZaFowf8ZXvvS1zdv2+crqMjgGVFVXTKw/vmWbp7yibtkSq8cXP94VP9Y5cLSN2aZt23b/5uHHPnb3B6w648UXXNTV13XseMeUhknUD3Fj5sxZb2nTX6rXm979oyAhg2iurCi9736t/YUXXhAJRaP06hFyaSKRYDoOaKVnOTRpk5cKEzSY0K4BZ7H2k05EO6XmYfwEfq1WM9N6opgB8jKpAA6SKp/Jpq1mvRPclClJxFMTfA+9CcP3yePHcqm82WgpMKsPsAP8QB3oBeuRfyCk+KyhobhsNp/HIfP+mXxaOI3UUaGM1ibBGWNpaeCuD3IvChnaHuWBmWjDeFzlCxaLqa+3B/PDJz59D9OSCORF3FL8RmEaX1KqkhUFCYXVkhvVUajArcpZCtQwQ66waqBmw09C4QjOBKlUCjRUpASxQWZQ3oSE8s3pR7FzRx+Ooj/fqd6XDHQmr7f0gd8p/U4qpP1XeUuq8YeWG99s37XnE5/5QiSvW3j2eT/89teOH2r+3W8fdDsdFRX+yZPrS0psXcdLSvyBvYMRfUWZ2Wrq2rkLvwrfzKmJRCoW6TJk8+byCnO+DHqWCqeefnr1pImTbrjiIrvLce2VV2/esh1yiuxNQUwgjQrU4yvyTq//cZBQjSgwVIRugEuxMmXkBOrAPZBQrCbauANlwoOK0xJcqB4lEx6LuwxqDwmNZrPwrEIhlU6RAM5pB1ANxpyukM4KAzIZdIgqbre1trYSmRAlEIjKpdN2uzsYSR0+ejSTx2RrIVeFDxrQ5w2QV1BB5tNTzPVNrqlDtXTbrR6XC56JvNndN8QMFXyQ/BWISu2U1DZ6KzwN+6tqhGCOPEebRFbcsWPHfb+476733VlVUU4CxXvVqZiRoCC9w5eCaQJciibIDSkoRN6Rn+Strnt7eh968OEd23c5HK50KiXGI1LyHbRDpZTKjR5qGIo3kos6FNZrJUo/q9zVb5EgaKlUBUS5K97+uR/tfTQa++lPfzoyMOyuqDLpDXu2bLQbdPNnNTLhkkwlzj5r9pQpjb978PcTJpV2FzI5m2X32tWF4UhZdZ3OYM1H8jGDwexxu0pLnH6P0+m1JW0dLa0/f/DZyQ2NM6ZNqqyuW7woPzw4WFVdTRMxC6cySZvO9teq9vZV/sdBQtV+6ST5r5y55IkCNTlreIhkj2uuBhNqDkB4ncYblT0DsNYjbMXSkECz3eHI5AupTFoTvcgEZpZCri0w+S28MpZK2a36gMsV8DmNTDUJO4FJMhXhGAomD7ccz8KfcftQQC4AjYu5cD/BAoDNWMiXl3gn1Fe4naJVkkEiGBSozhUqvHavQ3Clc8CiycXSmHEgoAQ6pasJnoAMmH6k1ZTusNm3bdnS0d5+0UUX1AgMiboHwdG8w8hftTcPY4RkiDrKIZZeZhxg3oIiPENwkBKZhkkkd+/eMzAwZHM4ix4OoD8fQL/g64KQ8tHYgYjNdbHbx55qF7jNSW2FEapy3vRa2jeW1bi2vjmZdr/ytdVr1m2or278+te/Ojg0uHHNq7gDzp4xtaPjBJatwd6h+trGRBzLUSaQS7btP1jm8KQszrTFlMllnKVl3imN7uoyUcWpsN1ssbsnTlsaPFR//7Or/v2TlW70x5qaSCgogo4eicaYjiT1bjdDRw3/at3eVOMzHAkZbI25jTVb6yAsMRpsgG+idSiuyDWwRXqFb8VpQK4FTBXoqMk5gS6L3er3eoHKWCQqSEcOqvcphWlfBsGCeAn86ZkgEH9rdzKLtQbTBY+wifcOhFuPdujxm5OZfWHOUhkBTJgocCjSLLJiZal/Um21SZeJR8IIwJlkOhaJI/+kld3f43Mh3Tqs5rGmnXYhWAunFi5NjbVXXIBQmTSQZhkaGHrs0d9LnaiuAm0Swsk1jNF6ACSUSmk9hdgtCiPdQgUhPiIjqMR6s3JFSKXTzKJoGEuGdJjGBsXUiyo2Wjm5UJmOPhj7lYKo7vj7sevihcaY3/z07e/XvfHGorOWOiy2XDL2+EO/Yu6E7rjo/LNXrFjR1dXx2prXt+3cLcOhM5YF/HpfyYn+SG88bC0tgboaXPZcLBJsj+LFhFoRzqYTqOwmm8XqjBoMz7+27n3XXYmzE+o/VaaZzCJBkoGDv08qPcORELb2zDPP7NixXQZYE6W40ukPt7SwFAVAURg4NvBARwGVBs2QgQUigVrgVrQvBb4ms87p4qXLhAEwr4/FomL9w44B6ChYzyLxZdO4eDLvAOPC2sr6iQxTIEa8sZ1IjhaztadroKOzD7gVZglySx0gnsoak09TDsDOqFSUeWvKSkaG+1ErVU1kgpD/FgMYJIiXTeM3ApcuhKLBnmBPUrEmshMIz+uT+RRaH9Mj0lwhzcjbOQxN5IFFlokRcmGmlMRSBSqh/quzgmlhnsxXWqVe0AhlqwHeJB0yMpYplUqQEgIkuEf7BduldP6LLUn0NgpneiafNWGDlleqNvL87Q8SSJo/eyhyIYW+gwOfz8WLlk9tmrZp65adO3dWVE6iTlu37pw9aw61TGVzNqd7ZHikrq7GHnHu3bh+yrkXJAZOBoNhQ7SQT2bdOMrjaRgMRiJxoy6ftpqsJktUiJrxlY0dTXVV58ybhx+Q6lgxoXUGRyr0ujKX62+pY7EZZzgSAr4HDx7YtGmDwBgjrOAAqQ92x6FugZVT9nqSAUlImHhX8y0ImYHT4fBp1HncDqcTc6NZzBlGYzAaRXYF2kBp8kVIraqsglpiEMc+Ab9iMgJEZeBJPxQGSjM4q+Uzsc6uIaPZhsEUVqFhITBsKmQn1VR5nPbe/j5MrA6btb6mDKaTRSTFZJoTV3LqBkCnmVgUldIOimH4wX8rnk4Gk5F4PCG4phYyMGXHlAtoxicghJiQFLtNRGLJKFQhyXOEU/6DYHixOl0uMa8bpLbyTPqKkwbtoi4Kaii05JEgmo7JN0pTMi+CgRQg6AePRA4VyFK0gARoX0KDDEarxaCEVcHRIui9+YfnuLMW38pACZsqCiljabU6jd3+hQtajp167oI5zz/7CI7AbrMdVhYeOTk0NGi2Ghsa6n/74CPtbccDvpIvfPEziCmtW3ZZKrw1AX+wDR8jV+jY8dhI2Guzejxeq9dlc7twAbb67GaXwZjPvNG5b/KsyaVGF22WnsAI1Noyuby8bPYcaZ+SSbWWvJMKn+FISO+wDMjhcIyOloaGImFqV1oPjr6VX4A+m84AX7IaKBlnrs/rc7Ey0G4zQ/NRGFhTFAwnsEBIYuEkwgluu+mG+tp6MCMUSezYvWvd+vVWG3N0CMPGbCqREE8U/mXtNrvDH8iAvXyWRzrFBgMGphsrSmZPa9ClMm6bnnLtkGFWHutyVo8XrQU0wM4pM40C3nrYO1xOSpcVVTyRh+B80RCJIdFKsXr0Nq16CjuyiLKxIIhacJX4cCFAllTSpY4Jk6HBYSdGFbfbYNJThhI7i0jID6xQSI3IAwJXqueE6wmDFEQs9iT9hjmKQ76UqUzmP3MJ+hCTVS5nt9rhn/SekCxyecvBd+qZnFVfaSneCQy/JS/1oLSs1GI3H2lt7TjREYuFC/oTHrcfKYNG5LO61ta2Yx3d/rIJdMpzz/9p2qwZh9sGepqPuqpKK8rKRyIRp9NZOnECA64PJzKRdHZkQEgt7rxes7fU1e/Lbdy36+oFy4UA4TqfLQTD0ea+8Lmz59BbMkqK6klFNIng7etYfHrmI6H0A32ioEe7BFDG+kR6izFXh6YIcYliw3NcX0pLvD63DcZCChYaKbsBjje6ZFrMFVo+mVTm2uuumTVjJgILk/MTG6vnLZgO1v7pT8yC6Exmo81q59NYJIb8mUjGzDar1x8QjTGbi0cY3myV2zV/+nQD7mWxsKmA+pRLJ8Q4R7Vh08IKWemgzLbwQCrJ0l64czSacLudYw2RCxoCRQDz8zmrkZMIitLWgg59Ehc2j8+74pJlpZUVDrultKwiDw3JF2TBzv6DG9dujkfjNifTYDg0KzcdcFEdoJvKfJTLKdIjOYN1qJEiEgsiyr2SCpg1Vd5GLNFg1UgGDAQhyTaZzCgEfBu80nryVFtOjc+pZ3/r1eQpU6jAzn27S6oqS7r8Q/1Dw0N9DXUNLBNFzIlCGFHz7G6q1HGya9Gis/SZbNP8WQO66KEdzRNrKpyl3jAWKGSRdDIzGDamsmaZ+jXmBkyZrkTBaXyxdaTaXrFo5jTcglI4P0TTx1va0RGMyECqv4RqAXDvoN5nPhICGkCJhmCjHULPFMd5/PCrZKLs8JA1Qn6vy40IY8hjqcEvCfXPwEIMk2VgJJpK4P2bBURBxfq6uqkTGjxud3d3f39v14wZM6ZMn/G9731/7ty5R48cHRjs37ZjJ0KtuK4ws240ZmOxRDwlrmzIg+JVowuUevU6pikjTCdSdj6TkwRWYbO8l4kGEBZ4UJIe/JCD5gBJJqPIzKolY+Mt9s8hAgL4TFjWRXsEkfV6ZFCLzXLZ9RfjKrDy1dV4JC9aNO+Cyy4AvxAXq+or0GPXrlzHHCQSLs6kIBKlg4Vj/Sb3o7xOaqYQj77KCG5yL6QMHkBlIFkiARux+SLgFmApmI8H4kjvCb/NMSpvjg7FX/ktDtNfSfV2r6dMmdzb159JDE+aVNfe2uy0utJZwlLYW1pacJIoKS1BV83l4ijXaB7ZLAw7hixqm1he0NuPvPCGte345AUL8j5bAqdjSyEbitMK1riYC5Z8JuPJupj2f2HVGxPqaks9jlgw5LLallx3ZSwRtefNLXt2IwnXT2lCm4eM/lU8PPORcGyAwJnxKMf12BMuSAYwKf4mnBO4SqaSPpdZ3C/FIwRMZHmsPpXKJpI4oYKBChRz+YqKsoXzZjS3HPndo48ODQ5Nm9R0zvLll1xy0Yff//6O9mM79+6aNWvm7x58WJYuGOEcUgUkuWwmL2EsDHmv0+bwurOyysmczeHRnzQZrBWllYODQaaPmdRkKhzkB9KRTkE5Dg0J4Y1MbGZkOpLKawNNC+V6KBkfGkg1lFdEk3H4KqwMnjhp2uSSssDrr6xdumSZryTQfGB/d1efv7QkEo2WlJYuXrp4/469w0PBLIqqSJlZ8A/hjUPwAHME4jGHYsyiYyq84yTvVALmMRAYwFuIHvXIQShA0XwuosueRE+OxUpsdiicfED604+xgTjt8d+Ir6d9q9NNn97Ufrh5xYwJoURy20Y3RCyXY4lJAbaF9SUUCpp1+fBQJ94Ml154Zf9Af0GfhmY0lPiyMwqRgxMTx7sOrNvQdO5Cr68kaIlZbLYEq53S8Ywhi8QThZzGMocPRV9d9dq7b7keV9Oz5s/tOnay5eChfP+JRN8gljcMzbXTZ76pVm97+w+EhOPar7RpGWP+tGvBT4WKcgGdRxbLZFLZvMPpQH0yJmJxUBS4FAkREIX9QPWZgtAVli5dmEkHT3acSMRj6BN7Du5taTv87HNP33DDDdMmT8ZMWl9d+fGP3v3dH/wYNzU4hCgL+P1ScDKL5jaSzRw8ljdmS0uYd2c5od4UA2AH4xhVvS571oLFxwj9FhMMLArLj154NVWCLMCfYciqXTRE0E9wUJRJwcNof097Tz8sK5FlpVShduLETLZQW1PbNG2KzmyMxxpDwbjXX8pyDqvFFSjVebyeocFgIZMGiSA3Uh5EQhRE+gQSAMZJ7SEk4JlEBxARA4kNXkq9sdJDYwoSS6eQoWbiEmg29YZDB3t7wzBYnaGUTMFhjVyoimojQndrF28+a8Ltn3n55sRvuW9oaKipripxuf0eT5nX1x8cMRoI4eNYtvSs9qNH9u8/4HY5obMuh6O3txvhmpVj0eFQnXVCWWW6xe1xumI2ky44BNaZ7D4vAqfTYjCijgd8Nq/V6bGbCvrISHjt5k2LFy30lwb2bdj64rOvLp1SsWJSoLayGnG3/1hHTdNMevGvHv9YSKhYnGJGqmPGD/94YgymoRiiw1RUVDfUVnV3d2VSaUASiUtCNgk/EJaG9QFUGhwaqp9wgX1XSyKO3QWQYWJDF4vFnn7m6ZrahgXz5k2bWD9zyqRF8xfu3LMHnib+n0p/okQyAnD7QpFjVnPMYfLos46cLsVCPD1zi95wJmnJ5g2s/cW+aDXj4IolU4edUXiMRMfABss0gyIlgHYRWnlHVTEEBdMsVCS0hrmisraxsR4Zc/acOc379sfiMavDNjQUvujiy0vKfYlkApYbGkwRBACnVhg+oiQoyFJWEIaFEMJeFbeVHw7c6EQdRijDKdw2eerkuQsXTpgwobKiHLoAz+vq7DrWfnT/vv3wm3AiG0yRGAVXJkCliuPQT3L7i8ffzQspyGG3l1VVnhzonTtnen1DQ9dA38jwQH//8MGDe268/vq6+vqTXZ0OQocYjd1dndAQm8kWOtHXczxkm1hm8bvT3UF3eaW5xOooc3Tub69yuKzVrrzdYS31Z+z6pJ1JQ6OzxJwcNOw8uj/QEzjY3FZT3RAJD7rsNSbmo5x2d22tkJ13cJz5SAisc2hdMYZ12gXPtQvOmmJDMh5yDcAgMSZZ7mnGMMMiVXMmk+YcS4WAb4POxFo5EpIskcwfO9ZTX1NdESg/1tkOQyopKa8sr+rs7AqGws89+8fUiosXLTm7rLQCkEJNQrwDmqVELoBII/pnbiCS0SF12kwGfLt1hi5MNMMjWPFKrbapjXUmh22gvw9AjsPV8jlUGkAHkyzMShjVqUOm9HggeCjzecxpWO/+2CevvPIyJlf+84ff5fHNN9+2YeNmIm5cfOlldQ0NWR2GIjvWk7ajnZdcdtVm14Y9O3dhWcBfoaDN28OeRRclL7KkLHCTqrMqT79g0dnved975i2cCwvFxoMySt9RH77OZ3T9fUOr16xes387rjiUC2bD6ZVzHtUt0gut4lq2f44dnmrc33hFdWvrG//7mWcCtXUz585f/ca6vv7eTMaYTOV/+8AjZy2YzyQq1WUIhbyyeCKXKNVbtvxx0+SbL7TW1abael2BQMKdslXZmkpmNb++zx9L1swI6Jy2nNuWt1tSNiil3hKw9qSGrQa7Tk/cEuPhXS2pefVOu0nnsHqqK95hld8Bs3yHOf1fnIxhViMtVdSwTqvs2LV2oaURGJMDsNP39PUxtY1VE9bF9G4qh0eoIChPkL6ASSbr1q1dPzQYN6YTH/3ge6684mq73dnbN9jS1srEYCIaufCCpSMjA8FQurS6jkVMFsJdYCgUwVEDRJHwEEGZwBtJZEM5Yyhv6I+neuKJqsZJV95wa760dNORo8E8HtJ6U84AIUYaxKAKNy5aQ4raYLH3BSOFRUvmYATM8qxFC8oqnYSVu+vOu1oOHLHZXXe+/713feCOppmN0QTqUiIajR5qPsgE6D/d/cGf/fJnH/7oR8BsyR09koLGJtlVtwgPB/v15vd/5O4f/eze5RcstdgM8UQ4mYjEExEiCcTxGIpFMplYVbV3+Tln2SWUgYiVnKV71UWxrqf9nIaWp735u26kHJ0Om9lIOLZjz6HZs+alkgQ2yDkdpRWVk9M5XWfnSamP6LqYb3HKY0DMLqtdPxDd8tuXMr0pncenDzhc5aUF+n2StfaSpXGD88jWg7FjXcbBeHYwkUNQyZhzpWWFxkpriScSjiSTsZnz5htsbjEdQ4vEU+IdHf8InPA0xBuPafQQt+NREdQA/KTnGEa9LhqPpVhkzYRcFqMIlkYzy86FJQg3lLl4bsKR4Jp1G6+4cKnHZPzw++649KILV72+5rXVK5lnz+XMRBwbGRiqrq3etHcPpmx8Z0RJIHPYoJhMROcy6BktIkHkgiy1N9q7ogM1jfVt7e3d3T02/Kesto07d030+vVZg9tKsDgzbmsqF6y1SLeniWwa8gk/UpBPTbGLYrxJpDM1teU3X3/xL+77zVAoajGbMLjCzcAsVmWFwyFmC1uad93+rnff85l7/CX+H//gXvBNBPBi/4x2m4i6prs/+fH3v//OXD4ZCQ0MDQ4w+UEhLrerrKzK6XKDpDD4FHMTEl4ZxgnuCeFCq4XkFPOD/ahu4Az7VkOgIY70/f/WgbRZVhGoqamZPHlKRUXl8WPdzACXlQe6ugrYtKC1EAZIKWe87Jvq60rMxlBkwJN0jWze7ptY7yhzWkpNWE0NxMKr8/rOnx/dtOfo9v0Tpqbt5WWxbNpZ4tPXBQYrTJ4qbyoWnjix8T3vui58aHcuNAh5ttjx7n1Hx5mPhFo3yDiPSjxcjMe9N/XTaDK4HTQ+Ho6EQUxC8rIANyHqpMy/oyvpldMIUMrbV19fbbVbbrvhKpsxN3NKrc991fKli/Ee3ntgz333P/LB99yClJnPJjA7YrQQ1AUqRXgT+IPRYvMgR0g1Lh6hbMrkcjuY2iZGYyRMudlU3uS0dsXDBmuJLpTyu3xUXpxmbDa0RKBf4y9FzFaNEZEQ8BfmAwESr2vErhPHjvzyZ7/cs+dwIq23WC11DXWTJk5Fuzx+4uhAz0giFn31ZOf+XYc+87nP3nHnu48dO/b0H/5ISwWDBAEVOGFAzGWuvf7G99317khkcN2a195Y88bRtjYc4agtEnL9xPrrrrt+6dlny+wE7WP6BOc9ca/BwKMPJbEjFewmg42JHgypVFU5yEiHFx0H1MPTxuMdgvFp34zdwNIDgRKv14MfwvJzl+/ctTcSHdy7ZwMFO11OCEM6iRVK5/U5z1k0t95febCtIzLQ45o4pZAJJ0626WaX+5sq+gdZx2KwWlL5SQGXeZZ9p66n7WRZMltWWRHv6E+fGDA1llTdumhiY+0VV1706mtvJDvarl7UZA0EmH8SIjRWmz9/ceYjoYZ6b8I6Bl5Aa9wh6CUS1yn8BJTxoo5EYk7xvcYeiiNhCHcWtCUYo9gL5SAbEy6jf3zq2fa29ttuvraurraCgXc7GhomJOK3YNjQpYI2h2nnnu3ofJSgOC+sCvwAp8XHWpRQYLigwwEN2Sgci5/o7MY9lZkDKDex2LCN5Ji89wfM+PBE4m6fK4OEjMyjTLSCzFITdVK4KIsWyZwUmOWzLAQpBPt67v3+T/bsaSNc0fJzzn7Xu2+fMbOJhch8hpfc0SNtTzz++OuvryY+xXf+4zvf/M633/9PH9ywbt1Q34AwVXi36kR6p7a+7sMf//DJjvb//sm923fuZuoUTKcC8Ec4+v5dh1v2/+dt77n1tne/i/ogsAupUQjGAvsTxJ6Jp6zmgsto9JutXtaSiOoJngp/JH2xAdIWuZV8ZWr1NFYvL/+WY968BRs3bpgze94dd7x7z+5dKKVHW4+Go2EmYKERZy2ef+65502fUu+JhR7+7RNWk+2Gq656df3mitqGofBQT1tbxVyvy+fKplM+N3OuhWi1pcEzq8t4MNg6mI2nK+prTHldVdZQ6vacd8G5GzftPHygPTfcddmKxYHaxneIgbTmzEdCbchAMIHM8QP9dmOp0hRfICsCxf19g5Mbq1kTyMr4SCyG9iDQotxlwFgQldTMIOIhtXPfgd37DzU1Tbr95lvmz501FOyH/tsdhpKqul8/+PhwMMTUumCu4I4gPKAmJ3BJkImJQkMskSQENy5q0WTCbra6XJ7jx48z35BPsSDX3NnXe1ZtTTYxwicSQqJQiERYPjsOdBVJ4F6LjANggxtSXj7z6MOP7NlLqFX/e9531z/90219fR0P/e6XrS1H6JX6+sbLr7js3//j6xMfnPzr+341ODDwkx//+Ec/ufeiSy/6w0OPmSWmBqK3uHGzkvj2O27HbvyNf/3K0SPHWEtSWVndNG1aWXkpBuSDB5uDQ0Mg7cMPPVZaXnb11Vcr7BU7M3hItyUlYJUpo89Fs7mRbMqRzPjNxgpcCoUg0Q5VWSpfJC1CVETO+J8dCxcs/N1vH/jjH5+45tIVn73jlpVr19QFFg719i2/8PKrbrwZNSFLsLu+kx3bDvf39zlLa77xza+dv2nzww8+QaUHj/WcPNzfML8+QvXSsXKXP2Wz5eyFiRdPbE8U0n2p7iPHfAG3f3qdzWRpaW3fu/+I3xs41tlqKK0yu70MkJDHdzBH8Y+ChG/FwPFPuOZguOk4DjXucgED7O3rq6n0V1aXWJ0luw+1E/xMqTDyVsEOvpYgomg7FivwY2w5cuSb3/lOY0PdZZddsWTRWVhwnnvp9dfXbgAd5AMpBdAaQx3IPNxKSsRDlAhCeJ8SCiUWDpudlqampuMdJ0RtzOvxucFHu7O7s8xh4ZbYHHhmsoydj7XKa/UnHwrQ9E7aARpiBTrcfGDVyjUFneWqa668+6N3btq88vvf/kH3yX7qRvqNhQ0v/OlPd3/i43e9/33BkaHfP/zYwQMHXnn5lQsuvvCZJ57BH0brDehRTV3t8vPP/cm997a0tLnc3lvefcdt77qNUDrxRIzY/Sws/O2vH37uuedJ/8hDj5511llGI/hF3whLwKAVzSXRJykTx3PEYDStZDYbCiUrWOqOZx+9It1Av45hHvd8y1le/H0HUzhEhbn0sktzhqwxE7+gviqay3qXzms859K6CRPjkWg8ETUk4l1tJ70WeyoSH+jvvv66S89buuSlVWtf37zryPZuq8NZObXEVIg5UtFKf6B7OOqv8TZdPqP5pX2WhO7cJew4kEghseRy6VxWZ8xcccNVbCFCbcdG5K/W/MxHQkYVWyIaC32hMEeQTbvWLrRrXnGMPSGt1omxaJLJpQXzlmzb1cJ7MVWMpoNVkl4hLxIlOj6KD/PpmFIL7cc7f/HLX//hySeYyAgFIzh+4vcisi4fCOeTQ7IhNzBX2CGe3LxlAk50TfLByeuVlSuRekV7pBQEWeba8zkiLzgSWYRY3KZwK2dSebQ6kicH1TaDerB9aYMIrK+/viYSTlbVTf7AB+861nbou9/6fn93CLd2MZoQpdugxxfkp/f+F1Pb73v/nZs2bmhrPbZq5Wvf/NY36hsb2o+20XVkhVSwZOmS48eOb9qwyWxzfOxTn7jptltWv/rK448+1nHyhN/vv/76Gz73+Xs8XveDDzzQ1dW/cf2mCy++TLVS1sWyRRQOrKin8O4ge90Y9YSO91odOHalY1FWQlc7vSooldYIrSWcta4a9/BvvCwp8U2cOCGbSjndnsa5S+I+bzrUOxjJlZRXgzp0FTNEvR3H4GmTSkuMAd/JYy11U6ailb/71usuveLCQ0ePb2w9mA7qnSUWXSaBB57T6g6PhOuqPJffsOKs2mleh+OxJ5/I5FO1tVUH9xy4+KLFFWWBnt7e0rISabsGHX+tzmc+ErKodowmcQHIagc9o92OdZF6PnanvRetJhiMExS0/Xg3faq8xwRdlETJijvBbSwrYjNlLgMEkj9ZcwT6hEYiEHiJ4qtpVWAttnC4YBFxFZ5IZvJPptrgvDJVrvOW+FlFxVoI5giirFrkWS7POiqWz5aUlcaJPgvzMRrC/cOhYEikudMOlpYiiFIlZENCj4b27z2A2815559fWe378fd+2t3RT3R9ZlAokjQiVBv0yVjssYd/f+/Pf7z8vHOOtrShJeLCNnHK5CNHjjBNSs8gkc6bN++NdW8k48lrr7/yxhtvePyx3//k+z/CdwHGNjIc/t73ftDZ03X3R+4hfMbuXTs3btiyfMUFVE14fT7vNJoXV9fS+/F0bDgW646EuocG+/U6v9PLNGOmkIBY1BBFQjRl6R76CCDmocLDNzXwtNb+5RtMQDU11dFo2Et0g/Iagy7Vu6+7vLaCIAWmfHqQCJDdxxy5zJRp9UO9g7WTy6xEGskRehVv4azb4Thnweyz588eSUQH4yOpXDiOAhtw69K5Kputxl1qt5q279iDa0U2ld27c9fCBbMXL5x58GDrQw899r3vfZO+fYf1fifGm7/czP+r3+L3fNbChT6PR2S+IgdSLGhcrTWc5EyXCVqoAzVGjA069DhTKBJdv+XAYJgl8yCTTO5KoLLxuSmQoc8lD16DNCh/snIA6VVUHfBO0mOyH8VAVQjMlvJAY4FVLhkMvrzrve++5LKL/IES1vjhDoazNXWw6Qs+uxVvsHA0EUvG2XIpGsNlhekKEdikWuMOhEDia4CJVCnCzlIDYdYQz54zOxoLb9+xD49VNdkn7RZmLlMlMq3V1tbe1zNw1qKz0EsT8WhvX0/TlKkaGFE3u92B7gd2Wh2OK66+YqB/4KHfPsDSe5iucGrsRibL008+3X7iyNXXXA6L7ew4CceQ1hMlCQcDY6HMbCGaUoPLu6Ci9rLJM6+eOmeKp2RoeLC992Q/knY83hcP5QyiAos7oF4c+8i3KJCMa93feGmorqkSRwuDmaCGOZPdXV5td7n6Wrc1v/Zk86onralwddPMmrlzyhsr0UfLKwL0ClZdRHrOKVl9Eikx26d4q2eXTV9UOv1sf51vMBk9MWRB9MzoWDtK5Kge9q4YGVy6dH5X98CO7fsQWdhUb7SeGh0ZvXu73zMcCVG+8OJfsnQp2KjBk4DeKP68qUOEdAkvk4NLQUlSygIHwuSxvIC4WkWYFEGjmEYSK16kMEpDYg3lFA0fQw5VbPEO0OeWs0imWlbkot3r8y6raVJZWbS3N5dOWS1G0M9r0DeWl8KPhoaGezq6EXuZIhzOG04kU2GJXwq7HTeO8F4Mf3A3KmbUMyHG/kJshVFaVobYyQ4ZNIKypSUUL9IuFlypSDQaHxkJBkpKmMCguwYHhyZPmQT+Sw0LhUApGZQHR4KsrIS3sBahv7cXSNUTiBH2jgU3V4jHEtu3b2MNETMrOO5RtJABJIVcnh0VpVhptPyApRUu9/kTpy0jamAs1tp9oi8R64rFg8C19GHxj0+0Hpde/nuPstIybZ9JIlB4qhqddbPslRPt3nKbXVfidQTqJhvcAXugqrJhAtYj3Jak32SkGVBMx6kDh5rpTlEU0AZUn23cunn3oQOiNhBiJ5mgsQMDA7OmT8NyvmnbLvYCOufcc0QXLx6kIzPVptFHb/odS/qm52fILaNeVla6bNmysrIy9C7tGGsbmACQjEcPOkvBjXQwr0hPICA6UNiNQLr0Jg/lhtcCYHKoS+1e5SZdzqgJpEvS0UNLKZ9LbpKGH+1WshITDQaX3O8ffWjnGxvmNU7wZFgwk55YXtZUXwsGhiNEjmahriGR0w1mMoOFXMxEWDc1wJLXqQMA0IKdaNUmCRPoBPvGv4cpdcCJsmRBo2KDqm00quB0u/0+n8y+yAFvM7BSnND9tI97YgOyAJ+s6ATEXHYvouYqeJX4ypFeIRfBQdJat6CHQwfE3CLTOXk2lxoFQ8U5pQIZcyE3vaxyXmWtPpXo6O/tT6X7YjFy0poklfjfOEoDpVSG1WFU0Wh1Gb1VOkeJ0eE1Ou2uymp33ZSCxWWwe5wBiXrIUk6RRxUSUhOav2/v/pOdXSCVNFEjy0bD8ZMdwyNBEZbUChu8cVxeT8vRo8dPdAwNDs6ZPweLjxpkMcEJDChM/HOtOcOREIDAyl9eXn722WcTFkLrCo08CwIJtAkEjXaRugXI1BvtLY9Eg1LYKEIj3S4kEkmTCT4R+dSFYKA8V/ZouWZ8mAMTJHx7Yk62kkw7pApyi4uJw2x2Wy17du/s7+uuDfinVFfhiDgcHBKfAUOhtLKMGERmr3cADxfQHA4mmK5RiVNDrKRgoS4AH0HQHE47k4GDff0up3PBwgVZtWpRyhZ+zA8ZEPQvO6VpSiDgHx4aQgZDCqbTnMQqZuc9lQZvcStxGl1uNrQhzYSJE9wq1JX0DEYbsiAAqcU6a9YsArek0kk2wAHh6R2AkGKIVyZmoPEHNA4oNxrrWONscw2PjPSERgZTSayXY2Z91TGqhuM//BuvCYpHdAV2fCW8DjyZJWNiM2aI2KKucabFEyD6K9MtervT5XNm8G1j+b+MGrXNOWwS9/CRRx9jrRlqMR3OKtDqykq6/9jxY2aTDjZInliGwzEE/+CK888rCfhbj7avW79ROpavxDgsLedH66W3Vv8MR0KsMlu2bO3p6cGogAVPiLfCMQCLHpJrZXJQ8iSdI5AJWAM0iHNiTUcyMRjYTlBcsbSAfwAIQ8ghGEgCOlD+QCJ6mvw5BKNUjkXkFs4hiM8rsEKKUejHmeeYdsAk/kmhJl2p01bt9TRNaiCyBg6oew/tP36iLRpBCLTOmjZl9rQJExsrh4MjGYwBwD7KE3AquZ0aRw3SLWiTNCdfoPLEe2cJ4sH9Byj9uuuuq62vRUeiOGrCx2ivuKK6PO7b3nWr0azfuWMnIOVwOGuqa4hlTCANjYrQIkJj1dbVR8LhtWteZ6HQ5VdckZAoo6ry7OwXT5yzjOhHC15//fV0OoUA4vd7NTpBWYij9KyiF1Ku+s+dOIl7HU4XiJHPDYaGh9OJIWjE2KE1Zuz2b7+QVur1paWlw4PdaQKk2W0SDpaxyiRNRrevejKOhCzUtrm8BRPb0PsyeBaqXtGKgowtmj9//4GDL6x8hQhb0qO6Ql1NHT2+79AhxBJWmeHqBDBgJ7hg+XlVlRWuEvfxjo79Bw5DnbCYM0Uj9LhYcwGwtx6jb9/65ox4AqATE+3VV1+prq6GSAOLCtFomxoc4QECwwKRivQhb9BPImtiuzcJ7Sb2CuGZcP7lEQkF/TDZyyG4JLgo6AheCsZyyEOZIBAEU6gOnheRU3vImWS8BSfVF6QiL/7Q4HRuQ8FayNRXBs6aO332zEkNdaWTG6pmNzXOmTmxrISJbQyoLKuKi8iIN7n4w1Dh4tBKnai80IO8TWwzcs0m2zNmzsRY9MYba7s7+8DAf/nXL9Q1NBLHG6MOyx3pE39J2Wc//4W582cPDfRtWr8RMjR79sza2hrZHYrVWSpfaTdxO+fM4ff5p58/ePDgRz/+sTs/8F5PiQ8WYXc5r77+2i/967/s27139asrYTULFy1ClkPcFk2KrSCNROgR1ie5SW+fQi+H2UoIAFY0SBCcZDzMYjHVDK1z/lfOFRXloZGhp1e+9LPfPzySZPGuLphM9hOW0ulNFXJMctrcPtZUGl3l7AmjgtmJxZh/COdTp06cO3fWM88+v2XrDhZY0x6CRJX4fCeOd/T19SMs0IPE1Dpv2bJkJvXcSy90dp2cUFd3wXnLN61e+ex99z703a++8pufdh/cI6qwgNZpbddad4ZPUWBguPDCi3bu3IEytXz5cswGGP1BFAUCCmQ1qBVSxUSDYAVPuckWTPFIgiC3fqtXvHzxwdIoOWClZh0FkiTtm2kbODaGFeQDzeUW0CNnQUtkElWKhoraGJBAXuuBVJ3XbmazJqYDmbWvxBvDI9IO9g8b0coIYJLVs+MfIYON7HwkK/pEcFKfar/FM7mpbX0pEgEqd8EFFzz5h2e6O08+8vCjn/rsx2fPX/DDn967ds3agwcOIUqyIPD8C85vmNDIxsGPPfpIa8tRi8195VXXEBac+GJun0eUGg6Mu5n08vPOfeSBut6TXd/46r998V+/9OnPfPrGm27q7unCZlNXX7dn165vff3bBCkLBHwXXXIxrYawsJ4Ypsx2p3DMt3YX2Ei3ANyYc7OZTDSZwhk8nc8S+0365H98KIqnK60oD8fCq59/YdrEpsK0mblS35Yjbf6pM4/v3rV204bzFi26esk5/toGNiU3sgaCGM4iR4rlhTqAi9dfd/Wh7/3oD48/wfgtPessj9vZNGXys8+9CIdsqK9jre/yZWdjsn7h5ZfgiovmL1i2dBHBvb70kfcUOprnTZ/qClUcigaj4ZHJi5YRs12BzGlgc4YjIcIeM11dXSdXrnzllltunjRpYnPzYXYOAIXG0EC7lk6HVmOlxjQNg8mnZ09uWDinaTCe2rh1r7heKccWhE1Bs1FuplBRGI52ADO84hp9jQsAixxFQFXAJJ+pg7ux9Aw3qUiBK04ps+82s9Nmw7GYSXz0Q7QOTCB8lOGRbI4NZ3ZaHSl9NIPmxpJebTDH4FWKlkqILM1idkonbMx5y5efd97y11etffHZ5xwO2x3vfXdFTfntd94mMbVlb2BgIB8LjTz+2GN/ePRJwltccsmlKy5YwRJfiI/D7QAJKYUuYUKCTVNuf8+7f/y977cfOfqFz3xuxYUXzFswD4m3+eChB377u/Vr1yVjSUD4PXfcwcKFSCwqjty5vNuOpwzZwKL5J5RIO+D+Cs7FbiQNKeixPCWhOrQCbqW1ZTTx/+QXQdPKsujO3ve97y5XItLS0nfWORfvOnLil489nLWb2zu6ThzpeO8tN1RWN2aTdIoSTEBAGSxIT6ZpatPSxYu37tz51PPPoy0vmD93wby5L7/4yt69+6dMlkCHBJt96qlnXA7n5VddSvSa7Xv2btuyY8e2ndfMrmuoqvb5CW9nCR05NISFecpsNUKn5BfadYYjIUBGAM6PfvSjr7zyyrXXXjt//vxDh5qLkKpGFTIMt0RZEjKtxHe5yOU+9d4b33vDJV6/90Nf+V4iEWYNubgZg1mi7AnAjB1abhpSkRvjxrV2QRpEOw6+InNuZViVcsg1aeRbHhOiHinUbHTZLIQPxa+LaUC7wykBgolFn8G/RNiITc/bVGgkyJy9sUCEYpvg7ig+a9XgVp7kdTgI4DeD7Ug4v8H48Xs+cbz9eFtr26O/e3jf3j0XX3rR1KlTsRgD7v19fUdajyBDHti7P5MznbV02ac+9wkWL7Iwl+wVRApDQALgDwPPLbfdPDg88NhDDweHR555/MlnnnjKaGZvGeRiApCKEeuO99xx+/vfS0Buma5kb7ZcDkdtTVhXArLwQ41BkS0yALptNJ2ia3nMik0t6LfWkP/5WYpQuQT7g6mRsIc5n1x2Wm3NkUjyFz/5Jfshe/PeRF/fI7v/sG/bzs9/9u6ZTdP17PWBwi16hnyJBQfl+/prrzl8pBUx5E8vv1xWUdZYWz1vzuwtW7dbbrt16tTJ7LFD9Lrrrru2s6fnoYcfJbYFsdtqSytqquusDg/ithWRl5A23Z2lk2eNtv5U485wJGSe5z3vec+cubMfeugh1BggD07Cw1MdoEmJggzioyzm6WTmE++98ePvutpiSB3v6ujo6gU+YG1KKDuNgGlwzxk0O4V1Y/SbFwCdgjjBW21ExyGhBomKJevBQNazF2TzJgczEV63l5WDOCSC08xzAuJJwFSn47rQO2gpGEqs9iBxollYqKxH45mG1IrdEZB7eCdcnYXImYra2m/957e++63/3LVz1+5tO/bs2IU1xO1xgVhRrOmxOFSIFcNXX37FPZ/5jNtnx7xJqFtkeOL8g9IgSDRCMPgMrgPZfPpjn/zo3Lmzn/rjUy3Nh5l4hElanXaPx9k0remGW29cvvycFJtYIDyoiI/orS5sj6IQojwKiQKjNcSgW3nADjdhJgagGGqnzWJPiSg9fpT+zmtGlAzxK3r8sUfKDAYb5M5myYSC9//qD4lw3OPzDLefJPCkz1+ydN7caCb3i8f/cNbMWYunzdDDEWVLKaG7WICryiuuv/qqPz7z1Egk9KcXX/rg++687PJLt2/b2dvbs2nTJhp+6SUXb92+642Nm0KhcKA0MHtifeWypbWlDrfHR9DorBETg2wEJLqMiB5aBxQbdYYjIYF5/vjHP377299i/mrbtm233HIL+7azWA74FAYlor+Qe6AWrzM2qSM+5wduvvZT771Bn47qLbZ1W3cf6+w1WjDTs3gCcUp6TZBLUXLJQV0o0JKHY6+4EFgb19nqQbHTx1JCbYFMKmOxme02k8/lIHQi09YjwRFUQzgeoIl0RjYENY0nE8TUBm6iPUHiSXf1DhiczAEo6U5Vo5i7qiJknEBxmOaUDqwjVnHDpIk//K97X3j+TytfXtl5sjscxSdmgJoQ9pdVV9NnzbrimsuXnL2IRiGAoStu2bKR/QY7O7qYm0CEZJ3E9777PeJZTJg8ASp23vnnE1Sur3+gr6cniTurw1FVVRmoKKNFqZQExVL2B9GfESuc7P7NPb1cpEtFokF3IrOHNBqAaZjmykpDEaN59T/AwbHOJh/59/xTT53ct3vFBReY7cSfIk5BJjIUspvtsa7+kVBw1ryZX/vql2x+17e+d293KPTqmvU3XHrpteevwBAGDlJluhjry/Kzz8ZevXt/88EDza+v2XDtVRedtXDB9i3bLrv0kukzpr268rWNG7fSxrnTZ8ycPunEri2VXpfL6ln3wlqf13vu5RfkTQUCxqrxGg8XMmhnOBLS5ra2NmzuzFMfPtxKVPuZM2e2t7ePwquMNYwMEOF/Lp350C3Xffkjd9gMubTJnEomjnd0iRlHTCnKbCqQIf6impY3NtRabtotYD2aOd/I5fgnY6+4ID2cAcQWscdgLPG4S70eHE2Q4ogePxwMVpQGWAKPTwYSHUY5zJIEtrG7bWZ2bs6nG/2+biYLhdmSEzVUUFvkw3LLzsMwE8L4K11Xh3zIAqg77nzvDTfdzNKQjo6T7BaOhkPIxprKWkxALHggsD9ViYUj9/3iFxvXbEglkyxrNLP0jkYZDBs3bGBVx9e/+Y26+tp4Mk72rFKvrqwQlEFUgOWpXaMFbmm4YF0+GSP4eN4htL9YQXUxdpKOPxkOEtJYVpIQuIrdWIj2oQnq5HGqL8c+eQcX0u3gnnydS8YP7z/w6M9+UWEyn7t8AfIx608QegI+b0vn0XR45PZ33fDPX/wsWxd88ov/OhyMMheayIRef221IZG86bLL3E6PdK8AAIOlu+n663t6Bo61d7368up5s2ZcceWlL//ppelNU597/qU1a9ZVlFdedOGKhprKowd2BbuON/n8r6/csGHN2lLWDTusC65YYfeXCNmV8SrSca0xZzgSAj0cCih0zBbu2rW7trYWe4fWCyINieGc2QLZZPe2ay788odvcZnYtJNY9NlIcLDlyDH6X5vcU5+A1HQiWUpH0oPcQvW1ruSJeqvdFc885FC1OFWT09KDPwaCAxrwViNULxEwGHJwg2i8MBy116+eEJ5GwqwZzZhNXQb91CmN7Ufb/QisFncoHBZ4Fzat/DwU1AI3uKUQqtFsMYrHj7ykcph8c+zwBIOsqaupb6hH0OJDjCLwq3Q2zc6jVIw5hfvv/+WrK1dZjZaqmpr+wQHqT01kK0aj8eSJjueefe6eT30C2VI+FB9ZbCpCbShZ6kEGyLgyJSgCKIvuYW42pkukG6TLoAvCmwRtoT6FSCrdEQmi9aJTEoLHZWFpBeFW1WtJ8rceFEHRSACs2YhveuW5fRs2Ne871NfSOndiY/2ExlSW6UFx6WNvAl029vX/+Mptt9/a3tHxuc9+ua97gG4nSM61N1x113veU8LWLlBfkw5fP9gzq8YymWzAW/q+d7/7P//zXjxjX3xx5Yc/9N5l5ywjmPKzz/xp0eJF1117TaDU09F+ZNWqV2owdWV0u/YcMNudlnia4Ihsx+Msq6RJQhzUMI21jWE4kw8gwu32AEOszcMws3//voaGuurqSrXFp1BKkZFwEUwnL1+++GsfvcPB+BvMzAoiUwHfGeFVWtQxDfNk7oj+UuAkGDh2raHZO+xKEvMtB0AspNHIBngGik6n4twR1xCTvew3ZrGAPbCF+rqaiobqguzvIv5oHrd14oQ6u6VQajOWunBDUxI1QjUX6o/6UUeQEFdsaSm3Qs/VjyyXwnMEYyteH4gIGF1T4J/gsGCFsa+ne+umzeDXsuXnvus9d7CuH/S4+dZb8bZhLxrmzU6eOAHHE7TTcETkNWEUcse1cm6XbhF7k1iG7Zh4JRK3llqYG0KH1FX63nACFSqTxtcmozPYLdaA2VqCOapYWy3X4ofvrG/BXhnQ4YGO333n3zb/4bHQidYSp6m2zHPF5ZdYXU6CcAvPziIhm372w++9645beweH/vWr/3HsRE8+rWNR1d0fuPNzn/xoFRukmSyxbPaxZ5/5wte/9r0f3/vSa6vC8RjUdEJ9w8c+8iF229m8aVtvb9/R9rannnn2+uuvu+uu9wZK/Ydbmx955MGHf/8IQb4OHziILppLFxAnqhsmWv3ltgBM8W2OM5wTIowtXLjw7rs/fMcdt2/fvv0DH/ggOlJdXR0SqRgy6RBwzqibNKnmcx+70283mK3uWJzom3GUxQPtXSd6BsE68YAU+so/wToNeeTTUSVw7AI4F0Q5/eDJ2Cca9vIemVawAtCUD/R4b6G2gy9MAMqkGXFq2YYJBojDAJpqKjFx+pRIVeXuLbsAITbqYl6/rqGmp7MfJVaVRw1PAStPqC4mSRZD4YSlFQQa8FyrnMCpAnSpKd/RLFUXYSB49xF5Bc6Zyz/22GN0IKpwQ33D+eee88zTz+ArgGCP1QdMpuZSZBG7VOlaFVR5krOOgOUJYzSOqRYTqCaQaEloNM0ezmaahwZZwcVSEFRj9lussFl9ooFLXbUaqXz+hhOdTbycJ+//r1R3e2XAPxQcsRoKt11zRdOMJtHgqXOe1bf5T33205NnzyNEz70/+e99e5vRyjPZxFe/+tlbbrk+mc2tXr1+9ep1x/o6TVaL0+04fLT96LETG7Ztu2DFiuVLl85bOOuT93z0u9/9wfqNm4eHBz9y9934ORBkdevWzQcO7hsYHMEBZzAYbvS7J9dXEwXDarPWTp/unjCJvS+0lmijMNaqMxwJmX5gifeVV17+5S9/+cUXXyT4eUvL4dmz52zavJlBxoGDbTAnV5ZNryppP946c8IKxKcYwcMSyWg0sn53SzCRARNEuBIEFCoLgmjSqQCRAl01sm/qVQEg3sqQj6Ll+Ceq9zXMFFCG6CKwmVlJmMsyRYGBFJYiQiL7/qazRPW22s19x044WNh++aXRcHjftm3MxUMaGiY2NA9EVc6cxuG/JpOxdQzhUpMyoV+sq6qSVm1N31I1VMKAqhNmKp/P73F7WYj82qqVtA9BALT/1S9/WVVeDk8GIysqK9CsoBg0kUaKQqshluQr/8awEvsK7dm9bv2KhXOFP40lU2XR8ua+7kFmYwwGzLCwfZ/dUuVyWkX00PKmYirp33g6vHtHtK29oaKUHbKshAMl26pSEzOFYh9ip7qc3mqrnTyR+Divr934yiuvsX0UEveX/uXTt956fTAU/eGPfvbUH59DAvCXByA6X/63L46MDB1sbg5GI48+8fih5uY7br15+fLFn/rUJ5588pl77rl75qymrq6el156YWCgH5mkprbxM5/9Qkl80B8N3lE38dDO5pjZ4Jlc76uuVR1E0xDDhfiONesMR0L4yZNPPfWD739vYHCQKNTLlp29a+eO9773/V6vFz9j7B0Om+W2ay+0GdLnXXkda2VTI70pWGEsvrv52M6WDlaww1QQ8aCvTPEDk8hXjJiGgRr6addjHTr+QkvAEy401qe91T5RAyFD4XLYXOx3jj0UkVDgOC9bCyKYsk4ilphd11iwFPqPn6xwePp7uvTJrN/pCWXCaCmAFBu2kQ+QL2VRkLoWrGJm06Rj4nEwkmZtU16XYGUFpY9VXjQ2uRUEFZiXg3WGBX9JYO68ecfbTxgRZbFh5NJkRuTqwb5eagWPZqJa5EzBPmWC0T6Vz9GhNOYoyqDCbFlStW3DlmM3XD1h0kRFVYq4nzXo24eG20eCyBVJEUvNPqutjkASDhckj/xpzWit6JB3fKg+DY4MypbIegR6M87rLOFncZbMmerZIFKH/ml0+NhQhwXR9/33r5LhBIuz/u2rX7jxlqtOdvZ95d++tfbVtayLX3HVJRMnTZoydUpNbVXDucsb62rXb94Mu9u+Y0fn8eNf/MxnL774vGQyzsC2t538/e9/X+q1z6ivs7r8c+fPx1Wip3V/99aNfrtj/vlL9IR8mzndZJcIetLbMswM1j8MEiIHtrUdjcdiGGOYpVi8eMnPf/7fOOuXlpZ09fRUe/xf+cyHL1k6M5pKOu3O9RvWB9jcLJ5uP3nypXUbB8IRGVNgUyRXxdMwYmJxVAYYmd8fxUYutGuSaSkVSEov860GQW96TnpyFfaqz5c6jQGXCX98m8FGDAv8/MF0UVx0Gb46uHe/v7q0ZuqUQfaMHQkO9vShm4lAyE5CaHSYTMbRVMqSkRaUlOiKLQdavCX1MvIK/bQzVaJ0rrUqadAgT9QfLb7s6stffeUVglEogJGnfAINggCVl1VMnzYNkX4sQy5GDxLKFyL1yo88JvORgeE3Xn9j0qTJaZ0s6ZTiDIau0NDBoR6WG7JRYzyTY8PFKodtKoHNpO1vm/loIX/5V3VFVV395lwOZVe0ayL1EwIZjw2LhRrBhbCNWlxeo8P24O9+i3+CuWC85robbnnXVeu37Prm179z8OCRxYsXfPFfPrXwrPmtR49XVddAUQi3vGjewpqq2tWvr05H4kdajv7oR//1tX/78tSmpiOtLevWrQcYLjnn8glVlXZfGfaldVu2bNm+2R6L1i6d4XFYqqbPdFfVEkNIeSxQRaHjxXZIX4k2cCYfwBnAzHHFFVc89tjvb731NjZePXq8Y/KEKTPqan7y9c9ctHiGVacvdQa2v7aykI7vONC89+iJDTsP7j3WHSc2NjhHFhBVZWRnhuBNuKf1nUoi3cnFWG9q15w1oNc+pPe1ARAMZCNB1nsbdNV+q9/J3BUzuXkWsLMLL2+5ZaMVttBjKrnnWCcbtJWVBBCVS0oDFIINk3jPxbxUkVwztvwxpjAkgO71VWtWvbyaSmmkl1Ra0fIVyWDwyq7LmUM9kiUZBDKdPnv6Oeefg8DJPqKCS4KGglQ0fsUFK8TPBhcSMbucOlQq0pCPttxEfaAQEazb9Mbmwf5BYUXonDpDy8jQ/oEBpuezelOc6AAGY6Xd1uTxVMErxD4knTj2d6qMd3xVQlRsNiRlG4NCnh2UnB43XnPMvQPs2ISCkVjr8ZOPP/XMb371W1EuTPmSqrLfPPTExz/y+SMtJxcsmPezX/108sxZK9dtevCJJz/w8Y+x5QvIzH51NWUVt11/0xUXX9JQ17ht286HHnw0ODL88ksvTZ066ROf+Pj0aTMIV05nIfJMmTTBmDEFaqbMuvhyg9drqyyDYaYjoRP7dx/bsTXU00PzRIoYbdEZLo6iEyJRfPYzn1m2bNmqVat+85vfLFy4gOB8N1664q6LF0+tKzXmsxaLB6PIpIB/w86jOw8e7e4Z6R1iizAd8a3pKFkvNE40EvgTUj/WgcWO1NKMTwnWgX48GUM/rrXUGloi2oEHJQFPNUGBMmwrzVw8UqhknsnIRtxs+QLgAtRsMLL3jc3eUm8gUNrX3QuXRJAWx1LMOBB4hUCSs2QvUwDIXh3HTzz64GNYedFtBI14OqZgKQQRtNFeaHUqniUhnOq2O27fun4rod1VlSVfEJXwNpdfdRUsehQDi81RFZAOEbyEzmtZCdoKzQcJuzu7d2zfeck1l8PMTwQjHbFoXG9K5guRbArLMKFaJrhcE9we1iLjEsFuR2M15WLsWsv1L5+pCRUe6u8H37AvYRHBaRQ3CIio7O8pBMJwvPPkV372791hZk2dn/n8Z3fv3fPr+3+Lyyhg4PCWJLOpH93330eOnOzp6MSuxQKaw0ePXHTx+cwisebSYrZcdMGFs2bNJhDW6lWrBocGPvrRu2fNmtE/GGxpb60r9RJ9y6I3sw/XpRdf6HI72RFg5MTJ0tqKfVu27F2zyhAccDldnvr66cvPbZy3GBoku0+e8ZP1aPw33nCj3+8jMNGePbtx+/KXlDp0+XPm1FfgTcMqVOLvmfTsx5AIhTFkHTveE8kahwh6bXVilEQ2hJqCM9qBuR8zHgc4wJMxpOIJXTl2C7yOwYr2ivPYW15xCw0GzcBDOwFd0jmfy0cIMxZqoHTiAE1sKOIwsSYQJ2zUMuCehRSR/r7o8AgmVLyi2ToCfcuE07eGJRp6C9xLQVhE//DI73u6+hAkCVgEJdEKFRTRqlqEbUHN0VdSZb5V1kPd9Bmzbrvzjl/+188QQ3kMpLAe9vqbb2qcPJHdTPlmPAJrdZB8QDopYbQ2Wp/Qg9nC2rUb6hfM7UomQrjg6Y2pXDZGwBydodxhrXfaZng9HtY6qeAuUknJ/u85qMngQA9qP4iH8yuqA4IuU654tyLVkH8kHDzccjicxE0Pp2J/OBVvOXkcb/3Zc5qYpQlmDRmTfuuuPUgjdZNqWMcMGWk5cfTnv/nN3OkzmyZMLGG2Xa8nDs2d77v9rIVzX35lJQ7cu3bufuq5F8rK/O++6SqimBqIDq3TzZjTlIvHuvbscRl0Lz3wq462Y9VeR3kJkYP8Bmuha+92d0lFScMkrZFnOCcEWH//2O+/8Y1/YxMVllSDbHU1VZ9+940VDhzFjDhkYWpgFjERTRB4q6OrC9QyWqyyQx+jxJ9CHg3fwBwOpEsxOSjeOAZ8dCVptA7l4fjnfPImaOIJ+MuEMeKmhNHOZYZGYtBoi1HnxIIHOyjkPV5vLBzjmulwdEVRHfFiszjYohTGwgQi38EmqT7PVQEALhVg3hyXUcPObds3b9zG/COWFbzQFFhrtUBexKIiiCKz6Rxvql7xlgUTqdve867Ww4dWvbSS7b7ZzWLROUtuuu1m5vQVh1Ma8igCa21UZ+2lvFUIKBe0kEVRrYdbdx5qtk+ZHGULC9TZXC7gdOHTU2HUT3Z7AhYEUZkEUvinlEqtdiK2CVK+g4MiZVSOHT0i/np2hymfEmEfn1zZjFV2GWAra7vTbrcYsslIweU1lTkff+UlYkZ+8mPvv3zhzC1bt/74sReDWWNJudtTGYAnwzvpn0wuc+TEseOdHW67o7K0rLK8YsnC+VUV5TNnzERJf+XVVc8880zBaPZ5XYlw1OXx4W8LkGTi2eEjzUd2bClk44VEdN7kJqQcl4NJa3vBYiqYrEPH20vrJzCy9NQZjoR0R09PN86NwOa8+fM+cNd7z57RmO07xhpp1HUl7hnxlYmGQ0ODw919g5gUsXa4HHZYI0wQICUHpHyAQPG3IvCNRzMNPjRA1J5z5lY7a29H0xdBk/GFu5EtcAiqd/cOpNP4P7MEFk9u9sE2uNhWRdCHXURRSgmkxPS4TGbYHDaWN/AJiqZ4suEfKgGFyBZepzLnKpt58cWX48ScB4CQpkEbpDDl5qpVQ4mIklr91ypYPGv14wVtZ+/Df/7yF5jPX7t67ZSmps998fMOl0PCq/F69Bj7mAdc026hBXKjJiQU9nDNH3s1HT54cHJjAwyQveYqS3xsmOos5JBCS20yMahlJT/Ct0/Du9Nuxop884VgK6Ego6GY0+mysAYlVUAWZXbebyt1u4li72QaCOJX6vUtmDzhQDyrd5plheS82dectyRz8vAkn/l911/dG8wePnJguKsvZ8gjN7GLBV1ObzNYyALHOjr2HTjw2spV1xBW4LJLiDTz/LPPzJ89Z/l5KxbMn9H6xgbXRFPWgstVgqVh617507Hdu5edvbiusgYR3s5uPhYr4YFlZy7mQlNJhBTszbTjDEdC6KvH47nm6itZeecrLWPr3P62/VViipT4Dwh0bGIbGR4hkk97R08onK6rqnHrC/sOtYIDwJIYVUbFTgE80FKgSwM4QFqQTYNvAR51jL/gehT9tEtUPEFmoAxOCBHAm4SVE+yk2z8cTKSd0UTK73HgbY8qy0ol9lHnwAhKGeh+YB+wYLXYw6Egs4pKSBRUVyXKCQQgONrhg4cO7juIdZSwKBLWTLZGKvIWKkg6SSqHdnnaWWuRCttRgFt5/L6vfOPfp89+4pzl5zRMaFArOeBsFDqGNZLVuCyozGgRRbogKQXtC7me1raJFyYqmR2yyAoRj8lc7y1xM3Ug3apaMeoQT3XJViozLuu/dik5hEaGwRbYVb8vkB/JgIQchEVIWjIeq/RUPpXz2N1TAr6rbr3ipy+sYhnbNecvK0SG09ERkz61bOnCyskLkGCPEljk4IFELEVlZ8+a3kIwyL4+QqolYok0e6wG47/+1YNouZU1ld/85jemsAVyvrB1x5aWPfv6Ok8suOyycKj36L7N2zetPW/WAmaQIqGQxy/eeA6XSwLPZvNswSacVqospzMcCdOpNCTrX774eczMV1xz0/oXX/yvr37CSohZOKFMxOnY2GhkeLhvYPBY7yCr6aNDwyEEiFgcsgqSyq66Aq8AA3+ClqrTBPL5Lz8KzbjgFWeFX9KzoK58pgBUS0bCUVOrPEC3xGSgl8XyEm2BXXLxEAAcy0tLJtZV4C6AdRSM4husNAhvabVNL4TDKZXXDB4KAaQO0Ar5EeFJn9+6aUtsOAoY4znNxB0sl/QCzaqG/MqldlbVU5enTuIfJEZOoS6YZ21254c+cnc4zDbvQWmr7BAs2+6RhBsyQNHSGkiXaFA1mhd9IoUK1rIZdVYX7uqzRqLu8hJvLlft8njsNgtUTlvyJPYoyUfLSqr3t2FgsUwc3xlcVqTMW7x88NjR1GCXw4p+54Tc0YsScFWnh7klR4aayuwVVufyS8+vKfFkh3tikbDV5Xhh5asvfuUHs2bPqq9lMn/S1MaK3u6++ora2dNmQpKisdjwyHBvT080nKBZ7GY1NDDA/Ofh1tY/PvvM8GCotrQk236iZN8+X6X/+OGDbovB7jQ7XY6ymmqH22ZFgzda2OlANidlaJ1uOLOqt/4MR0KgMzgy8sjDj9x+xy0lfm9TwOtB74IWCUcrxCKRUHAkNEKU2vhwPEXwo2Q4aPD5AD7WD0mgMF0WSUT1lOAYnFMtr9eARGBQA5oiCIz+vPUhTwBZ7U8ESLBV1urnkXUyCVbJsCM6twTvzPcPjvg8zrqKUtZ04NuGAZQ5CzzJWKHHwm2kaDwJCCis1YBgVAr7FNKQvUHPZok7t+/CTs5simAqkivCqCIf1EBQa5QuaJimVZmieS5VNLB75gmr1U700RdffKGiqprn7MHW39fr9fmxD+HxV1lVMTIYhMpg0SKSm5AbShBSIDhZzFBt66LueCE4b9QZk9GYrqd7zpzZTpMBN1hC6zIK+M7C94qKrZaUfOSjv8kuWiwYUTkSHHFUuKsb68sDFW37diSiQQcCPrs62iSuBF3i9DgJL7pv47pYZ0t4YJoxl8ykI7HgsNPpra6eEI5u27Rlx8bCTvx6UYwXzZ939VWX0P2Mnsfl8vs80yY3CunB799w4RNPP/fM088+98yzLp9n8XwWNs0LtbYe2rRp/oXL/f4qcD6bTro97tqGiWwEzBIBSC6jhKdOoqCvaZhIH2gtPsOREMCKs2InnmBBUzIcuuW9NyLsIbEg1ymdK0t8ZRYN9fQNsQ81axQyhlwoFmM5KnKcUGPgEg4jm67IMIsyxiycovCcNQRQqCVArMml2ick5njTtQSrkNyYLpbDYjJUBbyZpHEoGJJVG9hpRPk0dHT2EoC2tqoM6IUVY30hBouRFUl61svj0iV+cxhdUAkVR1M1U0CItbTz5Inenj7oh6yLl9cSk4KqqnacQhJpgsbuio1EVzJu37ZtYHBg2vQmomiUlpVKKG5kYrUEbvq0Jhj50NAQ8f/A9b7+PpfLuWXL5qVLl8Yi8RnTptNUYqZKdBYpSasSCamIiM08ZD8KJLHU0FCFnd2501QNeBQTFxgnv6opqtPeelKNe+vj8U8U1sqDQiyasDUSD8vIVGnttNnpWCjY3ZWLRO0ZDExWioN42J3e4Z6+JbPqRga7sulYMjIYHhl0lVZeeeWNgcapDzz8UNuxLpfLx7KTD/7T+2x2M9qbbBwTRVgR1YDwcKUBv81h7ejsHBoYvPWmm2bOmoljDepNqMTX27q/eev2ypoJ7pIy3IAH+npnzF9AzPRCEKuykdiPGbu7euZsX02dRvio0hmOhMBWe/uxT33y44P93ctmz5hSXU78GCAD0QBihncyAzwYDg+iDmLSzmatDit7SINfYmkUWz8KFX+IlzLMQvAh31wqrQXIk1+Fi+PxTYBh3AG+cacwX+RVYBJrid1qm1Bb7rfrWKxudZSHYMXDQ+l0jvlxmNdwKCYCi4s4n2aWn0ZjEWykiMcOq8WB6YbpQ52BpRBMmRAVCTAXaU5XgPR2HusgDLaaKFCLJDHupFlFQS3HuBR14QaaQTuEC40MjQz09k2ZQqyUcpx2pkyZFgwGT57sIth2V+d+Yn7jlCNiNo4mMBS7hN9uaKyHJ9x43fXoSbCLllYihhinzWxC8FSILpxVOkAmXMTmSdmwetRgJgypMN2JkCp0Tf1TOCjJObRHPJH+VD+gtPbqnZxFiUbuzGaGRwbBFrfXV944saxhcry3hwDaBpOF9U0ou9WTp7Tt2Tzr7IZCxaQMg9/dBUlgrFluyvwe3tg/uPdn69ZuuPuD71u0aOGB1uYdB/a1Hz8usUWC4WgwzLKr+uqq6bOmIaR85V+/zIgguFMo4R89FWUNM2Z17t8eHeybM2+hPhVlmfZwfx8LlpnqImikqTRQvfAsewAKe+o4w5EQPsAq3gUL53edDHgyET2sTiL3SauJmswaVlhAOB4fisVwSGb9UkNtTU+8n7hHmg1wrJ/gJUAIOhYoCYgJXCDbgKKYFcBLYSwizoFvGrRzzQW32gXJFacEn9WnBoPDaly+dMHM+vLmQ83rt25HQa2rqWbp2tBwmGgTCbs1FEuFYknzILOD/RANdn5mb2yP11bm9YDFsEZ2UGOOE/dIhV6KAel0fb191EpVUBCNcmG8XChA1xADZkttxTGF96+89CK2KIg6epvH5+vu7v3J9/+r+fBhNB8sjZg0ckwzkh5NUWQnUT7NFhPxbxonNCxZsmT5uefOmT1/974dkVDccczJZhXMj0v+JJXspbMw5sIHoUGwXmb/AVajSTYn1ZLJj+o6LugrDnmjrrWzeqBqrp7/uRMp+DKdJOJHBpbFEhJ4OBEbc9BIm9NZR6wX1mNaILSBTNLV2LTm2WqPIVJVXT545PBA5wDriKPB+Kc//yVLCYEsAic6TuAa2jil8d777+sZ6BPtRBavMHNMFLVEitmjgg6rVUlJCUMM5otrEX2DZVpvbFp23khfhx+Jy+NEXse0ENdZvXaXwWO0VVeXTpticrghVoqqF1tzhiMhlHjm7JmEsK2bOMEQ7U30dbitzMUh1Mk2AwwYtJ+9L/HkyBls6F2pTIH5JAEERhXgUHohdwAfMETEMKz9vMKGoIQ9vYkowYw+kAJbGcVA0gvMKRzQoEpxEnnIBZZPEpR6XeHB3liJg/lbv3iTh7GJWk26qorS/iET2wND0RFEGXgQPTgc6x+K+wOeIRGJMgEPaw0K2VSkoIRDctPQBLZ4rO0YREHAX5XOGxiDqH/Cz2kWlZKKW0xGNpPo6eudNXd2VWlFeCT02EN/WP/G+u7unlyaxgFRpIRdMc+eBc8xu0JONHGAwEyxUIT9UPft3v/UU89ed+MN1990NWYkIj5hty+3litCVWSFUjepjqqCwcDEGvqVkx2L5QXHGNIVryhTEHEUFbVE7/yMZEBhrPpH5uQigfd5mrgEBWYCWDAlOgYNsZjqJ/tX3HLLSGdb7MSR44ePDg0midHY3NfS1tObG4y4Om3Ll51VEvA+8cxTeBzheFrm8axYsWL7li1pg33x7LkL5syf0dSE6fXFV16hZVo7xF9N+jfvCpTOO//S4YM7iaaac/o9FXVMk7IAxeq2+xpqsPfR6vEYyOdnOBLKoCppEMgondzUHQ3hRg8+AYiISkAlMaadTnccP5RCguCtPQNDyjmZ98JOpIP5Lcqi8pBv6GiBSP5ErgK1TBgWsDygqgngnk7XuSUPoApEUJP/gDh4m68MeHxOKxFcRkIh9KuqykpYNBshBUPJmtpakIFdfgj76bTZy/wlbKZ78mTnwMCQhEjJs1dasqLE67Jbc/pkFJuqIA2lUjkikSLscSMWUQ4aysp6sU4KVkozoB4Y6N5Ys5nliDNnTiP0+zN/fPb5J58dHBhmITGridMmJEd8SPhH6CfntAVzdu7YQRxIvhaZVA6RCgQldXri4f/m/l+3tBz65D2fuO32d72x7o2JEydOmjJFTJ4ULx0oHSInqQ4OsSIrSsdKHjxTqbiXDOU8/pAnb342/v1p11peqP/SSNl4I8fkBJQzh59BOmM2OYWwyO41sogCSd3msOdsbldtQ8lA/+Gj28MZfchiu+nmG59f+dqCubO+8Ol7QIxkKjMSZGl32ON3V5SWLJgx0+sNMIEhhrVcnkAkDLxMOgk48UyqK5YEhNKyCnZg9fDSiA+GyeH1G63mXJb9llVIU6lrse1aG85wJKS54mwC82c9j7sMa3F+oIPJVyySWdSHFNHNbH6vP5XMJNAQC5lwHB9OuhNzneg3Ahh0lwCSyJyYFoi7RPylEo8Pp7chnMiEwTARoDpTQRr8kBttYoALbjXwEjaYE+9eoIT9QisCbjNDqTd6vK50PhWLBi++9LLyiqr/+un9g8EQ4Z7IMhQJouMwO4BP5ZRJlRMaq1qOEvV5OOF2YMrFAarMb5E5fIpRUE7O6F1yKXXWvAs00iBqrUpFZXPE18XvtKFxAnMzX//y13ds2IaRVTYhzOcQDvhQcIT2Z7MVDWWf+cJnP/+Zz/Z29GJPF8BRhalOEekU7gL9X//62mQs9dWvf2XipIlIaGL4LaaUhNJ5ssZYfukNekDVhV4pHtxypaqnSpBLOUivsHD0Xnv6Z86qxYi7oSJpZE5VOIwJcRpXBwYTOkJfkKkaESNbkWOhTRlMlRPqfAdaNx08cf6l137qy5+5/KrLq/xlXquNT+wWh99bAsbhyhuLBAkKC+SwFaGgEMWgJKvuVjIRNApc5AQaFmx+vzlQNtDR4SJcXq6LajkCvize/6gSTgv9q9p1qiUCMWf0oRAJ1c3IcgVdoKYhrbewl5AiyTlEfMhjbX0NsMFA0YnhJGCfZHE7YEpPIT2iB2L3y6XSNR7bsqmVNyye+umbL75+2UJHNN7o9EwjwDrByAhPRkIhhUV40uBL61hGHaos6Cf4CUgZHDaHzwVPk7rhRFpeyoIz/5GWluqK8iVLFpMRKgj+ro111TUVvPRGo6Hj7a2pRHDOrAmzp+O9mRkajvb0jRw53h1JpCVLBcVYBKHTCokombIk4C4WA/F+LYD4tEcYMttOnLfiPIyoX/3S17Zv2g524Y4j+0GBhXBrDBciPMJEdMTNnNQ05ZobrpWZDogQz0mEkidJeYL7AJ6tbHtm2r518/2/uG/qpElen0eizoATePUIGvEHcRBlGujE5QBHJSoHmaPKYlIi5egh0ZJ1ZuQ6sReO/Unz/urBN7pMKs5WpzgqytjhhiohnvOpZEy+B/fxHZX6ij7BE7vFZrE5GGZ2SK6qLY0xTZFOPPLkn7B8lvk8RF7PgrLSVIzmmVwqyV8MMQqxgvggZFjAcyjPYkWpvlg3aar0uE4PARKJoWHGnK6B6KatB/cfOtzb1RYmCMLhlv4TxyWJfALmMrEjignHmc8JGWgZdPVndHn1/pJk38kEURtkaNhsINVQU1UWCPR399i9bvpPAqgwgcEWgBDiHLGVstVu+/uvPm/FvCaP1yke03rTrn3Hs+HZJqO1e2gAH9BgivAjABn9C2ypAZEiFQ6Ll7YE/ObgiSLD7CJiAQ+pEqnVc4PP68NeNDzQt2DunDWbdrIVXjYVDTjYocmGS7HNZmRBU293F3ZLn7909vRJTGOwB1Da7kA2JVsGknwAtKrqKoCAjDWJlFcgjPA1SSEQz7BDDtji87vf+u6RFtwsTYAmvlmoaoJbJCUnETilYlOmToWtXX7FZc89/kwfkT5kXZfwNmkaEy3CdFiVDvuVuZNXX3p5zpwZF19+CWgvkCVZyY+6yMNpkS5KStxIENpwqIKkYqr6pCRrGqJ9Jbc815o27qHk+NaDokjMUmxWZdsdbrCbceSAlmE1cZrtaACI5dBXRG5EGqFGrDWx2hNRY97ihDSUuN2HDh001dRccu65yACqYGoC0RLZXlARAyhblSesdqefhoOebMSEdapIfKW6ZKu0ALEJ6+0lFXNWXPD6s89u33e4vcvfWJNIjQzVZw21M2ZTMRoqOKuqLUDz1iadWU9klFUXyTmnN5XUTx1MYq9m18Esu50k4zG33Tpt0kSoJr4VwBlrVoSB5CUIH9tCXLVkxkPfvudjt1w0ZVJNWWnA6XZCxOobaplCI3Y1xjJZfq5AB31DEU+BUQ66UbsQegrfUIIQDp/UYvGS+RBsEWhEUeNPmAX7z8CBp06cUFNawr62NT7HokmNdSUu4jF4WHJeXt5Y34i/VyLKLmnDHg9xPitwE8UrWStKWJ/eWF1bw7JYVTQnwSlVsgypYCIwpcSmB37zwOEDh+BgkA7+ffBDH6isLKcHFP9UuAWumozs9k5HVFZVLVq2mEYIZipMQcQmPD7MDcCjN6QY8s/l2Ouiv3dQ2167WDwYTQKSsoloPts0bapwQkE2yUjxJK4FmRVKSz1Hu027loR/9dB6mylf/NWFvwopxDQj654RedQdiEEtBeplKhNGbzAhveBhg6nckEu5zflPfPSfPvXRj7KLmtRg9E+KBgthgEzLEj4xSjiOsHhXKI/Cjo4OukKax0lMX/QDRQizZOvU2lmzL7j6SnZ03bfr0J6deyPDgwMtB6ODPdGhvmQkDLGnMir/MxkJBTK0EVWjQouhVuwfVOqsnBSJRiTgugFyaEGMWTJ/Ns72+oIly65/enMK0SydXjS14Wdf+fjP//XjsydXE/wnybRuPE4QJAJlhwd6jx9tG4nG+kbCA5EofJVARlKY2D+En3CAe6oCAqQwQA5QEejAV6m6ppT416JLUCcNZJTqyL3f614we9qk6pJlM6fOmzblqnOWefP5+MiQLp3xuVzVFZWEjUFog4oy+j6PS+J2y2Aycyh+QDX19ezGJ4AjAo9IgBr+K/gQ5gbr27Z565pVqxHQxW0nl/P6fBdcctG8+XMwLSqgENDgc6YEq2oqFXPUn3/hCvy5ecwrCqdnP/GpT06b0YQJR5JSDjZcg6mzo/OF5180YjNWfc8JhVxmN4jAmBNbyLwFC8W0JY2W3uK5YgryRD3XOuzUWdBGstLSn3r+tlc9nZ0i+7IeOo11ij5nO2sxBGl1AXFkJSjYKbERpOlUq6+7IzrUxbLbGTMmT2maiiQuJEPqpIiNwhISK3MXPZFhJVYqTnS4JFuOGvTWpqapzHNRiACWhlFSWb5Fno1jbK+cPu28ay460nHwxKEtNU5Tjde3/8UXtj356JbHfrv36Sd79u5gfPjwjBdHVYfSNQyFAh+At3LKlK6WHd5ChklvZJJsOsX2YwG/ZyARSaaSyPqzJ9Z96Parrzh7rtuow7aWzTKcuHBC/ZhKxyEsy7p1tiXrCoYHEvEhAtRDPhU1pDCR2GQgimdwj1tgndJRCuF9yH4YxL1WQ394hPEWFBWxVGRXpsVhF1dfe01ixbnECLPbzeET7Vddcv5AKGK02Hft2++yWZliGQgTgIpFALZoNIlLosJlgWb0ncbGhpqaqkOhE0KbhW8R+RPYE8AXcMZ/KBr742OPpxKE2xVTE/oiil9JRdny8899+YVXeQKUigCWzxPLnZhO8HZ4aVPTtPKK8q6ubpNedpN2uNyLz15CBKp/+fQXhNSQFfkLXzOsfHXlpVdcXl1dhcaIOCd4SPvzOE7Hl86bT8wWHNLH+kcD91GI5/HffyAuEmcJlZOOh5XDqSSigo74NbReZuKRtwnDCgoK5YEkGHU2mykRHsnG+pwm49QF8x1l1bB92kArFLVREIOXmUjprOxGeCJSpGyNlc7EhP+z21TBQIRoBGxhs2rABchImiuw1z1SbjgWtzktd9x0pX1oZPf6zXqPd2bTlIZp1WCpYbhncOeQ2+tkUM5gcVT1imCDXAgAyn/FDN2+0qmzYG6EIGGQgPsSj3XFonkeBMWZE777zx9++r+/duvFi+y6fJxdgjIwRbysM5A2lqgQ10Wil6TiBau+L50M4ozFbB4QTREa/VeANHYtOMaCCSbaOVBBC/n58+fU1FSy7SFWAbBPKVpSQ9I4HW4unA5zZU2Vr6rKEaj0Tp7cMLHuliuvuOGKy6+/9ioPc+EmE9Ip8xPMiThRZ8xYX2idgDsnl9+/5NwlYgeRQ56B/xwCiwUpYtMbG5v3H8BOoVAEjqBrmNiI5Dl91syK6nJSqe9Q+fK1DXU+n5cLEmElmjhlAqAm8mcuX11T4/S6liw7+/wLzsPIzEfkLvKq3jDQO/DKSy/DjzTYgsFZPdab33dTWW3JLbffxhIegJU/kQK0g9tx/aY9e+uTYuI//xMcGQoFkYQJ+89+3VAEcDDL1pRcowcyerAs0QuwP3EGLamY1eFjJ22iPMfNMxecnzcRo4A+wy1RIAXqIVUlI+k+9ET53OerQPVOJBGJmFsdjkQiOLKB7PBe5adeNGxRNoQJtAffUVMrfD5jODJ4oqdlx97n739g38oNbKxld9sDXme0rweV4gxGQjViGvTJJSMr3IFfOrp2chMhZLCFgD0yx5PPfuSmyx74jy/86pufe9cliy10nUyXyyxGAgcWwrBlCN4XK6QSmXhoaHjw0PGTepeLcIi4m0LHyJpDwE7YiEgmcjvKDzVmKIUwWuQTi+B9xqpt5D18C7nEbkpgMCvimhO6CA9DysvIoLJ7trvEV1V7suck8dwXz5t13dVXur1uVqniLuNm62o3uxgWZT+hNvibZwvLzju3rMIn4hjmEkFCATvqxh9NeOXlV4Rsa10hZFvXOLGRunr9/vkL5wNPWs1B4tLK8lHJTaJoz10wV7g8OeoLtXU1UAPe3nbHu8RCCOXHAkteYqTXvbFuXf9AP7Z6csMS+4V/+9Kd/3TXd3/0g6aZs2i+FK6RBxkU6bqxs3rwd566ThyXThObrBZSWeyxCNhQPnoGJKETIJUII0WazL6lVld5VV13T8RVO9U/oYnqKnotfUKt5FAdhZtuLhpJhfsRS/MmZzSdDiWHe4NdI/G+UHRYgrTKTKQIvkipaWQMQv8nIql4gr4k/Ii/tJw4a1anc+6CxVOmNmHiHunos9k9JtwG7MR4wPqKR/eZfQhknjoEEgVDdC5vWdmEaeAZDplsP8BqF7vFVFfuNyLvx9D+8BolSAm6AE6BLIdK5dIJM/QuHGTvq3V7W17a0bz+wOEIO2AJGInwxjFWDMPHtQyieog1kiEikWC/vjChoY5pSvyk2MdIhDUAw2jy+0vY0Bv1THE0GRSBBXCKzf0q6i3+ioGh4WwqDjtaMG9+oCRA9ixisLOlvIRgoDxBewg4JLhx4pRLLju/oMdJmocSxRBSThKTUd9y+NDh5hbx2oNQQI8o22xkA27qxtXSZcskLqASrGmMbFEMH1AUHnRqmDBRrPwIUvlceWU54TZwOZo+b9Y5552D+YP8xcQjipa+52TX5g0b6VgsLyabZer0mQazs27CZDHeF1n0WFf9L1zQ8GwmcfL4MYKVQAzoOGoLMeA5cid4x6DjgiekEEub2HVpLkKDED6vryLrCJRPbJJ4AUKntPrQ8zKa3CFoy94EWVaARFjfLOBj0nf1dhzvPtY12Nkf7CNSO84PjBo+kMxhAD3pVCyXYilcntApFpstUFFVOWmqf/Ikawm7NOaXnL/ivKsuh8liZqfHJIIYfoD/C93w/04WgImgoAC4vnrqzBND/S48skxseWQB3oSXQDWJZyCqNrAtIpvsjYnrExJIKHK8u3/d4ZPrDnadDMbBJzgYI63GS3WBoJ5MkTPeDNbYNU8EUCS+hq6iIjB5Qj3z4Cgudrc7PDhEkVBrltLXN0xQnEcYKd8CFSK3EcRaZ/A3TAgea4sFw1a37qz5CyoaGp9/+cWu3l70E2RaqQN/6qBdEPTrbrr+jfUbjrV04wGOVIa8zZJ9qrZpw8ZknKAPmkudfMVuSih7gCR1bJo2rbQiMNA7RALqIMZGlClZvSFBTEMjIUE0KmsysohOakehRuN1N16/bs0b5EDXCjmQp4XVK19j5zB4PI9ovsyISdgrGkbrinRfPlCHDMYo+Gut4LF28VfPlEXarpMnoqEQ0XokXh35QlOQOwTbcmaTBVkAOmqzOTBKUU9oB22glrTS6fLWN033+Eqwe+XZNo1NBqRIyUJj74l0OBoZzBnTDpfX5a6EYJsT4RpWh0+f19fLDk4DLIZz2jysCc0RZTkdMdqQCyQPigMFQWWnx1s3dS5rdirrq89zXsz6Rmgi0xtIP5l4wh2oIkLzmY6EReCkX8Ev+RMMZJAKBk9ppbu6QT/cx6wdIfES8UQkFmfgGFcRWlgll2WBAip+MhWJDQXD+1p71u1u3d7WGc1hnrDAEpD1ChjERZkXEqkB0ngA4hqEBAphhlyz4KCqqtzvc/IU1cJqZ6UbG9SncQ2ora2jUKmk1I8T9SZyIUYibuAvBn9tY+/RVqddZ6uqntE0o2zK1O7OHkLuRR95SjSQ0QP+hn+IP9Bw98c/1XH8mNvlePLx12DoeoNzZHho65atkA5KYa2vgBmrcjxen9cvE2r5fEVVxcxZ01d3raUmzPsd3H+wv3fAF/BgnSdy+Z+eewHbO4iELyWrCvkCAwYK1byzFsxXO4Sx4IpeAH5pcivL6g61EAqJtsDr4UFiGpIDeR2kUz0mfVZ8qr17u7OWYHQUT0uhDSlkInXkEHHscUYjR+kxoByfANHT1NxpGjVeZHJxV0JPQydgcS1cHSHH4/XUVFWLopjNmXO4yMoA0C8Y4Rg1NupIxoPx2HA+3p8YCQ519M5cHAgPnaSlfkeJp941EnLFgglEEhvwQ3HZRD6Him7BFdJidRHhhrkKon2b/JaGhcsG2w8xXsRJQI43O4miEC7YPd76SZD6Mx0J6VMZdDXgom8rRsjYCyCYSusmEH5LBBU1f4APPgOA/kMyBNFUKpaJszdm7HjHwPp9zWv2HO0Jx/WsXxBVS0x85CHfiuQieMM1UMXAq7EHf6VQ7SFgAZdjPWFjQ32J31cQGyzls2eBMzGUcrlcJYEAuVFVQcJRmU1uAEIRCi0sQiudf47V47QSfkanq/UHGqfMoviX39gVQvoaPTSYRUk5a/HSRYvPyRdi+/YeY70ITLtV1kb0USPQD0AjZ0TN8vIKVppDeqgO1pqzlpy15rU3qAU043hb23f+41uYauEYK195df+efRALXEZwqSNUvqL3yFwFs812yeWXbd+8Q3pZMTcqTWz7zZs3LzgLNZI7EQTVW+kjGqlVUjuPVvztfyXNX0mnP3qkJTQ8yMSNCMOQPPo6nUa7BtlQuSkyFo+yLlnRQVExZNSYnyGcnfIvtToIn4WjAjt2WBUDFMlH+DbjI5qKj0gCWQcR+vsGiUF0rCUy1GXzVkIYbcwvxmI+Twm9iXDODgbZVEg8MySYlBvxih5mKTMYB90jfl5gwtyT+WbCmqPWxEIhg9PbOH+xjlDrvHz71p8pTxVGaI3RIFrGVABCLAh5R0kgaMdZJMJkrIiLwCUcQra2hXrioxJkneH2/e2vbNp/uGtACCmzcsKvlMIAI5CF7YArPEVghbIk31FZlOsxnOQh8Al6s/MvHJSNdIU5GAzs9p4YGZk4YQKR2KHW2HVwNUN2g1EjLqF9oTvYXV6b2098EoYzk8+AmWgRsKBDhw+/8Nzzz7/48iXXX6WBKl/KP46CAR1UlW82otKwG4W+sGPLzkySdcOiihRTKRdtwBeeT6OQkafPmuH2OCMhBDaUVv2e7Tv37txHM/CDhviAtLSRoElOp0NETJkAJ6RieuasGQTyibHkXzpXDjpl55atwTtv12ZfRO6UaslbrapjD3j2ttfFh0KT/syhMg2HRg4f2C8bc4gSKGI2xA9WCNFJpBJOlx+Misej2LFQcUFPbKesmyFz8fhBysBqbTbjio1nmjVvp9fx5GeLLulBSJVYeciRNC6bi6EfOHxgJ67htVOwkea6jh45fnTfkguvRfmDk4ZG+nW5KEhLQBCJlSjgRmux/IHOeLmlIaBJm2tgZGTp4rMCubinxG9k0xFKUm5vf6aRZ8xjGXjRTBDCoMrF0RVoZQcEm628InxkwI26ADHLsQGL+FFg6YpFwyf6B17auHfNrsORdN7A8nYASfklaTmAV2J5EyCRP+wQgKSUogB8LA1Fk06mKKC+xsLQ4GA4GA343cxrgU52F6PrqqisYpExIAJYU0OsCgLhFrbq89k9AZPVhSJLxnAqgh0SGG7ja2ueefKpXTt2YgqIjAyT/9uPlZIMgR0+jkVjBw7sJ5nCEcEEISWCq2Cr7BYGQEKFamoIxdCwZ9chi062bWC7TvUJKqHQBurGdzabHchCyVUNFeMHMxkYltj6Vwgb7UX8NhDkrvd423FpONSKpipoU51+GlqNv6HTtH57++a86anK8sC+3YQiZ+81kJDhk+/RD5RoAMPHEwNzJc+wRdFeJg+VUAq3kjiXCsMKfr+fCENI7PQ/Ai3UN5UMgaxoI/lkXLlDmemIrKyETFfUVVnyDphobmSgs2Xv9Nlz2NSUgDFt+3fpMiPOkoDVXQK+j0EZ2Ke1yG6zAz5nzZ311PMvbjvQfPmKc2QoFOgI8LypaWferdYL0i5BRnWIXCqdQK/7auqjnXCnuLgi4YQWi2dw1U2nDp7oe3zVhgPHugsENsPAoHzBJAOUHEXWGXZF5gE59VhgVIMofhnfImKAlgw5iAAVJg4t7OZkR3d52SyMP5LMbPYHAgybmBPEpsKmZ5gSXU6/3+4ugfWBekiKREhlqDqOHWMN7ssvvXis7Siuz2IYJWr1QFDapQhNsXVjP1QHmMQqWsh3nTjJYihqpcgEyaVAyu/t7Q6HIy4VbRHdkm3PSisg8wc1niqu35AZWQEEGmFONEMkAGUMV/hgkoewdHZ0ScjyaCpBV4x2Autr0zu2bJd+kF6R7hbeoPWQ1As+wTNV19GHYxUff6ElGf9k7Do4PNBz4gRrI6khHYCkQidLQXqJsEjv4bnGgOI9Dzai2yPscI05UrHErBJQ9VikBwYGmYzCkGPIsrVoIpeMZVOp0EiQWWSb0222muKxoeHYQP3EGdHhbmMBiSB7bO9mj8uMTpeIR6I9/Z2Hd9XWVWJrdXpKhQxI30J8aZgeDw8INME16XncEhsaGzZu2XTukvlsVKO1G8J1hiPhqWGHQotxhP7RBp9fYl3rM3qrs6p+qHVfNpkIh9moJ8yGLEd7B3/3p3VH+obw5iQiGKCGeC/QgJmQrhU5Q4XQJit5SgqAW9Qq8ue/dmiwAgbyilUWwnl0BpvdcbT92MKFs4spDfpAWQB2BBPVs+bMV0ZgEguRiMSxg4LQaeBIia3btz339NObV69jjtLsMBNxCLlZs8pKc2Qw5X+xNvJEVUuhGSHj4HP79uyPR8UuqvUAxdEQTIHdnSd379h/wUXnQn6oITNtTHorsz7ZK6uTsjSSHVmCe0jJRJoZHBqpteNMg0QHYNlbDrfA4QFvzUQk4i7doNO1tRwpa6imHKmbECnpLQ3yqLYSIrROkjOdppJJzbVrefpnDvV5/tCeXYRKQLOGNIiiKxMQYtyiryEUxAylzjA0u81Hw9HxkSSRSvkWbJQcQBejgekpi9WMwYaHOARCLDPJVHBwkGCHaBsihuQSg73dtZOmG4wOo81LZGGWtBDhFy2yeeO6SDzKyqfyMraT85M/WyPqXUz8srsrNEloAcuw2R4GL9y5c+fiQVdRVR6JjrDuESSUEVP9cYYjIX2tjaOQZEFAIVHYz4ATZCsEDBibs7LqROuBUP9waDg4HIn2RbJPrNrUMRhh7oKZcAa2uEJVghXJihjJRctH5E9tdls8AMlZ/mv4oERBbaaYVIg3sBGlduo7O7tSybTZKuIrWDZ59gL2qXa6/e5AmdnugKAD/eAeOREnbuP6DU8/+ceDB/enEkkvZNntyWv+2cTIgY8L1AnsapBN6VTsFNwq9IT2o+a0NLfInCFSufoPbSZ/mQLM5X593+/Y4qKhkd2Lc+z8vH/fQdEjRczMf+iuu9atW3P0aDtMkFZQGAexZzas33jHHbfk9VlITG/v4MMPP8qKu9vefUdzc+vO7TuZ76Gn4ULNhw6F03HomOCb9MwpxNMqfKqqf+MVLW09dKCjvc2G27tyQxeYV9wdzGG1hIGAGgYcuFlwVGBjNLoFTqgUeJEFMJwi26vuAgyMBMKIxqNIpEj7IHI4Fi0pK8O322y2E4ljoKc9UFrlcZaHoiM6u0PiJmYSrLfmEkcLe7LgqyxzeSuhUMxXZhOQOqLVsX2BSaIZGUws1z7rrAUPPfQI/PaSiy/0sRGDBBR2qxZLNyDsnOFIOAqfRUosqpCauAKe8GQCMdXQ5WumTT+0b09PV//JvqEtB4/2jMTEXzmXRteAXIJnmuQJJAF2dJxCQrFLMEuBYwvDKdggIEp6nsMKGF1BeJEH4T9swCVbi6Wg1pgBurp7Jk2uxlcUhxl/Wa3T6YMNwklF5mPFr07Xcbz95RefX/Xqy72d3RYTY4qLFTZZXP+RTlmtRysEp0TbQo/hRrE+KVRgXRqrGs6P3mKzhiLhro4uIULoxRKL1Pj+997+4gt/GhgMUqvujhNf/ud/qaurhVd0nDiBkig4k9ehLF15/VUur+XeH/xcWiWLA2iYtPLJPz7FKkQsvUN9A396/k+th1oD5SVX3XBVVV3Njq07EBxUDQqs/MHgTOsFB+kSKBckY4xKjENEcte6VKs/tdcOvhM1dPR27Dc41N9yYI9NlquLIsjEJwSNAaAEOp+sWDnMssIMMYRARglpQ4xlAlWwLQyogRNoxgo1VXjIV/iQnezuLCuvgfzh0UOk++BQn8UifoXRcDeuw9Wl9WkmMfJZh9WdjQ7aZT8Ju7fUD+mxJGJ6m8fo8OcTcRLEIoNY9Lwl5Q5/LWQL5Zkm4NB73nnLX135GjlPnzYFszUuNGNtoUfOeCRkGGW0GSD0dXAEAzbDBgYCKdiiMd8TkAwcqJsxd/vBlta+Qb3d6fMbe/qH0CQwjjGkGgYy1Q78gYRoFBqsKCVE44E8EOhUwy+TEwoURABGH0FfYjBgg/gAg7YY5draT05pamBNcbm/zOHwEhld0FsHSCV37936/HPPbd+4KRGNYNTATRRMw+kZexHVFlZCdQTGBcOpwNhYqgsNO08DWgSwbuI39UocRF5DdHwB/xXXXd3Vd+yVP602GW3QYWLHHz4wovCLHJlDJFV26vQmf2XZsvPPeeyRPwwNhClaK4JmxkZC9//sF6hc+TSaLWDORP/UqprqpVZrRWXp4FAEMxSVEEsS/rGKLsi3YxdyqQbl9Cc8pIKn0msFjjvLW5BYzzZPHfjQy9ScyNBoevhayEJthVYyEIjKDBOTmTgH0ucIivSfWjyJ52+SO1GyGQ+mgglqbrYRFZM9Kn/+6OOXXnBBQ5WfmPlWBw/D0VDQ4yszWn3x9DAVYx5kKNTvcPuNDo/J4cknoladJWvA3IoXmhjfUmLVkwBZSPY7du3YvnUbjlAXXnjhnDlzN2zYsub11xvraxYvWghRoPyxlhXtB2P3Z9yF8AxplBpdpEe6UtysWF2ALzYO2Uj30SgRgJEKFpy97MLLLrvkootxUMbRhAlfqCgHn4BUwL9cMOyAl8K3IscbhSf1hnIEjDgoUuBAFr8qDgCACc8S9tV6pI0oYGxfOWFCE3ZqFsVEI9FVr77w+U994qtf+Py2DWvxzva5WEAsOEZxTAfzJ/yJPCRn/ksRWEWAIdU8rYlyPu1go0KbraujU+LKKSZEnYhZGKgqO+f85fhLgchwR3KGx3JIaSij0mX5GXNmknllTc2is5fABQT95aAK4iYqfBmbEykAQKNu4dKFBBSsqKiYv2g+e8Ir+UJSq34o1ugU0J1WxdNuxqc/7YV2o9qdiIT6e3uUGCIDgcyJZQabBy+hHaMJZSaG+Qmmc5ieJXiX0AT+ER04HAYjVWeCv0wPYuISv8WDh5sf/cPj3//RT/oHw15/FZt50A10hjtQjSDO5pFsjTw8eCKfTZktTpvLh5CByQxF0WyxkyGG9mw2TlQei9lJTFl6a8nixUuXLV27bt2a19eUlfoQnJsP7YcS4nT/pmae8UioDT2wBxcSHYggiEI10RvUgTt1KDjc3dsZigUzRKkjOojNwr4foAsjJL4jaq0DwCaQCu6BAcrGqEEY5zFY0XpWey4QzcGyCZmzkjjZzMXBOkjDXT8xm4bC5WU1ZVW1Pd0nH/jtLz7/6U/84r/u7es+6fdgUHCQnJlLhEzxfITzCaDLt8JgNQuklKth4pthe/wAA5dAwxtr12G+lw9FAyngsY0eNWvu/OraagFHHiv8JGuF5EJHmDyc2jSZ6XxmkpcuX47LuFYauUDIlEMpsq36KxSQeJtmzZBJTYNxxcUXCGKqTpEqcpzqoWJXybNx/TbWgW+6kIac3jjtq472dua76V2hRewuwQYRMZkd0XwwhVxyGGQXHf4hzGMFZ6KCViGPsIYbUUhjmHBO1GDYpfjOOxwth5sby8uRQf74x6fwm3e5ymyOksq6pkTGEI3GoceR8AhOCFaTHcMLCobQdWZDECbNBjCTiLDpfBqDmSgXUiimL/3yc5bNmjX9vvt/sXffgeXLl37hnz9XXl4qg3D6caaLoyK6AQVCvxkyxQMFjjDNgI7iu0CE5pEBdqFmnw+93oKf9O8e/WN3T5/4PanIMWwti8ACS9RMo/SfDDMyqsFAgrHO5CFP1OgLd+IVwyv6o4CSTnwBMimdxc78Ow/gwG1Hj5eXV9/7ox/t2b0rFY14XZ6KgB/XOVzxhdmIBYiKA+fkIAugFMAJFeBzEnALSI2V/ucuaDqUYKCvn0UakoOMfmHS5EkgBl4vsK/j7c+bCSWg8E/qqnQ3LnAeqKpiRS9grJszb3ZtTfnJE7JvnHwvzFy6leT859NAWVl5Bcv8kZkNM2fPrqqu7OnuZY5RUoqmPFrP0V81In+uyn/+uRSoD4dHujs7Mfmib8LiUCVgvMHQkB9HVTNGTjpHnARBLw5sV+Izzk065cZBlJ5PJuCGTFOQjA0FWE1K/bAksVYLljp3WlPL/uYtm7amUoW2Y70bN74xPBxqbW//0uc+PamxNm4Mu/yV6XBfMh22ZkrNdixAyBdCC2C60DKXpy46eKKsBuMZi6dwjpPZSPbxverKywIB74IFs1TvaboMI3GK/526+vOt/3/5TZEMM/50O7IHqIGQgl8SSnsyEg3HYpEoC1VYFZZhbjbz3DMvdHf3YbkG7ulBtothYUoynuBj1mgyuvAjrcsEkwEydWgdpMEWZ8FAXmsH6K4n0gmmOR1eVPBjeDLLQJ96+tmf/uxnB/btYuctNsngLXSbfEAuYZaKeaq8BesUKKNgqj8BRQFnzhp7fNvhEcojh5rIA1+LTEXi6vv8flCIB4vPXsTUJZeAt0pL3eUSY25JoNRfEqCXQEOvxze1aSqtoUyVp0bXuJRbPsHZwOv2iK9RvlAaKF24eBGemqqSUrpKxbdCTrT08qU6pLjRg3en3ao2SvrRBPJ1IXf4wAGwnZTohJAnQQH254hFR4b6RedXEWV5C2bxQr5lOJivSKeQBulkFsmwZyjdwXwgiqL4lCmO6ispw0aAGywskZDtW7duZ63YL/77/qeefw51D6sVcjvrj7DH6Kyy3ZrVaVamArExiOOizO6kmAsxW10nTx5n1a84N8Am87mFC+Z/5CP/1NBQp9oHwAA+tES6Yuw4w5FQtZWxEISBroscJOIUvlYyMc/sEGiGNwlYk0zm/vD4U4eaWxAahRcpcKSb+AWpCD6JEzNRlbCs8RAQUkApF5JUzHGiv3ELlnPw2ShjJEYM1DrnYtc8fCnITkYnx011VYXbifc4PiUADxZt0RglCwoFw1WUF2UsEcWUN4JyatwoS5iSJOcQ7jb+UA/5WoNAlrSKKiQZqgPGSHQcLpHEmqZNZ2Ug1gs+p1gpWQkOJK2oqiLGu7RR5utNc+bPU2BD+YKx0ghQV9EgLjH6UUOtz/ji7HOWQUQoUctWJdQqIzWQUjR4JJfRQ16oOow+ePtf7DE9HR30NGSCFHxCPzDnRp3i0fDwYJ9Mz4vep1oilRX8YHKCxDyCcUEpzLK4oYBDKd0CFiNRABHEnnW6HU6njV03IL4vv/QK8gLuhBDDCRMn+H1e8SUEgVB8TfZkItbdc6LAruRmB61kqOhBlp1iLLW5cIkvsdtMLhfLmIx2lleZta2XVBWk7WN/p9p4hiPh6DjLFA69BQoJCrL+DGkmkwKzsMpgIGWt6YaN23fu3o8YyHAxtEJhwRZRwcgDiMIEnSA2BLumsX6MfBgS1EXGnEO7II12aKDGczJCKILNEqCSrfLAG1nVi3siHsasNiPWLmQWNy9kY1lRKoIuX/E55QvqKFmOTNTwc1I6oYJjrYJSt7EWCiDLcWps5apA3TTbkvacvGDpJAP1PG7f7HmzCO4u6ca+FDzU+bHCC+qSTuIvNk2d6nCIoV+gSLBKMFDLkB9kUeomnwGO4PbUqYFAQJEREYYVDx5NrX3zlrN8/tcOiOahvXuROhEukRpUV4uIgaMvcxVwufDIiDjx4VsrdmBUjZRkq2cxRIxepSapNKsKlQgNFSZmiXoqVJm1D0S1mjQRD77LL7+ILtq8eRNeqQQyJpnCQJg8NgWR2i3YgKxORFh2GXR6y60e9vRlb3WPeD65rRCvKU2z2MFKzMP6HE47gv1/7TjDkVDrAUaE3gf0gSC4H8ODWJJGSRdxlKXS2c7OvpWvrUWDgVBpJJx+Ywj5SsGn9KIimmJoQY4Fr0ADRg/4Bso5uNUgSX0lCKREI4pj+0E9UdJ4Dv0WEkAW2bzdyipi9mUSJORzhfcaxyvyCgoH0IWuCy0Q6U4rjppwIRUSjlTUFdXtaSdBKl4rE4tUDARRiEBBVFm9oSDjwiVnCSdWjdW+10CGBROCVYJXojyXV5UHSksoV+NktFWogOoaqBWOkZISjgA457NlFWWzZs8Q6Vo0UgpXWb4FDbX6nFZpdTO+MuQgt+r5/j27GDLVDawlEzcg9NFwKIITkgqtBsOR0AHM5wn1Epdp4lxAQRiBJO54kF44GOIFdRfmiTCv/Fp5S625bWiYgMH8vPOWTZ08ibWCn/n0p9mRgqFJxtngHqqblGUn0FCdvqR8YnnlhAceepAsfKWQoNrq2oayigqXxwmbJQXrpTCrshOG1ktvbeObnpzhSEhrgXlARobEoCcsDxoCPE1WKEUjCXZBg1VlCuvXbwlG8DaEcAoOaKPOt+py7E57ILcIqCPBEabdQUfJQMmoEFYKEoTEiVFFbWK+CBgVnw5ZUyMwrUE/0Mu2daSEtCIWKj1QXpMDgE6GXFOK0lYE3KkGT7QR5ayQgVtqoknFvJSDTzjGLtSN7OkprZC0o68lJTdIpPlJUycHSkshDeqdlkD6QNAXQFJcj+JYbIVXjdg5yF26qFgcufA2GApxRlxQokMW1s7GtJJC1Vmhv9SNW+2sXYzr5tHCyXu0mjwqVluxkk525OzpxAsW4zbPxQUJo1ok6nS48BEjkqXOYAkEKkpLy3lDc6ggTJk8UqwcyWFKlThNUBOLiW0w2PAgDuuD0EBwEVQszELpjGWyBr6CmY4PffADDMuunTtAX+SVY+3tmHNIiJGV2EKsYbF7Svp6epYtmutx2uGTTgkhK5t8MY6qG2RyCzcMJKRxrRlt49v9nvlICMSg+gGHmGIgk1hlErEwRBFZVITDbP5wy9EdO/YwfQDdlT/tGIVpDRi0F4wfL0ED0AzsQqZJxgl6AGHFSI7Xt4ipAgQKL+VCxkQshLLaTYCQzwW+nQ5HRXk5wCRSFbIoOKA4BsjMQ8FYdShmOAaL8oi3HJRPVlrmfKclfutZygOx0c8QLEebQXnUU14gG+fzrGNsbGyUCqiDzFUTC5FIFBUa0g8ycqCyer1+LqQwVTQnuVS9cbTlCK6X4gwhe/sypyiGZ/InsYijxTy1Et7+TM4ihKi/8SkgHrC1rpMdh/btxv5Bzqi4UiUWyDPzKeFCIFwWu9PtYnIpUOZweaLR6MjgIEhIasgcUqlm8RJLJrOh6K4MG/2sSBtiEc0RKqnXu+zO+ro6BvKySy+++MIVyEp3vf/9bpeTIG4YDsBgcBKtkRWY4OycGZOvuGSFXQJO0nU5mYOiLmpyhBFGk0QXFFg5BU/jm/Xm6zMYCaUT+I8oIVJJNol3TDJCmKwwfDAaDoKTgpDJzGtr1qMUChcTSFUdBIiqKwEOdag7wR9AXx6KPi96huAOQhcnTX1UFJoS4VJkp1gr7wWU+IraoGLh8DVtapPf40V+A6EACz5X10WmR65AFvVgPo5r2AtZQVt5yC3PBawFF1R15Zr/Wr1V5aXVUhrPOVttiMpgjyTgQ2qPLC2mBGkLHp7mSpmKkNQc8hnQZNQfaz+OvgweEuWPvsO0xIZKyo5RzIeaa5+A4wf3HTx8+AiYStAiWEpoJN58uBWJnKzceFHzxWj+2ifaeXyNuaZuHOMT8BXdCAlj8130Zq2vVGYE7ZXouyIU69hiAAOSzuH0EBw7kUzjnADCJJIJmkR/QWcVUxJJlQ1X6WyUETqIMLMMBv6ipKJ3EGJYKxMIlDPZQg99/nOfrq2u/M63v833SxcvpBX4c7t9/qqaOmYjcVWnYKiA9JtACvRIxkhMB1iCGD21o7hqzOktOq15p27O9HlCRheXMTGFRgiyScdBKeFa0H7INdPhe/YdbDnShoMy4pei89I1jB/gCEwo+bB4/WYQUWk0YNeQUWGkdDop1ecKQ4FKMEoBOeIaGMnAsQQWCRV9EZTm0ORPLStutYIkTzHWKGSWLbuxu8pUJMlII7WUehaRh0utUO05oAvqcg03KCkJeEo88fggpfMMtsD+4GQKBwZiWGfe1z+s4bb2Lf1F1TpPdu7ctu+Cy86GbVCNNatfZx9FlChKPA2sVC/Ro7/4+X2f/uw9DY11wZH4Iw880t7aaiOYsj4/cXIDzafmxcxV53BdbCMXo9dvSiBp+K/ahxxBDRFBRdzlkBgteAIKFWChIy3CT8VudyDYQ9G8gRKLzYzvu82B+USfSLAnuQuuD9OzEUPAqEuE4giiYAptYaG97N6huh6MYneKgM2Ora6iwv/Qg7/t6u12OG1TJzd5XY5EIifuMQTFSGaUZUsiKUIXoBvFgVPiDA3FdUFr3ViL/urFGY6E9FGWYGmIE0mihWYi0RCyI8MGDwQyYIqvr9lAxyE/oF6NgVexExVhBheK8IDcAlG1WhEaQRsAhFcaiWcYFA4AW8UOL+agngsAwVMQW1gioVREvKRIwIG8BE0HrkjDLRecR3OT4jQQVZ+zLoBXwi1HpUfwAT4pcKrhopaDVgOu1fO8zx+YPmt6z8k1AC1wZswbV7685qwlS90eGyD4yspVu3fsRIMhB1W00Ag4O8Lcr3/528HhYZZN7di8dcumjbSaorX2qsy1/GUOACbdfujI1z73lbqJk4lkg5ec1WBH5Swp8599zlJResdNTGuto27SrerQKjy+2lovSilcqT4Ek2HgZEMXIfzzClIhlBTHUaK1erxqhQRiP14VBcIxsiIaCxgDBQ3Ecw1ljk/QApkxhPsRmgnDNhwegkbOql3QYDqflTOGFPsRmKy1NcxW1IfCIYcdxkfArywTGJQH7iE+0A8k5lBGHbkAGVmQBg9EdNfqP75df/n6DEbCYlfIULAdaCpF6HuZG8xgSuER3MSwc9f+o20nYEoiUyrKO9ZZAivFG8EKLulom03bXInuJygbfEx7I+kEYoqHJqeNsSaGWYilWnjERY6peQ7SMvx8BRKCbMAWiM0tB4kpC+VHIRtjjSmE0kFRhpw0UhM+5xOtigKro6Vrn49Wi1++NF5+9eXbN26OhlKkx/BwYO++L3z6i2WVZWwMfPRwc4Hg9/K5SgwLFckaQU432N9z309+SlGYQVCYMTmRQhWk1ZAPpFy4E79YWIl3tmfXdjZ/UiooO5Flr7v5+qq6enacEz4+2r9kMdpRp/2SudYu7anWIjmr9Ai34v8AQmN0QVCniqJR5wjsiUIo6oGWTHhmgUHCUQaRh1CiVnyFrLZ4PIRUg/iK+MNYgpe0J5lIKuoFlxb/ChqK1kDmuN6AxRgPCGHIpgPEhGKsGSx20YCdEsWJOqAxUB2tqtLJImQo9RTPpL/90CDmb//u/50v6CyIaAItAX5IkEA1kFj+QpH4hk1bpf/koA+L/ES71850L13MAOBIwQEmQDsZMzAEmUSlGc1AbrRrDd60lCxmAWfUPL7iBhBONnIhQ0Ghgiz20YaQs8CRwkDJSEKDifCjkgmFFn1MzdyrEZeUQLX2laoGJ57IISKSgkiNSRLCeNqs6bfdcasRRxG8wEFKXf7ksSM7N2w4tGsXqrLEspFM5XM+4Ye6kDPU3wqLFKWVVXYa7ee5vCWxVF9S0h/CQ5iEzRlxJwBZmEDTs6fVdTdfc+2N14uFRqVTNZMi5D/HaHu5lOqqQ70o3mrXY0+EKgnRkpkJmeIUmVRoFpFjYHp8Iw0QLRTDNUQBbsRWnkYs4UJEsKmyPguVgxgicgG3lMlYDFRkyyECPmv/EG3EwYiOpyiJu0NngOMMAzNaCC+UQGK6l/RyoQRR+V74oSj1oDsP3w6Oxrfmba7PYE4orYVtYNYiLitLVVQsdGxlEFQmxwt7D7QcOXZCQlcowJBhlENARgQyGJCIb0x2E48Lj2oBHvgYFxr+yFhJqBJmuoWECziKysI4AZY8EQAla5yk5FuFzBIM12hgp3NywNSGIZWSIKuk1NRCUqoRxXojkEHl+VpmF0VMFXatjbfURUBBMEJgWDEXfrgnB3moQbpgC/VAFChcdeN16Vzmqd//KTgSJRESGLDIt3wjBlzVNkFyruQObsgvsoJ0hryVBGM/UpB2L59jlkDrVg/g7Hlzrqy65IZbr7vymusIUiOxkqiFVh+VgaoT1VQdqmX51rPUXB1cKFinD6RtuJsxGyRiRV5mZrGQiE8sVA5aIdsPMlPBdCB10xg01l21Yh5BWqI1gcAY4gjnyGptpCH+7DZZaA8KcqZCwvWJfSh+9jp8fzH5YA9DnaQqKA6MEQ4zkIKx2pGMf3yJaZgIGlREVXq08loT3sH5DEdCOoneZHpd241ATSGwhCKDX/zq1WsU/IuJWYGJBmVaP+JlKp2Hk6fW+2qQBGTpd8XDhB+iVSrlUMADkFBAIheq20EYwkJJAEVtaguoz+bTTbNnsNEKXJlDsWSUUxViiKzVoURQoIVfiWbNBZfyLVZKEoipRcw51A49h4LFvELt1QF0atghkEFVuZF7kWOzeaL03jKtaeqrL7926OBhtrnWwgWQgLwEqvhTX2tZqQYpfyvFsmgUGqmcgXfsoVg25AcJDXujRViL+CyYWMczfcaMOQvmV1WzPkMglE4YJQnSK8XM5UL7L0CsPZfX6pCaS9W1Q+2+Jj0usUiYl8WqqYmO8DrYFHEkhXGBN7Aj2fqG2QLZkRfzKQ6BMMdEKmWORG04kpl0zBBjFrZZnbA7YljQdooCqZiIZyhpghQpweywP8lEhsNuo1NEBFWTRjQeFknnU56CAZn+IT1PaDxdUqzy3/5zBiMhAylUFE5m8wQk7rzNKcogBuxMrr1rv9XhmTVr1rgeEzCUURB5TsBS4A23ozF4GE1aBHV+lK+2+kSxIfm2CFLaBfAg7sIiSApalJcFVpy33MaOTrgzAiYJ2WOECglSCUAovAUchDZrh+Agmap5c9HVsCponJPXMFLwp1tWxxuJ66yBhUIquJgwcSoGjccgKi4BcN1cbvqchdNmzhsc7Bsa6k+ltUUGCsplF1+puTSAqnIIviH8naIsIKH2FMgTUwboqEnM8JjRWxQoABWdC+hH+URSIJUkFGSUfxxSCCUIf5RDdU3xufZEHpJMjR5xrBkEDiYeIqEowWDoRqxRIh+zAIkIEzYQxMgVBrOUSVbhQhRkGt5qp71cOrWRUeIJn3o8fjalZNoGMzl2URvbEVqt0FJsL05ZP60abzRgyyP6lhaHEnGbKIaCr2pSkn5WbZQm0Cd4s+EOIe34ew/wtwg9f28O/zd/J8PY19d74w3X48snioR0FPxBxhEpf3R9vIZkvONP+Bu9q66F6dHzGmae3k4BpTExTXsFCo2lERjiWyVAqYfFV0KvwSjEVY1TacVIUpEjyVLykLtieh5xaM94wYX2iuf8UVdQpe3IEVbsT54yWePJ2gdjdSO9YLf6UK6FgyphSpUmWassZXaLK3VXfCS3KhupmPZOfkcPyYfy5eXohKHAr0KtIrZxI4dQIMlC/dcyU5mobCWL0SxP+y0+BcGOHmnLxpN1NVWwJqoOJVINQqRGcRMWotggN7jds7ccKq5QC8lMrFlSJy41sUKhs1oYWZw/FIWXPhQ5U0iFZtWUsUPchcPRAD6UNqiNA4rZSt78545DyKMQIxVJoFhpKftvONra2s5UTigQxSFAKCt+TIyVggNO0lfEcqff6UoAjb6ku5H6AGwhsyyEI4XMo0lEZw0YVY8DXepWQTafkpGC8aJWpcZGoROpuSG5iIL4jsiBaCZoIDgwbrAkC/Uaxiv6vVAB7hlU/mH9k4dkqRgbX6uMi+lJx9eorMCO2+7WctKyVq+0S5VYe1f8Tn0Gi1MljUs09nq0QvJg9L32e+rNqavRNILqpx1FJCYLOvnU9M9pad5yQyZjRdJ2moyHrc1jrapv5M2pt3Sw9KZ0jeowKZ3OYaQlSyUiKoojqRgIelN8ExAL1Gd0sIwQ1eI/q6JsUqZqQLERghWqCFmurw5JfloT5QH/ldFNvpZS/97jTEXCYo9gF73zve+5+yN3/4X+OXS0DTG1srxEVGyz/Y0tO0sCJRNryktZ4s5eZeoAPYLh+CBRMDBeiMKWRo7B+5Qk1ZXlOGIrkBNvGOg1hhQGDGWPCUp22EaoIw/GH8EGxwwRNmW9r7g1jR+3gcF+1jrZmVnW6Q4dOdbWfvzi85aEojF89d0SVEwyeevx5S99CXD59re//dZX/08/oXPYFpwpwS9+8Yt1tXX/8R/f/KvNwfTdyyY5aBzZbEkJS+Nd9C+UENmS58irFeWVjNpfzef/4wQ//vGPz1QkHO1JLFe4tStKlTp4KPXaSoiiqbr6yORpG/e3LJo3e9bMKWVVlcfa2lhnd7i5dSQYDYVjq1av/srnPwVlZY2FyHx6XH7TwUjiZO/g4dajBJz2up0+j2NiYwO+FNBkhlkIp868a/vOKNseqZE+cuRIeXlgydLFR1oOQ4Pnz5kp7k7gIhTeYAA/EQL5BrmKZcNscrd9x44lS5acOHAEL5PWEz2vrd20bNnizTv27j989F3XX1lfXYmZFI9EZCoxWwpKCgoDcdoFJgfhzSwROHTA6POaqmuE8ItMLK0XKq64BYnFZ00IgCqcl1wKhxceDoMYZWCKk0uQG0mmUv0fP4E8eGmG1CH7b+JabbGEgsHqqiqt7PTh5tSqV/B8Ms6ZbTn/AuadkBuZq+CPBkbCkZ/+7Kf79+9nOuGf//mfmYxlFSjbyMED9+3bR/R+clu/fn15efm1117LEOD5xhBj+q6oqKRX39K8U0RSuk8dpNcM4fSV6hStc0jJxkHo+UlKofo0RGa0ikZ10kjvaz3O8L+pIOxMby37TWnOkFt6Itl2NPj8c87OLuwELf/xw/0dA5t2PXztxcsjg33NB/cToRp7SSgSjSfS/YPD+zevU/xNOlv9ERAJmppNqPjTTKCx2nrSpIklPt9ll11aXlGGKWDf/l2DwyG2+vTabSdOds6aP2//wSO/fuSZUp/96ksuIBsWIgLUyWx+cHiktrIC10Vyx7hHGKKXX1szEAxF81Z9PtU4ZUI4lgyGI8g65WUVie17ZXGASKq5o0fa/SUsmqk4fVQECJQ5RIc7ychdd7k/9enYDTcMnOyg5uAVVlksJYAC9ljMQEjJIB3gpGVCxfgH86YyMG+ZGtQ8XdnWy+ks8Qc8Ht//UTwEfFmmSVhO1k0jcSBmUDGwAk0LYwmgDPxrVU38/OeJ1a9nKmtMqbR3GQGLE0iDUB6qDP0oCfiuuPLyYDA4efJkWN8TTzwRCoZqamvuet9dF114IXF9vv/97x89epScZ86cOWXKFIgraMO4HzxwcOHChawm0UoZPSssE/ol7lXSk6xIxLMCO5SaKVH4L7VlmSpPRoZGOru6Jk2esmXz5kgkPGvWbIJtEwaOr9BdwXkxIYu0TD/T81rmxaL+UZCQxvuuvdZ39dWh97/fdu455VMbhta8vm392md+81+sKtT0h9HeB0CNbYcFFN50CE2TR/K7c5tcoZL95Cf3zpt3FtuD3XzrLUvOXnzw4GG+nzFt6t79zXiegGnXXHF1uceeYDMW8eLXR0LxVa+ve/97bgfy4VSonuxz5HS5j/cM9g+FKgPInhYMp+mcLpHK7tqzr6G+DmmXDWowPUyc1Nja1r1r78GLViyX+A7jD9rADkRPPWnevtPscA0HB04cP0T1KELmlo1mIk1DKeLJPDFrMRDBV8FJmANwzwWgCXDwKFEgGqp4xrEIJBQK9/X1V1VW19U1FEn/+BL/B9dkj7DOWo1BIl5FQkA6qzoRUmgU8ErG1IpliuDh+EKy4bD3n79ovvRiU00Nm+ISl9BqNDPfg0cLq43gbFCNiy++eOXKVS+88CIVxo2gpaWVRaPvu+t9K1e9eoKY+SpGCY6muNSKjpjPd3R03veLX0yZMvWrX/uqz+cbK07DfGwFbNSsQraJRygWnL6eXlAXJAcGKI7SaQtTWcqbStd2tBWrN/5ZR1pbZE7Y76H/hX3yBxriMGC1ULrZjFmWCeRiaf8ISFiko/xEt22L/OGPtvNXbH/99ad+9TOQgJmeCY0T5s2dM2HiJFbroDmIDVM8nKBuMqlNP2njgUShLHPCT9BVRkYGW48cObD/QGfnyZUrX9q8ZT3TxP/yxS8umN3EDB6dzXat7KU1f9pij81MyG12jBeJkPkukwXkxLGD+M2CIPkstLWhvvZETx8urfj1eOw2piOQfu//3aOpePSf7rpTT2RUAUtkHtbgCGsGMpggQ7IeAxrknMLISPSBB8zLzmatBDOBrN0KlLilROYMdKwzJaSKVWe3BSNRdE94oQbu1AkPbOQoagJkgId8QtsxnSPJo1R3dXWhG5cGysaV9XdewmnxJoNZEdqnvb0dt9mymmoWYGLWpHkAMpWhaHIXIU2JiFrnS3kgzcmO4Bf/Ofkdd9mnPtu2/NynnntyqG9wZHgkGsMZgz3NxTsfzoQoC/dhAPkWarLytVXr1r9BBlxr+f/oR/eyIeTUqVP4e/bZZ48eOdrf13f//fcjxJJMqwDlMf7PPP0MGH7rbbdr5BfwAFFphbjgG43sCPCNb3xzaGgYOeIzn/3UE398Yv++/YuIdLh0yX33/aq0tBS/07vedydsFopDxkwKsdCK7dajkXRT0zQ2PJd2wfa1nzP6LIOq2dHskyfnvvPNxH//V3jaDCCeJUWfvOeeiy6+GKAE5kQqEz1KmTSVe4qgY5EYa1MHRPXRsWMMFk+Xy0H4UjavWLVq5U9/+uP2Y8d/9P3v93R2/uTee5nUSidSc6dNnT+T+Eg6nHRwpFJUT7zYGIyhYAghaigYZLFbuaxYz3ns9nK/dygaXXLWMrClaWrDu667NB4eXnjBOT6XnZljqoGZNJ3RadDDpgcEa2QR3djAAbqRXz9gmzGTvdhN+szevft3vfHax+6+i6VtwYFje3e/vGjJ1QazG+WP4gBOJp4BMg0oWWGMYoUQRUwnWg9pgGYDi1ANZebI9PR0olyNSbBjhY67KFI6JSaMeyygn5FAPrEEIYjxngc3eE3szaGhkd/97vcXXnHpxRddQARBk0WCj1AEIXoplwRkpQnJWnYUYLj2OvcXv8g+pYmvfumNAwcefPUVYrjwWMmKQjRBXSjIOOvLKc94KJZMQtAo9rs5eOjAgQOvvPIyO0xNnzaturp6YGBg1apV1113XVNTk1YcKY8fb//RvT+aNXPW9TfcBHkCiHjIbnZCDsmloB8eHmElKiQMgeZY2/Gbb74FJ5vbbr19JDi8a+deks2cNW3q9BlefwBGDdvN5lL6OL6KEWbO+vv7L730Uo3Q/CMgYRE+4CDG0lLfZz+fOHYi/fLLi88598Ff30+wd1kgn0zs2bO3tfUI2jVLyhYsmE8EJBQAMW4LEoKBDDWBnOMHd20Z7u9ChiRq+uRpCx1253vufO+ll1z6gQ9+cMvmLY899vuGhvp/+7evGeA6KpI1s0hsEqnPEys6DUr3D0YSKow9LgOvb9jA0N545ZUEdJtQX+v1e/BAiSezj/3pterKspuuWEHk+/6RcCgaKXE5ATGcOU929bSf7AyGo0uXLKos80nDFFjxmxkeCt/3M6vLZoomw6tes82fg39XOpE9fKzVagzWVMSx5kqkW4OOkFOAO25zTFqDEMhOPAYaCFEknsqyg7qBsEXiZWIz5VlKbjJEQ7JjOyuGNAB9u7PWyYrUKGESnh5NxEZGQnGWcSYzbDFA8P85s6fiEq1kigKK2T333I2wQOAzptxpHw5GzMGLcoovqr7AXK7ZKn6hWnEw7tJPf4ZiuM89/1xu126rw40/rKCG0FiJcS42Z2RQpUNyBuu0a/BPfSc50WESsVsOQmDmDjUfWrBwQTyVHB4eXr169RgS0gmP/f6Rbdt3UKlwKFxaJoiEBzJR1TAEoQuCY0AIRqMTJzqwdYF4tyy6adGihbzt7XVeetlFiMef/synQsQazp2oqKy0IoOygYXd1dXVjywAAh8+fFhzF/lHQEIZxRyiwx+fqPjoRxiFzNDQxFkzbvrGt4m9iehy6NCh7373uwf278cfhZFhhyUUhptuvO2zn/0sk7oMJS5JjDJS6qF9+/o7jpa62SEsN9zV3mF1TV90XiQUI/1vfvObK6+8Eq3jt7978KZb79BbXbv37iVgYkV1XXXAQyhOnKl8bsfqTRtsbhtKYBS9oi942WUXMGaAFX6MAZ/vpdde/9WTK/tDqZoSx/ANl8+bMaW2pspT0AejYZxlPA77tMkTaidMuOdfvrF2y+4vf+5ur1PkGQ38TURz+/Z/FPoG9E8+ZZo4KVNb6QoNQEBmzpyWSQZTsZzOWIp7MuwUfCO0cC6d12EGkhlI8cXhLwqa2R3KJ4utwVLYIsBN2Ug6xfINHQD4F5FQWDyMSGJnEbQgGIsQFyuVGQ7GBkcibS1tB5tbrr7qUs0HRcMEqBuWLZvdh6cQKnASeUGv7xvsLQ8EDNkU2hSyt5vpzyIO6rJDI4Mygh+WJo8EDdilI2FGE/WWJzSgpqYKZEN4hrQhNCKdIjeCcph2GHyNWwrmnTpg+PR8tr+vv8TvR81buXLlhz70IcX0dLThhRdeEOFcpzt5kmAz+pGREeWHJE7eIpiw2bjXc87yc44cOQrfxhrE5nM7d+6orKyaOXvWv/7rl5Gt1m9Yf88n7iFe2w9+8D0kWBywSvwlg4ODr7zy0oUXXrR7965/HCSUXieiUuZX948cOAAMxo60Xfmr+2x1tSNDQ52dnR/84AcRlyQSOotiZINO1rjEfvbzn7N/w09+8kPUbqCWDgXKbCaHPm1OhiCOhNEvoCFgK3M4bChyROa68847v/71r2Pl++I3fpRxltQ3NKYj4dyetgTrpkR3txIV5crzzh4ZGM4aLK+v30Z0kvoq2c0H2YYa4u1x3rKlLcd6ukfiM5om72s59rsnXyLqSanXjfUCoyUzI3UNlWa7bSAUL3GweZ7omHyosQrIu09UF91A+1HLsmWzZ04rad1ntltwMzHYShymc9iICNuHfFNg8waj3crmh1j28FeWIFQQdog7M5w5glXnCyUlVe1MyAQH5k9ryKajqFbsW0QwUvJ/04EklkikkC0Ji8RiFWrEXGc0kukeHmlubUWor29sDAYHI5EQkEpicEpYlxzEYmJOAgRM+31OGwsX8vlyXwkBELKZpAyGy3MKBWUEjZlf3xfav4fFtdmOduNFF+sef8JoNTEJg36L1EuIN3CDaQmwaNKkSRTANWMEQWFaAuREY6SlUjCERx1c8AmEeNGiRSBtc3Mz/LCyspI02FEPN7cgNRAc8/Dh5kDADyQogZYvRPTlINntt9+GSgnxYlPUn/7054ATBt17PvXJq6+86tCh5nt/9GMkGdoIt5w1ayZ6azYzgL0BsWvhwrPQURl9cvtH4IT0lc7g9Zf8/BeR73+bEQj89CemufMHek5i/37qqWcQDPwlLhYnBXxNJSXVXf17I7Eej9f4yqsvt7V9HFu2Rj6JBTN55iymzYf7BtAJ6svL6qc2QQ4ZDFAU8Y6tPxgd/rUebbfW5COZQlVZ6cjQQCiGwSxqtNjCkeHbrr5weHho3+G2KQ2193zwZjMr3sQvClwCO3R2i+Wmqy76xaNPv7p20+BIYvK06RabfjCTPTwMWYhVlRlX715XSIeWzGp6z7VXVAUCeJLKh4pZKFgEBfTWCy82NjQgVOsy01i/DKgQFFBfYCs1tpdCqEZIY9NP1gGJaQajAFZ+onsAEGyCTeVpLItiO3v7OnuGiAYJZuKpTUwCeLX04+hBqcwo0JZwJNLfOxQOx5Dc8cqMJTNrN+9paWnzeN3Tp0+dPnmSz+deMLNx9+6DtVXlaJtSWa1DyUqfrasrFaAGqqUPECXFFyKVdsCA4WQSqUURGtIavJ7Sn/8s+p8/QPbz/fS/TTt2kVQtgUHUZP6A+KJi3aEhsC/OTNajaAj1lClBCasDn+SaxFqenEnPLW+ZoQX9WlpaQEgNCVtbW0OhSFlZKctQQWbyRIGEqZKegzHjW1Courrq29/51kMPPfj9H/wIWs26KsSfBx94ZPGiJS+++FJwBIogK4w5FKhQyURNTc0FK1ZQOHUjZ7L6R0FCodBnL3E8/ZzWiSLVYxuRoXKjftARjI74ueTRIlgdw7gSZV1mxhFpoKYk4BPi2NfPnNcwg/RGHJ8ZasL8ALiicuj1dDS5AAZTqnwf/PT7dzS3v7hq06QJde+9ZVlDdekb2/Z0dfW6bI47b7/581/51pyZU31ODz43Yu0E/tRBtPaa8pLPf+yDq9dvfXnDtgOHDxjZD4Pw7PNmEiziQHMHMDR96tQvfvyDNSUuZiydmn+5UGTmgAWK+ee59lpuC92d1A4/aqCHoIQEjWaeA/9qphPhd6FgfDAYwn8ZrlhV6VfLu3JulwNXSBIIBHucJWXZgxs2p+dNyiUTrCika1Qd5QTTOH78WFdXN07UBFfrGhg5dLAZqRaPInZemVBff9mFF0yaUImftEQsMBL70DRn5rRILJbGp1psnlqD6S0Ct1J7MYax8hc8VPFecAu1wKOZsrVnbeCtap+Ua1+6zP7M03KF6LthHbvSM+lpZGs6SIuuAO/SkJC3iI6cGU8GTrugUVyMHQoPBQkZeca3vKJcBbwqLoshGXMzCOnM7KGkYNRhcFGYjx8/oWVIAjIECUHg1taWlatWkk8sngAP+evp6Vu3dv3/j7y/gLPrPA+90c0Mw8yoGTGzZKFZZltO7MRxHE7D1KZJ04bTJk0ctBM7DpiZZBYzw0ijGQ0zz2aG+3/WGo2VtP2+8zvn3OReeVnes/eCd73w0Ptgy/lWPKgAG8TRwsJCiINCT7SohdGqTU5MLFiwQJ3V9woSKmvx7hJAeYm5HvWM3H77zW+88Qq6MqczPRZvHvNcAC6j8RCp7z/20U/k5ORCTcUcFKZkpJFN9snjx/t6ewhRKi2vmD17psvljMbVCriAlCACOrD66pI5FUWk9Wo7dz4R9o4O9E32dUXGvWsXzxvs6jTOn7VwVr3f66N2EbpHVu1dPCQwLxZzm8xXrl2anZX1iwcfue22LdddschpM8R11q9++6fN3QMfufv2PJcDgGaxFfYxPSigbRpCIA0E3QhmAkCc1+upEKbFlIaakuRvFDFJ60w+jJHsDoc9UJuCXCcyM7czjFAsEfCFX3vx+UAwMjHiITMdaTWnEAczTzCAYYb8crQ85gk2tfUHfIGewf7msy2UN7rz/XdBX/Q67NcpXyA04fNFQ8TO6t0ue4YT9RKbUkFmOsoBJijIwE/BQE7LYEAcKkgbtdlZmWAnsyqkRbkwdYPyY2LC093VQ29VFKU1efQi21Rukbcob1AfV89Nf3K/XGRZ8a2xOZwbN206ffqMmvSAm4TmcoiwkDZbzfC3n//8fgxYirKHzkoeTYiv2UA+7/itt95x+PCxQ4cOorxREpynDh04hHVHHBX1uptuvsFiteIngbUe3c+3v/3dvt6Bm27eghIB3OYl7xUklAl995A1Z+eAgAEt/M1vHvjVr3711ltvI4oAImgmiouK77nn3muvuXZsbBT9NcyE5XzyySd+9atfdnd1o1pkcQhUryiv+eQnP33DDddgpbLjqSiHgAKAkZOZubDRtmDmTPZLVIS0SqI+avUYYuGwSZdetXQeJadbWs4RkkMhB3qigqaAH8CbTgQ9Ht9I3/Xrls2tLXPbLCS89PnHN65YNGemb6C7fXFN8cVX8RcIU+FQzl1yCBPgUC8HQ/Fjp8+XFRcCDUNjlMTGmSRAiCqEHL8CoittRoMbNyujBk1x7+DYG6++daG5bcGi+XgRYFIf6u8sKy2kcUptdnV2wgQANUKIoslAfXV5OBi5csMSlPIXWi/YHMauvp4Md1Z3d//o6FBWthPfoLycHBLCg1qKsMH8vCsTSg+Vg6HTVTiijAZ6EQm/uu3lErKbwYX/+wHKWKdHrvC06V9T00DDco+Cn1On/uKP3M+DvJglmzlr5qc//alpgVOkG3mWnCaW/Lz87p4eGOPOnbuJDpcQC8QKsWnhDwCDTdlsruuuu+HChRZ2nryTAWKEaGxs9AV9N9544/r16xTri7S3Y8fOyUkPpS+hv2rv6OR7EwllYZhl3HyZL1TJX/jCF++998PDw8NMjdtN7HsmzAGcRIpAVuGep556CksukES2WZ1WxE5Spvf29nz2s/+AMze+iJjCZDkVmg1S5WVnsVkSuHLbJO4WpxWh9WmrQ5JVz66vSdfVILgCjAC08qACHaJp5650yB9YvWwRyVHwHBBDPzsQTXIVRZR6ukFIPJUhAtwLWFwCh0oLFz+AHq5RXZSBIuUGo0lnTgGCV3dP/9nzF6h/Qh0iqBC2u77ugYwsWywpbI0oPDoDl+sfHrDYbVVVNRTpCMfC2oSPshm0zZygqKdXdFTJ0ZIsyXcnEy7mY/H82Q21VWTUGRv3krUARr1w/rzcHAdER7aChJIxWgUfVMRTe8ps8JNJxgkWEZRJUraNMiWLFy/BYOnx+osKRVPyVyNlp6C2oH7+z5h26V3//XeeldakJ3RnSmpFUae0mc7MzCLJ9ugIqmZqg4/Y7SS80DNFeBRBie776PuK83Peemsf7gcKD6QFQcLxiYmbbr7pvo99mF2lBLAp9IE/lLhggQEIqVKD2Vem5D2wJ2SQvX297H9ycrJ/86tfHTp4sLKy8qabboJQMVm5udTKteF0yz6eA6wR4qkh+tOJZMKz7AnhhH/6059AWpsjw6BzV5cvpVb5hY79Wt0Qm4JHfv+nq666mlnmZhpUkIqcfCF+AkkoGJh/pLwE2dxQVEryEgq8BlF+aFNoStQoR+WVsFCUiXjDROJ5haW9w0P5eVbC2QEMVLGZDiui0cyqGlCP8hYipilKFJ4Emb3eSUztCnHltaIzgJNAsEn3Aq8TF7V4cmhwKOSzAms3bdmMHzgaSIB+dl1hKBjuGxhqaW8vyC/INlpJxGM22u6990M79h4d9/hy8tyj42OhMT/YybsIQVbAVHCH6N4MsyMWCSohRCie8MXK9ARCQoACweLCvEzKnCZJpgQQCtJywLnkU4E8PmmFYUAEX3n55aPHjlvtjjvufL8LKui0otCf9Pp++5vfdbZdWLRw7huvv/7MM88QNollHPUJjWBYUpuinf+jQ3BPSY4vbgyiZlNbg0CDmVAGtJoQ5YH+QVQGRA+XlVciTXR3dyCgWqyGTZsra8rdPb35g4NMMtplWpAGKDjC4kt5OWUvSrPAFXHYN9x4Y0dn51NPPg3V7uruxk+LS39BTv6PBvP/qw+DGESx7z909LZbbjp46OATjz++aNEitGGYaBg/qAXNQ7RAwQ1M4DEDWDAUnoLggVGqLMo94IKCZVSlw5EzDA/jacCJNHuggdS5UKCKT1YrEAx3dvWUlpbBQMg2K9oFTPYY673enu7uvCzim9J5OQWXBu4LCUVVlEh1dPWcauvpHBzj2bn11ehCbWp+Pt4IiSChiUjTIGYcakw36WEkGvT6PJiSkZNkM8MNOn0Y3SavFZ6p6e/vKcx21VSWk7oBrZ5GcCMp/jKJNGkg6morRib8HV39sXgWzuvlRfl5uRkoKgL+cVGNwneJspTxkUoYGd4YI2wkLr0gKzDUREm/jQjJ2xiDhjQ2qFnYAVpI+SLYp2Cggn30Rmlm6kOBTM0vfvHzXbt2Ij6QR6asshr5fN3qZbTk946jksWfjqfOnz/3xBOPL122HBsASKg+z9rJpP0fHuqiimpHLV851UPUnrk5OSzaFeuuID9iICAKUjqJeMLAEYiCAZaVKZg0aPwaTSidclZXweVSaHTYNworp0lx+77IB5W+MpyvfvUrGKiPHT0xMDCIvx2ju/yRkEH6fCGPP8R6gUscqoTJnIBjKLiYL+YXytrW3kqdmJys3NlzZhcWF5DjGZU2a4QyEFsidli/f9zpSHb3HwCW8HqjpAVXP/zhDyGk4SjIdKvwkNIaznX04TCiHx7CN6ykpMRmxCqQJsDiyJ6DwXC4rLQy22XBLzOtxcF6CohokxZGvYFzXX2nWjoSWsvAuA8jXm1FsTnTBRMVBT6gzD9ZTjEZU1pRpDcR1GSl5awEhaP7NgUjyc4hH72Mhv0ToyPUSXe5HKTtR2XK/UhMMjSlvzTGU/mZbojE6dZuJLPcXBdWVV0aB4dJqonB03rbu5grHgQrYKs8oJAqUSiTN5FILLKembVGZG6GGYyEMsTkI2ANatI1/qoHr6Kfyne5yHiZuubmc6SS4HwqmvBOeopLynmKpKGlpaVzZ8966YUXaACmhJuLaPsVt3V1qqXj7x4i2vMLYUKhBTQn75JNJtMj/ZAvlz4w/ag0M3VhagW5hFvih+79ABvCBQvnowbnHyfxH2ALQF4M6J3MG1RR69YaXUZ9JyE41157TV1D3Ze/9MXW1nY6L1aiiz1UR836QOdZnfe//86JcWqZhIRcvheQkFH6AuHm1q7uoTHmRF05mYxUCuyCmp44cYKg2JMnT1KmHFZEkhK44g03Xv/Zz3we+zLaLXzZ5syZ88ADv3nggQfOnTsfi/loh0sNDTM+8YlPwFFpBwo3vajIV7sPHJ1RW7e4onZspP9Mc6vb7szLznxn7/FjTV07drxdUZBfunRulOhfURVeXHi6lEDek3jvouwsi905MekdGx45ceqsa9liaoliJSEaCQlOoEqx8uFzJwAGbDEu2XgyqCT5WLze8KQvMjoemBzsPnJgB/7imRkZCxbOEjCUBxTmREwHDwoOgxnQo3Sm01peXAwpYv+GqGk26D2TXmDLnWmLFZUwLbwJwx3yOTKXwWjx+gJAthWQNJlpBtUxqD025sfzjv2m1NlADMXFXEE8hemp6KfOk0A9AI1ry/XXX//oo4/zu76+sbqqBq0YFAVRmQ3B5k0bThw/KlxFDiFA05ROOaN+yBCUf7SP6Qi3QCwXZr3dDAVCmBH8E8zk5kvvnGoAHPB4Pe+8vf2eez64ffuOurp62BTXUGwuWboE6wIpZJglSB6fzO/gYD/bXYRJZKRQKHHsmH9oSI9NESsGE1pXW4vDxp49ewGY/HwJQIPYyYtVbBTwkx844qJan5j0y/xc9tpRZp5/E16yBMV27zsaIUMh84EUpthzWV2MSx/5yEfQZWGHACCkGh9B9H7PL3/1i+GhYSLQABRwcmJiDL+k//zPn+IYQYUQWqBENFZdIJJJLCsrUyk03zmwYhcUFTdfaJ83sy4nN2dk0v/AH59Bb8PqDk14lq9YjnlseGQULza8qyVoQaJ7gVeiiuJ5Ga7rN60Sei7aOZ3Pj/GJ2EKfjv0XwiulKsnjh24OuFQ8XegJb2RxCYFlRbFSRfHPoQiEwzTc3Xxg91uxRNBgzj3X2tUwq1FSSSsoIc/g9oMMxuwI1rMpYnuayMmgiCK2/QRpVTPdNkKs9CZKT8cLCovx3+MZAvzY1uL9Q2gl6AcEM13AJnhodGdM+oJGm32se7S0KBdygJjAJZXQ87TKAumwyhakC4qn280331xXN6O/f9BKvemyCliQPCtydRrFGGlaxaYrsEsLihCgPvnup/A73oTrOb557FV1jixHTo3enhP1DMbG2pNRiAUzyvuVZvhxyUGbLOLAwAC7zddff/2ee+5RL0r6JlhdTMpaQt8sZnNWVsbmzRuk7Ll4iicw9rBmb7yxP5mKWCxI71n0A5ZZXVlVX19Hd5gZqInMGm+ELfOH9yuSC0L+uvXrtr32pizBe0E7KsunSxtQhOj1aNuZdNyXOJgcxv/888+zzVDUoaYMd2lGRuXwWLvX3+1I27e99hpSKPobEIzYBTQfGA6wWODxoEiy4oHBTwyJ6HVAVFVgo1kkttHRYaCHxNRtnb24cC9esYictDadBk/lrLw8nN46OvugkTZnRlVVGWZ/4H5gZAwUKcrNI7s3bSisTZPtsOK2Bohv33MA37nli+cnolLYRJL2ClecUt9DbYGSeJQtbYJqN+xbtu98+9Dud2jQ6SowWjJr6urYQBHDIWAoOCt/OFSwFrAkY6gBf0yCZCULo1aD15gFfaA/GLZZEBrxw45wF2Ox2R2oWwFqvD2cVMlWotqxtzEbBCJPBhND46NBvF8Fl8R/RZi2Ihnyrkv5mPpqloAdAdLEnLmzIS5dPX3YIan5CXuV/kGG4LfKAS5ws3JSFk79whX5Ag7KqHR6kzO7qNpdUjo4iZic0NqLrI7stKcvNNmPMztoAB4o1lOVtcqjNIWsi5qAbkCaRU2qHLQMqqAsYFwohEmncPTokZkzG81uJ8IR7+dBReFEaEuIPc3RY4e//JUvy9RB4vkU86wyvVPNyf1TvVXOs3+hben1Zc8JlRlIsw1EU0hUHWwFwg304NHLJTSHwDezo66pVmMy6x16/JqVaeIGlDMsDeZEbstgo+MA5oRV8gAroVhr9bg7sXjsM9ne8IWnMlxuPOb7BieeevGtkN+35ZpNpSUFXJkY8+zbv7+0vOS6K9fFo/FwLPXkK68ZtfF7tt7Gcv/mj4+uX726pBizZESBKToldQ7JIYZfXFNL68rlKyCc0aTu6NFjKxfP4UXoxAEr+oGNmM94impEEbK4Hz96fPeu/SazPTOv2GBxVVaWzppZacYRCIxQNoI0yuMcAhbCXvgmMAy64OYvWmI2ReLFRgg7WaT0A8P9c+cvVp7QwM/7cWg2W1BiEi0ukCTP42wT5zvWw8xM2+i4pyw3g5yvMrWCgeqjCqbwJmW6L56US8wblRzAkKKC/NbOgejQaE1FKX0bGRknAgNYV7agAq/TcMxXfiotCY9MG6yunOK6unkme0bbQHdppvmTt1yXjsUfeOmNjlSxy5kb9vRHJ4c1iRCE6F0UVERi1OCQWpBNHbh0SMiNvqW15fjx47NmzqyuqnK7cCiPvP32W3SA3i5btgy7lOjxZAMKd0vedvut4BVeacgVqLQpf6KaHJlXOko/GTVQxyuwE3IpKycDGqWOYgrv1Rdflp8MPsOdgQPK6NjEhg0b9+18G3/53bt3f+rTn4KJ3Xjjltdf23b23FmI+thEO24Y2P1QfxInevfd97C9hraJWVZZFeAGkY8JVUBXBYJkWVmpCgqHDh+G1FOybuaM+qqamrd27sOZZtWKhQePHOrrG6yorRkd92fkZM9uqBlDoNXomtt6hoaHr796HRWfg76Qwe4819oxd1Z9dpYL6EG00RpsrV29J06eXLdxXSShae/saagu6egbPHji1OrlCxW4FzlNiAFSTSqFLQHl3YkjJ954801nhtvldJnd2dQOmzmzBtE3hZZTobuMRQXlKYDmDyCinAPWJaoZvNKCjbbSsjLKoOZmkLuaqC7BAQ6IUceFC2arFTIE6ikClmAyKSacNishOrUVpSdPnW+oKnHbDbLtU0KEBTBFZuRG2mHqFMyX9tSfIqahGeroGNy1/9imtYvEGy2hHegfohuKIWNq5wxVkodoQWlOdsgGoz2rqKi8vryynvogENg718+/q7rM1Haczfnc1bOeGo88va85mV1jcudERrsT/gm4vfhEqLK40hnmAPRgkz8tzoxPjP/ghz8iEwjhwYsXLf7MZz6DkPnYY4+RTp/Zvvba66677lpYp1BkbBGpdP9A7ze+8Y0ZM2bcddf7tr+98/HHn/jaP35tRr14F6tEB8XBv/zLvxDye/rUmdq62i988XNI2eqKXOZIKCvM8uOvHI9lO+wbr7r9uSf/TEqlbdu2vfHGG2vWrGk5f/7nHL/4OZtpwnC4H2DB6f6Tn/zUHXfcgdSK8AlRVBZeGKOwDRXnZP7kO4ALKT1y+NCTjz/OqXnzF3z0vg/G9ZYZVflhXLbCEZ/TcCU5f62WsdExTEnwTB7FbrlsfsamtSvtTtszz73syMxGIsWx0hsKTYyNlRQU2IgPSCRdTsfQyPjTz7zsDyZOnblQUVow7vXki8gqew4FplGjJOOYJXENc2buP7D/9dded2VkkvT+fPOp/JLyhYtX5GY6xWSp4pk6kr/6BCm4LjH42mIpQE82DJJbJdDO4F+OWpJc8OlklIeYH6JNsPKDE4r5Jok7ECmZYA6ZhFoZDdju0flSPfPg4eOb16/Ee1zalXTcYlhkbmmDjiiaWeZPsFH+KWy4b2jiiee2IWQrHF5/9HhT44xapxNRBY6BipSnpaP8LztAmXqjITOvpH5ubkGZ3mCb8Ew2lme9/6p1xe2nQ/t2RO0uS06W/kLLB+2Z6+7e8LODLQdauqyVeZrARGikLR0YFcqDbkoi6MEiPnWrV69iG6zODa5RhMwXF5VCqV948RVsevd88AMEZ7S1d9555/sJhiQwAvInc4JYZNAfOnzo7be3Hz1ybP78eY8+9vjJk6f37N0/Y0aD0nmAUM1nQ8m5YQIG0Ccg6vNGef97QRwFRitLC9930zXZmQQA6b7/wx/eftttyJD33XvfL3/9qw3r1/f09H71q//04Q+PDg0NQgjRSmNUQASFdIl14aLjvLo2ChwoX3GCEZ8mXFAlh9d9932kf3AQbPy373yfjVMsHChy2zRpm2B1RRn0G3WJu6wE8FYhSSEOeh5p7+gkylpHhihSrMdj27fvyCCcwWYx5WSincl0OXNysrzhpDfkKczJ3bnnwMJFC1g98QFQDgWIFZLOXjcUOrBvL5nphwcHYvEgDL2msvrG665KJyLplJRGgfYDMepA/upzelzIkyCkNxDZf/D4viMtW2+7FgqEblFlhAIyMMOsLL93gpjgqCaGPCb2AyXcwWyFE5vGvf5lSxd1dHUeOd60eEEjAUs8rjOZ4JnALBiowK3C0BQ0lN+CqdqdO/efP3/mipWrsjLzXn1rT3tr5zrQWERzuRk0YbDkjBRM1JoNrhx7YX2a6Kq8wub2zoqijM9vWb3UOxR84/VYaYlx4TJ4ZHJo3FFZZbCZBvbt+sOPvv/E7lO/+tMLExqdkwQz4wPB4e5EcHxqYNJ+Cg8BSIwyRAmD4gvTwqQhCp08eeY34Qc/99nPOTMcsDsV/dQ7mVLIIeFmcGeo9rZtr/X1EdNoJPhCuSLurzhjEblPth6MuJByNAgk44M2Ic3TyGXOCdVpAg8dJoPLRsGgxKqVyx984Def/cznUIhB22656eY77thaWVXlJNVMZQXzpcx7GtxDWXqpzlNtamre0ZwkEn6P99ixY3hyPPrYo1DNwqKSn9//01WrlqNnY3OFqAKlh/zjjiVPsSBSuUg2JLKJEH6gyXbj8GVYv2YFFr9MKgZazezEcrOzqfDNLRgj2eCVlhb3HmtyuGy4j9VWlGG7Y+Mkyn+dloA3MI28TXgGByYCe/fu6evvBWoAaoL54VfkGY+HcfUGbkEigfZp4j2NjSr68ZMvHLh2U/P65OnWl155m2wTkXhQo8ORmv4K9tIIrVBke3R4wJVhQ97zescpymei/qYMkJuQYy2BeLSqtr67q7Ote6iqNB/d62lCQLT6ipI8PBAkXbVS2EiZCXbX4sLe3Tt8YN8eUzqCNPzLXz7sCQWC3tDTz2xD/SwWFTmYT8TvlNbislcs0ueVxo1mOHDIN/KR91/9sSvXBH/y7/FwyN5QFy4sMOnQ68Q155rC3R0n3BmGhQsrqiq/NmPG5jULf/TAE28ea0mVz3flVYRHW+I952magdMZ4kLAInxlOCO7cdg/mz5l4CDV+ZYLD/72dz/5zx9Rkh4ZdNpjiWdhaGQGUSw/mpGRUSLFaCE3L1dpVvYLoNz999//wQ/es3btFazsI394pKurffbsuSAkd17+SMhEoKIT3zOZTn0oFL1uy42V1XU//NG/v/7aq3/4058fe/yJsrISoiIIlMaQympMQyoLo4CvgBcyFAhKEzTDAoUCvj6clAf6yYwQS+s2XXPtD7/7nYbaSipxQzoFWCmpRQqwCIoyHX7bzowMYljDQZ87A8STNQeisUPkZbvz8zJFMJKXSICcKMEJz9EaKHjEEs1pbOgdHG3p6QmEicstJQPATddeCULxAqg1UCKLiJ7UaOhsb8N1h9zu0rRIWuaCohKTSYppsqWVschb/5tDhRUu0DHCHcDvxsYZd9x289ETZ8eHPMnaUhpT2hQ85mCPDeYwozxot7oo+SC7PbZGKGfTGqfDOe6PnDh9prSouK21m9ilycmxx594Rq+3YMupgtKlo0TZk8otGk2YzLbxyWESPbReaA36J5D8d+7drdWa8ONBqN61a2/zmcML5s3lpcw58wNTMrmynMs2jU5MaKKSJu+Wazb96PP3xCdGdkWDTr0pt6aC4JTo9nesiZhnYvIUZVgXLxkyaB760x/Xb7yisSrvT//5j9/+zTPffWKbubwkc1b+yKPtzJ5KfDs7O9HVKUNUiCVjToNOgoe8m74dP3HiZz+9/1vf+iZp9uQ2ZUL7evufeOIJ4pIwDELZK8ur8IZhhzmrcebRI0fYSZLzAPej7OzckZGhBx/4dXZubtuFVixVLBLUh7W6zJEQKEGzAh2qqq4BELGjDQwNndtzQGc0fvLzX1q+dv2rL74Q8HsHB/p37t5FiICAmlD8qYNJVujgxd8KMwFWSUdkkWzZLo0tP7Msp3bWPKvNMuSPVLPVFi8tMtMnA8FQz/D4wPAoyUXIYWS2DkNXQwF/Y2NdXk4GOyqWAPxBQMXWAKOSfFDo/QMeiuNKRU6K6WnIPsimpokQh7rqyuEhdouTuFZWVZazt9QrjgSCP0KJdQGfZ3CgD4INHkAxQIn84vI5cxaguAM/uElOTSHR9HCmvgBktKOQHpQrdp6my1esnDNn1oymM+cmRseF6l+CwcwjkgIcHpAlIHN8uLfpxGGcXBpmL3YWlvi9nsLcDPOiRgqFlhdmjI55Du47ODrQBoZHgv5oXDcx2kvyCLJ9knDV6c4cRWk4PmKntBg5nkx2+kEuCVxsx8d7yR8gSRhFa5My6k3r1m+gwvfXvnu/34bOqSA62pMOTDz59j5t1PuVe+5Y/ZN/33H/r8ZPHarPz7LZYmf7Rk/nZozkN2L1zXI7t+9459WXXr7hxpvt+cXP7txlqco11eXH+nuJCoWasPVgBpTosClCpcyJQAMIyP8cTBZ4SKju6tWrt9y4BUbJhJKln+ikP/zhj3hc3XzTTW0XLvBJg9iujhw5/LvfPfitb33rmmuuxaSM2ubPf/7TkcNHoF/z5s9/39atew8cVCb1co+iYGYjseiR4yecGVlU6hkY6DjX0koU3YyZM5vOtcycP7+mobG7p8834bEa0zPrqtm5YZOA3+H8iQ8uVnK84ImdVQiWxJ+SzvDgybPHz7YFcb1MafMKC+xu98CEr4vQpM6+lfMbBYkRObW4mMYdNuvYhIeE0egsit0WLGIrVq0Uq7hiW1OYibb5fDsOPa6sbMyVvQPdC+bNnpNTyBYCuYgE+8SKN86c0dG3g9AEvMZHhwY/8P6bLSZ9Ev8plT4IzAgKkWQBvqvIm7JFJB5KGAciGdemcO9d8OIZTquApSLiJT9pLWlkJ5ZKZLn1c+fWhgNeuVM8dTiARWknkw739mY6nb6J4QundlvTMYvV6Blssmc5tUY7bD3P7aI//kCQWlRz5y3o7uoaHu4M+obHh9w4oD7z1NPUpaZnYr8QwSDlj+q23HzH3MaZk55JZoYNPDtI9sO/e+DXeO0Q+3Pb1ls++pGPPPvss6lITEdiuAxtuCDH57dPTg4/uKvpSHPP57duydu0MTPiv3DutHb2/Oja/MPPv2SenKBsVMpm+vQnP336XPve9uEnn92VqC13FTvjRk0qEEFlhfmXJIjf//53cHNhW6gMU6JbBO8E9eSYnis82P/0x0evWLcOFY7wZhQvokcwnz179s4779x6++1kG/jwPfeA1ajfgQECfJlbhlhfXzM6MszuA983VNezZjbu3rePC7zhMueETB9idyAYYy6Gh0eh39dcdSWSTywcrCkrivn9Zy/06ggPTyfv+cA9pJHBZREVdMAfPNva0dJFjr6AJpyub2yYXVdF0nVgRWswLV575ZMvvrlj9/7Pf/Ku2qp80ur+w9e+Xzer9sarN5GDVicEksijVG52xsDZFvR7nb0Dhw4dWr5k4Zrly/fvOzC3cQYVuWCkREvs2rXDFwyXV9a2tLVTt9CRmXn6QndTW+/40ODmNcvgkqwy+SeuWLX8bEsb2fJnlOexxwtHnezyBLcUTQtYEQhHSsvK7/nQfW+88ToyFTsapFMxC6YVoz/dEcSUA6DhUL9Pf6oYKC3KAb7xRbwuQUSH3WIxpCOTKHcJP333QbaFvd1dGGDbh7rctlTfhY6hcLhu1vzw5Ig1vxa4RH+L0ooUvuTOyC8uXLXx5lPH9nacP9rTdT4vv5BqHz7PCBAo2iLFBZOaqXk5uQvmzcFlF55MoA+SNptk9GQEby1esri8skyEDDoYj1oD/gZblIDItsxSn9MVnxg97vV+8sGnPrxu0We2Xl25bGk0QVFBe+UsUVRWlZbfftctTe3DTxxr2esJmBZVx/FzlfSPxrFACNsOYyWqe2xsgtTmuBYrk8CHTALoo86WOkX0FVld8s02nV25agWpKOGHs2fPRncA1pHmi/i1P/7hD8uWLr1q85Vejw+UszuRLETcpaQC1TVy83MhKMwk2+nysjKcYWnz8kdCxAkc/8mYhMjOZOGeMrO+FoZGWErAH+rrGczKz3bXFEq+IjJ+gT8R7dkLXXsPnyThUkyjGxz3OuzOnIxMh1nSn2A/nxzzZDnNt16/fn59pSEd9/r8992x5XTTuQSJZOwm9kksoBjakvHikiKqSoyMTrizcsYmPc++8FJVRSlBEk4nPHnYHwysXLmsq7uHEoIWC4Vm42aTY2xyLBL03ve+20h9pMabASSZTtuaZQsQOvEuZfHQc6IzoMTwFDppte7MnBKNKa+gtLq2FuX4scOH+/r7nDhVG9ji0h/hzNN4CDyxs+Jzev+jgh1nLoE/9StOIfjZkPwKoVaVZqfwkMI1CKUgm9OZGRxJF5cYHY4MAcngBMKzcGHFPEj2CrvRZA/qXBmuq7bcFFuz6B0yYff2Z2Xj15YK+saga3IneSSI9kiiSY2CFRRvJJUBDhJ0GpdxhokQPjo2yjhEZkaCJG2Uy9Sg17DX6HRXh0pq4vYRb3D8/n2nD7b3fm3rNUvrSkjvWVqa/9Uvfzaa0D34/O5fvLnPU5BjbCjItWpn5GWZJuHLxmSElKHSAaizw85efWR6EkA/5fv0nCgihdAhLX587P3UO5nDGTPq165du2PHDlQyTzz5tNfnfeHFl3D4xuOGbs+dOxeJBM+7goKib/3rv6GtkRLfRGTqdUWFRYoV9D2AhEwWHlg52Wy1sHqx9Km4qD1ILE9GEO3WG69kEYBRbG0CNmzGQDSDkZhXg8XOPk2SZ05M9A4MVJWgcwOF4/i4bFy9kGyckhmXNdKlF8+qy0LrR0wT66khdy1bSbHg4iEwsPdwd2fHlhuvQxQJTHj7B/rItpKfm+uwsF23Z2a4slyNZOY0W20ej//PT7yATd+SZ3dbtST5Em0NTaHXpFm6J/0j1RiLqyM0npAZ9K/Y6dH9oqQRz7dEKsNt27xpXX1t9f33/2JkuHvXrrdv2HI9udWUbSJgJEDMjuXw4cN8oksQmFYOZglY5+CXCl5yt8INeT2GfqLluMTQRJMgt+gys7IioaDNnWFxZFr1FsJ99aTZD3piIY/BlqGKaqpaqCgny5WRRbLsrJL5MxvnHDy475VXX6LkMHQwTCQKDp+SDtgMmZQeiAem7NMw1OAKC3KC2uR3wfiBF7VoIMHFaGjQO5H0RiqYKo3mfGaFvzBXG7DFfK4DgeBHf/30h1cv+OBVizN06bMDY//x6Btv9U+mast09vRiR8ZXNq1OeY4f3X/Go6vtQdIRPGcq5lMn8MHfPjh//vxpvxl1HqRLyrTwqZyhszo8jeUsPYPn67Qf//jHNmzY8OSTT6Eqg8CBc3v37iNcbsnSpVWVlYRgMPEAzLLly7DpskzQNcFSrRaHRz4vc04okgCEHxMc2zCGzTwKhoi+HmUD+2xKGgjmQZc0ZBMVRRhyXGNV0YyqEig/Gg1EMoKgvZOTQX/A6KbaVrogl0I/yq0sguI7xmM1VZW4DIojpegw5Ekwh9zTsxrqOrv6jhw4jNLBYbYUFORXV5aLb5fbRT/SgsVpG9mm04kMh/njH7qd4Hc8FfFxw3ERUwENSVPCzQB8goDZROABG8Qv7Gxb56yGWsL7WFah5qI0B1jSsUioo6Od+4l63L79HdSP1117JUnh6SytgEO/f+ihV7Ztw1USb4QPfehDMBN5ybuHIB2/ZIrQelIamvzfGpIZyx3Sj4tHdk5uW+u4MzMTgRxhcswXKyp0y1YyNKm38kWc4NhjMxGgutOiNea66H8oGFu+fBnVsLa99kZ7x4XB/gFkeErWRMnohsulErMC3yVHG+w+RoFrVg4CEyN7cvrRx/48PDTKEsQpYpEVj7m8pVFt/ng3cVdnCioCrmyjzZn0jA9H3f9xsO1I/9DsopxXjpzvoO58bVZcG5ups31309Lc1PazYy9ds3F+8z7bGa8PnQ8u40ihTz/zFEli/ko6YKwgCQdvV8cN1hEqQWZ76IIa38Sl7OysgQEbmRFhcEAM40XXhO/5dddeTeeBQIE7EY6weDAfsAJ835KeSc/ZM03cfJkjIaAkqkyiUWQOVRBiPvDZNfYNDlEGORBOnDh1Zumi+SbixG02g0QFpAg2kNhZngFB9TqbwZDtwJdftB00yPShjRSqKO2BjXyCcATUyo4RoxmknBSBgCMPN8yorSrHuOfBhS2DvbyNUAlCgeKKlMgHSyachVA4iLzNaW3p7jty7ERDdQ1aA7yoKFVPNxQGBjcj7kFCc3p6B5raOtr7R2fNmcMr2CSxkNwTCPjRVZKXqbWlTa81ZWYV4Uq6e/+R4tKKxQsaWH2oDqm+Tpw8jYcByo933tl+ww03udwu4JwuqAcyMAwNNo/GYGgQ7uUvys8CLy8qZrhrChOdLjdiqslowW2TSIKUwfL4KzvqaytWZhZrHCGz2UYiReaIvjFMSYphtJgJoSYsI04xj9hV11xPtbKWtq5J72RJbkZPT49BD8cjXhgjQToWRXvM/BtBY2UGNFhfd+3eDTiLVE5Yu94aKYzHfOFyr8nh6anShofyq8Zdebq8vARFrZzO7f7Y9tN9mqyMuDGiSUTs0einr2isNB/oHdpWVEE5+zhpBMiow8CQSdgTgkj4vspETh3KCis0G8SDv0G26AnB8l/+yhdzc7PZtcpcKAfU/NChw4QHsHMBzaTDnDl89O6770KJfhF/ZRaBGwAJjTgvAn5IxE4blzsSKtMkcHDJwS9cQ/2R2K7Dp/Rm19lzncO+CPunWTXlZUX5wBagD1cBvjFdD4+O5WRlwEWVqRTBiYkk2EzlkoqNjlNo8gjksz30p6fPt/WSP2Lt0oWRZPTgqTPkIsmwWTMznBXlJQh1EtUOFCnoy0rRGmvOP8onxeORba+/M+YLkc+7reX82tXLcLORjRxLqtNOBiLIMza2jlg/sNFJkmJdW1sHFEGgRkCBMSJqptD7+wK4mqZz8vOvmD3v9R27TjZdqJ9R6cQGoNMNDg2T7AV6wpZSTO0eP0Y/leHyHloiJVT/yEBlSSHFEn7920egVl//6mdpWUBGDrXL8g3S5nA6wSirI6t7sP/Pz7xksmWMjJ2pLKuucpVjYaEnqFXwCGUIKKLIB8UrrA4S5xiyc1xIDVa7YcGCWcSXucx6sHdibLS56WRpRQX8JC6pdLAZScpJXo1zPDUGyUUyNjpMH1Ihn9mS5ykKhmuz472xoiFfwWQsMxrtL4p6Ciom9RnxcJjQQiZKl/SAFdnJ8Devqd4yozcRPEN5Ars1c8I74jbUxIJ+BgJPkzocbifyMOHFMjY5ZLxihtWlicFdtWoVgfBoX1avWUFQm5LmQ5VOhe+BhOwSsRuJmR5HJ50+EZFaizxO55WJ5UPuF5MkmWlb21577TVCNxoaZ1EY4/JHQqYS5iazqhwIdVC0zq6h0y0d3YT6hdhZ6Vu6Bt0WE5u6htoKTAhgGN6Tk6Si6B/GUO5y2hfMaSRhERGYCmUEqGTeuI0AAEFVCV2FahsmgpGRQJAqQTCqrILCPz7xUnFB8cZVS/cdOFBRtlXZ0wmTldg2GBhAr6aY0eq6u3vZfEZThklvpLTEccXSOWzkSAwjNwPuZtOBnQczXI41yxeNEtA4Nkaa3dwMZ8jnEYlYdm5QDHJjmhmphyqnobDWZGFrS5UFs91GMuKOzt7q0mIUdBabY97CRdtefgHfHZywiYIAy5WJEUQGUITYsMOMpy60dVncWbdt2WInJx1sfeou9d6pT5yKRoeHXe58jbGjqqbS6w3OmdloQaVEVFUUXzYLQwB9RR4j3l+qXEiANb5aCBdmg4akVbGAD7eHEc8ofnD+ydHO7l6DYVNenhQCIfgL/gNcC4gPDvzhkUe++73vdXS1PfroU3g/pMk5mukK2MxxYyqaGy5omTQ19RUFJ7IjE8OF9R1WZyrYmwqPapOxGnvgazdV3TE/kva1jCZSWTk1UV0fHgcmLQrWEMPiLXhj4yy4bt0VJPiang1lQlIfvvfe226/HTWmBIzoZe/NTlAheTJj3MzMw/s2btpAvuCKyorlq1bYLNYD+w+tXLkSNsiKqLdxJ8obWli0aMkDv/ntsePH7XbXtdffwJxPvVJ98WX5yRRAreFkTJcAvohlqC8RN1MkNXM63fFketLr1xhdk4HwydNn66rKnBQnIvOE2RYKh7kbBenDjz+7buWSxvq6ZDwK01OYgmAk+55wLHlhhMWOmm1uTyTVMKMOHc32XXv1GQW+kKbCZFgwf1YyFurs7S0vyuXNRouVLR8WZNy7qBlIbm0QCPpIaRSyhPccOrl8ySJywMfCIRAe4Jc9Icnq8fxAUGRHmuWyzZqBupM0J5iGz586jP+a3KgcwMfIyLDPO56RXYaOHjXJFcvm1ZQXDY4MsYMBYfHbqKquW7R0JVX7iktKKTiKK9/0uoOABPKRBMBhM69Yunj+ihVO0CAZwYkOV4Lp26a/EFHR39vvdjiKC4tXL1k4MDwyo7G+oLhaZ7LRYViBBfbLjCuoDUtESUisLmSLzK7jowM+z4SfGm5jw57xUb9nAjd0szNjbHjY4cxisHD1ICUJwqTWR2tKnJRxZHh0354DvD0dC+pIVxJLaU06g9lFJZmsFaZofKR3/37rYFs0v9O48MYoEkPUtyjL/oN7Zq2unEgGcElLugz5XZP6zp5+nItGTGFSybGUtH/LLbf6A96TJ0/wXR0dXwCaq6+++o6td4BIsD65D1d+lpDdndg35QRsEPpCEqMlixfN+vn9ZDAx26wIrmvXrCXOSwE2QTHhfi2t3/3u90iG8PnPf+H48ROsPVtKVE1cvfyRkFllUtSZ5ZPJI/Y0SPhQghS4ou90OcwTEyN+39hgzJKX4S6vNjjBilSaXDTLFy3EHflIUwuKkHBC9/SzL69evristIAEhLLZpLlk6vEXtj364mtf/9LHek62HjjWZMS1LDv7Ux//6KMvv42rE+Fwzeea8b5/ZdvrN225NpJI//LhB8Mp/YQ3YNbrCE36+N036lLxkqL8srLiLur7NlQSGev3BQxiwNLoUIairDAYsZ8IrukME2MTGCTQ65J+wojdUrAUgwiaWqKNqD5owZOOXCxWkxF9LEWFyoqvKi7IyHRb0QlkZuSAS72DwytWrnLY3A31NSa9OEkKEivAxycylbIvkmAfN07lGkw3MZ3JbrU5p+dw+gu7KUlebDAUV9VkFxWjnkF49AeTDiPcj6IUhDsyCnbX0BFR9+BQq9NE8WsdGSLvxm7f5ATFCsGzTIcrJzNDa8zpHfGSFtWZ44WFIspWVlaTLIvlg9VQeOzU6VPt7Z28PR4L68OatCdiMSeWu2vKbMiY7fYtyXNm24Udo+ZUT1YyNBpK20LxBtfA8jK7Nu5Nh/HCKRgY1761u/PpPx6pql3hbyC8MArfZ5r/8Ic/DAz0dXZ13H7bVnV0cLzZs2fdfvvtdINACk6CbwpPUygwoxE8lIMJxIX9wV/+qo+4Cr3+1jvvmLdgAdEzsjAGvNtkj4iATUgaQgoCxeEjR0jGZ7VbPZ7JlpYWeOPlj4RoufNzc1CyMYUCC5qkzW4sK82rrqxAT4DuX6nGpydwjqBbj8979lxzfnZWWVEJlmKcyQqyHW47+Tn747NmbNp4BakQ2/rHX3x9+7jHv2rpAirXv7znUE5xSTieBsEgmQsXzV61Yt7g4MDY8PjY6FCvMdXc2rFw/hzcr5qam8NJQyShyy3I7+oZWDRnFtnK0N0StyABe6jdKkory8uoTkOkEOggzhRaPfVkAh5J34JBEhHt0Seeu+2O2wvzcxTvNJFXgQ9ezDDRRCLgDgwOJdMGd6b1fXff2k8g4+hEYS4FvBGZuJHoJN2SxXNJ4IeY53SIVlaUTEKahK7TfxLtMHADRTD0VOr1UEAK0auouFLYqBwqCCpfFbjE9OqH46F+EMGW8usGtyEJ7Ik6WoFTZTstugpRCsKpme5YcqC7t6vlAow9Ly/TWVEEMQnFU0arq6iysKiUBBE1xNGSghEEoIogEwKUo7Hp6e1Fc8K7SSygCyY0Y5Gk3ZeVbV3gqI36LOPak8ULDUMpfd/+Tk3bCX1WhtYbjJsmeYvgS1oXCWh27Tox1GOOBIj7NHX1D6M7gxf5fP4333wLd1m84emhOjbo1q233ULKBWgHb+ekMD05pgQqvhF6xkloBDdQ+3NkcAh2TWA1nVRuZsqn5oB9KZZ6HgFp0cSSOdbpdBw8eGDFipU8e/kjIbi3ftOGnv5BDDqpGB8GB1mTlOzz+BbjLE0aFXZrLqu+ONOmM1UhhBAfjcJf4CUdDSZiZk1i2YLGWTWVWZmu/vGxr377B/6YAefAd3bvR/LE9bOmpHB8ZPLQkRNU2MpzZ+KbY6msaG1rnddY4Q9Gra7strauVYuXOdwZPYMjX/ro+9u7OwzhsTVLGrBNN7ecR/oFhYi1bz7fxlaemtgO3KKdDgQd9KWDY6Nv79xdWFJhttm7+vrTOgonZKM6IicNYK7sGRVqDq7oSV0zTIGuVas3XnPVRrZgOe6a5pY2N24vZkF0IAlUYNNbVpQHOmGzEsS8iFeIvrFEOpTUZKE0DpBzgEdAXQw8mG/MZJ3Il5v/+iDzyrkzvWUV5WTIQuqUSk9AnIpvatkZhOmLB/p6dgaUaLHYHS43RoaI1uwMJik0mFlfWTVz5ryKqtqc3FxAefo1KlZgJGSXiPyuhiZg1NWEI6awLhqIxCd8rS0n59XVuN3LJ/0n7YVhbY5Ti9jLNiNBCW7omfwfC6U6znWOtA+Eo5WOZGoyFBgLxDAaMHqwiIPhz5+/AG2T+mrCAkVfasIg5EMriyHxwIGD1159tdlqAS7Y1TMWbBKUiyspKSauQhBP+CK4ir1LCqfxg3ngNDMM+pFri0cI0Vi3bh16bFJFPvzQI7BBXn2ZI6HQHr1uIhT91o9/W1MzY8HsOmIqqZ2ALnzT5nVtrRfCgdCGjStJRoARDxEuEoRcpfRGjc0iHID0mty5cNaMxVjo4xDguGd8sry4IJDAuKzHxyoV8V91xYo3dxwIUw0sGpxRU48K1ObO3nvkGDVY/vPfv/eP//rDH//ygQ+97+aa0sLTnU2dA0OdQ4Md7RfWrlq1dPECqmGfaev5wW8es2SiEW2e1zirt6/ffqQZDzWi+8HDcDBw1fpV0TSgEKKY5t6D+5ctXwznRJjGvo2cegljYtHJteHdsG7dVVdtItchO2GwkwD73p7+qqpKBUTS0G5ZePxi8Tqg3I2wYgYqUmM8ofGHYj0DoyVFeXinQvbRSKGkDIdCRjNPCwv6rwcJr7A44+kqbrd0SzoG8AvC07BYQnEFFE2n4ssKfqLzBU5NNkdOYUZ2HnkJ6mY0VtbU5JGpWj+FAP/1Lbwd2zcpubZv3/H662+Idpp0JTCaeCpfZ7tt4wYqx3f5+sy6gspcTaQkdH5iUmvB7wLUwlnsgoayGD2G6Li2RJ9bumDF2TffiUKPvRH4NUhIMkj2fouXLMD9Xem/vB9fFlgWfP74sRPf+fZ3ueGNN96cN3d+XW0Vg4nGY/f/9OfPPf8C8cf//M/fuO7aaxgja0ap82PHj73+1ptocXAlJR5V4cKSNHnRooUrli8DmYmR/vjHPkpkOXtm1oJ3XeZIKNOJssFgyC/MzM3PIOtuJB7beeBoZnbxw4+/ZtTFykuLv/QvP8MYuHntsqIs16RntKAwL8NJ2szAQF9feVWp1xc6dfLkdVetdzvwIwvUlpf+6Btfo9jZK2/u2L79nYrqms6B8db2dsS2JQsWV1ZWtbU1nTx77nxrm06jP3r4MFJHZU1dJKUd8ge7Bsd27jqyetlCvcnx7HMvLmqox7/0bHvPzqNNJncWLpGp853k/R4aGKXgIbUibHbXxOhgXXXFooay7bsPk6byg3deu2Te/FjUj5iDH8ac2bNfevFFuIsMU0A/Xldb3djQCIPFZI92HRApLsn1eoKeyUmX00bF2QvtbYjWixcvragog2cibqE+oKg9epBYnNwQgTmNtdT/g0kCG8iu2ERhbzqcFi5qLJR3vfthttgKCotGR0ZwGwCC4XmK6Al+QM1Q5yg8UazUktcYPkjFbE7hQrj1Ax8mfIxsdLgxKM3xgilREHby7gsU7SUxCiSVJBYBlsglpbGkJYwFInXq9JkZsSxbYeFETBNLZ2usiZysHKtHF6EMajyJLDo0eN4SmHRoa0yjptVLrnri3IAmxoZS4lxEChD3DOO9996bk5t54MD+6WFC3dB2MSnohLCdnjhxEtLBMBgjjO6Jx5989LEn4Hvs1f/wxz+tu2Ldxo0bZ82evXPXnieeEs81yB+5ZL797X9jx0ebHEidlAHetWvXc889t2HD+quvvpbkF+RtaD5//vJHQmAhOzNz7YrlKA02rZj39p44C3nkzCmr2XndhuX9Qz09VAmjOt+Zc1vWLKurqWtsqA3FE79/5GlDOtUwb97Dz77Z2tK8dv0VFLNW/Bxj6XDcodXeefUV121YNRmI/vmxJ0tI9FqQu2vP3lDIX1FWfOzIicqaqtqlS06cOZ+VkZXj8jSdPLZ00cI1C+desWiu1awrLy3CVohSiHqYS+Y0zivLaSANZV3N/mOnDx5vApFWL1mTmekeGBo7e7bZbdbcfM2VxLyeOdu8aP6CZIw8a8CHhHTg8ajIk0JoVJYIp4GFAs5wRRgma49tAeEW6IF+Dw6PnDlzjnD+d3bs+9znPnvq5Om5c2bV1VUAZqA0HI+c9lZdBjhJ2Wr2iWyh0cxTfSGewK/oEqZ7KYpoNOQz37N7F5WxFWETJBQhGZAVAU1Ml+LvArDCDp0OR21dfW1dHSZvmMzFZhgE/6Zf8Ncv4tVkQgC9Gc7FbqQw5rvGTBmuyJnezqV5M/NdJB6oOHlh6Igv6h/3Gx3lsXBUwsPIQRA3UEwpGnRbgsmJiOaVF54pMdnHddZ03GPVGBNgLsyZjbc/8LvfPTR/3mKyDdExMk3Sw927dz///As33nhDQ0MD+Sx++9vffeyj97pc7ieeeFIQWP6lusmE0d218aorn3/hpWOnTsOlLRY7hOb4iZOU+qiqqkA+51mKMRFpoQqtTz/93BVXrP/85z8PEuIncPkjIctGoYbXtx8amfD5Iyli2268YUvZqdNAxakzR7/0sQ9duS70jR/dD93ykLu+t7dzfPLAqXN79xy/efOGH//yj/ubzq1fPhd9ScDrIRM0+WqAa/4nPoFKEdYM21c+9aFAKIK0mGEzIpDZreZZ9VXHzjTjJTM0MlaUn71odi1WjSvXrc5xOZHZEgQZwRcw76fTko07L+fBH30LHTir7jCmBnq7kxFvcLI/6h0aGZ5cNn9mwDPW2T2wbPGC7o4LcBKtlLpOmo1oO5UoOAWQgQUkLxTmEG3+YIUnkw3GfRH98GUV1wLBx5raWryHCEpsbWkfHffZHZkk8EVq5RPXfkIiamuqaIl2BCNkpAiUylfR3Pw1bqgoRLNWq6Ompm7P7j0gIeUWuA9HSWynoCOu2NyACa6hsbGmugZProv7Pfoz3SZPSONTBOVdbFTfIGZekg4jHsoYpg6885LaEYstPuzTmfe1926sWhzoHx4L6DoGE9aUy6Sx4+uOapdM5YiJwWhWbvbGU8Mv79/9R4dnwlBaNormGt9AanoihYsChWyxgeZz57GIqG+YGJ945tmnTUbz6dNNX/7yF3FyoITJn/7056133AZlJCgH5YLaH9Q5aIXPnj//n/f/HE9DFKHIAmwNsbKoOTKwTzz11LOnTjch4CrrAP8MnWlqOnToiJF8rdOmyYtju9z+Ako4EGHqDQUmc3LzXnlz16pFs4eJiL9w7p//+UvPv/RqIhJa1lj2y29/9aHHXhgcGtu1e1f9zJm9Q0Mrls4tLMzwBbyLF8w6f76lr28g1+1AtweASkpn0Wkak+QvS5EqX4u3DTGpFetXA+5kKsPOfPN1mykEgW7TgVIERQSiIowm5APuRLvGsoAYKDHF0TQl8YGEB5r0y2c3zJtRixhIEBBrCTCTuCIWiyBO63WZV29YM9jbwUaQ3astzzEx7mUJ4TisGbRWvCtFNSNx9AiQYiLAoqEULQWI2XvAGTnVUFejqdcsXbRAmATx+CZtLC7aPDKsAQ+U1EOCRZMCrPA498seSToNDgqe/NdDPU+ZlKqaIbKYoz5RQRPFJplUGhoa8WDG0+W/qAD/m/aY1v/aPmfA7cLCPPJNKn0S8kBaymTYlwiXZugnEwnf2egFQ3FzQ3H16fNDqYReG3Gh/GT6wWs4epYjZbIt8QzZ2voHtOmgsbi0yVU8GkyZ4jGD1SG36XRvbX+HlDBYkpQ0XNILrAtPPfX8ffd9aM3atQ///hF8XBoa6pcvX1peWf7sc88zsSIUK3SD2SML+etvvEXJHWQTTsoWmxUgOtkkRSn4AgXhvOC6bK1ldY4dO3Hw4KG77/4AE3GZc0JmGL5Rmp/z0fff0tTc5p5XO6Nhxs9+/sDNt92EUXX9mlVd7T2+sTF3TvaMarZ//jkzqm/esslm1pM7tH9w5MSRQ+tWrjxpMb7y2lufvOd2fyDe3jfc2tHjDwWlDlhJodVgUBZbnMlAdYPVTmhudXkZUXgOIEoPa4IgikyocBfyQyvrABtkCcEyqrLg15okXMhI3B5PuDAYTFXSFOdxdi5GC4aENCb6tSuWok4kpZI/lDh0+GR1NamKM1htIAbApJAlWxdQkSUnwQT0QgFzDB+ielGAho2kBs9M0B6NDmpdKIkIkFqym/FqZM801lMoOBQAqzoHcjuP0ryww/8BCQVgZftowLGL7BWjo6Mq2PH90voc6m3/e5+IdohtkBYmDyFCr6EAlF2bnAxMDBrt5bnWkYTHe454S1uJNbcs3dVpiFMPEj6eNGgTNrMpr8waHS7a+cbrg8bgoDWrxeCcTOvtZNbCOkXtTlFgGkin3d3TTUQSPi5qJ6kpT0InkqzedNMNL7/8MqPHMe2fv/HPxHbg5sp8cqCM4WbigJmcA/sOXGTyLKOsr9vpysrMZjY4brxpy7nm5pOyscSQzHLo8QpksVSZ/DJHQoF5cngYLJNjY3MbautrqhHZN18xf82SRtQGxbl5GQ4CZMWU1VBdRvKPsbExyknD7ELe8YOHDn3o7q2o2CpLishGw4yPTEzuPHSsY2AiEov1j47fddM1drdDrNtaSa2NnuPQmZaKsrKGhtmTE+NBfMqU4lhCLxXcU0g4kbZsmRDFBK4NGlP/0Bhqa7KrkbkXsUj0flNqCXmGA/rJN3EawS9Rb+4f97S1dWYRs2S1KeoTFWbkRv6pcIAyQc7qwcDkJVsvWqEQfNjqsCk+K8mhgWH0RmhYCf1w26W4O55xwDm94GlRmQqTIJJIcBCYkzb/h4P+MZxy5fgfbvnfP03LMCjkeBRLr297zai34i1ksdLBEe+kPZJTmmW2ers7xtzZAadbj3XXr5PoIYkNxaPBbnGVnXjmpcHh+Ehu1Z5ezaRO5/BHrHESJppxo8UhiIQgq1evxITw+BOP3XzzrZRmo6/MW21tLVYKKp+ib0JWx5wY8PtReU1OjtMlbgC7kBQgN0jd+Eioqk4u8Ti4TbAvK8sNnCkpLv7+975DIrZH//wE9bOYLpJ5k5uPV+zbt+8yR0IBTClgpJndUE1YLzSVwPnbbr4ZuRIQS+pTsCzMtcQclBRkQJlmVOTJ+onHTOreO29FjIRBIIxV3vu+aDKFphTJr7ggBzclDBKtLeez587EoZB34PVP8qxhP76Q3hdf2jY8NJifk7Vw/mwghZUCjQxaE38IgSeSQrZbym6tpbWjtb2HunylJXn1NaUQA3GIUnFPgBZUBR9EGuQDu4VsFnv78nMzqbkblLrCedwka658MlIFAkSTqWIjUKJenGJjWh22Sl9IMiCGw3HShdqdJOxInG/rXjCzTmtKIgNLyTNmTPypFC/qOIlkYmj4/p+RUIU85V3/9z+EeIk0rWtvu9DVSTg/wZjkW8QzzhRPT8a99qAp15UYSp7Yq6muiznyolyMsG3AuzCpxazn9U+OjQy5G3bjqoYIEIk5cMrV409Hu8RAYjzR5ORmI4uSb2q698gINTXVZeVl5Df5xCc/tnTJYlx262fUg13sEZhPdZKZYfgniTw4mCJlHrgiFqyrrtxMt1lPzkOpUY+9/673kQLv3//9R9ywbu3aisoqBHUI5WWOhMwpSxgmNECKWhE0Ym3p7e/pHQmEwrisoQg1G83d3Z3DwwMNNZV1FcUaFAkiLJjxFCT5LpZlwWGpzqLHkl1dXopiE0gHIjDoJ6mASz0m8jLhupTWopOw26yxSLT1fOuVmzbMbqxNU+wrLjUD2SZ4I9Ht+w8Ojo6tXr68GNOEldjfWHV1md5iPnayibrKJK5srKuBF0mKDd5nNKIIRapESYKeAxMc3NJlp3BSYUpnauvqHRkdsWVkK3sMwVpWXlgWXCxFskMTBMUAIBiNQX+Q7RlXgQ/QmJrdTc09mRmO0qJsQAlpORyNV5WS4jY6OU5N3DxpQhoTcIIYMX1Sj0Wx/k0D6N/+izhPaw39fb2QCAIyLMwFHuoGE9ons24yGssMB20ZsZCh/YS2oDzhLEqGCDPBtTdpSo8OtGgPDJU+2RGbSFpdqbgrZmCPiMEEqZA9AtVSGScDbu9oR57nizo6Zp6qsjt3bqfe9Sc+8VEFlyQIEIZM9ez+vn4miIM0mZs3byZ6EAQEnRTpHcVY9Nprrly8ZCElSTweDyEyaIPBXHYTV1yx5q233xwfnQCfPV4vIi4oepkjoVAsrYaiTOJzazQ+9MRzwxMhk9V+vqXdlenetueI1WwPR8PzZtaMHzmO13JZQT6OioeOHKuvbyC3CvQX0VF2S1BNWdU03jUAJ5BuxqmD6tI6K5G8gIjRYO5q79i1ffvy5SvZhmVluyPRCBWORM8IQGs0eIqdO9dCcMNjTz6zafWSDeSxB3HJ/1NX3XTubCQeLaussDmc0WQc08XwmO/I6XNrly10i7hobGltRa0zt6EGX7YxX/y3jz1mdWWOjQxizYdHSKUmOZShatI4OoJ14CM0G44NRaEnVqstEk96/Cj/AuQ1RYG6bdvLGzdsys/OCwbDAyOjUXyu7TlE1xqF5Bj8gRCWar3eHAwFKDUjIirt//0OAkSgZRAXZswiXhUWRTWDrgABAABJREFULQtgsEGf0oa0Me1Np+3+oKMwGnH3dwTckcl0vSedMoNifu2T2zV/Pm0Y0zkcibQjQXIZyb4juk1NNKUlrxuZPkTcXrF8xac+9SlERHWULGlT05nHn3gcqZJ82yCfEhOOKsu8ePHiw4ePsj1h3fEfyMvPtTuslVUVp0+dIvkx8ufCBQuw1P/xj396553tpGXACWHJksWf/NTHMrNRNTi//e1/RQEGhvf2IsGSb+byN9bLDIeCfpzWj5462zM0WlZaFglG1y5dHIuHh8aGo0ntzJmzr1+9MN9F/E5izOvt7KNqbeSdd3bMnzOjorycuG5ZI5FJQT9hjAq46+IEMJFPMZF+9tW3m6jNoNXec+dNn/n4h0fGxgmcwCOcQB2r2ZghxVVwwTEvXDjP4/e1d3Wvve2GxorCYGCchKT4tEQ83pkzajOzyXThkhBeLIAGc2f34Ivb3plRPwM75Gtv72CdVi1flIQBm0zPPv0s6WVQClSWFUgWWng1PYJMiOAKqUjb7DbFDyQtcjUdJ40S+0wyykTiAsSa5Nmmk9tee4n4AYqgXn3lOkTQTLd73Offc/zMkpk1lSWZ4msjaX8TJkk0bwiTPlgqOv0/7QlVwP3/2qc4QDNCnLkRQQ0mwv3NFOrQGCgJqEN7S7oLnTmQjjsnfAnK2Bm9wzpNqdYBhdQf6TJ2tsbGiTkJxi2opHT6GO6tErZNXhtZWIMx6vONtLa0rF+/AbcYvGTUUSCLCGcTSVgcRBUWJ1IGYupNN90ksWG9PbfdftuVV20GP7HBfPITH/+P//gPwm5Wr1p13bVbHnzwd3v37OVZ5Crm8uWXX7U5bF/68hfQ9eIgoWy601SbWbJ0BS+6zDkhOAMWAqjkmV0wq5ENtM1upRZv0+lzV91wDVWNt+871NU/NNDV6a4qJ63Ta+/sn/SF5hDLUFxUW1MJ/Ik1QWECmBZU311ZJBZPr5kIJ77+/f8cCaQm/YHZtaUg+R3XbHLZLQCyyohACnAAkNl/svn0+Qvnm868/87bXn39nZPFRdxfWVqsJd+NQVtNvC8qbbJI0FfBKPTjJpcrZ9e+A+haTVZ3ZXFOdVkhTlgE8l535eb9B4/Om9mAcRk7oKh4eEhepEhS0ldEZXZ9CdTimDbgX8mErnVgTKc1tZ4+sXfPLizXg/3Dd9/zqfPNZ5GyXJn2aDhmtmefbO3t7p9AM0zZXJeDeJwkHl/4qZgoM0wltr/fIWK2LEG6pLSEgNrhkRYN8qTBBrmSwAw82BLk+w2kdL5g1Bwfz6Bcp4Q44R2vT59kG6A1ayd82hR+5XqyhuilorBJ4qloEvZqsKDHAh/WrV+PCHrpKOFp+OhQFAhsZHKhaCJWhEnHEaHqC7hXVl4q+m0t9uHkooWLqCLLJYKVfvzjn+zZvQdLEq2JCEVwqtlE+BJZ/BxO1GnoY3m5tqene8HCRTR7mSOhOqexGBwsdezY6QSBoLkFg6PjfZOTbx84uH7F4k3LF5MrmgoEI57QgaNHC4tLj516a9nCuXMa6qg7iN8p8g96cXBZGEGamHQBeDK643T84B8eO907vnDB4qGTJxD0xv0hvMF1yZAkPGS3wbJpUmarfdexs//2s98ZzPbG2so/PPkSOePfPnx8y+YrcvIKsk36cIKqgqhBLOMTXlAmKwN9d9qd6SA0CR/OazavHh2l2PdYhhUP5nTf0OiFvu6icslt1tF6oaowD8UsiTQgCoATaIOPIiHJQJcaZ6x4zWh8obDFbN25Y8dLTz+GCpRLOO/PnVcfCoznZmfhr27GShJNrphdc+xsty/izs52k9QtnUA7g5mLQCRF4fN344QqBuKthIrSsm7dhkNHezRmO3GZsH623NQ0JTdbLBrAWTce1wWTrsxErdZpxn0hQqFG0JN9VzgRNJqiVJziDDzQYoKF4pauzJSZF0CdWV1BS5UKIr2kSCTnvP3227B2EsbBadIcbt++E68XTBRoBPAfyMrIpLLilVduXrp0OVhKBncwEHx+8823zGxVVOCjRVg17FrRuDGZ6gFqUo9NuXT5i6PKkLUaslR0jwUOnGkbePJVdnFWuzOzc/jQ0dNf+cj7589sUCH3lus3jHsjAwMzJ0dH8eQ0GM2ToTCugy47uWfEAksaWRYPCSUYjOzad/zlHQdJTG+3GKoKM2dWFO57550bN60ryLBDgpW1FOjBu+r3jz7tdLGQHleGE81HXlZm0UDBKzv2dw+M3Xf9ppKiTL+4dEVPtbTVV1cF+gb0ZpSn2OsSC+fOPNd05sip1uuv3kC+lrjG0NrdfbKt88S5TlLFZbkdH7nlGuz+gA4rzIHEqDUZ8bwB5TD2gzv8FwyhPIrsf/vtN156Fh2Ly53TOLPhnnvvIptD2a1bcDiVrrK10lMYw1palEOJbG1xDvQDodduQqNDRUdxLhCg/fscwrDgReINJ6HMpAvF28yc0JlwT4FFxaIY+YKRcABk5LZgYiQdCuWaFllQqMEGm1tSoRhBR3iIxhO2RNqZ1oN1jAf2ozAhQwwSm5uTA21SxjiFJZgiNm7agEJFQaL07x56+I9/+JNUmVcOZEgm2zPh6+jo3v7Ojq13bv34xz9Kc0zSoUOH2MCjvFXwjT6LkALbxOoIVouqXCZcDqqMyO6d1LJ/n4n9m71VphQimiguyGqYN3dwfHzp/LrqmqpTJ8/iFTk2PIh7vcnkxlSEFoLIE1IRbtm4lpxCIx7/iC9wtqOrpb2DwNoVCyl9XdvZ1uWl9FE0VlhQcODIcW0yUlhUyW5q2byGT2y9wbflKtxj8ElDmFQ0lWLCHh73BL2+gqKqssUNp86eJ8a3NC+zsrigsji/rfVcMLmGjR6l69/es3to3AtBN2niM+bU+vB48vtwuqoor3tr36nC4nz8qyHb69csZkDJaGLdmtUB77jbLp5cLLasvlTcS1BYxuZwyRrjCSAbGC6aTp859tKLL8Ao6mbM++Y3vpmdkznhGSUcxGFz4heKlzMONvi4JTRxEi6R1aJ/eHJkbKx/3FtdUjijKg/FMiiptPg3W7a/eBEjRKEk1I9NnZRtpEaACRuDuKynCdLHbYxo0AChJVJpFEtSsgd3JKc2J9rbkvRNanSWZDKMPxlioYZEUxZEeAMSqfzEN90QQc1TXzeDwnWolBXRV97OxEHIkF75xGXq4Yd+j6mGeb7YM77wi10Jvu9J8LO8onzL9dezOvi7cYnbII4iRysrBA+//vrrEFuEHcqKCVwi7pI7h9sudyRU5wM/C+JpfJNXLJw5b/ZMPPryl87MycsPBaO5LhfeZ8R4km6bPX9hVqbNonW4Cv/1Px9MGI25efmtg15CBm1nL2Af2n/oiJ1Cgsi2FvfKJYtXrlh2qKX3xZdfe9/Ge6j6NBAMIod6g0SFawuyc+LpJBJmeXHJj779zydaOnbtPcgGMD+/JOIbqS8r+ex9H4B+kxAU6znZYG1Wp9c72JMaLMp2ZDhc59qH8EcEiY8c2d9QXWy3mZMhsl2kkv5Ant187y1XF+XlDAxY2LIqq6kABuZjs0kXNooLbDSWm+fGAZ1tZu/A6PPPPYOrDCDjcNqPHj00NDhw5szpzs4uUtDe+b6tV111lcT18TiKR5OOXKn9Q6MYl83ObOA+GI5L0n5cXQWt/y6HADTR98JlhJ7i7AeTs7CvkpBP8kkhM9A3PWoX6BQ2eG6PB4I9wdZBco4QfokMw86PDQRbbdlG6nADQrfDtpAWMRgas7Oym883v739LewNOMpMDZJyPKSfwI6h1bc0N1/EQEEe5QCbhEWjJmDqOPviiy9t3LiJbHclxaWCdwrycYeIKOnUXXd9cP6CObiyEzIieQIU0Rc3IJc7k7rLlz8SMmVGs420VzmZOfk5ebFYyGExuIoKMNzbXRR/D6CRD8WTv3jkiZrqivtuvwGHp0A4dLytN222NWpI/onIoxsPJU+fa62pr2bTGE/rCee1W3SkuEdIynFay/ILzjZf6OgZRkMDoENZJ7yh0YlhTFsum62itHjbG2+W52fdcsM1J043VxTOwtU74pvAxkWoj0TNErqen9vZ3QMBxQOUnUYiiLdb8vdPPa+LRj76gTt0UTLws65YDwzUMCCQJxQKuDPEosjoIKWwQXYXuE+72PhbtL5owuML2I1gafzNN94YHh4isynAcvzowcP79wIesDWiUf0+7+8efHDOrNmFRfko4JGw0IVighueHDu4d+fpUyc2X321y1gdDnqzMtDxCjL8PQ4cDwx4L3m9/qVLV0qiC50pBQoxI2CIkbTDGWYUwFpf0hDVIFRAMijGiDzPSR0bQNnBg35aUlGaLXqkWbOFFrG4Y4ph7g2kOkik3nn77cLiAvKpTUvdyp7QRfA3QyaeiU8R+y/ioGCZnGHa5YDAkUuS+pbVVdbNV25+7Y3XLrS1KVRDQ53tu+6+66673s/NwUB4x44dJFkjrRvWC4wct92+9PDhI+8JJEQjjOYD1X8s5gcYkdMhchAoHGxFT41JjcDqFOkZ8OmN65ElUUoYNWfbW9Bz6izWI6eaKCJbkZ9VEk0fuTAwODbR1NKGurJjaPzMuQvXrlhQmptJ0pChcX9LZzcqVptVazEbavC6Ly6w6PR9PRLs8tGPfpiA3bNnmyzmeVkZhZgiJPmKAtqoRrCelxTnezyhFSuXx+KB8tLM+z5wG05SM2trMLkjcJGXKZbWO91ZkHE0gqhMMBwrKToFMoAD5CKSWfzmgYfqahvyikqqaqri+sjRo8cPHdilJBBDUSSKeZKcIcORkv3uD33wJz/+EcG4R44euenmG0FCoAre6LCZqCt4YM/Ovu6OUyePfe7z3ywuzv47yqL0isPr9e3fd5h0xsj5rGVKh4ZTcSYykiTPjWiqM2Ggi0gVHOYllUAoFfUYzFuScSF4MmEmjLRGq9NgdmiN8FJ0M2ybUZqaQ4HQ8FC8vKIMJz71dXyi4kLRwmYYToZzGQ1MYyBXFdRT0U9oE1tEVDvY5REZCvLzvvfd7z77wnP9/f08uGHDej4h5NwD4v3bv/3bli033Hvvh375y1/hW3vH1ve9J8RR5gg86yQ/yeDwqhULiDaSnTHJ1PSm8xe6MrNzSvKQMEPkkjBgDgZg+WI2k2zElpl18nz3UF/PvbddtWpOHchHQr5YMlaf51g5a1PXwPCF3pFcp/UTd91k0acngn6bUXvbDVe/sX1HV1/XjPVrSDEoPkvxWElJYWFpEXkQHQ73xGTw2Vd3+FbMnjOzVtl3IO1AudOuDLfB6mw53Z7at++69csXzZ29QGzDOgRO8bdGD6ENnW5te3vX/jtvvGF+Y4Vs9i4SYwbIAegRyMC6vvDic8ilOEA67Q5xp07IbgrQhV6rajpIELZEcregmgc6T585e8ONN03ReS3eCJo7b7nxjluu3/7O9o62CzjVBAP+bDd2UPU9f4dPlg+e5yElgp/MdIiUxgQhLIKEIkMj4FF8XGMmkAw9cyzFp0iAIjkgByIloHrWEYKGnxoONiRbRPHFpl0RbhEmYaqI2mxXsLNDNtmnqSNErMCAB5WEJTbMJN5z1smTp0HnS6cd5sar1E+SyInySNnp4ez2hS98ARGK+2UfyE6GjD2J1IULbeQUOHrsOJaPsbFxPCi4B/C8/Dkh0ANunTl/zmi0IkBmZNrxe2Zd2QG2dXfjZ9HT11vXSIo1A8xSrEesApX3qHGb1Ac9/sV15f/wvuudukRsVq1Bt1KEeSLozPq3j5z9yZ9fntswI5skFAFvJhuLvGxyGd24cTUZNbgBrxQoJFDOEygoj5w4vmze3I1XrHjlre34NlJdN01+NEQlxESdkUx+T778TkoHlGh2HWzSa08RbEuyiP7BPkCpqCC3rJJ0Zomm1kf3Hjo8r7FSUCIt7mnTOCEwl0pff921p04dx07h95PYegw9AvYooTnwXdEuciCF6To626gtgYxG3zo7uwOKfwxSLlfhBiJjadNbrruGkkjBELkIJ4lg/3vhoPpeJonAO72B+llaAk/iwDSjYqenNUhJVjghtvtUjFQyiKMSyCg+CuwZ2bSJzIrnrqiuEG907AZlGykcUnZ0bHb1DHPxkjmbN28iKkXBCmWemDgeghNq01Sn+NSnP/nFL3wJvZciuwgn5Cr3yWaFTIgSwYxka4Se8iLCAEbGRlkd0ltQQZwVZCEo3sojNBmJxIiokKfQhilYffkjITNF/jnIjTcQJvcYTkbRaIhMMBjVqmtrHnvutXmNNfMWz2M64xKTDsAmzDpjUabVoAvPqqjMMmkcRmPY70+QcoaJE4DWk6G7uCA/5Bmtr1qCuZa1ZDWVv+w7cGukfmCINR+cpHje+IKly5zODAM5LaxGGw6rtRRly4+EAiQwAXHwzwT2r7t6ky8UO9vWbbY6Xtt7JOT3v7b7qN2dQZI8ShEmYhGDxeqbGKksKty0bjXPSM5NseApb5Z3cxBTH62qrl68aNGOnTsxiUH+VW4gvQO9ABq5TfkqhQAEBIGD4eHBI0eObdiwlqrCiJ04iMlTqWRHWwfRPTW11eTF4OfUm5SmLv0QoQKIvuRQ7/yLUyDMX95zye3/718Zms3qwH3M6XIwy7BAox7DO/TNgpJFcoFC7MTNVal5y/JITR7xmxarBvFKiOFMEDyLBBugJRsRWsGDgQzHkr+c/bN/7Zorrtx85YFDpLdQSZUG48HY6CjKUWXWNFhWv/DFz333u9+nDeZHxUBmU/0CIyWdMdm78QF8/vnnX3zhRTJusZVds2bNpz/1SdJ/4fyNb3B5RaWATzpFrBmeN3mK9zbjfy8gIbWsUpgW2FPpbY5nn32+oaG2tppaOYmy4gIIZ0VFJQuaiCRDfsm3ywLZTaZb16wNhD33ffCuT3/un/BjK3SaECzTIjiIEMQyZme6Mm3GhbNnsL2G2BKoLZyIbYjF+tKbO9csnkWqz2NHz3V0dtXOnnP8dBNO9JtXLSguLf3pQ4+nE9Gr168gARj5lsIhUl+mXOnE1euXh2Kx/aeb2gfHqstrfH7PYGcHmhV4gCsjY3hkbEFt6T/9w0co0YZxWoE5A4AyBcJaTYjSmfByk2n9ho179u5VYElQjv/lz8UDoFF/QpIVj1DhkI8/9uiC+fMyc5yxWIJ0RidOnIB6TE5MfvjDH0GvQN5WhSv8JVpNN3gRu6bxTJCb4y9u/4sfFx/9X/pLY+xXmfSVq1fj5gmljPrG2clT1cpgjuPxqqeKiDGJPYEIXXwrrDhjKvIqyKgHOVLheCSOwkbyDZNwH/EmpolT4jwSJE0/m23KXxGDTx3sW2+/jVmFNandQhYdGx/Lx9xEPCdLm0rcfvsdGFHvv/+XELRLp5T7+WnH89hs+vGPf/zcc88zeGWeU6SxINrwc5//7G8feui1117/0Ic+PLOxAf53/fXX4NsNgz169Cj8c+qV/0vz8f+fNzEd1EUhlWZpYVZX+zlKeRQXlSCwAYIOs6mytKSpqRkOQHzY0Ng4sgUpnfFpAj8PnWgmFUVhedGDjz6pIYjdaiFPImYjo5moa9hKEhsDIU1sOIQJQjAx+Bj1Hf2Dr+8/eaFvbCIcP9R04UhrT3vvoCcYc2fmkW+Gus0V5RUCQIip7P41hr1HTrZ3DyD2uKzGD9xyzcdvu+mqFcsuNDdV5mfdc+Omh//9a1/42O2+ybGaiuKP3n1HhpXMiORfULma8L+pNUlryNNOUiPq3ZNuaPESwn8lIEC9YRrxVHBRHxGKoUAPMNHT0/Gzn91/6tTZ3/72tz/84Q/eeOMNUmJSuY3aQzBGABCgmn7V1Bv/8s/Ffihnwbj/faT7y3ZVcNbqqab8yrZX8XumM0lNvCo/Y92S+pqybLa12XbDrNLc6qIsoyZmNyZm1hRXledgw48lo3mZus3z6suzcADCkTZRRwQK1W/iuHqaGqoq6ivL6ysrcCFidOQ2oWU2abA+tQdM2vjYGIUS4bIsNoIpk3nffR9F0anO6vQgZZbT6erqip07dzzzzHNgrypkqn2nUAwmsY7ODnRgb7755he/+IVvfvMb5FwjoIkkF4ODQ2D+5c8JmSBC+K5cu3L2nAZKCqY0lF6hILaEREBjN65Z8btHHn3uuW3sE7H4DgeiQ8ODJGg61da1/0znF7/z64KCvOGRrqffPJiT5aRsNhOMS2+GnVqvloTR9h+/ePDHX/9ChsuCZMP2njpkpPppvnDhnb3urLys7uERnKmCMVz3DWY9hdVc+vSgPhletWRBEriQeHrTrn2H8NJftmgJtUFN2kRtcVFrZs/yhtpP3E2WxOxwKOKaVX/NqvnoG3JcViAQN27puUA5P0SlKQeCGLnlRSkIZidXLF9zYO8BtBJQB/W63KJIZMCW+l39VH7K9njf/r37D+zmfrar0rxWc+rUiZdffuGW22+OxsLixa48ON3aX38BR4UDyv8qQgLasGGlqak3qg0oyPzfNCYP/3Wj7/5GtXjkyBFywPCFnFV1hbkuXYrwZIPeN6e+qKjAdbJ9EEuUOzMDDxrM8CQM9wYSuQ57wDuWl2nvHJ4kgQjlRjIK8ocGx9BK5+e6DClHa3ObJkbeZNPKVauQHpUN+lQvwLxQOIiTGnx48eJFyEpCZfXaz33+Mz09XTt37maglw6D+F2Il9AIWQMRftWrdBiBpby8jLRa6EtJSNM/4Pv9ww/fcMMNZK9SGe/lj4TMCFSMLRJ2CJLuKYutqAvZBaaiFcU5d2695U9PPBsKhbFTo4XIyXCvXDxvRn3jyvnzR70BNmRzZtZ1Do3iMgbrYUtArl6TJgFHXb9yaUVuBmXGJJUEYioOHPFEY1XVhmVzwfYzx45duWa5wWR/fdubWU7Lgll1aN4o01xQlF9fW8kqoWdgo79pI3EM4muGFBVORTt6WuvrimY3lmfaTUEfWeA0o/3j92695cCBPQH/pNtRRBJCgW2Sz0oA8NQGRuA7re0bGqNUoMVonDVr9opVq/ft2w0Vx/EV9FPBGbDg+6Wgw0/BHuwzRsEfPe6USAhyP9V89X/40+8tdmt1VQUcnBe8ixOXfKMvCtiCv3RHgTzglTZFjyjvEkQU/JJ24SiC4MoDlzanfJ/qpYi+l7avnMZWTqQCcUBkiE1QtMNuSCbCfX2UuJ44fxbqNt/jiXUO+JgOq0ZSYHUM+UbCqSqXJZQMRjSm1sHhihwHdg7/mM8zOkrK8NkFWdR01YYC3WfOlBUZVizH+ZMXvysYoqGjXs3jTz5OWqff/o6ktTWMjhFRzPCuu9+/d+9erIvT3WRIpDM8duyYcobRTE0y91OMEbekq67a/OYbb0E0T546+dJLL3YSTdPdDfyhvMG2cfkjITML9uGAgnaYecbay0aBPXdFRTluSoFgBGj77MfuRURhQ4B5iDRlwIy+yKCZUY7qjHVB2KTOEf6cGA3YUgt8pSJouyTwNhWPhSLMppSUJq43Gp1VWz6r8eM46LCPgGcePXnOP+5ctXT+UG/Pa2+91Ts0Mjo40HSmKWvpXKLv23uHJic8hHGggdxy5RXAaR1RhTY7y8gaS+i/Ll1TUxJLpFYsm082aJyV0dahfwsEw2OTPnJqKKsO0IoqHPtET/9k1whZrI6Th5+4O3F2E4AAaYUTcrNghfKFTwX0FYzmAnxTbps6zR+IdDgU/sEPfpCTnfWT//gh+Ky86y8+BNywZXu9Ma8vRq5+FJYIYxazohMWEQ61E+yFt4LndEFi0pHeRR2iICdMXeE+kCTERU4i7TMY9JnY1k0O2fHSYYgXeb6J0KuqKjuw7xCbPp83GExHe0cnPf5IIG3bf6YjpDNORkKx4XBJYWNxfll85Mx4PDQYDqyuLd3b1DYaChfE7baMHKct8+T5NvzbJjyRiYGeuCdClkdRtwl1ZqLUAckY+YHYw9Z4fHyC3DCkPGTzr0xPqrS0hJ3exIRH7uPOdDrD7V60cOFr27YRIihTKcQIi0WysbGBnSQbUQz0n//C586cOXvo4OHhoTEKY/b1DxYXF5EP5OTJU5c/EjJHFEJK6VOtbZ1kCSRIL23QnW/reOq17Umzo6er/dbrNq1ftpQyTdFIMIFaG40anlqijSAlmmQmRJHvRB2SMh06esJqs9VRfs9pELGD1JZSuItaAgayv8BwQUJ0lAAULFPoalI7t74ijwKFdvvKBbM5CQW48dorfZM+9gPAGoSwobaSimV5eW7U6zhiUTcbxR0kF+CUvZjOODbhcbszsWqgZQDZMBuyynab/aHf/3njxvUS1KtAAupL0vefP330mWeeDfknBPIxRguigTxTsKX8nMJGHgK+ARflaW7g/BR+yhlFF0EfwCH0QLIR+x8ORN5Q10Da7weSeBOTltD6RTJQ0UxomMLY5AMDgkIu6IfyQu4W5sibkSWUjuDBIgiJk1NmlrG+ginkVt6+9or1xMNiIiQ/Sc+o56mXd4cM+oQzM2237cFFm4zAVmva5vbHk9uaLljb+yfpgdm6d2CobWx0ZCyotVg7KTQydkoXiY5HU+Gk9uUd+9PERvixlJKqC3zBJ0bIxPQoeW9ZWTmlR5j2Sc8k55kuuarV5uXl5eblTSMh52bPmt3YOPMDH/jAP3/jG1gg5DZNcuHCBf/yL9/IL8iFg/Ls1q13VFUd/dxnv4D0i1CKTwHJAcBS0c0oD1zOH4yfAPOhCW8M5+mCXFY1no7VNMx4+q0DOmtmaWlNMK7/xo8fdNnMH7ztmiw7O0appY7mDOsta4MIi/B65MhxXGne2nvsRNN57Pu33niVURMvLyzIzclFnAiGJ8cJPtCm5s+opcgtrjk8yCryxWxOVZYVApokv0BFDmPNsll5CwvvdmaWlZZgX54/E4+KKClyJQGb7Cigoyjc9QOj4539Qxc6unOzMupqystLCjCbCI1IIV0bZ86chbIPmFAWj4zE8V//+jeHDu6SjT55FwWyBdYF3Pmn3nVxneUChyouipSooKmKGeol5U7F2A05kIenQFA5f+lHwOdJhiOkEJMdKIjP48JQGAWbAG4UezloBvGQ9/EuesVtCiuUi9I9gFQwmHNAvGAyaOf3kdwGVbP6aofdQY76E0cPv/D8MyZLKhCe0DtdxvgExShI3oOTujYd0oX8hCGnjAYSc4vdkLHrNBOxqCaedsRJBBwdj8TT0TibBjN+UZqwFKbRRTUmaFcU5XN9w1+MkbFn5+Qg2O/cvQesU4fMJNBf2OD8efNazrdenNb01ddcAz299tprnW7n3j17GCIxE6S9wOjKUGRTIMPU7N+3n4gW7qQdlnh4eJifwnEvndDL8jtrAa8619xSXFIi4X4CKwm71cLSlxWX+IKeB//4NDVJRgYHFi9ctGlZ4/F9B3UWx6SfsJUAyQvJSDO7oY6ky6ebW5csWRDVm9/efTTywq5k1EvN2eyMrK6e/qzsbEKtiT/8xL13FeRlHT58bPmiRbwnlk4Egj4yPlWXlZG0q7ZuBoFMgZDXQoYiUcugoSXpnRSJZv3BQK2offAfxZhsnAhGjzVdON3aTux/S++oJxLPy8s3A6kqx9DrPEFfdl6W4IcgmpjIVqxedfDQHpBQwT+FrSlXgBUBfwXlplifglRyRsUM5ZI09NcQIDcALwoO/5eLyu3aYFQThY0QainFxPkGnIJIEBKVz/EY5wW1pJf8knfiQcIfeDqrwCXEU8FBaQXWKeyRcg/hYJicamqP0OYTqfz8s8+2d5xHHZrUjKQDI1q/NCBeL1p2ytA4TUqC5REtSdVljAD90qRQNLAcl2DaRbcNbWA61EhtrYVckZpoInTuXPPq1WvkYUEXORCHX31lG3jyvve9b+3aNbKfF6GarvFX9/73v//N19+aUDhkfX3dpk0bZVB63YYN6/nHMCVkAC9WojSU6GGeoQUKoamNi/IAq25EkiZy6fJHQmYN/oPKHvimOgoiHl4S2JaIO/dHw91DQ1l5uWzOjBZxMGK7lZlf+OuHH01I7KuurmEGG4CKuvrBQ0dsTtecOTPPd3S4bYZoGOdnT2ZlJXWp8cN0mHQrbrnB459k3imUOTo+2ds3AH7uPny0d8y3csXKJ55/aOH8+a8fep6qIXXVJYtmNdjNxoHevrKyEvIsQQr9QR+VwM40t8Cr5zVUI4GhRRifGAc38ckO+CO93X1NTeeXz58LVRUoTSeL8/NI6AdcqOtq0RmWLl9SVz+ztfkslhSBdZBAEfcEdAQcFRhSIUn5zSkV5lTwmmpIaY4z/IVcqYe0Jf/+6hAUp9K0RPtxRfHV1LGRYwtL7IVBF03FQS2oBgE/IIa4FIABCLhSLgZ2zpZXOD7plXBaBZPBUsgQu0aBS5wbLvEHwgNicGjgfGsX/npI/cJZBB/guHQNw0xqRlUVeIwSCtJJfjRqBjvczknPOEUmxQGNwAsRcCyU4kDPqUzGFNbLZiOt2bVr99bbb2MQ00hIH/oHBl7d9tqqVav9/iA1zLnKPpkxYO6YPavxu9/79ve//30e/qevfy07J0P2tIxOddZRNtjqnPOUMpmy3cWrCggxmx1oa1gjyqNCXCCLlz8SCnhAlhIJdgfZWSvwoAAaKPlCUfgf/uIP+cXFdXPnHj956qorli+aVRulGLpZS9J7YvG9nsnzZ5vJGfP4tu3HTp6OxhKLjp8Fpr72uft+9/Af77hhE4FqTz/1HHWFrt+8oZaAxQynO8MVjaUceu26lctJJdI93HeuZ+iNXXtqCzOz8rJPvfW2N5QYpfq80bJ8DlmI68DwwyeaXnr51bs++L7hMbY625fNaVg0ZybpmLIz7NdvXosaCaIOc4iEPNTSmJgczchwK/bAWGVZMRAzBTQAeiJNMfitd9zxve/+m8LuQDlJfiG8TPjPRXzilxxTz0394QQg8+6P6a+C4nL6vyKg0grXwBiMruSDQ8rQImcZKShN8Qc9+1qjjSKjhpg/bMSaLvE7SKgGLQK/Lwhc0iyEjvhltDg6sxElGd6FcCvERdkPx9lxB02KRzVd405UTeTFYyUhlPAiMEfICognoVap8qpqrEQlBSX9vb2rr1jT0tLKeIaGhkgN7vVM4EJN3RWixi60t/NaYb4iOStjgD0aLcePHT9w4AC59JAPCUfiAt1j2GzbduzYwWYeOypjkackqTl9TFx99eY5c2cxM4WFRVAhzstTQq6EmbOZBt84jxkWK4U6hfi+LVqyqKqqavasmdw7OjKKY5NwS6Ujl/UHihmTibJEOCsoNcQxrBMDFGmsrbrhyisaygodJs1Va5esXFDndhrRtuRlZnzg5qu/eN/Wr/zDRyj61d7Zk5uT8bUvfPw/vvX1sNefl5G5efnC665YOqu2NOEd+9ZXP42A89BTLwWT6Z6+oaEJ38GmCy/tOfLKkZPPvLkj5Pfeffv1GVaq0ybi/snvfvVTNfnZLc3ni4vL3tlz2BPTPf3G3t8++fJoKHXgVMsvfv/EhD92xdq1eHWymGTWzHc7SrJcBW4rqh3qh9ZVleTlZLKiComVLRfFqNWVU8EpESXP1/xPfOrTJGhDOEMIF50pbSmUf+pO4YpyqD/V7zwuIAeIqWenP4XfKPfLFfUl09eU32mJ9GPSUDNIVadgND7q0wZiyUgCq2UqTDKtiOzzglFxZ6AlvociuHTCFKEaGnK5EoaLUBlNoHRBySRiKQG5wC/OvVbb1MuQODHC4mUfAnvR9KjdpcfK/llIRDoQ9Ge63LA+nu7oaPf5PKQjJyYXnB5CDT00CAaSSY1nhPHJH1CJf7QF+mi5+tYbbz746wewqqsvRXTCN4ApYcIRI9k3ykZWDuRbYW7gIbHdVNeA5HEPrSiH3MWcPPP0c12dPagEDh448pMf/yd+HVwoLi264/bb582dyxtxrpCMT8px+XNCppk9fW5uVlVFBaI4wIsCmZM4BToMiZqGqu6B4dKCPDSch063YMZwWq05LhuYMDE6Cupu2rCqp6t35YK5Pe3t+VmO3Jysl19+qb6mvKKk5IoFcwGr3GznD378wJkzrc+++FJOaZHX4ynILX7siaevWLosPyt/tK/72vVLCrLdYxPexbPq/uObn/vFw4+eazrbcqGre+xlkm5Ew94lC+YR6V5ZWhb2NZ8+d3JO/SZdlL2SiFp8AGhJnfHQwdPzZ1eRSBRmAgwCPijZWGMVeVh+SDSsMhwJb960KTMr6ze/eWCwfwBfLlAWiGOtFcAWQj2Fb/B0RTUq1y4e6qWLv+TvNLr+VwwVkCTIIhJHNhcNsYoakgtDfYuMQNnjSXJkaQeyIYij7B4RBnkxv6d2g5LNRxuXsBP20vyHIJsRiZqdTqXDoKnGYbfDWWBHCj2QFnmag/eSwUDJMC9bLKbi4MGDnGdy+EnmQvAHRzzwhINZepee0CXpDwpXqVvWdaHDE/LxOA1ySMKeuGzqqFt8/ZYtdrudO3mn9JfH6K2iY6I5aZamlIPFIhDnzbfe+cd//PrGjRv+9V+/9dOf3n/27Jmly5auX7+e/iipTXEPEAc5npjqv/rwZfzJJLtclJ1YQR4xSakCWQV1IGO5GffevZV0zr996E+A6fmWjrMYlDwQ80hZSc76ZYsQFFYuXuQiT25OVsAzjin/U4TDR2MTY/2zGuugjISwA/eleXnf+ZcvPvTHJ2bU1SxdvLCusgQdwfOv6993w8Y/P/E8TjHXXbVZFO+Jjn0HT1gt5vffeN1Df3py1ozq7EzHyrtvMCTjxUVFP3/gD5WlWeXFqw8eOLxy4eyyvCx6iKEDGMItbnhkEnvj4gWNcIER8kFptTnZLi6BbOoqMkZ1TwIwhYKhBfMX/cu3ih555A+o4whvxUVOdmyMHDkMIBIyBA94F/tUCHr3919Cg5wHyi7C2SUXAaOU0WHX5OcowE3Ra2lU+ATsAFsr72CuxddE0Ydyki+chB+C/1xElBSQljx2EvYnF0Vq5ZNsIxaHygnlieefe45QIN4iBEXtCTdeMgSkWaWPwqDoIeCOYMlsqNtVeaeyb5VRgHdTKANTlLcxNZAGuqjksphyW6NB4gPJOPqP//gVbBW8C4KmSJiioRGSB7mhy6pgIQ+zXZQucZFM3rBGEkO99dY7PT29NNvV1c153sMj3CXfRWjljJx8D3DCdLqkqNAzMY5pWxgIECkK87SJaaB+dnDyuk2rCTCz2S12ihMQLMtMASKJCLIM2V0V+icFUjjNLNss1vzMWlJ6j3mD59q6RwZGa6oqjzWdznE719+45tSpJrR658+d+dDWWywm48KZ9ZS2Ls6wDg6NFOc7NGmUccT3+dcvX1BYVNTScjYwPgYEnj233aRLrJo/EwEtFfSeO3u+qmRdJB6gWMKh46dqaxuHhsZJlU8tYGodvvLGC/NnzsrHLVKHnskFOrGKdIxDpeJ8wbJHHqfPf/5zFAN96vHHBvr7EH64S90GcQNAwCewIJ8cKoAofzkzdVIgBphWoExUfFOQK/fzkPKLa+68vKTdropqSlvKtkhuEQ0kSCj3K08I+ggx4AbRhapqXiELjICtnWCuotiQbZ68QeJ/pt6jbW/vGhsesZksaFkQZ0QSEDyXQ94kIA0+yAGywQNnNDRkZmaMj423tbWBTmCj0gvpgKA5yyDiEE/Shg6fYHBGuaYYUJRbBwb62jsufO6zX5w3bz7C5IGDB/ft3YtStKiokDZeeOGFt99+B0fQ2mrxpGHmOXi1TIxSOQOODQcmdQVXIS/YdZVWlQ4rsoLaH/XzPYGEpOiivh3uokaTZWRkmM2LyWbt6ez0TI5W17Nprw14ggT7ko0ShmUGIjC1G2SrQhI0bH0ITgodRfsBGEORJUI0O9u0KiubjJ3jnsllC+bBzXKyHQ3lhWxpGqqK8eVGG7h+7XIBt3iytCiXcDOS8EtiFI12FnkxErG5DeVkHwIQqiuL112xWCLOtLrKwi28NCy7EVigaWjU29G9F0QYn/C8um37zVtvGp305eQVwveAm2nYAgKAvyhuk0qVEsCRykr+YHjZsqWzZ8169E9/3rVzBzcgm6qkGlwQ8FVAXDBHQUgBYgUo1EvKafU2Ad3pk8otUx8AP56RAlTSmBwX/079kscUeOeKoJQAveCYtKi8TXggpEevMEOQHgIhIqrcoKaW4CEwTqhiPNVYXctjbNVwhg7G0ZRJFSoOhbMKMUJeLa+ouOO221H/Ezw0d/ZcMug8+eSTvb29EF+ml4aQHonxM5FX3GDkCxZIq8UCwtAFRWJX50ATDARWr1q1eNECWCtZ7r/7ne82UyGvd+CnP/txc/O5f/u373gmfWSp+Pa//ivgwSgVFk5JktjQwCAQhScq/WHXJ1zdbEQZ4/ej87PKaJWD4SMOCAF5L5goZG2Q1og1MpqOHT9BdHx+SRm+mnh1j1M0tG/4dGtXxBNYuXQpWbpFXhViDWwAfxiL7Oeb28iiW1ZaDNpgeIXd0CD7AYx8wJfRbHAV5mtLSnCMA3UwAA+PTJCd3kEAt9BWYkz5gwpQQ1HO480kgCVat/K6jRukvKEmTsQ1eG3ERo9TKk4bcDMN+KMnz6/yWKqsuOjomQtjHiInzJ2DIwePnsA3LRELazVublaXU/ojSICuP6rsAcmiGTYkcQbFpy6Sl5v5uS9+dvmqFX/8wx+7Oi6Q2x50UJBO+ABjoB0FOEVZApjSFnBDm4IKgguIsopwOf0y9bT6E4HZZTNqLmpQLr3nku/S0sVD7fSlZwQs/+K3citAKoNSD8m5hOlCSZRDDL3dhc4GhBVFD2vC5g01Cl4VSY3JcPV11+7dsXO4fxB8Q0TMLSm6/eZbXn31VWOKdJBk89ER7cWOTLrBLKhvARfIOW7SzJw1Kys7S30ltSI++pGP4vbJOnZ1dba3d0Dp3t7+DvWSt72yDQzkNuqHIvQSwSTzqWM3aHrqqad+8uOffvjD9+HFhgByww3XnTp5orKyHAy8776PfPnLXyZLreJLqLw8KYpl2rn8OSGDJPVD07mWY8dPzZzVWM0aGPEbohyPIa+kHNMciQNvufZqu5lULmGFFAPSMD2wTLd7x/5YQiAAR02KuJQUF4HOSDxCzwBWZSaBAmg1caVDo54zze2dvYN4mc5trFs2d5aF2UXsEX21ETfHN97eGYhrXDlFDz/3clE+2JRJxgawLjPDXVtWjJN3BPcOFR4Fq4CieFFBjqn5gs2Kh3c8Jy8XagKLc7nskGewVwUXPoElCD3Ul87AHnHbBybxLOc8y0/y/KVLl8yYUf/4n/9I3qd4NIHakMdlXyibMHSTCbPJjrcHPuJgODOkMCQGCSUCXIH2d981/VK+KMh/6Yn/pe/TuDV191//Vk5f2rRCbpgRFkLl+ZAKMjUiVGD6427SFED3GMXchfOP7TvoHR0niVYUKVSnnxgYPrrv4LrVa8+eOIXHvOxE4XeRqEpuKNIrEe+Kfx8sccv1W7Ctq71izUiJz0/e6PN6EYIx+pN5cd++/adPn1HvCQaCLATSL4um9vfsuXOE8z766J++993vUB8Wj9Of//w/Sd3/7e9+58CBw4cOHlm8aJnIzlrd0OAwMXSbr9rMLL4nkBAJJDc332a1VFSWsaC+yQlKgg6MTj73yusVFdWZTqvLZsCRd8ryrXAIFotVX7Fi2ammlnGP7/DJptmzZhA+mOlykiRbsE7otErWhQriRNrS3n2iqZVEs76IT2/urKsoI41nPBmhYguKCCwit2655pWdB5vbe0nK5nL2Uzx5aHAkngzpKW8S8X/8fTeuXTpfVOFKy4ALUeEFedkNdZWHTzb7fZN52S7C3vx+T2aGM01s/0VwEYAAS0hni3s0zEHRQKDQgZ9zIRQhIg76EiKD08c/8fHlK9e88tJL6NyJsRTY1mpwzpo5e+ai+UuykZ1S8Ycf/v3JE8eR1cRJ9ZJDOvV/4/jfaIYFUQQ36S3bMyaTnTlburKyCoInORP2B9o7OgxUXwwGPaPjDKt+ZmN1Tc3Zc2e72zq8ExMUSXa6XdzmcrtLy8rOnTqNHhmhJsKGIxxHUoUmQcV4kTp7MtCptRUCZ7FaFOTnrJakXYODg8wz58kERX0YOavMDp/5eXnQePAQX4vKqipSBlMchgKGbRc6FG0t6la5nY8nn3z6hRdeWr5yBev4nkBCNlukaSGfuSTi1FD11o75qCjb/aE7bsbPkho/+BDhtsJSq6K9ojZnmnH1REE6u713+Pipk1Q16h3oz85sZMIlB5NsbaYO5hTTEIJeRXEedq9AxKJLRDraL9hNdYA+hjAUDtGgZ8X8BuJxXnxzz/DYBF6DFE4rzMvVaOz9I95kJN0ziJKGdVShlE9RVETCoZGBgWWLFry1cx9WwcH+/iXz5gAtmFouAokCLihjlGr16uNADD1jJwXFdTkcIljC8fBPjSeq6md8+asNBJGEIgR/SAlRygM5bBYpQZqII3h//sv//OifH3nzzVdSuNRJSjJVKuRt/xvoc3GC/s/+Mii4Cub/wKTH4XIyML/XZ7CYSGN15PBhgJ6VhQA5XBlYZRjqnAXzl69ZdfrM6RtuvfmNl7e1N7dgtScJEGtEst2BoUFcesnlQRYhzBBKteN0OBBQ59NsEaTiECIs+lJsrXpQC9cW9qGchq2J/KAgU119vdPpYt4wlApqpdObNmz84yN/Gh+b6OzqefmV195443XSzHz5y18huxQ+dbm5eaA77JzS3MQ9UXVUqan2HvAdZUIFfFLMeRT+wExJQJ44SGntRtJO4wdlbOnsLywqsJGXUrxMkF4V9R1JhPC0gUNq0lYjgbyWObPmS3xFkmh6QRJpWaGIZASlhtnKxXPBIMUUhbt2MhQNySqTnlCPsYPwqFQ6GiSoIjcn/8/PvUwp38998kO5Liv1K/7x2/+RnVNzw7VXkhyWZVZb5i+mDpPRTDJZqmUU5udPTIzzuWjh4mh0gnT8yp1Cp+kIH0LNFQWdiof0jUMEZpE3pa/IVeR3GPH6CjOJDSCJcSZ6EE7CWChxQsJvFLCHjx4/fe4CUZdf+MIXO9rbdu3eMzI8CIrKUP9OhzId2g/d86Gdb729b+dubKOsJVkOQiS9B4q9PmTRyfEJkh7kFBeMDQ5RbAKPmYf++AeCgM80Nd19+9autnby0ucXFjBBLId3fILaSWRlQ6/jtNpwPcfFF2JE1FJfX9+KNavVgTKNaIaorXvq1Kk779i6YN6CHbt2moz6+fPnOWz2cy3NIP8H7v4gqURPnTo5a+4sfF8gapSX+OIXv/jM08+S5HL37t0434yOjv3+9484Xa6y8pKVK1eyfUXCIYYmEAxwP2k25EV/p7n9W79WYTDwLnEgknWVvY5YI840nzeaHYdOnC4sLmIT3lhVbjPpHS430QzQQrgLPsG4Hd547Was8xrSzMCgDJYgWtFYArMx1JEiIOFIMBwM5GRlYKhlIwfwg6R2iwlHMrxJ8AtAX8KiA8qJaGh8tK+xoqhoyfwChxW09I1OfvKe2/Cb806O2rMzsFIAZKCViuOxRHLmrIakLu2dCPgcVsoSBSYmKHcN2UUlwa0yEIbEG8FAxRtYnleU5lxSb1CpOjdhNcnLgPiIoxRFVOiWH2Maed/SwhL5MjIy+sQfH6IAw93vu5MggLUbNh3cv2/PjrdFGlQ6RIN/6wN1kUHv9XmbzzUjVdpsNth7dm6Ok+rlIyNEdEJ9JFu97OsgjwknrBIH4MwM8hdyM5lECHrAsAGrZE+IJoeQGmH6sRiVIdizkXuit6eHQtio5ci7p5I2xkirOOg89tjj5Kfo7x3cunXrwUMHKyrIZlh+6sQp5GEaf+rJp8jEg8vb577wmdvuuIX9IUCz9Y7b1qxZ9dGPfULRb9FBY0vLeR6/447bi4rEwU2kUZZGSKe44PHG9w4SihJfIBxeJYPXBGPh0+fbBofGBwbPU1Bp2BtwIjpGIquXLhDnQuAOgw/mNSk4XsAODTQjPYneZA7HgwOjIzi70AwZ7E1jcJFJ7oeBUuTIQsA2pA5pE9f+RJLKEF4/iDOWn5vldrlZGKqOrV+zFM2oZ9yDcZK0CDkGo0MqGTppAbMhSKSgIB8IkkiRoXmN1STfTmhRNiR1kmRNLAcs+bv4oOCbLK24XsJ70S5wC9aBJLoEteYeT7FltBq04kJFMD95xLTkvUdRDwywxSX5dxT1BgmpsAGQf23jpvXZmZkkj165ZJGiAFQm791X/o2+0UmGs2/vPja31Kqn/FJ9bR3Fj3Bmkqyq0DiJwRYNLuI7d0Kc0FKDDEC/3E8dRkURoqpORJkqiihcug0ZmTi6udmq9fX04tGGIgf3NuKYNZkyNGQEKCwoCjd748237th6+x/+8FBOTm7z+Qs7d+2kV2A12i9qjiL279y58+ZbbwK+6An0gGzcXV1d3DA9R7DTz372MxBMOsAq2aw2t9NNDxVPOtm7v1cOJg4YhfAoDELnC4diyWR3/5AvEDYZbYFwbNIT9nmjYxRJIzYXZ0UESIwLhB9COKl2G43Y8CcWxXgaHQa66Y7+4Y7+EczGhTm5C+fOzc0i76jE8hrMlv6hsYNHTp451362pWPnvkPb9x6MpI3tfcP7Dx1HDAr7x8NBDwnn6QobPG0qgbKUPE1QB71sRMm3Z5XaQ6KGx6dKbkBJo09FzOSelmgdlky2LOrKXRzXu+vIGXHmlGAFDm4TmwT6C0o7MQNMAYKZ0UgoCVHFSNYS2IDug/ZKS0rIUAyLzcvJwyTgsJoDPi9qXKXU3tTr3n3N3+ob3KOzs1McJ6jKGhHzIBs8MggGSUlCNaZQEIsboyRiEyyMhcIhf4CCNhdaWzEVjg0NhwIBnsVyyNBRkfnCOEWxnlEcg9lIDw4MoPlUasolhkdGYLbqsJhDkATpAOSmls7hIwcRJufNm0tnhOMJLokFlIynWDaRb4XvKYjOeVDuooKN+cZJTd/RgdfABdi28hQ5Mpw4sgn9zc5FVnoXWf9WU/r3eQ8LwKG8G9M32hmd3x/qHRjAbRhwpQzMoGfEYLf3Do/aL5jWUfcP8zcOjOJ3LIY1phfDH7ZgKma53BmDFzqIZrC48996a3txflZlSeWuPTuxAbA8KBt9/sD5tk4SsefmF59va5s9d25L64XHnn7eabMtmT+3pKiUMmkk6pMwN9m1sXZsVRBBiXiTTRr5UfftOJiVnbNoTiNBvnSaiGR6DC0wWamoLU6M4Awi1sURKVxPKIwMUGWDLC3gYqUauN2O5Ax5Bpi4DtzYKGEv+sYkYtvFxRAE457S0qI5c+ZQhAiTmsioBtSqBAdJ6eyLKH/xib/hX8bFVgrkole6RKq3u6e99QLpeZBTYPLgxKTXm23OxU5rsln8Hu/po8e33nrbvBmNjbNmHdwtIbZmmxXbPnPiDQRYWZfTiS1+dGTk4IEDbO3EjMz+GwZlI23hlGIGQRF5Fa0Vid5Yoc6uLvBq585d77zzNojE6EFgzJPYjPiOGw3TLqImtwIqirscpJsWkC0QmyGk3EBP1DWiD1u2XB8JRxCz2Zm/V5AQ4GOylClAXORbqjA3e0ZVaV5W2GygHpFuTmMpeoug14vV7+gJaukW5OdnM6WxeAJNWgaR3UkjmwRKQ0IKa2prDh1tOn3mXF1dfU9vX0vr2dqa6uGJyaysTJKcs13YtHljd2cXSrAF82b3D4/0DwxBDuuqyxbMbQj6gwTIQAc4FKYmXAg0EgQU6msaGh/+4xMvELqRn31fVXkp+7sf/eLfq6vKZlRUDA2PbL3j1hSJ2kjEpARoTxEWGZo0qKwyMhrZHXRiZhCtDCsudgvoOt/AeXoCEgIi0gEFnniW13MbeoIVK1bAYJYsWRQOeWG3aAttSWxx0oq84G9+MCJsOVSugqu5SUmo04E8cHKUS+gtRQMM9CcSsDvoWlZ+3vjwaGdrGyV9SoqKt7/2BgXPTAYDnHNieNQf8DHHsDaSGGBK5UGvzwdZyszNIacTudXq6utg++oQWQtwcsGChadOn2XS0K4dOnT0i1/8MrkvZZOZSJD6aWBggAlEKl66dCnzoyChuK3edONNXl8AUXPx4sWkwmAzyWtxo4mzxVcK72CmPnH82M6dO26+9VYefK8gISDGcjJlOJSJ3iOdtBkNS2c2susX+APc5DLzTzKlZADTOE6TVMIzmwcGB/ceOfr+W27RpmJG9NcsIZRVIt/igyNdK9bcAn/s7eqaGPfMbphBJH5FUSG1kWFU9ZVlcFBRnDqceHPm5WaVFGRGwz7gHU0DEK3Y4cRiLriAzSqZRvbDhwrEKSgquufe959qOn2h9XzaaCfp28KMrBUrlh86cmRsYjLbiVoXG7OCdgAIKKIQZpqBPNMaohe1hxkrSCgjlkO5je1QNGrRWtQgNwUg4PP8x8yILQID/qJFC6gudOr0yQbxrZPQPtEE//0OlgVb3JIli4lIsrmcwUg8N1tqmLJnR1hkvHg4MBfs5RAy3bnZjiy3d3hsqLe/v6Nb5lmvz8rN4Y9nfFzRTMqDdgeqNxdYbcfeqIgMESRSbbq6phrEuzhWyYpyw4037jtwgCyY6Kh/fv8vxkbHeVDYcihE7iakDHxxli9booTei/APUUDGwO0GHSk/RXBNJ8luyJrTVSF2yvHM08/85oEH2J6qkVDvCSQEFn0+b4Y7k2p07IuCGGo1qRynHRkknqZOk0lkCLiHgCp7PkgvyCNRQpgocK/BYRiFGksO7or+izi4SHjRvDkg6qkjR51me06OG85ZWVFCugpJF5YQRajIkdKiNttp3rByoSwP8hDaT8yGwlhQDeCmSrq2CGB+vrP/P379SHFh4U3XbX74z08OTwZ++evfffoDty2YPev+R56y2TPHxrwUOKitLNm5450rN18FI3v2pRf/6StfAEfwVqVLwvKUbQws32hU67fA8kU3oyIb0iY/dTq2mnI/wKCcUX5chDu6iHi2Zct14vdMbC6sBpMMe0oY0N+JE/J2Zm3Dpg0V1ZVUu/7FD3+MRCoJs5LxKzZtRC4fn5xsbTqH0AEzbDnXvGDJ4gvxhGdsnA01N+bn51Ea4ND+A9FQGFkAQ8W8hQvFNKPXbX/zTUwWSIm4HIbiEZPFUlVZdXEmZKpQr1RXV/3oRz/A27CnuxebB3Im2pqU349g9eyzz37sYx/92le/smnTBrtNKgXwrICMokxj5893JpmTGMf4wk9kKPAWxdiLL73aPzC8ZPFi9KUg6nsCCZmenr7ByprZ3//pL1sGRpJ6MynQ8x3mb37hI7mZliRR9BQVERO8LAGgKugDTMMWKd+j1UteLa2evOWsa1ZmttDSZMqs1WxaviS9dDHwIIIkcaWBgInad8w8Ch1piA/ZrHM/gX6C38QicCctk6Espt174Eh7d09tWeHihfMfee714cnI3HlFJGacCAStTufy+bMWzGqEWuvN1oHhCXzrDh07cfsNVwNeuF+EE9S/lKBdll1daWwMIBsrinTDxkaBCbmE3h5ImEYh7uEk7v5GZHAuADRifpDvfMH/mN+oherqa9n9MgHsPDkP+MrNf6eDziPa4WadX1i0aOXyvW+8TR1vEGn39p2RZLy8qnLm3Dm7d+1C++IZn9i3e8+KZcuTlRUkCUKRRqd37doZCYTgfjjyOjPco8MjOPSdOHqcwWMUBv3WrF+L78P4pAeJcXqIzCGKZY/Hi+lvyZIlmzddKYofmUr+l10fnI1g/McfewzVMdRV9hcynTKhrDXcUujWRcql7obYwWI7YZfI6pBiRklwLm53lz8SMi3IVO4M9569e/IL8urmzd69/3hlY0NJtgOnSkEN5DpxW7IyeXIzCtGk1KYGZ/qHRigGga8Mjvo79+7HTLeyoBAFgMAssy0uZiABaKjvHyC5cgKbIS6/WPshjThkC78BncW3RqLR/P6I1zvhcjlsrtzv/+QXTR39WVm5zS3dfUOeE+cu1FTXoDN/+ZW3ac1tN7qwfbDb8ITxMKypLiI8rrKydmBofNWa1QazLTTuv/n6G+i1AjRTGzY6BbaAUcCHoomhCAfebcTZU+9NWB+ggFIXUID8A4DYJNiC0gKj5pONFt1mWJhFxMkYYCKFGnPHDJE+4FIvOeWtf7MPes5WbceOHZTHufWWWxBqmo6eMGn1MSrYpDVt5853G9ow2TM6hub3+F59+RV3VobNYcc/hqrg+GC47Q4shAinLWfPUS+7oLCIIVPjAI+NdVdtxLqAcOT1+Kc3hAyNl5LPF8IKYTpwZj+x+WTWI2IbMsclZU4MZ840Hdi/f936dfiRS0krId+iZD537tyvfvXr4uKSj3/847A+CB9ICwOEpLJFRDV68803d3X1XHfddfKIeHJc7gfkCSKV6XJMTvTffN2VA0MD8YayubNmOs26zrZ2HQo2p40FaO/uxYJLtiVsfQU5bvCGEn97Dx6yZ+RZxdYXGp/0rVlbI3sHQSp2UFRTAnbFssf3My2ds2bPwn5F9YLegUHM4gRe4NeCxCvYbNRP+vy9fWME46KlG+3txwetJD/H7nA21tdSw+TT996Ovo6NPiG5Lrt9Tn1NMOCjEMwrz7w2u77mEx+9+6P/8MXv/vj+D995Z26uq6etub1rQBMNLZldJVSD4l4kxlEQkk8FoWB9mAqR2YQnI6lOYatGi74OIq0IqCy9UG+OQMCHogIERiPPJfAQho1DOGBN+/xvtcgO8+8FKYwIVRYm+bffeKuutua6m7YcO3ECAwOIR+pHZGZEf+mmDEZUmrJn8we8kx5s+JgCFU2vSCTgIWI1A+zo6iROpqS89Nobrl+yYgn7ZDL8VlZUT6tGVdwg/qjtwgXEC2q5kDB75crVeJn1dPWwIeQV3IOwcOzY8bXrrqBNfqoEl7QY99//c9Tm7DIobvHZz37W6bRiNvzZz37KHH784x+bN2/eddddX18/Ax2YOqWXPxLCAxhqMKHbtvvwjuPNxQUFsXBgZn01tRtSJvs//fABvd3FfquxsX6Mclba1mA4RN4sLGjpaHjLplVdfYNYArfv3F1ZU5OTncMWjuYgeCAi/8gxASPsGSQeqiOm0Vdfd+WeffvhgOSCHRwcNhlMhYXFbBJwjDpzoff5F1/fct2mK5YtJgzx65/7SCiROnnm7Mvb3jaYTShXDx06XlVdk5PnXlA+x2o0EVG64+DxvpGRsnT0wtmmTCel7HNG8XCJxTp7Bo6fPr162TzGpSChFnKrqMhlHwLiwLgg2DBDGF0oJIku6awoP6NRvLr4FHyjtKYk8AZ00zAQ4AOwRlLii4rGNA7bhBVAuS9lEZz/Gx8Mijdu3Xrna9te27H9HdGiEUycg/ojC+stmZ3EqUGD44FU0WBGDCaji/RtsqVADSxhe8gqgqL8UYLa5sybs2TV8hWrVuBe4/VOUhOCEIqKigr1RerolGk04JUK40UuRZ5gN0iMUn9v/4kTx/v6+nksEBaPcPyBMV6K24Bs9TX9/X3NzS3YAJFBdu/e86UvfRn3VHgjBhNC9X/xi19+/ev/DEHB6jitBLr8kVCdU/xoWToK32ZkZex850RLc/Ps2Q0T/khL36DZ4jekY6eJacrICPknCcLD6BtL6YKTI0wljvVvv3NgzfJFV21cFwuGVBAVCVOih0yjE5Ptnd2THh/17letWIrWp39kIpLSzJyRQho5c7blTEt3KKEZC/iHxycHxqiEF0HLgUqE6miWdHLFvMZ5DfWo5nbvP+R22MoKc9EBNp04ATZa7Rnbd+y/6fqrx0dGDhxvqqquPN/adqbp1Ma1K0jVcTWqnnkzGRr8SWUUdruDyi0AGgYGi8UGEkIilJ2J2K/YPmKTAPU4D71XcRVQluEIL5V2SCuI8yo/kZ0AQdTJgBEcEgzPzs5Vp/Fv/6l0FfjWIBx+6pOf+sl//nuGO4NkhEQ23HjbLatXr246c6a/t+/QgYOnT53CcogIIMI26mlJD5KGDRLuZEc2tdvZWKJKXbR4yZVXXQnivf3WW4RBFKLNNprrZtYzF8p0IR1AYjn4TFD1gDjR1atWHjt2sr9v8NnnXrj+mmu+9a1/ZaPQ3tbu8U5sufF62B37GhGJhCgbiI0hGB+NbnZ2FspPfG7Qcu3Zs4fIldHRcWr0QhY5D+GDxrEWsjp/+2n9G7+RleCNDdVlX/7kfewiyMty/YY1Jnybgv4ZlUVbr15JWWmseQeOnnzq1bfnz5t1w6ZVVoP58LET8Am7NrpxyzXHj5zC6Qz7EpnxAXv+F7uRXo/9cGigH0+XorysxtkzIckTQ4MWqwN/qkmPNz87MyM789i5tsMnzzkzsrOc1puu3kyAzenT5xx2Y3ZWDlFR8WgQP1AMHzdvXnPlquWYRSa9noHBIXAgodFH/J5tr7xst7mJta+tq127cmGW27180fxELESgUk5mhvRE2XfypaiomOKnuDQDB6p3CKuLhpOMwvx04KeqgJZyv/A6tijkv4JbMj/sIRVJTLio+hOJS0aoVBSqqalVtpq85O9wqLgB92aDd/XVV3t8k2dOnw5isDWbOImKsq6ubuXaNaUV5RN+bwQXmmgEXAJFr9m8mdoBcBt8vqkVkZ2TLWFH+COm00eOHX34oYfPNZ/Nyc7+xje/sXz5cnAONsUwOaBH+KOxwwdaCBYGFWtq6ojHbWo6R7OlxcWULcjMdC9Zupi6cdzc3t6O9M/uFNjgr8fjQ8HD9MI/wT1W4STlJk6dUuZO29Awg6e4DcmWdyE8QxYvfyRkFRm/QaeZWVd5oaVZGwtabHY01FiBQuHAB2/YiGsMzg9LGmsmxsbjkTCJe0fCCbeJCuYV+ETFA/6lC2f6vKMIZuFQkEWFPxDccOLUcRKHzJxRh7CHU1nAH+7sGrrQ1ZWD0plEr46cF1/fVVlcMKuhdt7C+dQ07OrsDE4Mp1NBFHuxlPbA8dOEz1yxcqkTRQ6iTIywWqJzqOKWR7p7kIst3Zqlc4EMIjpsFlLg2EWvmkrgMgnmlFVWioTFTRcPKCvYgsjkZf+q+F4ho4FROIMwA3A8pgHc4wxPsHfii0COojdHuccBNHDksul0uQcG+jlTWlrKd3gst6nTePFtf9O/9AoMhPkA2UuXLiPPYFCRsd955x2gHEM5WeihI9xDJIRqu0cizcmlCETy7Nkmkiwh/hHPDdOjKSJ0n3/u+b6+ATy8wer8/MLx8UkKhiOKM4esL6QH9CYBO4JowB/AQwNPYBCbIOwzZ053d3VAg3v7eqnlQqAgIgZzwaT6ceEJBEAqpPpnnnmG+zkPQSQFRnNzMwIFX+gqilZy/oLGvI6bwcP3BCdU4QWQxfmjqqa6o60d+x7uRGRwMZsFDHEggSLl5OctnF2Hg4zYfER5jfyWAD8pGnHF6mUjYyPegA+iCiyGw5HO862BoPhkcH18bAQ/uJExH4ntRycDGfni3f/QHx6zm03llWXksZyYILbd1t3TQ3W0zMxsGidz/p49+wgyrq+pcNZUgyl0EqDHRCneM2JhFG4LFjlJaC2Xk9FwgK4ADxabCdzAgQOB8RIclJQWBMiwouRWZcmhyYI5+MrgdE6Uk8q/BVxonERWsoHhPO9hmKoqVbkkxgzOIDVxlY0Q6Mo9f0cMVF9Nl6AsyJAoGIHyBQsWwEmIPMJ5BW/pA/v2yS7eaBwZxpVXuDlY9Oabb7a2nh8YGGTEjBc15vz583Nyc3gQt/ubbrqpurqa7QY1AxHgER0hpswbw1cPsSKA2AE/q8lLoYZgI/pk0JUzvJRtHhwP6oYAzLS3tl6ATCDxsvFB9OURMJDdIDInCh7sgbyL3tJnSAl7bF5HP9V3Xf6cUABaAWnBQ7O1qrp2cLB/YtIDqLJbQIEmG3a4BZsoVtKoCUb9AnVSqQ+cwNFU47KYM8rLMAWRBwYIYA3IDkEhHtw3AXwyBuFBRlV67PWrls7GmjSnuqx/YJTSlZ1dHQ6jbtm6VWGSFEnq3hxC6YFtcP+2G6+dM3sW+QgResTypEi5SpSgdJa1wbOb9LViySLAMSH16AEvNO+kUgcxADSFBCtjE3UE4mUAUAMa+AR5VMlS8Fg5OM17Bc0VHghkK9glV4EhwFRwUyz7AsFyTskzTfYWgA9svFRtqDb4N/ukP8Ar4AufAStARdgdqwBCojVhGPSwSUp5yn8IOPAu5oMOwyT9fi/UigGiqcLk29PbT0k5MISQfFRWNEjMCN6huAHTFAjDJfCQ8XLQMp/MA1PKe9nawesI9eJ1TDU8k54gZ0Ih+YdGmXuYEzpGszQCo6Pb4CHVLOg8cAI20iZYCkvkUHFSZYaXMRICTIqgrsALUwrXw/cCyk5dK4s1QCRoIOCXTYBMFuk90U5INjJmSp4UPxRF5wwU4AEjlkFhm3YbUl8VF1gbDYnY+Ib9je/alNQ2T8dwlK8rK6wtK2JbUVu+mug93GtA78rCTFrV6nLoDqvG6qJHF6u/EAheR9vSP4U66r1ez8jw0Mjw8Px5C5V8MGkb1fXcLnUTwrOSkUE8YOTtIlGSyJonoax0iIGAU8Rn4FRJBL5yKMAk/JZ7wDSAgwfVS0oLNCFKds7zqV7iNgCLRoCqvyMSqr1CShRmkplRUFRYVlE+PjqGbx21pgFx+A9oSTgZHIY9W24MKVSIESMtKMhHfc2kKWKmDWQA0+BCVFnCkwbBm+9YlVRBFEBg+EwUb2TUoBAYwg3IHWCgyoRBwlFKrUlt0glOMjMIIEieLAC0S8FZSTICEoLPPM4baV99o/quaVRXUZTX8a7LGAmZTAzQ+HCBHAJYUDdRNGgQ8LABuslhLgIcuipR++MOD4zCRlg7bpb7OTipSGsitnFBgVv0M9Ie/8ufqVPTf0BglCoC6VyLJ0nsxF/xpxE8pVnsFTyjoB0RNCwnF/FQQUfCE4i4vA6qwBoD96tWrwZc0PgRb6ruPdSXXvrJktMyxgi306n0XemnkA8BJpBcYZPyTrW3jAIAZeGnG1HhVT2j3qZe4k4VmtUJnL7/b/xFpQu8FDzkILiksryCn+wpcDdnBdVFZB35Aj4wgYyIgxEB4gqNFTmT+YR9TR/gwKWD/atBcYnHObgNFJ0ujcZttMxbABj1Reon5xXw4DmkKQWvwH6FE9IB2vmr9i/9yQ34gJPh6zI8gHNgf3B46CP33UmtWUaoTpPC6gSJEMH4T10JLqmHetul08ENXFI/Oa/ef+kN/8PJ//ZGeU4uKO+WL8p/dJUzcuEi52Yd8cmAoCukQXp+aR+klYsHm34uNzQ2SpNyI/o/yeUpDfOTBqV1OaR5ZSx8l/v+1w71verj/2tP/F++i60XLeI79lftMhbOqJ/qpelBqV8uvXTp9+l2/ur+6fPTzU7fcOnj6vdLP6cf5OT0I+qXv/rJndNn1Kd4BOWqoaFhKopxuq3L5QuYw26HTbAhJ3tqTDIFCg9Dkp9CgIvgyHRw/F8f+/Skq43zU84Igoi24C9fB++aPoMYqaCS3METXFLRRj6VLssXuATX1JaliDf/K5B58WYRogWdBTgkcB4Szv1QaGFuwp+BYmlM2PfUblB6J6cvTsX0Fzn7Xw717VMdAO8v1eIoY4RRXMIHZKNKG1Pv+C+t/bcn/qoDCuGkw5JYgD5zqALkdJvcz8FP9Qzfp9+onlc/L33X9JnpLzzLd+6ZPjP9XT0/fcP0F7XBv/rJSc6ol6YfVH/SbbXzsFbepC68vPJyPNq+893dX/+ney+noanLirTIQv7TP30Nlv+9733vfxpgP16t/QMYr9jNIEWBt6IaF+U4anxMLaQBkCzU6CewZ/9Pjfy/nge7QPKJSXIyks8xoGyw2bgS+2VCOnQ5XYA0ch3+OrgEqGjz/9qmesM//dM/8eW/HSAqUOCYqyqqTCPMNFqqKMHn9BtVlJg+P31JxRDBB+Wgzek7ISJ8n/5JUxzqGfWp6Zv5yXn1KienDzqpXqIp9RXqJW5GPP7BD37AnhDCDN77MT4rUpJCa1Rawugg2lAFUUlIt/it4DWUWXGyUOmazAL+BaIS4B6+iXpANj+8S3YnEFq2JipBRmRWCC/t0F3lsjQpN0PtFVdH8bdGjy/Kekjr1HulJ3zlIfmrTBHtojDkDCIYL1XkTBms8loWh8pKWag3pRLTxSN2+HD0xHHr5s0GglaU18rw3z1USqRemLrMRQF6eb3KRS+lVuqzl56RtpgEtUl1umRsyinum7qgjARZU5nOqavgkvqU0oQyM1MNKQ/JI9KM6EvF4IweJY2zOJsglf+gRQk++7RmeBTelybB0dVX9SaSh/bvH+gfxD+jgizQ5WUAAX5e5P/CjMH+CmbiD2JUDE+MTzadO0vb7GIKUFvl5SuaY3HX5BEOdVDTMPduPzWars6uQ4eOkGYTqw+LA2KjpQAhPZNeUBHNhPiCk2OKja+FfG52HFBAHl5PPAGxeYX5hezP1VcIsE6x8nenlE5Ov47KqeGXXk0Gfbarr04UFkXCQWBeIE7BQ7qn4qSKBircq1PIGS5No8f0QPgyjXgyt8qh7kLVptRXc1p9RP2c/nnpVek8661olrlBvcSn0uRfUAFuU0+qXxBPJBUfUOCyPZKdvctosDgdtiCG50iUhAukqWSPSZk2h90WIr8xeY0sNqYJb3TckiGkVDchE5nHRypbIjJ0QbmJ0JckBJYclzYHKT3FYZ+8f+xzqbiYmyGJyN2ODI2WR6wOGzGylFhRUidgnaZzOpKZ651ON8mwfUG/Ee9qHTG4RDVE0NWFo6mMLCctRyNBrFx2h210fHx0IuIP0odQGhWMVGvXo20mlGcyuGnv4esvhezw0aNjd91lsrk8jz5W9OrLWqd7WsiTyVORVzBGQY2pE8oFQX+JeWVGxXtboQa0rNKZi6g1Pe/AhLoK0Biek7t5irNqy1PwJTExADetKgRH3kN3JAhKlkh5lzxAihkaoEHplfJFfnJCPtRDXfF0LO556x1tR7vook6e7NPqnh4di/h8DTNmFBUXYADr7GwlQE6VD3F3zMjI5B+sCeUw6nKwglIWmMUmx8aHhsfQeHAyLy8HGEWhh0qddVT6pUwVHVWYBiXHHnv0MSyutTV1oWAU5CNVdSBKgc+o3WLjrnCKxDxYTI0Rks2ldSFvmMw2LqeN4rrDJBzoHyA1IDGaBcWFedS4cWcqehTJJXNxcMpI1V/p9Oi3vpF64y0cGoLPPOV64kkWXGivsnIyQRefUvupgjjtqD1/t0GZTZk+9ZPb1DtVLOK7in7qVfD20gfV7ypKqy3QuPqItKJ0YBob+cl3tTPqp/q42j5Xpy8R5MKqkXfxQiL+hklLaIHT7Er4dQFYFiQNbEwbSVViNVv1wUiCciYGDaWn8awkLNLidFnJ2YfVkQjlkfFRMrCQajaW1KC7zbJTZdVC2AfdcDgskp0nmVizdF5392QonCwvyHS7eIEhP9elJceHNt12oT87N5taQoFw0p3pSkVCOCziyhLweKCcbqsB9LfaKFnXEwlFSKENDJDrxaAb1aaDJmOYfDBEJzAfTruFnIPUiw152QhepyChMuMaTfiRP2Xdd5/9i18ae/C3lHRWZ1cQQz2Uv1M/5I8oQcESiTwXbFDuk1nWhjUJA/NA5M/Uk1ziFVOPAhUSuEvku3J16h6tnkpC6Ef5j0SfRAcL6gmCAQqpKP9DQEiNiPkdWpeQui4Uq6AByYBI2UvZSskoRDaQ1ROWoUAJzU+9V2+3lT70EM0lmpvDX/vqiWTCOzH2/tu3EsPl9XkefvihXbt2oTNX1X18qgpDgB6AZH3z8wvgWnioopEvKZYawPiLTExOkDKH5C7EEGC+h3mqI+b9Xd1d55tbHnjgwQULFq1es6bl7PnOtk6/zy8h/zp9kCghTRprtDaStKXIs6Efs8WsTkcm/nZOs77Akak1WXUWa7Y9jrbaZAHtO7s7yDtVXlqOGxrkVX2RfDI+GTrWn3jy6Mn83/9eP2fu2BVrk93dqboZpJMV45My/9PIwwzJAzx6Cc9Rv6uf6iXlFrkHQgNe8UU9OH8pnjAVKiryhfPcoz6ozL9gsvoUjagIqTau3smD07dxXr2ZS2qD6lUehxOK2BenrmvKEI6n+4bG2bajm3VQVd2kNZmJVcUNDnNWOhpPizXNYCIBBHWOrFZjUWmpThPvaL2gMEk36n+xaMYoxGkkG/zweMpiN5QW5JNKkLybAX/IpbO5HdTWY0JJUGXLzjSGYwmrwaE1aitmuHyeQPeA1+HEyUBHpFtl7gy/vy8SpHBR2JVhB8GSEZPfgzkBw7QuOycPgYzY1ISAtIWkSVyAquN8DTM32/QxVUeoLqAyAamhvsRgz/DdWzO+8vWY093f2UW6F63VDAIgSLc2ncZlUARcsCAeyygpmb1s5fFDR4c7O8wgSxy7U4KowqLaGVWLl3R194z0dkVDEVkPQtsRv8jIFyL3QiyvpHTVmtUTo0PDvb1Yl8IBL6HB1ozsees24Ltt1hnYnL32xKNxn1fqiSXjgVi8Ap/i62+Cug21nHvnmacSIS/SInas2XNmLV28OIcihPBRkYYFhWUl5a18vwhqF8co/FOrDb7+umbu/IYVq+atWk3Om4nxic9//vO7d+9mydExYpVGGqQNQAFBCGwEAy9caCXfCVIreVNUKzMhBXh+kMgdlT8GOfyYMeRwBkTl1SS0Pn369JNPPrN02Yq166545aVXxnvG8sy5FrIeQYPJI5VMubV2ry6UNqRdcTi+geiT8LgvEdZpJpPhkST5GwvSGQFLNJFlyja4Mix2vfiQWIKe88FoaP7cuRdriSnopQyQ0tX2uXMDX/iiPjdfD/CVVRKxDqFCqlf3QSrHY5jggzpAvnOo+ACsc5JL6hf1knqb+iDnGdr0zTLDF89wm/qdk3xXb6OF6fs5qSrJeHz6ZrUp9X7u5KARTvKg+oVLaiNwQnUtE9Rnxt5IdgNC2vwTPlxk8UDmheSLQvawWJ0DIxNYYqDTeiNVScwkTgSD161fm+u279x+hKx8BIakHRr/2DilMBGuiGrFR2TC6/cFkotnlQ4MjHX2jxxvGXDanTdsLDl+tsdgrcm265CCrAa3f9xLGGlVZaEo8DSp/KKcUHAyHCVPhKEgv9AgVQM0/qCHkGVMNAhTbDypwkd5XVK58giuD2yO0P8RiET4FoFxDikSIENTQVYyZ/f2YVIzLVnk+YeP77nrww9teytCjbRMlzkjY9ny5Q/d/zOzCupkj9EkXeUln/qq0zse+dUDj+iiISU/WZzcdHpX5vrb7164+fpUfR61oMXQiomdsmpkNAtHjMmYsSB7V1PrQz/590QknIGn/OgIjN5ZVqWrW6jJyMHMpNE5MhatOfL2661791ox5KdTG2tmZoU1WNaPHDr5zvY9NrJtyF439dxzz5Ns5vqbb7r1tlvZn2FJFICSASljmsLBiwNU2DGTEtn+Vua//Et5Weno0CBuGt/85jfAQGXV02jDRagxmxFKAXmwESAAFdnOwQDFYEXifcVPkioOoJnd5WycOROBdt+evWvXrIEf4jAJO21paaVOGNEMK5atevLRZ/zBkFVvoVw2sZRmrSGij6QJ9YunLaQQMFu8JOWJJRw6PdY1SDgCtwXndI12KOXTEd83mZ6g1qNmLDZMhhxNWWG+1WE6rdHiYgavFsBltBeZTyrTnejqIl1MuqxSQ80XSIkMTNACgFYRTP1UIV5FAMbIl0u/8xA/ZS4hocpV9Qyf6hf1cT45VBRVMUrFGfW8ekb9zlNqa3zhzHQj6j3Ky6fwTX0d90z3ipslzRHSE4mCUWBTro2deG5OJg4VfMFlEf9dlCUT+ET68ToNEueKwDnh9zkynCWVZZODQwadccGC+QcOnevoGhjxeoF+xQNSz05mZmN+tjMrkhlASTM6HnRlWyvK8+0ZxsNNrbNnzqqvs772Tke2s9aW4SBBvSPLVJFL/DgoGM8oys/NyAiHfVm2nPHgsNPiYFNPkmsEW3/AQxoxXJBikThh0TBDM17P+pTXT4SDzCwkA7YbC4dtZhHnOBTgFGM5CT0zv/c984b1gfdvHd728uDYJLs6z0A3OWILXe6Na9dlk8BLJlE2sUjInceOmy2u2266iapzhrSBTCTjk0PHmo4+8ZtfuItr3Y3zIrIrxlFGlzIkdFad1aWxwa19I4/99GfB3r4tH74nMOnd9/xzpCAqycuLa4zBUEz8BTS64hlzymvrm+cvPvD6a0NnTseNNhH1A/6W02fS4bDo3kE5gzGtM7X2DD3yytvFi1bPLM0vgI2SEwdmqKy0ClXqGNVPuk5haIiyacGi4dZW5LSD+w8+/8JznGdq4KKYs4kbrqgoB/EYIupQgphxDHBnuanXh7CLDzQwAaLyk39ofcjg0NPdvXzpMupdstCYJfFa7u3t83n9CxctObT7gMUH55GcqXAgq97stNiiJi8bpSBZdxMaUxxFmc4GZpKsgC2MJAhNkT1Kn9C50voIQhNIg4SOQJM02hMmX/u4LycD9osKZ2bjTBBAaIwAtiY5MBh97fWs17YZKipGbrgxRurBK69NR0LsrWFJwLg6IXxyXAriPKviBuen0UM9o84bnyoKqTjGd76od05/8oWDS+pVFTPVF6mNqI1PN8gX9RH1Ka6ql/ipfp++nwYlZRcXoH8uuwFFSyJCyj9tQ23VwAB7PKi5JMAhNYPWkHQgxOPNgXcGXdQZ8wuKJ4b6ezt6kqHI8Ogk7jp2uxHVF2Guudn6vMzMVfOr3t53dsWiGYZUbP+ZwVn1Jaeb+xqrsr/+mbWhgCnis915qy0Y1/knM/y+mJ0MISydUWfNzr/iqlv6W05E4hmRUHAw3jE6FM8rzCJLtdtJ7ClbWC0hlMF0BOWQxRpB+CRRGTUjrWBjkpQFKXJvOZyG3pGgMmyBQJkRZM6CYpXtA53RuGYs7LXEiJZw3XLtDQsWzif4fWR0EBaRkZNHDW32NrhGCBE2VogyUrZ5YeBn7arVr+86EPWMaOIBsq6RoKb50LGahoaU08GzhMQefeONwbMnHBbdO08/ASkLhQL2itqVt27VoKwfH5wY7Kf+wPDQAHS4vLr2rq/8Y3dnd0ZBKaV/XWSXyHB6w/6sJCCq9aTNV99+a1ZJcWZlTX/CHOgcXl2dlwP4YsGnSIYodSBB7x7qooZfedmyZq2GyXK4SDjz9POvaXRMGoxC6p9yt2dyPFmSryX1PeF2KRNZTMsqynxBX0Fuod8bGJ+YQONKnAGlEhJmEVZdNvvI0BD5OUvLyonIQb4dG58cHhsVBzq9fqR7pMyd3xbo11qM3nAgR+ciokRnNliQB5EV9eZoMmawGkxRAoctCbtG50vooY1Wrc2TBinTRq3Fk0iZ8ZJNmqM4y0aMZnfT2dZqc/XZpqbKikpMKcIjlBVM+ib1Dqdp1kyS1Znr65KDw1xiv6wnsbj+LxiRioEquPPJqFW4n54sbgD0+clVDr6rn+oXvnM/h9rOpY9ziZPTZ9Tv6lPq/Xy/tHFOcka9NP129b2c5FCfFbc1fjhI5044NnYXTdxhdSOSFJKRE+fiWIIibFAvp8Wel+sOkQwwEqJSTXlFGUmN0KP0dLZ3dQ97KDqVYS7Kdvb0J0oLM5fOKT96op25WbmwcXjcX17gikXRo+ryC83uTEttTsnh0QvPbOv6ct115XkFp8abBgdHcwtKEzqzdyK95bpbcjNLLgR2F9YubDl/UGsyOGw25F+oOLo+k9kGfwh5PaAb2ZdJu+Cw6/yBSfQY0I+cLMmQza4V6oE76MVhK0RIpzetXjHx9a9an18U7Wiv+sdvuh9+xHeu6Y4br1lCOr2hgV/84qd9fb1sCYkS+vB9H8vLLcK+Tf16o47iFIlk5FzId9hiNmRnrtu0bvm2Q0fKGhom/cGhvo59zzw3Onvu4nVXYPSxFRaRMshPMr60WeuJBeOh9be+f/411zuz8s/seGP74w9HJ0YIhohTBAryb3VuvGXrkquuTTksIaZHo1921TU9na1jbR02q2PLLXcsv/ZqBAOTLcMXT/Z7g6f6RlZWFFhTEtWosveLA1SoDNVO0FPv3Zf3i19x3uF2tLS094yFK+Zfx1Kzkmx047FAmvVMusxSNzgVCqdOnTrrznTiUZSQ9NxpBB/8zsfM5vb2NrgD4gMowOaiu6sTm78/ECTfIsyTShzkXPIPoWOLtKUH4kid4Ri7a685ORkNmGPaZAz+J7WjiJzTWIxhpE+r1kz2Qb0jYoj7LZQHSKDbNBFnTD0Akg6ajdq4jlzLQ0afifsJWfdNwpZBQkFBZQGNhYWxcGji05/RF+SHt++y3313KkY+OEqFsB8QLFJhWsUBfjIJfMJoVVRRb1BnjDs5ySc/QQP1JGc4pm9Q71fvVFtT7+S7ijnceWmblz6rNnKpYDzdrNof6LV6v/opSEh/cdKPmWIUcGJxkAyi0UTQg8CH8MBokDCp4Bw3mZMev0cysusNdhu6yby84uLus8cng57S4syK8pz/D2lvASfXed39DzPTMqO0K2aWLDMzJI7DnLRJm6SQ0tumKaVN0jbQcOw4jhMzCCzZki3JYlpJq9Uyzu4wM/2/zx1JSfr2bdPP/1qevXNn5sLznHOeg7/T7nEncuiw8sGL3kU9zT5//vTQ6Kw//MDNK1xOw9i0975bl589P6ctR9dt69meKwT8mcNvHtl2y2IAkmOp0uljlyfHMqs2h7V6hclqq29clGbFmRyBfHRaq1DPihTyGKPhOFmZBhPOC9P0jBfnRl2DSRkgElnCMVMoF0PhQK1LLwUAqs8uzUdFZvvkp2KsmGdO277xb+b6pu61VwbGJxpbmjFQ9u1/g1p7l8vB4w5dunjs3XdvvukOk03/zHM/X7ts85K+Wrly3OMYpeYip+7Ox+prjI6pS+d/+ZOflEM+9N7z0yMX3trVuLhv+eZt6zduMFsdTqvDbDFNzHkXrV0LVMz85UvP/8c3TemADrld0pXltNcDTDDzxpPfi/oXtjzyXpW9tlhSWWo6nviTr2SiMXKKk1Hfz779DRadOx582NJUr9ZbZ4KJsMtWr5c8+NfUG+kJBTHxP/3XNI88ounuEgcrpbn5BeqrlJYmiNikoTG7WsJ7zyRzCSA6KsUsdiawK3iax3x+vSakU8pMes3A5VH6H8rUWuKPyooSYwR9Ua8zUBhONQK2Bu4ZMGm6OjvPD0yoNEotelJBZq9oyWgWnF0omWXaDGBTVk0pmlGif84nSlZ8LhZ3SGEu4WWTLRTzEZeGu8knU9gW6Ndqm1kTgKWK8gwt2iqJSFyuqYB0TNK29IDiRU7viG98I/G1r+VHR83/5y9Uq1fnUkS2Uc6vRkoFqfLc13RO3rJVqZyd6vHq2XiLwVlVKXExsHGEj66zGb8SarKUk3T9YPU7HOTTKgPzynb9ON+sXo6DfK16nNfqGaofcZxPq2eofsTbarBepMOKakh6SvMPNcMfJG+YVZGAkooGCHQalqt9AR+5/GTNmh3WeEKUV9U0NEfmRmyRaL3B1lxryGcKsUTK5LKqDHqLVfvirnfuvHX18jIRIOfMdLi9o5b6vUg++OPdY3Udjy5tWz4+OuFpzQb9oeHhzM571oxNjCuCsbnpixZiFNYGtdbosLe73B5CItFEBMpxOJu0BnkxW5CV1RiHwLPjwjEZzcQJS/k4N8z8ATrh8ThkFdpJSK5LIWEkGsU20entf/4XjAJbcWJmenKKsMmsdxYMNXpNnjpxPBoN8+ztbW2LehchXFmy7r37Po1Sh1PUpOsrqzRlrapcbImEinKDJREJJeambFCdMHZQkTPTR44MHzzUuWbtY3/45aLRmpUXF/V001UvOIukOgbgMyEZFsBCRSsCjPh4KmW3WnP0tVdsbR1rb38gWyAyVFQZtG5Lw6l9b7z21LeLoYBBpv75lQGl07rxrkfWb9gejiXrDYRefksGSw/EclFROt2eT3yy+pZoh3d2Pp0p2pwKfNrU5hdRwam4od5HZUJbzClwUQOihgWRpLs2y4nIbyvTZ7us1STIJzeatVaDFn0V0xEnSCSWp60YxQEQEAuGpkQT+QJGBN1Rs8pynLUd1RBocAPRrZJFoSmB+aYrKAwmZQTcKLUzprHp9CPlAMztyWpk4VLSrjTb7aUgHa6yqiRtcTUJdU6T0pSTxUIso3EZFrwLshXMn2APNuKB+g0bDM8/X32bJZTNAiuqL0WgB9LmeJXuIYLqVj3C/vXj7DDFZHtTkk/ZER/19vZu27aNegieCs6s/lA84W8PMlY0R/gtzImWDnXwzeqydv26VWa7/kNOUj3CRdmun5md6neqx9kXKyEbHIgnjboqE6FzJcU+TlKQ6GPHVWOJDMkNJD+RigEAEW4PYMnp8IyPQV4o0z+RwN3MbMDWVf/2wBDDUeO2DgxNrlxa/7lP3llnd6El/vyld8pFg6egAnj/o49tKihT6VjgP3504b67N65c4UmkikmFzV3TdevNq2s84+XibDaequ/aRLRjcmIoly4a1Xj45UP+0cxkqq62KRHPULpispgsIKglkjOz85FokvExGFWRcIziPkLDIAzCn9KT8cBXZ7EqJJkvIfV4xX4sUPq5F9SDrrauj3/q06Mjw7ijOrs76xobKXOHtewWJ0sBpmAu35sptoTTOX8wc2He375s5TPf+VfwL0gOwm4UWTz4SSnOkJfHjx069OpLax5/HyqHsSi7ePpEJBZq7u/Wae8/uXtfMRZHnzdR9ZQT3YJSIOqvWLFq8xbUYL2a1iF0Y8vt+dlP333+l1p5zoC7Ev9hMUfh8OzISGH9NmIL2KZIbJ6pOpfSM0ov4qmkjFiJ5Jg4mg0VcYeg/UpoKzwzViCmO4IfEhNxVUVJTeNFNrUAXOB+BDwEalBBWaL6KlFEbBQrwtmGHauuJDvrzX29GkA9gMYX3QFjSWOFBVapMSpoFsWyCenmObHVEKwUaPqk1hm1yYpRaTQW6HelOJYc8eUX6ClWUDQ0hs1glenK5ZRCkzHLKlpF2EjwQE/yG75VeaZIbwLg0q4+naSPcnsSq4lXAjVwnyj2JNVEFKmxoguNtPr9KnEzPlXSr77ytspaOIEJsVD8TpMZ+BDcQard9+7dS/AGhuQIDCnBC1hwlMCWsBzbr371K2pzqQBGEdiwYcN1RuKK169y/boc5AtsHIEPq1/gIGGM6gJ7/ZvsiOP8z8bEFNSINrwNpJOWaHmJi4yfQ+KxZDqbz1L4w8cMM0SuVevzyXx4IWgxqolAOy3GK5lpwneUBLkdhAoKt9+0vLu7o9ak/ub39y9f0XHvzRvOnJtM5OPzCeVG/Uq7a3Gps1wyq11a7cx43mS0bNqyXGus0WtdbY0xwtXO+h6lwYFrkbwMnSjxUxjUrm3b7zh/+uDUzGVnndtSsRB8o7gLkwDIrWQqb7KAk03cCPw7LWlSIBVgSQpWE5v0KkkfIS/FU4u8l7bu9vmjaiAtfvDkk2u37li6erXC7Un6Fy4PDw+Oj2mwQhMp4ctj2Srn8fZiu/jCAZXRccfn/jKXyga9Pj3KGZoJOg88xBRD5EqBYLowNY55DTu98fQvHLWOxkUdmHyOvqW39SyeuXARg+jy0Xfjo2NyjUxlc9/70c/qXbVD584bchnv3MTw+dOjJ07RjVYhx/VKY0KIG/IjN0AF6wB4KkwcnkI456WHE89z/THFIfF1wY6VMZ5CA8tlqHalpI6VmHwBqn8osuFjQuo4QLl3jdZIvzFkloBfYQRV1OwTqiKIDyQynl8lD16sKIt5OvtlAUzC9wYNMNiaggIgW9T+cC6JCYNWX1DIzXKTjoi9uCUR0pRnyaIpurhdfTFcjm/ZtgGWmT0/B1smo9mcOm9X4xc3YkPS/Conr0SVpawOCsrpRFM3ZKvYqo/HH/F44un4y6RwdvGwcCC3iiSpEj2vbNLvfv3CkSon8ApXsMEPFPgBHgWdU6WN3gvvvfLKKyySpAqRZ9vQ0MDvkaTU4PN9WJS6M75JxsL1dZIvSLMhTs5+9fX6VasXvX6Qb3Ke6g3wKu7y2n1eZUKRy6sWfmI+xf7Dz1ESeTFpT421psFBeiEPTbkqPVxzqXIsGkBoJRIpGn47LGWLSlFXY6a60uUwltQKs16bilUiwZRFo8uoEgpl9sTJkNqg7Ktr3r5jyXzQ+9yrs1/60w9tWdl/4uAbE6NDy1e2oP5ojHq7q8k3N1PX0qKyNGI6WUxWBigIkKBSHl6IxMNKj7Wl0aUCQIf4frqQs9gNMEM8EnXZjKFIDqe63eKIp3PJNJNXCAnYbGbp6hReGwsxWGy4dy0ODwSoKCmdrV39d95f190VOnH05RderkQj+H4J+IPti8rGwsOgsOyTxyda+BqtKyamaxf3NSztmzg4bQbMW3gdieeUIdtMqVLbs2T9lq0/+osvy/Jp3+RU0+Ili112rd3GupzXKOpWr1CkC2Z/QKs1Tk1PPPbxT3t6OufHR3Z/6+uFiL9EwD+fsdscFXo5l4BwVgo+RChW0HbxMOL9LTJ7YhUUD3H1Wa4/WvV99ZVsp9m52UrZzIRCeIKcRBVcji+zQxonEIFSpVQZkA/sbVZ7JCzPKUBShbrF0xbQt+hkgl4uJWnwiGWAIfAeaE3aZD5V0soilRQ6AKFLdUle0qryarkra9TmK4lytlTMG2R6c1nrr8RD5RgyhUAF7uE04eNywaeMs2aaCiq3TDuXSRKI0CjV9ETKalUpm1aeoS2dIlW4WmQncV31ca8+snh4Uh3Eq9iq1MzrdZbgIHdbPS595SrRc4S1iJQDFjdinqw9cBdBl2ER0RHmLq9QnSg3ravjDER04Dq+xpLIQdJq4fbrZ65ejrfXr1u9oiCwa3YjJxRz9dsGZ/VrvLLxBawUQaZIdCHNaa2oUTXUtxh0WhyhRJJRO8mNNjU24EPnqbMkh+ZJQ5LPecd1JpvFblYb7YVs3iQar5Wbm+wet2d80ueN+Daae91Ozxc+cOf0lPeNgwPrViw6e2G8tbHFVedIVC6fPjUYT46PDw+3tDdbrYZ//ee/27TjwZ7lFpVSZ7fX2+yNei2WWLGuqfPSibcTwYX6Wjv42Dm0cVWWcCUu9fqGFrPFPjh/KZktBsJkLKJSEYOWxSJg0RN+B1/AKNGqgI8euDL0vR8+XdPStmjjuoxYZOQL0VTGN0dynLOt64N/9dWU1RkplTpXb/3U39TMz4zp5Wr/yOi7B9/SYiynY0jNgkprb2g3u9wmuyOnKWXU8lvf/8FfzM/QBQ8YA3QjZH0OojZYHv78l04fO+Y9fcyirOiUat/k2FrN7aTvSCuqnOQDuUG76f4H5i8OdWZSq7dueeGpH13cs7cY9YmOwAVZQ3fvhltu2/fcc0X/Aq4DGI01CvuRdpXqbMpIOBKeF2qZ2KTJFbPHzksvvYxaSastgul4kteuWUENuExtKFJqILKdxC3iaiCuC6thMMNx5Mzk8uT4kWNkwTIs5NOQJv7/Cu5vdEtWL6MNsUzWd7mQzJE1JfbJjqCXqC6JJ00tL7GYowhm8mYZqPqaoLJkqMgD+URaliUKUsoktRVtXpsv2tVKcqcqDd6xeeAIXFZ7HNNQS6S+5M1G4qgsQjSkPAWbnLVZVjbm5PoC2USo3egaqBSigpbr8pjVp4bE4SXIl7e8svERr9W31a9VD3Lk+sHqEXgGXvrBD35Q5SUml5PfdNNN1a/xysk5A59SO08KEZ/CeDt27OAgGydBYazeAG/ZqZ6WfX5b/UJ15/rb6x+xw2mr37n+yo6IE0rvka84jQH2gv3iZpMHa1yEYIH6IipQKOD2oCkUA5IsJknrwo5q7+huaGowGYoRb4G+jDgrSRacmZinwdvd69Y99/KRjz+x8+Cbl0E2u2tL/7Q3ZrSoT50bmN2d+MAHFqvUs0v6+uZ9IzjI5mdkm+9wJAKHxwY0zc21RMtS6dL48CkQqTGzGhrrshYFYGSj4ws9HSvyOb9KZ+xqblNUVIGFgNNGrmkUMwNwtGwamLoCPklMfJJopMo1nkkMTFpW3vP2IYX61JZkpqhTT4yOhmZm0jPjNOmkZWuexP8C8lyRBziwp7+mpWnvL5+xmQ0Pf/KTZDB//+tfi8zO7KQH8ns+XNEZMqlYOZNAdTQ5ax/5/S9dOn2q3mB86tvftAL/4vKsvelOa1vHyJM/JZxKNjkR11wunon6zY5FsAJkTdiEPJs5/1wwGNixfctPv/4Pl/e/ZaINi1ZOEb6tu2fb+95HFtfW+x94+9mny7EI1i0p4FDfsq6u7sa6vmY3JPBbKGsSWTCD584OUDsejUe1+Jr12p6uDvDClE6RtyT1HRIo4Cj2UAwV5zCOSIInUi5aiwHBQYYT6bTCr4VOTQsHVKFUJIgbnNQo6vYTmSjrJxKfbBty5dUqA9n6ppIWk1OWKeAjxX4tptJaWmZyP8US2aLIi5Q8PGrOaO0WebpQCsURfniVdHJVOUL2bUZh0qQbdMVISp+XmdVmvypPI2QtwX2DLKktmwhDFgXqIFILfmNjOeIxIeIq3XOn1f0qWQsiljbxCBIz8DV2rjMeO2zVj3iFr6QviN9IuBaohBwWJ4OK2OHLrIF8DSZkv/pa/Tk/qZ6Bb4sfXGM/DlYvWj1SvRxf4CBHqm/Zr/6EI9e/zLWFsMXkoOiLEadOAkUCyBJouUwHdhllMbQyUJFPg+cG1YaespF4vKzSR0O+eDjR29NktbnbeprUcpneaJ2aWNiwtm9qwuePh9DSz46MbN/WPzXq80ezuFEMnlxJnXIZaoxW2Zu7jt1wx02J6Mzs8PTKuqa8J4pcVhq0OYV2fo4A/Xg2EHIaXYBsabk7g0yuLbz08i9WrFy6ZvOWNAlscZGFjyxIEUTDolFW8incHOR9Ip5RZrLCXJA2FDOry93U3RmYnDj68nOgWYCPS0YVsIc5NAC1DvOSIeCfWqeev3Lp5//89/mQjzIOV0v7wx/9+H0f/tiRd95dc/eDUuw5vXB54KVv/UdLfX24lL/nQx+58fH3UsVzq0bT1tLmaWouKg3kEPT1di2ceiePMsnAZ/NHfvXiko1hncOcyiSjcwvhsZnh8ckdD9wfCQVO79/nYdXB8syX3I2tOx57OO+yRTNlW0/vunvuPvzUkyyPmaKsccWyTbfekY7HtDIRri9drd8R08+GpIRENFoNSjbeSzJYAfOjkwlMaHayIKlEJw0SUkDIUJM7JuJyJGXQnJZXbrCsAu24DMRRNp+ilZDNYpNRkKTQItlwpebypKSgqco1JgN5YjAh1RoanSZLJjFpHUJf5fJlKllQGgBVEmxNzFOnCKVjBQeCSFH2RVQ6fV13q6POnS/naAmIcoSzTR7Fl5rXuW3FaDoVzxBBgZgJvEtcjCcWmDs5oU3pEQXRS0/K/YppFbd9jbh5y6fiA+mj69+sfoGD7FQ/qlI/Sx/Uj/FV5QGSnSm4PPLuEYChUVCBbEbcwU2clevA/PBh9fzXX/khW/U8HGSfe0BGMLbV7/C2uvERR9h4K+7v2lrNDkeuf3TVJuRiHMMlGI0lsQmRGPSKIm1Xh3CjCXspbzTR3JxAhYYhpv1rgbsslQYunpwZHxLZwhs20Vtv5MpYvdvx7rGRzlbH43dtQvA+9sCaJnfNK7NRg1kh0sE1NGfXXLzi3bmpXq3yjp3v2LBzs3/u+bKuRoVhry2WdU14aJpsdtLjRnLHzp46bDXp3E4LBVDLVq5xNta11HbOTs6k4ynKoeob3CqtKgEWoEJO75RslpWQAisVpTOxaFBYDNIGdyFKuvoWL5CKLfrFFTBxyKgiCspyabE7sIFgXZYDZSb99gu/TM1O4LzUKivZuel/+7M/e+yLX/jAn/5ptqIYPPL2gad/5MBYCXhnFmYIl+76dvbB3/t9U0PT5jvvSWeL8YJIVdZkspcuDHDDwn1C5q1CV5oLHH/uFxiV3AdpZ3SnaG7t2Lh9+8z4lFoYVKLogpQ6Jmj4xHFjX4+zoRPSM9fV0cmFVFCM17uf+HCiWBy6dOnulZ2sVmJCJRq5+oTk30tFd9ABI4xzDUzbUDBIdoOpIqAQsZa5KCsi6iirZSaVwcyGtihf4CeCRHD+oGiqtOh9FBxqtPT9I6OG1Hhs51IykSiiZhQyWo+FJQ4ZTYwtIkvntWV9psyqmhPxZIHFqkBcyuQerW0uFcjY0FA1JIaqHMY7Hr7X2dYYiIYoLSFiWMxnX3zxVTzyumQ5vxDX1jsypZgzSrqNPpBOaTUKIMxLmQISWRjhErFWCZ1b5bbhJbYqfVd3qjRd/Sb71bfVV77AT/gymjZHoHP8n0BE4RoFkPfChQtjY+NT01M4QtEBdXptLpt/5NHH/u7v/l5EqKQZ5LT8kJNUuY4T4rzBSQPGFL89cOAtwNpaWlq3bNkM9/IpG9+/fqvss12bKXEqPmKr3j+nZV8wIV/hEqDBYZynMqQpGEVlnoaeRLN1bncml2qor8EahMISsSj6C9i1OQyItPDVhWK+A0eiy9f0kaVLihedG0wOvd2oO3R6hoSMGzcuenXvWQjPaCDUqKzxuHQGs7vOcnkiqtE6Ll56u7PHs2hxf23HDX7fkMHgUqjogOWSV9Q5uU2lN6tMGZsDeZy1mBq1StPy3vX+wMzFy4dVZWtTxUmFuG86ppEb6zzmQCAMLSPdU7E4HEY3dpNBpP+yEXW4cOZcS0PTca2O9Dy87rAHhVhJBtls2XjrbSwWqAjU3bz4ve8PHTnMs3vaWsA8l+cLEPGRV19uW7NBaXLkSZ8dH2PA1Go9DX7Q40OjV378Z3+M56pl5Zp1d9xtsDmzQJob9D1LVx48f9Ig8HsZ8SLptCxoJJulczKtp3bN7bevvel2svOy3JrVkosGgP3F8onNzs2PjRuvDN35yc/IjEaf30f7WKILqzdtb+hcdOrwOzWpGJJFOAWZVOGo//XUwoRQK+shljD+CLo0XLowCdQaiYe0dcHHRsEanhasQsqfEUqwPXPPWkMqEpTGSIAiKPLRwf8vFA1mWSDohUXNBhsTjSKI5ACqWK2xizRyoKhwDyXykE4B0DbGmbJU7sysd4Vlc/mYh86+OLw05UoyV9Qrlu9cWzQo/u6rXyX/kemA+Pr7Fj32nkeffuZZpUWvmqtgFZZNKmJRESRAoWCPyn1l4Wk3yoCIF6l2PC+UzQ73XD0Db9ln42zXj1ffwmbVg/wKtuEgLEfyDcnrVD/CdfRmIdLAEU5SZS2CWmjwuI4ZFojqmV883dTU8IUvfElcAGebaJAWheWADAXw5tKlS5yQDV0DMxUGR6sgr9Nud3zxi1964okn+L6YoGt3yxl4W33litxSVbOtPhFfY5NWQrkMXHG5kbMl+Dn8RmKtQ6erq/egydhsFhqb0BA1Fo21NjUF/WGMd2NFYTEZo4m4o6FTbVIZzBZis/OzQY/D0NBQG4rH3zl1zubQjE6Zsvp8X0PT8OCc3WSe86WAeXfZbLV1hV37z3zwibXlyszceNFSl3F41umMzqHTp0689U3431HX1Nrmsa/cODU2hvCW476TlyZGr+iNmu6+xWoqQ2ntqdPmgz7cdIHZVEWhSxXCfGo02wo4GXmeVEIiVbHERX3zI1dme5euPHnwIPmpykqBahBLe+edH/yQwer0XrzoJLPcbK2IsHPZYDOvuPn2xnBkYeCCd+TK3NhYcHSqYWWto7ae5xSKrqBhRrmsV8lKqWhwNDJ95dL4pbMPf/bzhqZ2uKFv7fqDLz0fi0QYXJVBpIMXC3IS9lbesn3TXXe727tjuWIwX7K3dTz22c+/8ZMfRxemtWiF8rJJo8yTChAJaOTF6QsXWLSpG3Q3t+eYpjxLJoQI1QtylJRSQaBswmGGcJGLegiB9C6TWcz0Sfdxh4R/WUwQBNAwHgRRowi+vmgBj/UjiJUph0g4B6KKLwtvDWtFPM7KmeVJi9FcJiVgFcRaV5LKLASOgbqgdmW0c9mU+FVFVTTSIKCEWi0U1GIpIsukLUoC9Zy+4ja0dnb+09//E6CEy5avEMtLuXzm9Hm8rDfs2H7g7XcMTr1iIaZRaOPmgjmSJ+EbC4HQqHA6kwchemBdpWBOd533pOcWLxA3r9WPrvNqleghd5pAfO1rX9u/fz+MgT+Pn/NNgeuh0RKfQLVmIzPOarbQvdA7T1PmeYaRYv+fPf10XV09UKOUkrBmwoGEplkAMW2q5qLEvRRUkYqnymREN0LU2r/7u7+jTvruu++u+r2u32T1utxq9T6rr9c/5W6hE/EY/I+2xFtUfKJf3gU/hYKUU1AK6DI7kaWNDbWt7V0TE/NGix0LPh5PEdeNp3Ib+ldu2LRclswdePvVaDJs0DvfOXxl59be3//wTR6XrbbGfrO7KZWODJybITnRZdZt3bnSplPp1Pm77m1rqHNhZ6AXULtoalhhbVh88NAvSsp8a1OrshKeGJov0FxaRnjMmM1QWGyy2cjWtqjkmlwmTS/yVCrkcTVolDGDOjcyOWsxiZ5YmDwEW2wOdwTPnPRo8AuUy4pwwyMPqeta1Lnc7hee33bf7Zvuu19vsVzYt/fFp35Iy6ONN995+wN3DJ44yKzTDVlv0EbDAdq2mO1OlGNABhraOp1NTdnxIZEQLa0paHc044EJzIpKeOD0D//kD5feeufm2+9xtTQ98eW/opWSQanuXrxoIejL5fPtPYs47p+dfuabX99555325pa8XNmxacf7mlq/95W/Ks5NqmTCppLnUm8+9ROst6Q/YFDIBOw2VcA4MD3O6Og4+QXMlBCz4rX6V+xAZOKWRGN6MF3KqFXhSIgpFYlQGqq/shAPMXfIWK2j140AKYUUqEoTBCEsEZZVjZQFIDoT85aVAd84kTqccwTvpUYxuFvwzImwZVFdiRaSZWVZR7Z2qRKFPswae0RpkWnqlaaxSgSGVmTyBSKALvKNU/PzXtxFoLxSmWq10PrNNDBwftnypTqNCpVXbtKUQkmV26KKZOuU9mJFEVMVZUZa2NB6jKQBsRLyvNUlDlJmq7IZN8/x6pGqXsfxKqPCgWiMrEuUfXDb6KJ4RDGYwZ53OsETEGVuvBWWIUqNSJwj3chJDlY8mdJVVLPTsyxrLFlIDa4gshm08C1d85x19bWUfbW3tzU01BPe4IT08f3Hf/xHllYcOc8888z27dv5snSTYprYqvdZfQqOc4T96lZ9K62EIE+oUcck35CwjLLuxlqc1yB+a836Ba+vo6sZ6p8jWJdMUVDOaZKZFFIqTWmf1aVXWy+NHFJrciajPpjMUtcTCJHIEnY4c6pV5unZwXq3va6G+j59jdOkLbgMOnkhk1vWu6lQTpw8fW7xck8hP1zOdhgtutVbdjjc7Sa98a2Xf6mXxaibotiNETCaNAveSZq/RxMBYD3nfXMkjdFvPBaXscS5PZSPiwf3+0T9/pzf39poxUiRRkA8plyttdQ16hpbtj763lwi1bZha01LY0mrifi9B/e+pM1GMxOB17/zH6pPfrRv1cqh/Yf2P/UzdEk1BklFkanIQYlwKUjjsq6/8Zbd3x0CKh1yzpfkxsa6JqDU3Z7Rs2fDsxPyeOjdp388cvz4Y1/8Uv2SJQ1Ll5CYRt64i1ApCSZleaZSOn/4wJkXn5k5dqBtzdq2ZavbFi21tjR/8m/+9rmv/dP02eMmFGOgYrxelE29ki4FMjo66Y1GbMna5uaf/fzbTz/99Pve8748nlZEpfR4116E0QLRcGOsTiCP+IlPiLxKUkmE2ok2wfRKYJkoO5K5ipcSLwriqSRyMND7WB8yIIjAyXTXwF8MOZPqBhHi5JU4HqMJIYf1WqR6W1fSZRRpWQm7M6fVWGKVupQ6UEwlCMLQeRUPD44PlVIvmrSJZtHkzbGeQLIsKZ4aN038oG8mNByJaYw6VtwiDevUufE84VBlBznrRRmunhR9piQ2u06y7FSPiNuSdFR2oHKmGS6tfo1vcISWL6idfTSiNJkJ8aHQgcTLDvg6MzPT9Jjga+Jk6AWEZMmQFgV9Qlck6QIHO6MBQHBzcxNY98T0u7u7gBwg0x2HlVKhAUuf5Y4mPFx9zZq1BDP+6Z/+ET3i7NnTo6PDdGVioKo3VhUK7Fc37rP6CNW3Vf6sMiH9GJQmvUFYDsLEJ3koa7ObLVYDhiJ1FPaoi4QGalfNZgPIMzgdTE0N874gzqQacjtxfCszNCImPOS2Kjs624hh7H7n5BLNojMXJ9ev7vT5Yw67XatU17osU/7pUNwp+o1UzA88ulVOjF7jIAiUzITg823b3xOLJs+cfpeWlTZ9bXCevmUCooRbcnqcJGFpypRQ5Mnrr+2rQxk4cfJcRZGNxipz80nEdSpDTDvd0tqGHKYvSvU5MR/cDfVtLiP9ANG9SPlxejw46YYvns/FfIkFvy5DcooOrfXY7v0Pfej9EycvyFMRFYAolYqlsfWWRx5i4RUJVkpZz7oNB3758xwQRmV5/5ZtzuV99u4ulpWWDetmT5w49stfok/6r1zY/8zTD/3JX4Qmp5///vdWrl6usTv6Nm3n6tpiZfTUGUoYSqHAwO5Xzu3dY3bVb3/o4bV33XXP733+Z//w94HhQStRAXylqJ+k3ultm27c3r54FdUceo0B1+H5cxff/14eqypNq88nSKk65cw38kHSlFSgOQkmpKyTRxYQpiIUQdhf5ADgliHBWlQkizi7COZL62EmTU9vOmOr8M3wtWqQEsJiLeL8+FTBOOFs6LQlcvsLhHdQhBUo2+aEoj6v95WSOd4plFiAhJ/xhxVVOEK4JU6PLBCuf/5RsioMWrRbTDJR48saraQgUQ8aAyUEuLsLuVAy5co7kghU8oSlBb/Kb9VXCJetul8dAu6KHQ5e3VGppqYmn3/x+ZWrVkF4LHdSfE0teKxMHkSeFRn9hd8gXdSSSYzE0WfzWwqlY6VsIBxU6Y3uGvcvf/EMTIhgpD1MKAQk+uz05OWVqzfT6OnixUsAR8OEkmkuRwX9yU9+COo+ZejHjh1fvXp1NaDym7Kjem/X5kz85RGYD3aqTIhNL1QXDD9kNgF6Oj+RoKRRaeua3SRw0cCNSaCaQa/VUMxUELUqogK4va2LFjlyWY5KvyvpzOo1/R6b4Z2jQ4sXNz36yI00V8kIADeF2WpgGFKpqHch27eii3x8s0vlrGu9NBBt71xFsevg+eGuLld4ft7T3EYXoKbW1oR3MOqfx7L2eJzkM+VSBaPV7nTWTY1NDV667Kl1JyPRoWEvmE8rVy6eXwjPhOPoJkYF0OVkZcZyFSFErs5fqbRkUb9/jgIPCEJGV6af/cs/1Or1Z48fIU1ZnmIdoGE92lgpMD4+dmXiiT/+45d+9tPQ+IS9re09X/zj5raWX333e/EZX/OGdXT87Fm94eKeV9QlZSKbWtLVFoa2ygoTPfDMVuw3XMYGtTw8Ox0bH339Bz8MnDy+98TbJYPePz5yw32PDA1cWhgeMtNmQqYAWgeKz/omX/73r83NTN75kY9+7K//+o2nnzm79zVDKa0uyXIKlcFdd/cHP5IV5gBDkEYqPvTA/SJpBgIXlpKYv6pYZcqr6xuroDDTSqLRl5DzrIJkqImljxnnsKBojlGDWVunD0XjFC2kc4RRxFJZAjpHhudGBDNEnIF1hlhdKU8WKOSNnkcNIQoiUqIYKyjIxMM3jcsmW6xRWGXZYraSMyr1uMSCMjK3YW/Ua1k0EQNPEUWF1nzwHajzMDlnBjoFZobcRDNj/sd8ZLmXK015BdkDYWValdZo7DizaRBSlTi/tgx56usEXX18XjlS3ScSikY9fOUKPbSw90Q8Br+0QOXCAQ8QSTYXyyp8AaoNHEw+RSQ1bo3DRWC5P+B/zOMes1nfCobfSURLctGccM/ePXVuJ+0OkkmyIMyNzW1qtUjypr2SJBx4YURl9WR1emouCYA59ZWhUe6vOkfME+N+/faq+9dnjfMgB3l7lQnhQMKAOp2lps6TAOInnsIJplM50D97G7vwYet1Cks6NzG1ADMgKYVnOpFwul0oHfFQBJT2m3esIbnnuZcOhlIBtUGHIZUMRZvqPfl0KRzL1TSZWhqNlCma9eZad31nV7dc63n5+efGx0N33Hdzz1KHwagLhadsdQ2UKRr1ujRVjJmUxWwHr5DCUhx6pXzZ75u7PH5OZ7M2tLYyxOt6eyv52MTwNN7UZYu7Z2fn8ykq65W5dAylIzUh1k+olGloc9gbEqWpHM537eE9uydPvLtQLIIDR0AUm0AgomAbk+JlNcwFAptWPv7Rvr7g3LxMo3c3dQyeOD6wdzedstZHve0f++S2228bOvAGqZNjJ0+o9foVd9yT0OFSKQ2ePUWFhUBxkpcjmZTUJr4ETgVN0dAL9v30R5VUOhwMkEVCdim15KRjQubkpGrkhZMvPlvbWLf6ngfu+vSn25cs2f+LZ3LhYDwYWLN0hcpiJcinxWOfT23YtGHF8uUgZhF3Z2654evkiM3JvuAvWEgj3C2CCcX6A5IE9YfgYLEDWkBRiaMzl7La9NtXLMpmjMFkkb5gMFuQMEROmchUMtSoEDHhJCiWZFaTusSwChtSLjR+0QZYWVAWZbiSUCy0CqVJ5ctlW3UWPe0BhYLLqgTME8NAkF+RzqBcz73/iSf+6Wtfo0hF1tYiog+Z7OYtm2lAj1+hjHnD8gkEiIk1KZeiq3FJ7lSYRMoUnVRRZa8JmiqPSY8pXqpvq6/S7YkcGrEYssqrZIOXh4rxtEDEQnsmPhGJWYL+mnzBXsxaKzKjTKmTlVTFUlohH5jKDGgN5mJhQz5hKMiXVeSLXdb32EwjHtwN8s1btxrVOhU2JFwlbYgz/pLFdvWdxISAt+KxgMe2aHXra0hRRCgw/Fdd2dxelQ/5yW8+hdBEJDfvVSbkPQTL8OHIo5o+WCrXuNqI5CTSGV8gWN/QyGTgkMYOLBSFWcjKBsQIHdzmp2fC81eManlnh/vYxQuNHe67lm3cu+vI7GwYzXvN0qLDTlS8aFSayJLo6XFB7PF47uy5SKYSNjk8RkvmwFsHV69dYfN0GnOkT8/WN7QX0hkAtFw1jnA4qi/qbVraVhHdiuj0qh033kmGQz4Rczpq4pFQcD7oWwgBY+OwO0lTzhZLUW/MbmFh0lIdIDEhzy2Gy65SeAvywOTUO88/b5TlVVp89VxEGPJiqqheUGnu+8wnl22/1Ts+8847bz3woQ/lFSQgKMYGzuTiQdwEx196XqNUrtm8bcWNt5587SW9UT548GA2mVt9/+2JWCg4MUZcNKvU3Pz+J2qXrtPXNbT0L7l8/B2nksxyeeuilau2bX/637+JD4AZIfekdtFifV0tbrmFoQuTg4OjF86uu/1+1tX2LTs/tmJdKRo8f+RAgPZaIpmFvE7qsHSdixdj8sIeLGnIWkS8RAfi+fCVC1oTyVyokahqIslLeDSEmw1faJGU7XIuLi8RISxopVoNFF6bMlPT5IqaaKlpkuntgAFNeVOHLtDiT4eWBOuSM0amKCOIQodTGMqDnlhq80pZViM34dDlxshrteT90USjtmEmtYBI0ykrOUqw8A8Rg9Rqj584cdutt33+8597Y9+bQAxjSrS105Ws8+0DB4XXgxx8PKokxOSyxqxcU5CZFPqSWragExKTGA3PwGNyXbbquvGf1haemi9wUHCgRO4k90QHBj7YWHcZoeKwU3d32/zc0lTGoCgPKJSnuZTekLE4iAzW1rnafYGRmel+o7aHhUj0LqnI4nFzrmx22FlSLWZblS/gvWtsXx3168SFUgKjEFQyU67y8OLGW5qacsSdxfgJyrvGvOJXgsskzfn6I0hfEN5R8YQ4ZmiJnErREDqNCwkBRo0CQV4PkXKrFaMol8onosnGxnogSVBTSd4mg9MX8C4EYl1dttOnpmd9jW6PbV3fqly8QOcbTbY8MTWKJRiPZ/q6m9545xJJgzcoVGnKAmt0r+4+vPGWrfKy69a7bty/+wXoJLIAQIba6jQvzEwcfftYuUDNZ8pscVlsdnLqI6FQJpWocdWSFlVRVpLZUio2NzNxOpoE/1Rnc1gCoUgqmbPZ9AqrzShK98j8v+rBrw4DT084jFwoxBtTjxUuugAyTAyUQpErlnc+/sTSG+/Ip3JvPvvkheOH3RpZy+JlskJl8Pi7IqFSrjLn5Yd++tOZ4dH3f+4Px0fHQ+MXDFrV9NmT0anxRC5FTmtBo7vjE59eesu9MZk6XSktveGmWCgweuJ4a3P73b/3B+Zal72lNTB6xU5VrlLVuXF93coV0Rmvmh70Q1dC/iCRMTpZE5Qg8YglS6PXTQ6cObn3VXtrR0dPfwTwSDB1RColt4x+xd3DhPxjh9I+KeMMdYuAp4Z0FtHjUoQc0CcF8SjllbxWkdFQmpnNo4ImFEU67BkUhdjYhKe2LlPMhWcWPA6n224muVqvsxaIZJDoShVvka5SmIh0sxKFsDhs4A1FqGDJKLI6mbWsMqW0wVyM6geqxzrMTmVedaXkA+GCejWyadUm0sQL+/bt7+jqevjhh/Hg0z0xGAodeOstkpPJ69UAoxkTYJWihKCYcamNQW1u1pjSGI24ComQQZ08oBAwErNVibh6pLrPK9v1FQaBher8/qX99XH/v8SiQ2bT+kRkRzJhKsqOqJV/n8tsfOzhvp7O2vqGJUuWWpyuwKlTqU99bkOCEmd5qlhGAZAlhOXqk2t6lVXfHgN+/fRcmU2S69JLdR9QcaNGj+LT5NSrHWbGDtknpkqSHvyYjZu8zntVbuQI5+Lg1ZWQN3iwSVULxyJ6nbK+1sPvaRLkdtnI8rFbzYlYKkbAIKVqaHDj0YII0rnC1MwskJTNzTsMxsroxJjFuUinNryw7yWPy5NI+mwArRQrg8NzYI5kS1mS3xZCocOHL3zg8a0s3b1tPbF0WKFyr9lwQyRQzEbmQ+F8W3vdyXf2/fJnrz/0wA12i6YiN2oNBmCgLJ5aXdG0MDfvKAPcYKHGa2RsAhyA3r7uSDw/PTUTzsQJqGDbKTVEl0g+lVF/LIbr2oYDgCbYp4cG4/EoyF9SRTZKg5w0m3SlbGtpX3fLrRFfYPzYyYtHDjuUlbd+/OOeVaso750av1Kr17FUEnC3a1RTF057p8Y/+ed/9u2//ovI2GUSK5PzPoYIGHiFw9K5Yi3rpxIEDkXFWN94/2e/QOI190RuaDJffvBTn3y+lJ44cpjM7JQvWA6F9/70pwrfvJlyIaoqC6zPBlgGaQFFnjl8KDU79sK//4vVVbts05Y1d94MnTJpKIRMnFCKrhKBeELJky4WPkLr+DARQIItBSnwbaQynFrOAhqB6Uj+n1bncprXrOpTl7Ozs/6DR86zb9dmgpODeX0bJQTCDuQBilpWVH4LAaFRo/4Aoo3PHh+iPCe3l7U+YutKeVbGIqyvAH5vKnuzVI+S9AY4ZEEBAGwiqzLjRaUAWDY8QtBuDOLDasUTSghB0tjlinipEMupneYSiNPKylwlRX6t+MdvDSZmh0etEqv0mGLx522VmtmHM3nLHV7/FI+LTC1v9pi0mdzdemPIO9uXzRkLCnxdP0tEO++++/FH34uQjkd9yTTNAeI/fOml+zz2ks/3RjqzxU5KRu7VTPZENGTp6rxJj58XOc2As9heH+7rO0L2gVOQTeZp3orAcsmUDvSPxhbBtKKaS9xo9d5Y6qs71Wep3r8kWEQ2r2BCHgENB88VfmqH6C1KWDKD79EIsDmdMfN4uXR2K4X2CspnkYuskLJIjsTBoixPK2GcTvffte3g4eNOu1Op0/Yu6Ur5g2SU8BMWsWPnxhPZQnudhWYHCnnhxh0dLpvCY1UPDVypb28Iz4dbetaOj79mUKiNZmOarJTwlYWFwVR6BwVA1POgiGosdrQwj7O3uT0/eeXiQsA3NTnTADSazZPLgNdChFq/rG/5yMg4RRakrRVlWWYikcS7LcYL+uMVww3nQV1rkzgmNAjxIcHXVVt30DF08x33GCy2p7/615MnTppFRV0JPIjZ4cFFSxctW7d64uRpi1ZdhG5ojpvL7frZU5/6ylc++X/+5if//C/leMjurPVOz1LrHCW9Np4KTL5LRQBpRpi1S/qXzkdinqZW2k8iHZXOpts++OkfjM0oI5Gzr78+ePiIJpEAFZiMvRIoAamE1mRDg8OYSCTJdwgzk3rCtcGFQy//PFWMvf+RRyVcIzFf3L/gVkEf4n8UMP4wn+TOYrnxFuVcPCVzxgcUJYoaegMzDES9y2OmU+o7J0Ya6u1tjc2u2nk0fwtlxm7PWECRJghEAomBaKHQYymmgkpw4PP0Wj3Ij8QdKDJKB2RJtV4nw1rE7ZEpUgkOc7OiVbQyNwybysVsMkhfFsqUarQCAbFKg/hnWauVxCRxTtGtpyCLIrUIlhTNiYq5bAyT90tNI/VbiUzJDrfiz+ZJRfSPrcpy1dcq7/FRlawZBzY+YmB46DKMKKss1asezavrIlFO4JcrhtWqj918Q1t3DywxNpy5fGGIJKdFSxbHz547Wqy8nS+uU8sPy8v/J5eOa4wrBYZclfdEsCBP7RzOp1gxF80szCR9C5FMIhedR6/AEJd7nGCDiOTA1Oi0ZMUgIgXz4hKTSE0ootx/dQy4T+65+jhISw5eXQk5hsqh0ZryWXxKxoA/XCoZydOh4jrnMk1MjuExo2ErYFAiu4caT2gjQFSOnCwFIUuN0tBa15gIp8j77+5cEnXMGxzRI95pm5Ecek00lS1XbORw+4KxR29bokZ05PKOWnyuoPmMd6j73W7DxWNnbrz3nngiho3b0uIke2Hxoj4LNnEFXwbZ+yrGgJgvDWdRNrQ2vbWmKZ6MEe01AamfLPgXfD09jWFfFAxeanHrau3CzpE2iWJlYRzMkXx9d8fidWsH3zogMkfkilW333nnJz4RL5R1BtP8zNT0xQHQ05hIOn2aG+sis7N7f/XKg5/9RNAfzM1M6VjhMOg18vnhy9/80z+590Mff/+f/ZV3bralqZNMCzp44sJU57J7n/zR8Knj6MPojqcMxjgwdo312+++V+uotdc21re0fvSv/+ns7lcO/eppRcjHNJMBRu1wJpbwzc411DUxNxRgzE3OJOd8oliRVUEBxKZ+cvAKGXlEA9GjhQS5ugkmZLJJXOcvd451ZdBzFyK+TgG2MHaFFceCxp6op8LhbrXqOcPkVHDg0jDSc9uNmzCuw3mQI1HLo8t77N5oJhBNgqMAmrYo6CU8BdC1VIKBG5ZOf1GSzKwqKzTGWXNFJb5cNRF8CrXlNejRGNvZDDgzMrMuH01qFtARTGWDSJhGEEmCQTQbkccyhVAym5cbPY60D3qrABFEd/A5sPP9eRkIjMjsIiEK6RmvMtjVx/5NauZQ9S2vgiFJYC3L8wpNTik/M7+wVG/Wl4moqANUAbgb+noX/+j7P/L5/Y+/90E6sRqsxN6wTCuNxdwyBqpYupTPUnlAiiWpyGd2jWfDaKdUs5GmR+4AmoFeY1PRkzurNmrAOyIQCtIJWoGRxD8WTUq7SLYVmijSDr9UlQOvs191h5us8iQ7ktS46h0VXAsXEn+ENWk4TAtTfAYOu0VvZCJJIJID9Sumo1gOg6OcK1osehvsFY5TTDSPPTg55/MtjEzPLZqILV3WYzSYnOayw6afXwi0NXlmI+lJH+7GaIvHPjQwU2PPqLXKnt5eUi8s7i5cPXqFIpsNpONZcjlUeGjrXQ0NBEbMOH4WvEH0O4vNpc3H5fK01WTRGq0N3SuSkQWzThdc8AZ8vvmFUL2rEX+AGAWD0qJxWWlHoddfM5mE4V5MxqPzIU/Povs+/cW2vjWg+tU3tyzdtDHNeOhokl058vpLBXw/KhXenY133enpaX/tO98HO8XT0vXeL/3FL7/xL5GpCdQyYmYM7szQ0PiV4fqlqz2dvclSydrajANBr5Tt/9EPR88NWAnEAbsAiUKrCnl6cvzVf/1nmElpsD7+pT9v2bB9zb0Pn333cMo7VZKriC2svOXWxiUrPIv6QDpEr8XxbKR8G4pCGwTMTwDyq7Zv2aEDxRB3JVMlqS5MIWscj8ZfBCg7zCgyniJ6lBq4kfw8UskwL1VlxDiFXkQ91HW1tva2RlaW9vaGoaHsseMXt29bVwJ8vVwiW9Bo0Zemvat6Ow6fnEJ/FmYN/IrqWy6Qmg1uCMU0wlGCsk0v01w5B+QqYTQKnuEeUtboXKyRzeeS5I7qU5WMMqd0GArRpM6XUFkMJJiqgc9EUlDIkgUrmfuRaWrt2UDMkFOlrRUvXEEtISDHmqLKAlEIpqpuVQZjv/q8vHKkul0/yFvxqaSx6jyu4WLp37yhP6lRtJVkEXQUtVZvNOfSmUsXL23YuIWUNGYejWHvnrcfzZVvNRm2Wc0yMrMYQxQQhi5qnHuJckw7BR9m5BkZwCxIigoefpvSGsnlo8FsThUwmUuZqJbKNuQLN1ddt/kjOFCamuqtcpPV26u+5bW6cRD+qq6E4uFQOMgRlRvA1AOMi/vJKOFCE2dXODyA9GgvD4545wKU7bIY+qZpHBxMZ5LuWgeLgHc+nCjmbrl9y4njw8ePHrc7LI21jv7unguXzvv8SbnKMJ+PtTS5Ohpdk/M+vC9r1i0NTZOgrPfNjMeSp7OpydWregfPHfP7S57WLrtzqLnBOAfRQ1QyncFiwIjTqg0Wvdk/Py83WgFfMplqQASGJui+5apzR0Mp0LobG1uSWdYeJo+agauOGTFvMhmdR47vf7djzdacyb7m/gf1isr5Y8d2PfvMpk2bzo1fabDYj722S49PXATflFQG11PdrFVl1XlQeV39yz/01a8vXL5MNeXU8NDFgfPbNm+99aHHYiLULYBQBdopYjRfvunu+yITUyPvvmMmAC2WCWD8VDWLeqIoCf5pZTL8wnf/9QPNLY6apts/9IkLR9/dsGmrL5PacustcpVufm42752z19SDaUEGesVikpGsmMtFi/nlN2yjlMltM4HxQ/IqTwSlCbtQIkpMW1iOuYQJWfooDCdiATeKUDyubPzduQjGIaIUIND6uho8rrjfEBSYHRPReGtbwO0CtRijsUA/aY+HXNyJrWvbp+YS0wuZRBaUGlHbj2+KEB/4Wbh5EBR4qgB7Ir5TpjUHgIY4gallAsKGcINwAJd0YIKlwH/KK52WMok/CQgYbZ52Akj8MrGNsh0pWcnNBiglMYBwQaU0hg2+JJ1Sx2wTaWH6Ubs4mbSgVOm4SrW88rZ6hMnl2aVJ5oWFlrCMGHl7rnyL1VJr1hWi4AMKZYDZgjNQ1KMxqi67JbOafFrSmgSLCK0Bz4jk70UFwAbWq+0FqotxUCupFEENKhexZwuyiE+Wwz2Y19z0PqtRf7moWH18T55iNC4g5KMkICSGFKTHTVaPVF+5c7brN1x9W2VCYUvwj8IlBhIFmJQvrdpIuFDdXOt2uJWkOSgUzY11TGEywf0oSMwnPhmIkUBaAFh6fGJ8cX/3g3fdkQwnyRiY9lbOX5R56h9Yv2nNT5/cW1dn1OkMAV8EbWn98saONdumppM/+eF3wuGs1eJasaFty6ZlC94YgeCSPCIvW4UOInIdy+R64Wy3GGvrWjqwHFh8zDa3ze2JxSOpVC5BOwedlZiW1WAvlebDvkRZeASNyYQfn40YbzF5pMPKaTT0zM9/vpCVxwIL5PoMvXt45Ng75w8d0GeLF3c9b+roqL/zIcqFTu9/Ob4wS8bmxKnjKf8ckMZyLU4TFSFdud7StHYD9VvNm3duoz9UJPDOrhcunz1DMxR3T6/O6mCZgxbzVtt9X/yDZ/8hN3/yqJa0eEV59S03Lrrx1mg8fPKF5wMXLiamvIeeefbuT/5+y8atbZs2AAUovzSw59mfRaa8kxcGEunYg5/9fPeWG4xO98f//Csnd++enZxeu2LZ6ttuAewR0YiFzvIAuQj+q25iliUXgCRrmUZMWhDxEJR8UVXOFLKUzCcV9NsyWgm2OuzEnPVWC3m2xU2b1r71zrEzZy+tXbPUYRE5Q5FwRGdQEmIM+8ebPDVwXGQqRkSFS5CxwSsxErLR8C9D9SmTQMjE2lPEC0bWGtIkTBIUiEKdoGY4l1GX1Y0pTQxYDRMgqxilSuDdBJXmClQHl5JZY7RsK5jDumJcnoEu1ZRWUcehUxtJSeJCTmw78WhVpY7prG4QLnfCK28lufNrW4tjwieJt4b8+Ejk411tqWgkBcAccS2psQ4Pv23HFr3JhH8IBxWs1tTYZJ6Z4TeFXMEqvOYUC+F0zJf1YONVilaAkNUAnpXSoNJr5LVRozZXjhqN1lJkRqG3D0ecR7PZviKJN8L8FrckShGv3jbiRnBgdaveMHde5UDu/PqDXLUJ+TazQia4UECo8dVo0zhg9FpcNOg2wYWw0YCDGieJGhRXQnM0NJjPAPmbSRZlo+NTZNUsW7ZULtOt27RxYHzB3WBfmJnLlOkG0wz0E+h09EgzWoj2p2tq62yunp+/8vPz3hFA/hbVGG4A38Vd/ubff+uO225ftKxjeCTg8VgvXZ5ta2kHpTMtUyK3FLPzFoclnInK1DpLsWyzku1Az5UZnVKTTAKYiflXGZ8eTMUKRKKIrBogNHDdJW2NTK5gwO/1ztf0r3J5XDOjI8/8498rY34DCd4yRSKY3HrbXX2bt7RvXH/5/MmYdwa8FEUuOX9lEPgFNZWNLjcxaEBXQIhg4EizPntg35tPP5mZn8X56zt7lEJ0jc2x6ea7l95+LxmPMOTG2+549tQJnUhcyVNkmFbrSnb3jsff8/p/fCd0eXx25ArRVlYKeSL60rf/9eSe18u5DHJXQ7ZqOb/rx9+paWm31DfLHZ5bm9vIYdSZjAB6+ObnnCZN1qYz4EpnioRXhr/SrONVIxdGCkMJt4dGk8lUU/Yo/SPBCb2LtAIz5Gmx0DRApK6F4xG8axodhResvtEhy/SKlV0IPHRLKAEjf3gE376tvcmUzsVi4UwoJZ5d8CGLINWPYKIRkkhV4FvyrbFik+SagruG20Uj91Qs1pxIbWNhsxoNWdS1aNkok/sUiQKSVeCjyd1A/QI+RiwRBaeSpVgCO0xHTib19XJZRJvVlxVWEnIENV9dXSSRWl1WBFUL6oair7JolS3FcRhBcohQD5pRxZPo42aNijwd8ulYx3FzPPToI0QlD735ltvlbuhs2/dyqg5FGu8GSjmSGyGHIMAhXKnkjbnJ4rjB4kjrVPpsrYFwmBxEAbLvrMUY+oBs1iw/UKtduUA5NReV7kgK03MLzExVQFSnifvEzcZE8bUq+4lbFXdPPpNwzIj5JNtJfKZS2SUX5bzPj95A5HeBVcZiFrkPlNlpVCMjEwSswzES+qcT5MlhHWbKJOxrNdr9+06kE7KGFvLLm/OwsVybSaZsZjtL6eBEaCQUrXXblvfUG/VmejPM+2J/+ud/TI+CUlS3eNGyRHQoV44xW4qKA2Wdwbm8MFCXl1ttWpPO7rCBMgBgZmHl6p1anWF65CJBMBsgpA3tsYjf6a6lcgmvwvJl/dFIBFdVc1OLyao8dY6nFKQpFg05/g/Vuhtv0dqsw7vOVlJhi0ErwKUBgVUq3tr1qqq+ftXNt3pau2fOnsEjIFAoCJIZTbd/8EMqm4MOEBJ9VWSx+GtP/ujc/t36chF/qUBCyxYr2XDUHzgu30tgUK0y0R9DpLBhjZWLJAzQZgxmZOyVelNtS+v8pRFaOCgNlLjnXvr+fwztecXCxzrheUZHwe6ITY8f2fP6HZ/4DDgDiUTo0uXLk5cGM775ybHR9953963dHxWRelFQU51BoaqJyZcmlAnEOAS3KRoFEEOgNmB7q/IaI+gfag0VAps2LKNWNBiMkM0MpSEWN2xYc+LYoHc+4Jyz1tW4CANazGZkK9hHRBQ6OlsXNdnDJu2ZECDLxK5ZGwQhiqJ3ssAgRrmOe0mAB4RVncqpMjJ8NkXSv7UEl9TUv4A3QwWnQ4ZEFDmnuJoA5cYwc8iMCUUhpc36dJmyUQX8NutOqlJIldKyUB5InJJRRwYdG1En5k8QsbBvrypywnaW1G9GoUr8VQOyStcCGAuUOHyNibTaagLGA8HjLpY0vvlLV65YEWok/peLvcvWWK2uj6xeqTn4Vh73MaeCeXB8lUpxRtAqD5dSyXRBaYun8iqHrAa2U/gdQZ+ZeS8rk3k5Sa5tvSZr5kI8mTfCZPyen/Mt5AiKNP8LKSnkpCRHeAah8fJeuhLXEhekpluyCatf0+t15H8RdgRUlDpscn0gYuYDyEOTxcqvQWSki513asyoUdPAgKaQ+OBAXAPcd3xs1F1bs2f/Lq3e8cBj97/80kunTgysX7Ncstg1VMez7rtsLBnoaJVIxDd0fthpaV+8tBOGKSaDQFBu3roiHI8GFuLhUJpmlWKlXQhbPb0OtzsS9jscOsxjSlKnp6ZJVwR9GOUgHAqRV0Civ9Vomp7OkndXY280WQViHgFpg94onp6nFISjXXnTnYs230AYdn5qjAxpOU5XFFVkGwmc4dDZPbtXb73xhscfn7o4kBgdEIU/zsaP/MmX65YuJw3ghW9/a01v29mTp2ILvjBuUmrn8JfI1YAE8jhQp06hTQR8+URE6TYy8maHU8HVk3GMIAoaKFAyoq8UK9EZv0FlWIjGMCD54Z33PBAaOJdemBWTRE8FmJ+dcrmzrb6YCO7+6Y8uHNifj1OuQIc5vMOldDhAfgkEJciu+k+aYcgRuuF/ppxpJkRBoinnRHXE0YbY4qyseMRprRat02GZmvEy4yLdNBBgkcwBLp8tTUzM4NEx6SikiFOFQZlPY2PzxOi0p8ZFiNHtIPgjCmS5FL4DqqfBYAMNIAEiH1W+yBR8zSivFVnGRL5/mdQq+s7GowkcBk61hWcrLYiIHwn4AsS7oCaynwc6CMwiSyVnwrEqSlZweVXminTVQ7FnWagWWwtJyoOJwDdmEFkHItmSp8dsEymtQjXnnfiK5EWSiFw0LOR/Fe1iVLWuAj2Js3lzuXJnpfLcr55rMJtJeaV/1jvPPq2emms4eqJDo8XUA9hckIrwWCrphgnsicKgsGntwmZUadOCI2TkCuXyVK8zncqcJn/stXm5M2WsKNXgtWSEnCDjVMhfbp44F0/KNOM4YJEToWnWxmurAhwoumwJaDse46o6yiNSPYb/OAXoEAEHsN2VumAY10v28tAovSPRcxgVq1FDS9VULD0+R+E2U05/SR2FyX39vT09XbyeOnH+zLGBD3/gI5FwEFkeBkQMSi1Rt1ZsafKUMNoqucDsOBhmuXK8o72V2qbdrz23YeMKKp1C8fSZcxf1VodMnUNvSkRj8wtyV70BPYfMQhT7aGwWKBSPpxP1LJ2IoB4nEulSPjEwPB7wx9taWGZxKpLRoTTqgNLAXyfEC/TprG+8+T3vBenWTOU4fZ0Ya2LOJHCCcsGMKjQrN24FEqast9zxkY8/9dW/KGZyj37691y9PVeOHz+167XJd/b7DuHaoHxJZq1x60x6//S0XoYvU6gdYtZklUwsMnTq2JI77scLaa11L9qw4eLu14nrTF8azJGrXSgfO3PMNzbU1rfojo+8H+ApypIBOcPBBpEZhBOUfmyUUysWbb2pxu741uc+HZkat8sURnQAInA4QETEltsWLngh/cUiwS1x+7y9uuHukzJLAafgZHxXzD4Jx9htmXSso73B6a6hTr6jsxM3ODdOeR0ugEWLMAGmF3xRhzPk7G5HtEA6mUreRP6y3UaLGAFOo6hYrcAMINyF15cVCeIxQXNqyJKWduLewMPmhlR4/CPFRr2Np8sHEppabdpAFIMkb0tjSBkpF6wWGu6lnEtaJmZHCjhs8Pvny0pmzp+IkxkpcgvwrFS0AKgIacOiUqHfmywRB3WYhVBiOUadKZQSUqTkUIkrBR9KwwGGh7pAX0v6zeCsw7iz23xT0w6lZifx7qmZ+b/6GxJQNMWiXakA67YOmlGo6C+JxUG5ssTRtPpCFVYokmAxqMsJdQGYnYqSO2AWonJyWhUlkBvlNNBoxlfnC8nsRqrLYGFp1SbMRgWPqJZmJUQqipuSpASvYqakN+KFOyYVVImk4J3YhFcJs5AMQVUJ1wsSpxxFiluQZDYLZUEqncZlc4ZDAeyBCfx4pXIshb0mo0DrzLnBG7ZtPH3yLKipBuD/ShQbJpb1d05fmRqfXDAYza1N8oVwFAFhNhlJ5fBO+NesWXbfAw85bOaLR/f29/ezuJ0/NdrZ1cW6SxfnFS1LlKWZY0cuDV2+2NhAGWYz2IqpuHBGsq5MT4wj9Wvr7dOT40g76COeIMNOVH0g1P3zk6SVykzKqMBvFrPCvGEf5uOh159/efOa1Q2emsv0MwQfmhZdFPQUZK7WrlW33BKGuouVxhUr7v78lxx6XWNTw6/+9Rtn9+7V4RkiWY7mm2W5Z1n/zQ8+ND0+4fvVc7l8ksJC6mHwgjJahOrOHDvef9u9BF+pKb/pPY+PD17KTY1OvHv48pEjekJB5axZXmH1pqYJiGBlvmSw1Tz8qU8eP7ArH4wlw3F0x7vuuHf91m3/9pd/lJ4cpS0E1UIskPgJIHL0WQRplcyY2qszK0XSMT3FDEobSyJ/+WL1ldo5CIPlSywZpdzwlZF8LqXRGdFbEWc0eMRcb2pwz8z4g9HY+NiUViVva23kuARXpyJM5ffNI2mqlRiSAlWV5fK4nBCx6BFIbxlyJoXHVQdGkRqsUGNBHcKpzNpdKQe9gQImer3Tq8/qNXlHzqBMFeg7NauMYe6mZqOAawkAbxWZKiIzTo7NCpARNdy4OkgmZOHXqGKnT01+6xtVyVJ9TEG+/C9U0Wsjwc2JhUxshOFs8966KgfkcxQoqxrry5kMBU07uVtRaEUNM1quMBRYZ6lVoE6dFUksW9IJGd+8sujo0fpni4mwkR9m6PPhxEOnrKTBVC1hvheC2uC0xuTRTk/l1O2kggtqI+Mp/MKLibffQt0Vc3BNPEi7vBHHuGfpu9yEGLHAieNXmRDlBLognpnMZqlXIipCM3N6vtAwmQzJZCqLNKYLud1qoisyg4UGyBCF4uQaCVKhpCgUjZisdhY0bHpFOUNn7FdmJuPoKyUZ8IHRZEKqxymPTs/H4oWlfYtb6l2XT+4rlmitXY4k5wcGLys1ulWru1QWR8iXwA0bjZ7q7qmfmrwQuuhZvqJXq04ZLOZweOHcuWMrl62XlzUOZy1esImJK+2tvUaT6tyZS2SYLu1vgx5GRmcX5m1QI1MltJZifs+Pv3dpYGj08L7b7ru3a/HSmcFBtEpi367WttW33ZHTGsnyIskikS91b9ohi/m+++U/8V2+aNIBKQMR4yuXNS5bsvl978ur9fWrXLc7HO++9HJyegoYUGx44c1VqjdvuYFyT1H8Q5JqbeOH/vKvjrz47Nk39ukEAmceDFI4NZ2m1U7B4ESzIRItW7RypdthBy7e6HDTlcntqTn0wnPeiVE7sT7EIORCOhyOQhk9m4rpLDUGkBAhAYhFmPjSuoBsFWsif1DIeMV+Z0lksmFFYSkJGKiCy21f0r/IqCcXh9k10qVJZHFqtIQQKnLt8Oh0gtYiqezI8BR9XV0Os8xMd2qZoagvNjeNjoxTasiZBXYGEkcptyrBrC9kCKnSIkpUTJDiVqkYBfY5Mo8mHBSxyUNKfUmZQYqYDaXZpEwnCzvkpXAG32rKVJaPzuKf1NUbZaGsyIqJxmmjjYnBUojjNK8sJ0gWQVkQbn8KDXPgdYsKxuoyUqXsq3T96z8QuFiO+JTOwLk0ug5WJOYr0l9hNJJhzWAIEUXaEH/F8LH2iZblAL5yZokxBLlwEaxfzLGgn7anwkuTKIq0nlBAloopI2EUSCq+6ClCBxv13Dyw68VEJYeclJY9uSEbp/mV4GWGXloGpPsV93X9XsWtioW7pFViPAi0HvEZ5Mi6SVIMWgVBJ4Kq0WgyGI4v72tD2WKsLcRxdRrvnJdLEbUAeb4ks7C6+mLx7bfuwPwbHh45e2aQ+BaIGJV00mWnbZJ2ZiFIIzybmVInOmmUw7GsPwJmiXrLFmsuMphPURjlqG1vev2X+7btXLtscb9eb8ypdBWA2FXqxkbbmmVLY6msgoYJ9Oa1N1y4OOqwKLbfeFc0GKbegk41NGsg73d0bK5cJMPEv6i7kxU9mRQUU1/vRg/mDok8ZRLRufFhK1phNr3r1Zfveuix6NYtC+NjIa935+23T/jD2VjY7PTgl8ZMgZ7nRsbmx0ZsOnJRhbJJ8Kahq3vzgw/k9ICUl6E0Y3fXzvc89tq//ns5HWX0SFW96T1PLLvp1gg0hPsQWO9iyVHT8NBn/7h/404M4okLAxfePeSbmVyyYm1LfeOlgXOzVy5v2bjqB1//2uzFYbO78fEv/3Ftz6J8KXv29DHR+1fI9LIGqA8cLQ31izauVRTKA+cGvPMLTbW0+60ufZCPmGaIpsqN0sIsyBB6EHNKiiS5K7TwLlCVokolg3R/pK42GU+TZEcQIME3BQy+dvny7nCMulVSKZOXL4339Xe4a0w8NuLY6bAnaj0DAxeEAUZCB1RCNaBS71Yo58oLJorW5LpAPgwuTCWR1eZV5rIhpEvpaZYKWDAlVwCEABacAqGbtQVtIovAUKTVthzQegUaRJK+Ro4UiG1lg+AyV1ofAgxGXdLqtShmVRqGXqEZ8mfEA/8mLfPmNzaGTHzMM/E1sTCxgqNAQwv8RqjuxA+RypAE/1dXUYaFunrqaaTfcFzoi+wTKSA1JR81VYo6YebRqaWc1WjN5EtTbC20ckIaNAGi8Qb1cBUS3NMgUaL8w4ciK4inYkeaBXFH1fu6+irecwpuVarm5z6ljBkOoSSTngvmG3dJSCCfISZLL06AJ0GkNKJ0gLNWSKXwE3rsNio3CoW4LpvF+uIrJoPDoPfcsGPHvr0HP/yh93a0uM+eOjM6XKQlEFxCbgSnFt1/laTlCyhoIByANrLpczW1Fkf9KgLykzMzDzz63vHhsXw+2thh6uygc7qqratjcsY7OJy667aVOmvp/NHBxSv6C9lYZIGMIpG94bC7YhHf6JVpgjt1DZ3trR2I0oX5caZ/ybI+f1TICOlh0XIQu/j/8zpZxTc3Pz85tfaWm8BZUIdCr/z0KQBMU8HA6tvvdHb0pJFrVLQbDWXq5UQqJGU/AkYRrK1Xv/dtld22+Z4HDM6aYoH6AIOSRH+IKi9buf3GHY++x0d6gJq68ZJVXTm9+/U3Xnhx5023LdmxQ21xda5d37FhA0xionNGPvHaj/8jNDh47HktsU6TTJHzTz31Z3/SvHLNez7zyRWr1kwcO6ZnwcHoAZdTId96+221q1eiA/m8ge99/0df+as/plBAzOS1TUhdaKcqywUByhBMfCjt8iqzWa0dHS06HV4i8AZJ+6Fglp5metQcuMpqR3UHuTsK9cD53gWfzqCx2XuJTgGohz+GpQ/Ln3JDsVxwVxV5rJAMRkIujyeeCqXwWUDveI9oZiI6j6JDKrIGjZq8e2r2aBtMKijObdJ/43Lcp4wn+1F5ETBNJSDeBhb6HOuMXKeiNB9dDzGmKaup9pIWJZ6Gh60+b3U2rz32f/NXMKC4VTEIeEQQHFACrjQ6jilIouB8PCm2tuQKkwZKUiKunREHkDDOaK0hCj6B0aTSA0cQqI8MQU6UbyEPELcs/AJ4sFhJAaCJAUgJhmAt2A/G+jUHXjvtf/FXcCA3x0oopg2Fm1pNLTB1GB9KVRwnMo0dSCINRbCZif8aQLbW6gtpKtqTfrKL03kCh0n8IuXy1MRMgMSxGltLWx0enYELo6fPXySzg0LsWDDitjuorUYzwd5AP0ik4uilk7RKC6VvfeDe2XBk33OvkvAYipZ2vfHWhg03yuVau9MT8E4ysdNzvlgicfbI22cGT1SylhpPdnTiXMqv3HbPe9wt9Qszk2fODZPlA+GfODO8pLdtbnbS6bSuXrN4dm5wYmwCyQRVQZ0if0sOwDYhLRxslcOH3jw9OuQwWWdHpxSZhE2rOL375eMH37r9E59edevdABTVtPWsvfX+ky++qKMFNLdNF3tK2+e94elpGhquvOtuMsSDoWAqETUzHxrt2lvuFsmEZerYgtlEYvj8yb1P/VCeSL3+vW++9eJT1rq2O574WMPSFakS5aS53d/5t+Dl82aCx/EcySJkuJCiVIoHLr/5+neC8+/7wy+F/A8cf+mXIj1NCGXkMDFcea4ka+7qDk6NiLWIGRPy5eomVFF2xWFRwQzdUckuDkBGOIqzaauBHmQWh107PjYOUjhYK6hb/MRqow+iiZ+BwGK1N+5+41Amm0CALywEvXNht9sKJYuMYtBijUaGUBicaAZKZTSVJD0tEYuZrNZoMcJ18MyQWowDNF6klJZ5lplKmgSQaXoloqmQpIRaQ02TAF3GIYhNpqE/WUVjpL6kCHPqslqMcxgmxiwRa0QwiSVMqHiCoP83G8wHaQvqFiqf4EWeVAwYMgZTWFA9Kj0iToQLxCCJQ2JjuHkF0wAxUsLtJgffBOdbvkgWEByMQlSmgUaaPHXKBQskkpC3LrKySdvN4kzCgwRuB+ntDKA0Q5xVnPC/3yBO7u6qOoq/hBhkhsRfjDx6ehl0ooqMeI+G9RCpJMtFowoHoARaP5U7RqPLRn19vhxUGs3mfC55ZfB8sKmpvt51aXCC5XHthjWzo6NA9yDdGArgg+jFjR+bAdHRgU6rp64imFn4xXP7zU776Pj5RX0rs/nEitWL09lkwB9iBR4evKSREa1Jr17lCk1O6vVRqm8MStnmrf0vP/mLiyeaNzu2D7x7TFVUu531YEN6PFEgXGE2Wr6/e+S4VpvWaPurz89IItnsLc2Uj9rtrkyx1Lh4sbml2WgwXtx34PiLz+MRs6oIo2XT4RATgrGVVaju+fhnlyxfte+5p6YGzjrVgBuKICoR5NjE1MzAGZTgoZOnlCDzyuXC9093WZV84tTp/d//j2IyCTY56hj2E25FeSTuD559/jv//OGv/ovO7j707LMnX37BhmpVBsRJ2Xfjjc76hiMvPFcuxeiWOT04GEynt95336l9r1eIugKVW66ce/OglVZ1DQ2Ds1MNSGjJAPzNCRZyXppMnld8XCrZbTbSvgSBCchAZCugNrNGQ6PT5aIylBoLIPH1QJbQrJMgRrlMAg22BqYRRbTwiFBKL4+ZzUtNJkOKBgdUSKMLUdELfXIhBVCjWgFMgS0UidXa64IlP8AwFuwIyhe1RTobJ+kfYZLnKC0AGjOjJJsipaZJmkKXEE3UaMilLqkcFS1IYkWbXG0kiIiPhIoNlYIUB7BDyDIrZoQaJRz7bFU2kXb/pxd4EMIWiisyiV9Du0IzxF+IZ0qgV+HjxhTE5gJtSEXZnkgigyOF8MIOuEmjHKiUB2JJs6kg3CT8QMbiDAY9jXRFVVihlCLVTTTXKcGkRM1IrEYzIZOpjAAbKFVW4T7hg9+BA689igT+yxuUDu4zEafPBK0ZCMtj1Ai8t0gsWa4nUJ4TyRHcCFq728PjwapobkSmauvqkrEoUXKwmYeHBkx6x86bd0aDqZnxmdYml8Wo53bJMjVp7EDThBMUoJEsryLavmbLmvHpkMnqWr2yt7t/Y8eiRZ2dza++sgfQkkjUv//NPTu3bqBZaGfnismx8Xtuv/vVPS///KmXdtzfd/r05M33Np05/8qVgROtnb34GQbPjK5c3lFQh999d3p+Jsdcvue9a2eCyBdEAJKPBE5l/+atyvZO1mO8mSLJQFBcqWvl8stvH5CFQwwuohfkDqYC8WlQlk8f2Nfd2vLQ+z+876VfXTlxDIANFHU4zT8+OjM2zAgg7TD2hMVRrtC5dPKdgz/7p7/VJ2OIGpVaZ23vRb33jY5paIGkLfoBJz9xpK6j68AzT+kxjUg3sTlXbty8/PZbOO3wsXdDo2FWOEdjU01dM6UoEiA4iwtN3WXR8YlTu1+/+Z67pi6eb17Ui54iLByhY3EXko0jdoVMF2YbjFcuA/7L2pWhdqVYItlw66YlPB9+EEGbZOBhdIjcEJlFbaDFFQYFpwR0vH9x5/CENxIMUy8SDkaGLo9sv2ENCTcIaNGyi/DgNWYgRYNQNNnFNYZ6AGhMsA4pJTRIk7JO0Da5oZSGltiqYoKoHzD6wCvR5EVHrAXvOyxXoK22EiSLQiUmq5hVGTX6HQxOoLuS46azuDRNxCCrKuU1Yv3d/2IM8qDCrYUJQDkD58nFY8pQgmCgKHUmQZUcQ7Q/dD+UR2lRFCZkRbaoVPxnhdFn179o1cdF12+Bh0gKMEKYwjeKtIpESSuZiq5cyODUJzIBcAKesgLfCyoVf+mf+5pC1kH2r3AB/S6bEBbVkeUmha1P3gz1HMTgSbQgrclms9GMgEbouBpMJp0v4Af0xkwSI8BAhWyNy+6PRFatWXb5/NkkxrS3TLR/irr4oyfsxnJrrRHPcMAHnHnOQoo9HTVKJZqeox7ZbAaK9EevxDbcsGPw0tmB8zMNzSW/L0uLz+bmlo7OFqhkybJeGlmnU0NyhctR3/L20fD99z3gjytau5t7+g46mjotra46o7ypc+v01Lnhc8e08p7WVufBgwfw091x185INGU3UZd8dUOeRkPh2k4NAC8i1xkiQXXAKhBtbtQoF0TN9HV1bYv6oVucdKHxy6999+vk8jb3LfvEF//w+WeeOfLySw4sCqriqGYRs8Z4QfFignFskrX3yo++B7iNQqdGf1h3xx2dW7ej1h97/sWRQ2/pSwV9SUGgPzA7W4jFHc31y7dubV6+0mwxn3z7QHx8PDI7zgRoza47PvRROVWcTnXXspVDB/fRFAVDw6BWxccnXv7+97OhINRCmSftb6WVEJZCjguxiyNCGCI4akGVKZZdThfth4eHpxhtcOtC4RDrHBnS4Wisxu2KxyL4exx2WzSW5je0Rkessxj09LQShI+HKBnFm6/0+SPUitTV2wsChrAa9oD3IW8MVoo2y43OJhLcJxIzZpLNcbwZzdF8hPRSpdLcYnBMJCeFZYNrnah+JaMFJj+vzeiKJI8qMkWy29B3BHKpFs0PgaaoM9bMZv34XpQsknpC5BSYCMVSYnxJylybzf/+L9PKKhjMFvRFhZV1lxVKFCWTRpC3oJkwWIgf6axC1U5naXkrGIbh4zirfFlpyeWteZWnVJxmdHF9ivsDLkiVIZKBBqMAWgd/NRB9nJaurSJ6SZWJnFAuPTPV6pTwFv/39/gbn0prPEwobUKNRVHgpoVwFQmsFfXCvI9O16I6lr5fuUSjxYZOgsYCfDWWCWinzS2tjY3tBSCZsnLaT+dp9COXRyPzbmsN6FMgduNfpaWueHaGQlEx6BSxvMzpdNW4NDO+yYMHVImED4SckZGBi5dG65rMHe2LPJ56UFaXr9jgsCPNx6PROQf4S3nfzLRmww3ryUT50Gc+SMlcRd/4zvB0NjFlalDDOclS7viJmR2rNtmchWhkKBwjULBDGleehmhMZWbwnNJg0ja1akyOLDnyCsLBhalT5xLhsIEAptXx+Jf/zNrRnc5X9Krirpd+qYoEDSrN1LuHf/pN9fu/9KfO2uYDzz6VCwaMxJcoLWIWSbUpyVdsu3H7Q++fGB30z0xayP4UNKiwN9WlNbTHLK259eaFwYGCz0ufBkCEhy8OwLlam7G1o/PyqbPJuamRE0c1ecolVZqG5vt+74vtK9YmCmSua25/4sPesZH0zBjxYOIjVBIjKfAWEAhFcWKtvb4oickTU16ddgQ/pFUiPLh27dpLl4YbbGYaS/ETVGlcLBod6CoUwWGf0faMPCgLOCZk2NBDBso1GAgGl0xGGioLQxoszeErM9RkAOcVCkawCbmo0O2wsQUGAxhVsmAqWGOpNymMM/MTepmhyd4cLQajFNnEAtTmizL+cslGYJyINAWHaO7C0QXdlViJKL0j4C1SwQGfUhTnEgusteaSwWJ0RfzheCXnrrXyOFefTDzn77QJm1BeAbsKJzaDgjbHBYnAEXUUVxabdE5ck7j9hb9XHJe4svoR+p3w3WCb0tAO05lyTExjQjE4ZgoKYZ4T9CEVXnIwwAjpMhm4lJhIFiyrLO6e35od6ZL//Ytgf74hbEmA5bVakZrIIOFzzeALUIC2Fo5EkQQuhzPLfigSQkOlY3IB0O1yGES7WDhOOXww3Lu4HzAUu8MKuoSRzrouFwsF5YlwYDUvlQfDN4V9Nj4RbKyvM1OoWMg1tdev37whC4pvdIbm6AaTiRZLKBEL/vjlK6Nhf4VvJsKhjmZr94r+I2+9cGrP3umh2bMHXs0lU+cvDu1+7a2W5iWu1vorw9RWzSWjBmddLZ2E+trrEsl5Sb7xeAxjuRCY3/WD747s36fNxbWg1xayB5988sizz5K+hGgUqNVGm8jckKuCE8OXTx0hZYKYukmruHj4wL4XX7jxvgc+9k9fX3rX/Um1geIegruFUl5l0q/ceZOju4fsBRV9HcToi7DIyPlLSnzCcnU8GqE4k3UX2OPVN2whB9xaroSHh579l78feP7p6cMHlLmMrannsS//3cf/8Vudq9fjFCDdlACXsbHt/X/yl7bO/jSylyQXSlvyrIlmZ0sTwcP/NKM8YPUIA4eyykKPoNy5cwfBwpaWRqfDhiM+EU8DSoK5MTs7x9li7Mx5GWGvNzg3F4gn6PiUhtP6+jrXr1tBoin4wii3wVBkYswfB1AIZwWrPyQiVt2KTW+sd3l8CX+trkmT107MjKM5GQD2pS1OTulR28wakAx0lN+xZCTpQi9H7pGMlq/RiEo9mBPqJ2yBC5ooOTqpsqyxKumJ7sHjkUrGwdfRkGsm2KnKOP/5kf/TCPzWW7iwylPob4hKPuPWRdBHfPDbmzittLhjEbLCi3fVf8KIZDnCFUAgGH+3opSTZdOlRIWEIJpYybD+8JykKoR+hW5QlNeYoCK0XCSaRAW/fZ3/6Z1YCQWZYnlTXSLyKTFDUICFxozCQMIg3lGGIhpLGI0VQheAGGdptSoTXpzahoae7q7RwWGyoq4MX17U25eMJ4zaQmtjLQ4wap3m/VHx/Ag0FlhUAQp+zDo0fXCpPU5zXmNqaO5qbFxkr6k5uOftZSvXnjh5pBwr39f4QCwVprE7ztGFubQLAFeDtXXR+tnZ81eOvBOMx/IpbXvfzvbu5tBEaerK5LK+/nRz6M03RxemJyqGmr37p1XFqWCppSqQxNKh1FjtLkpLg0NDvxw4t3T79hZavgyeswprQIS/6VM+551ramzDNTB54aIMPQ0vv4gagmUqf/vFX/St32Bo6brrM59fsWX72UNvItZpcE2Sh72hNZOnmYxAj4IHUAQMStXFQ4dx1dxy7/2vHz4kY6lRqoCNWwCSo7t78MhBAwNSEMoFpYam+rYdH/jYsltvDcx7L+zZNzU5vm7TJpPLozDbXR2LPvLVf7545OD04CXGu7GprXv54iWtjXjJyKGHsMSQXtugnWskJGxCrLjVq1d1dXeigkIrAI5xkIBhMBRF+4ZbQEnGVtdoDCwUuHDAm6Q3ISAOTDfLBt5YMV2YSOUirWoVSqcEoo3vRFAawp5OWGm67pBFqtR6I7Nk8FKBkC6lG+rqF7zeSDJOdxKjxtxo98TTZHBR2F7MiMpG3WwuTiwNrV4DaKka2C0lIEJmrdEfC2IjxEsZmifVeDyTcwtQXblIV7ZrS9S1J/0f/0qDwlooplXMCOTNqXkrGEwMktiqf5EpbIgtFEkJB00kO1z7Aj/Jcj8KAWOJrU1zq2TKj19GqdGT5FEo45gCfhfjXgnav351bXEf14STuO61q1RP9d+/StcTTMiPJKcaLnZxHsqNuXGOss/MFUh219ozuRxJ1dxlBolQUSKuMoVCT2+/yWCOxnzLli6j3dKsd4ZV0ay1Avo+513Q6x0AnlNGhENcp9fTWQCEFiSi2SxapdfWGse9vskrOFrbT5+4qNY7Q4HolaEJIC1wL7poGKA26uyhhQUvrrKh2Vl704jJ5O7sa9fMT3pHwVaLr99+64Tl2EvPPXvztpt7ekxvv5s5vP9CZ4c6lUvu3HJzRv/g6++IpYkNf95jn/7isy+8fPHtg7JsYvCNPTN2O6YAJIcmBP+w6JOToqsoAd1EzYfCkcIs3BSiMUXgsA4NXlzR0h0vVGqXrrx92TLWuxsTiWggUDFRWVXpXtx/SK1FgcHthEw1qRRzZ0+9EYlMnD+D8ke8EfClhVnftkces9fWvv4fP6AGv6Gnq3f9upXbbwLk4/Se1/Y99YPw1DQL4cVdz6mMlsbFKx/+zOcVVteyO+9dddu9kBFpCUf2vCwPBW/s7hCTLLlkpIeTXqrEg0GvVgG1xjuLxfqB9z/x6msvkgiTSaehQKiNYmu4jtbrABkyR9gzuGTSBBEAdqZpE7X3oBalk61t9cAd4C1HtOeInpDCT0UbHTxJpeTJi2W3uzHmD5mMzmQyTIAokcnbzNpUMT3iG3dqHDR/V5sUsVS0qPFYNLZUxItSZ9dSUUSAAnElksXSRAfJ/S6VwH3HBIqWknaNJRWPevSOwakrhMpBOgYzSSJoHvcqa/z6ef/fe1VWk3RSidc4BZMpiPzaSap/pbNWnckcYCwgE3hITLw0+fyCwAPuXqHgywkiMc/5csafTWTUeg/xYNEwQJkqNii0GxvlffbSPnEKSqKq8/H/vsHf/kQSMlWbENW9QgaT6MSJk0qSH9w190/wlSQBXyDa0VynM+CwThd02Ifku2YIMdV6bJzk3vtv/9UvXqD/Rk1D44Xz5xo9bjCFAMubXfA3tLUaTQYrhdMWdEA8/5poOEXhWCkvt1jNLltszDt/6uThwYsX1my6+fVXfoEW6lrcEw57Zya9TmfDfCBKe1riM5dHppX6o2ZTRlEw9/X21BqngG9uWnSHXpnbvfetsbGR+ublt2xb5zHOrlzZ2NlpXb7xwV/sMkuTJ6gViWLCz9O//ORbB/CjF6PxhWDEaLWmSZYl2cDm2H7zbQ3Ll6UKRb7cubT/gFmvQNYgJkUOrkhggVUYDAQlyQbIOuJD5w8dNior7XabwqxvWblky0MPvvPKc9pSjvICwlzlcHA8HKRrQZH5NRg23X/fiptvCpWVi3bcWtvWC5Iyth5lrgtDl84cOnT0tZcNJXq9A0hbLmWItCWG3o2G7rrT3r0E3yXBYrzXxCNDo2PeK4Ofe+geMpt+eyYhGOYBSSxuGR4gM4EuRA8+9OArr774wvN78QdCXRKF8XyCBqEX/nO7aNfehMrGuoceK9LdAMBQq2o9daxxkjuUnPJcOBQF8l2UPhlEQQZqXYIuenjxFJp0OarBQkTyKPG1koyXowxA1NZrdJU8iQws+MpIPiFCNajTiajJ6IDA8oUU4KRMTBL9XFFTKOdzGuB0zcVYMJ0vOay1xHJTuTRLLil4QuL8bzYmTGihgu+qTyoeVUwi7/7zuYTKyYeC3qvLl/QFiS9ENhuwcXhrcWeQAIMbQE7NewmhlSyEvWWDo1Bbp2i1GJdZC936glbYgaxgnEpc9f+60v/zCaTvV5mQH/J77pzrCUgJHkTMqbRxM9iHE1OzDJ/N5iDOhLFOWzKimVgRvmB4w5rlNa53jhw7SWWTzeYcn5hc3N1U5zSfHZxSO7vqajutBnx6qNl5S4PDRgbZ9Hw8k8B8mJ7xJ/OmlL/Ys7j3+PGja5Y0405pbWs2mI0zM16Pq765XlHIpOj+Ri4ChU4+7wyFrctWrHPTWCI8XCmtMxrs29fW5ktzu15J3Xpre83OGqDiLNaGfH6BVHCZrAMGZGAJ/gVmJmx67ZK1a6LhIBYgFas6i7V7wzqf17th82ZjY22yRJhVPHM0jPMwRz4Ev8Qkp+DUSvfW5ctEji+HGA4ydBLhAz//SWbe62ihQ+j9a2678+6PfayhvfXVH/+4DrCPmRmhWrCsVEpJpQJ7csf7PhCn8ofzK+R1rQ2Tl84+/S9fs8KEpAEWSgbQ6/JkeOHMx4+L5wfLPzN2+cKqRUsQAqiFVPAY8caQroE8ET7135pl6Z4E+ZBQitMlnkhCXMwgFv7f/u0/PvLwwwsLC+InYpMopLoLtEQOi52kT5QxkVJEWROvkgEPzwsMCwZO6HGCQAXeIWwHzZRkRa25UszQc47rkIvOUlEB5UCvtFCrBHoj2j8FfejB+HnC4RiOZI1On8lnMUP0FO5WCrkSkAOwoZZyfNBvrBYTIJUFHKd8rZLPxghMA5lYqqtxsnr/7vRcfSymEFrGMhMVxoIhmTQ4WZTMS18Qr1f3BJVTHsH4oXCK1VDwrmCF6leAAqMiEnwqRoIHA4SHsmuAic1gdxfqyrp1RmWvPe+UiVpCyp25SKFgKCBPEABcgX+/OdrVu/u/XqVbqTKhpBwLuS9kMfQj7oJblMSQJEJocFn043OUqWodLgJB/mDEUVd/ceiyy+HKZnJaAwDE+vn5OXI8M6X4ybMXbIYVrS3Nb+x+o8ZTt3RFbwPdWKmMjWsMOtfJ84drtrUQNmS2opmi6DHVXBsOhbds35lI+UjMRPibTPqG+vqOlvKV8zPT08mpqWhXNyEPazLqn7g8a6ur/PKpFx8z9aRTA/WuxXPx6Onjc/1LLafPzKxeVqM2KeNjZ06fCchkW3kOHgY6evPnT836I0qTcf0NN3na2sC3n4skXU31iTd2PfWv38AArG1oqWnvXrFh29zQcC6eAHwKDQ1L3NbQ8ujvfZ7WGWjhanozaeW5mP+1H32nEFigtUt2YWbPd//9zKG3t9x/36rN21oXLaOhcSwayacT+578WWdvV/9N2zVOMPgFXZpU5fDE5Wd+8sPpS+epfg8UClq7fcu9D8x75wbeessuBLYQvXhmdGXZob27+267y6TVF2OhmfHhGXBsTxxvNukx1P6vmRQhEzHnxFd0OpzS0BYPTtPC7u7ub3/72x/96EfpIiS0LDGXv94wC5lr6bhEuuLMwv8CDUCe0CZUxH/opaQuSp428QXUyWAoYNF6CsWsTm+MJgNmu5soB8gaInE3LYKHeTlBH/LFaBtcoKdqjnolAfpGA1Xqn/LhchxqB9qNfAmUWCNtoOWGVCnpsDjmgzPIDo3KYNCJHm8ANf7WHf/63v+feyLjTFapkzIwhZNRrGjiJGJplCj+N38JZxH8FcN1fVD5OqOEmcJfVnBugF2VIqXKlRh7varU7lS3NGpa9eVabR4JRjoZwUaRBiBzV1R/UutuI8NGuvBvXuj/sS8ejpu6qo4KnuOq/BNrs9iuroJiOpkIAsFyLAO+FgpHyUfDFCSEEonGPTVOoAd33ngrDT3ODVxORLOLF/fWOjQDg8M33nSDVT8IeK7bYQv4cKYWgK2nBreTskqr4eJgeHRk1tW+pL13GY2vG2vqHTWtjnKtb36aetzmxhpCeKUCPVEcwfgcrjWT3kqyYSAfOnjoHVet/s1jw9tvHowUZw68PNK/TBfPRY6cHnz9leGejjsOvLEP0LBz57ZIaWuSjg8bZqLeoQGFxnT55Gmt3fbIZ/+gbi2lGDTgig+fOGJTyKdlR+iwcXrl+hvvvWt+245IKFzvaGjr71+5dbva6YzCgbKKSVG4fOjtt59+KjA5bFQJeDc0ODLAwkPnn//qxSO9Sx74/Of09W3Uksxf8EeigaELCQpeN9xxHwsGLDB16dzP/vrPZXGACMi/UeqsNTsee6+1b5GdFGe3+/Srr6DqEF+GEVNy+ZaN6wD6OrV314FfPBlbmGeehO/b3Fadnd9+FSzHBfBpU/sSCkcgQyHk6euWSW/cuPFnP/vZF77wBVrM8isxzdLG/PJ9wPXwyV0/229yKfuwKOplTU0N0CYQgfit9GuCWTgw5+cWautqiE/FYiG6j7Kc0BTLDASXyTLmG2+tawX0WUTPWApLZTWkStkeokiu1iu1WRx7igJAZoQDFUZtm7HnyvSFWo/eZa6fi84Dq6fKaeqBsRVuIC559Z6v3+d/s4OQINxHJS7dbfghlxd3IEi4+qPqnhguDopX6T9xEfFJ9Uu8woeYfECGZWUkq2nK6YaUyBhz2coOfJUa3AkiRo/mgsM0h5JKiyq5RSbboiib8oWISX/9ev/NrVafi4tVJwDuE1YBRMWGbORuuC3mAEWFHQ7yOJRFMC1EVXQUgzKcNO4LxBuaBJgIgIhEoih4Ib8qHk2v6W0262j1lt2wZe1Tz7w8MjyEVGlrq5uenLDbjJs3bTh77uTAhZE165d1Lt3stLmK8URjQ30S8G+tweGoAXsvC5BbKnP8+FB7XUNfjys85zXQrldvy9vVR9+eaqztuPP23vZOz+nhEVwqbmO+v8uaTQeWdFqm570Xh3x37bA0NsjGCVIwIdLAAq6AYQXxGwhKBL1Pfu2rH/nK38fC6V0/ecpGq2SMIlmJ4uvZC6deifvu+8hHG5atVuhsArwCJEhc//TlLWV3/eg7R19/kag9nltKSwiWkrJLyJEyR9AyFs6df+Fb3/rwl77wnb//29joKEY26EUTgxdHz5+/7bHHu/tXHhsYKPhjNhPgNkXcIt1tHSRd4mhM6zWdWzYl5mcHD7xFpmq8WF5+1133f/Cjh/fte+ab37AXshbAcmm8A5ybHjRrQZGCXK7xE9MFQcGI8C8ygUYrJCQIVUtIcwU+lRUrVrz00kssiT//+c9pEnidLOBAppgzXzuT+IQzcxBTFBalWPTmm2+GS48cOYJ05msS8aKpU8BWAnNzPjBV4+pI5aLpdALoSKu1Ri8rTwamu1v68qnEiP8SvErNLghOxDjAHEzRu4me3kZTORuvgCpGeEhbuDR3ss7a2t+18crEAM3AWhvbc0QtBHwn90ajecZXYqPr9/3f7wi9EG0XlhJ0DOVKQZ0qd/3WLyWmE6cWEyl9juDC4BD6KR518EaNU3J6iXhkchuJi8aSXlfSkltKJjFmEzHOssCbo7oDACtsMxZ7eZ4yLr1Yc/+Ly/3WtX/7zVUpyCRLRWgYhGJK2DDSmQwGgvMRPMRE5rrCtVUuBgMBBJujXCQVg6BZIBQfGx1+59Ch+oZ6lJdouPCLlw5uXddrBYxwcWtvb9fI0CgdGi8MCYfBg/fdFA/NUPn+3kd2nh70A8CqmbrgMBVShuLI+GxTfXfb4sVA6E9OeW3uLl94HlRjsIEa6+3FUkyRNbfUULTouPG+3y+XJ3HVEZVx0zWiYupts4xMRevqnMSj22p0i5sMXY3md04hDBle6BNpo4HuqQ0kCxDxnAxHQ+Dk+IK5RNSIbIEH8dXJs5SwhabGfvCVryxeu2HDXffW9K/AQcrHWAbB2dlj+3ZbmVgIAyOV2TCYaOxOg2sGkZRInV4dm5s7u3efb2AAJhXBHqXKqpTNnz/7o0sD5Ky1dvXq7UawXtC38JUMnTl+6uTxpXfcufqeuwDtjiXjyG+QHRxtHXd96MMLYf/un/2IqBBanJhS8SjKRUuWoT5VZ7AqydkXUhzKgem4T62GauZIJOq2W+EjOBXCoks7nPZHf/RHH/zgB/fs2XPgwIHz58/HYjE6SNO8iZVLmm7mn+I2vG96Os7SFrO3t3f58uX42w4dOgRDVr0+nIfAHojh8RTowBqDVhmIzZE8aTDq4S8AyOUGU3tbX5pe4XFfWpWgdZBabSTY5XHV+v24FcgvqWhKOpfDEwr4qURJyWJynTpMxlVprqWpBygwQKVz5azOLAcZB3C2qwT9u1G1GAmmS2IlsctkYeoxdtIH0srzWydicMTywsfclviBuJpYfhCTqoq6PqWpM8mdRoqwcOYKZzr+EpEeg9kmI5JJRYX4qTAnhRQULyVShiXlkfOJpe1/3vjS1RkV9yHK1MGVEWJVNIUUHCjuiLtkn3dEDpkgPgVtnrg66jTTZbU5QsHAoQN7w9HoQ+99z8hF4J4yZZP27SPnTWZP/zJnT1fL6JVRRp/WAqSedbda48EYqAKv7TmrMdtcsVBzjSkPOERkfnji8sQVb3NX54Xzp0eG55Yuz6xfVkfF9a79g8sX1S7MxyKh0Z1buj73Z39lbVkT95UO73q5ptG9cmXm8JFRl0N5/nJGIzN09Tu6GnTFSuHiSFwYg9LGMLm6urbWNx458E4pEMFTDrI4RfqLV/YfeYHiRVyaePRUORWOHptTZwxMTF45sG/k+LvLbrljx+PvL5lseEWi/gUkHamSVFKWtbqe7Tva16xHeXjlO/+mjIW5BGPUWF8/NzkFDYiCfy4vyVUcGiQZegfP5TOZD/zRF9/eu3/w2DGDIm8Q+cnyyJUr8cvtYFXNDw9Tqp8ul51Eb2z2k6+/EwWGlIp80FTwEKGqaDUGlz1VLqP2QCgStUiPJ3Eh7EFiMulmZGlOz8zVu+mGC7BwBQAZ0g/5nt/v5/Whhx56/PHHyYahsQ+NbLEbmWV+y0fUN5HM5Ha7wAVkYwmFA6kGFH44DYQhhLKgayS+1NCC6AUBM2A4C0SfkkWHqcbhsYOq4PWP51KZ+vqWRCCSzIasGjqbNJFeTConkp41ilHhso0Nnd7odDxL7oS5qb55eHogmPNbjPbmtq4MGCRJP0gPpA1wYzwfYoKd/3FjlBgY8VWhGVSXOn7KWlqNdvC52MSaLv4CtopwwfQTlU1CnxAMJf7nDoFzoTwZvxNJ8ATGRQoLAp3ENWafEkR85dTBg//F1wlLiMtxl4wj5+EEkuQWP/mfN759lQmrl+fWCH5wZ/xUnIqPhb+Oe8YnL5AneR5sWfbJgYb7O3r6qKs4f+rdgXMXaWltt3laWruPHjkN0kRDU92VsRGry9nR2dPaNjE4OLhzzfrertZgELff3Lkzw3VNDoqcQsF0oUOhNzUZ7Z5w6LDL7AZR5N0jR+me1dVRmwuGR2OJK7Ne8hY0hqysFCrJbmjqWE0bLXKIXt9/8rMfeFxZHxr3pi22zOhCsMZgpbs7gZyh2VwybhdqhZgUMoNL1ubWitl+S/fiMy++5D13Dle8NxDbufPWvi1bh9/cjb3n6em6/d67y6wLBvPx3a9eenufqZI/8uqvQvNz7/3Cl4m7nT9ySIMrTY1QLPeu37T8nnsSqHtKWce69UN7XqN3GmnhnUsWjUxMk+wuLH2EMAWGBjMqJ0yoVgiXiae3+/G1G17/4VPn975MLiewWonp8V3f+jZ0QnUoXly8ETj/CYkjz5lXyAOpLGYEuiExXFgEDLyY8GvTi/ollFU21EsWWFaz4eGxDatXiLhAQTSal3Qc0ID0fKG6+sFjra2tNIImnjE1NcVxjsBvsJndbkf/5GvsMxj8HDJlY9IhDYlOMFKwhbCHVeGIz2atMxqdBq153jsumrUmgizJer0ly6JHEY5OkcqFC+Gix9UUiUC6WbgBGtdbjVOBsWIlB35DiR5b2QSCMVtOpcAmigZ5nrbWnmxxQcqVu/agv+tfRubqP8FqgqSJ3V1fDDkLR7l96UuS9BEkz2xBKYLmpTURyLOKtkwrUS1NUeUZQobCSanGihDlhKKiBo2XMxHJEPKLueFjzsF/MPD/dhOnYGOKuQEGmk0QguQg5Z5IzkH9EKmiFVmayD31JoDhprLBSMTuqqlrbOebVPGRXUMlISn59U2tBoMJho3G07UN9Tanu7m9d9XGLeBq77z1TlCeZ+dn0oXUui395J0mUhpSqFQai8XTorc6KeJu62zP0hQmEmvvWky91unzo/TBrrHTpBeMiPy2zc1zs5emh48OHt6tBtW5lKXED+nksSvWLnY/ckvLsl4tN1vQ2obGdfXuJsGD0ogjtmiwkSMEXNfY1Lk4WyzUNNbtvPOOrEy94657lToDI+kPB+QGo9zuJKlk5R23N61cTSzMqlaPHj3y7isvZebnLh89wppG+hOuQ0tTM0B6jEaqUuxZtUJttYoRQ5Zq1AZcVpT8UkRXKBvbWm//1Kc7tmwl74v0kEIy8ot/+JpvZPKhz3xyzZ13pgSqiqAHeoDhj8EAwKVoa2jtX7eFFNYVm7ev2b6TFH1h30iGDR7IRDiCB7w6y9VXQUwckOx2qIjqSnCWR8cmiO4S2IBhEZ3MaXVCYR6YDSggfoKygy7KcoffBduPaYbf6urqOM58883qD/mtOL+4TVwDIl9cRC6F10LmcbU2NPTCgfM+GiifzdGVM4/FodIb7PFUfN4/oaaDbxloWFANg8H4DKnpqWyElrfgFnn9k2ivyA9lCfvaNOub4Bo60GYAASFlWlkcm75I1iPtfK8+rUSl/5sXzieNkORTlMb5+q8FVVQHTRxCiWeAxRhWvy8OiaMCBM1emlPmR+NlX6qcyMmiqUoSyYKQxKLB1Sakn6ggBLeePCDp9EIpZRqk/d/95SoTMtYMs/DYio13V6ft6pNIRzg3c0nCK15yKeRawHcCHIervm7HLTsffuxRi9lA12VczOlM3mq3BQEPiqeIhtfUNy9aupokaSIZhNxcroa5mWAiVZqY845PDsdTCVDtKRzduv3mJauWxSIBtZyQkdIXnhqa8JOl2VyjL+VlyxfXtjVZZBXvseO7f/D9X1I+qzMytwGzVrGh31nvMd+0YclDd7aGfCM2Z317d+PSZTb4U3ocRldBDAW9GgUQ4Gd0iGg0ODl6mTTA6dER7DOWnlxgYfT8Kdpe5gHBUKk23Hanyuai4zotuC+fPp3wzsuSWbHaMN4qpc1kpP2JntKHCiUL9PVzI7/gwmg0QUtt5oDE+6LesOre+5TdXfWr1xnrmgWCsKw4d/zYD774xXd271p/6216wh74lLHvUXHEgifL6y0Pfvpzy266JU7/ar1l1dYbGV8BIyVmWMAzXzxxEpiQ6gxJj1Z9kQhAFHigXLES6v3+yMXBYaYG4oPapGXyNyYabQn3myRxKfBF1WT1gxXdbjenqzpLBT1IX+Ob1y8EZwJMxkXQjBCRkYQ3EJmcWThfliWAqgM6G58dbG/QUx/iVmst0CdAjXjvzVZPND1XlKfViCt6y+aSmQJttG04/krofGqZ3uS2WWu19JIBUV+kSSKYLcgBjdp4jQKv38XvtiONGGMmiSexxolV6uqGwvjrtzyg4Bp4lU8FH4p/4ulZ7CmTk7tKc/LchbBqNluMpIAbIQ5KChRWLkYi7hkyD3jF7SSYT5xH+nvtSr/jXzHE4opo67A0/wA2r7qFr51AkhFCcnARpCDo0cBSm03Ab5SGL1+k9HfV+h1bd97d07cahG7qcFFbsCXi8VRtrTsa9QfJ/kwSQLdThz4+cmV8bCKRLrT3dEYSGWdNY3cPZRN1JIXjLl20aC0+qd1736xxN3V1NYfnFtqbnBS79fW1pEu0oDFSDIRhWknTKHEi6g14HHK9OowTdtkid6UUB2YdfPCGWk2N1bnzhr580Ss9maBe8X8ylpqZHDtxdOD42xRzZKORo7v2+K5c+NV3vgUANsoEDv2Y3y9WpIqSwh6ru7axrUMk8slRluLu5nrkB5lKmGeYKWd27xnc9drlN3bJ52f3PfdscGoSLDSQmuqbWujDTo4FaSGNHe01re1xTLKGxg333JtVgfBLFx+lJhPb++OfAL9418c+TZePDBljZABmC6wjlvqmhiXLItwuFCArh0nxxAKR0vyhFJwUcZ+Pyv1rM8NziU14NyWpj+RkHuET/r25/22SqlkPBU1d2wQcsFrqjiT9FPqD5VgAiUCgrLKDS0YQpUSL7LBOYjpCsZwE8UvlO/8Ep8vlwdjsXOBKthRi1FUqMmkIaYunJjhAkwIKDgEHJuGP0xvNWkrY0OP94YlaT5ve6JoLT/KAUIXZaCEgTrEbLB2MTUPSRBbQillOEKCo1ipw7v7Xm5jua//Ek0uswfjxEL+5SW9RqjmGGBTMVx1PoY7yFpue0QF0o6iwyWKGwlhC7c2q4vlKBrdRnqVQIIYD0wvNkV6BXL+qdom//+lKv3nV/3Jf2IT8hhFkaFn+KNdARDIRkqAUPxE0LDbIU9wqc5BIpkXrHyrRI7SpHetf2kf6OGB+ZB96F0JWK30N1WFwninqc9cDDbp/38F8RbW0txXutRlV2Wzy7Hkk9SzOvHpnjdlS4wuORKMBAKEnzhDke/eJRz6AL0FRytdY5d4F0DRKBNJDiTwet+nRma42983b3LFQ4uHbd45PzAK/SPZOIl52udG70k4Cv25dKTWfw00jOWYkHb04cuzQG6+/ZTHZgWZidhHqPKHVZDbSZC8D8AVFKxU6ZqBmkCpikqn84xMLU8CNKyiSauvt0dXW73zkPS9+/R/MRJAVcuABI1Nj9AQcOfhGCW8NnKrUNvUvX7puw7/9zV9DkpytkMnAw3Lam4kcEpWNLouhANiDVInU19ZU1NqebTd+srfvyumT1JtmsnnAUc2eGjpGV2kiVSl3r15V09qUmbqCm1W4CFhryeCUOhBLfHJ1YtiHYWA+rAZSu0lW1Wg1Fy+PDpy/1NvdBIqaSU/whah8eWHBx8+x9EwmIz/mlBKNCo22Ose8cjI4gPUPpZTeASDxINyFo6ICglaSX8GDuEVL5RT+OcF7kI9SQd2GTo8yrkXBzGUorgNYNqPXmkFuT6ZioPjVONrDkdlQdI6iCbPBZjY5/AvzlVSUPj9FwjSZAE4+CogtFgeSAT7MF4BOwtdLL4Nr3HH9Fv/HHYSGUDKFeSe8+1jZIozDvVcfk4/FDoSNvBEihswB1A2RMSNcOHwofZf1XvQojeM3MdllpUx2OioPZxWtNkYnr8ROLODXpSCHjrCopeIy1dNeu2PO9DtukmNGVF+Sv06bAkq7YEOEM+HA6v0JxuNGOR1sicrD/9lcvgYY2RwtejJB35xCuaxczL71xisyhdZis/UtWQ7tDpy/fPjdU2OToDJRVZ3HpXDkyLE1SxdVculw0BuJ5ix29+CFi6Q+UUfT2dnvNM/PTc1kkol5r/f02ZFNO9YAO+0Pz5sM5SPHrmxcszpeoa9qJjAf81j0zibN2MRUb//iKweOmFV1mVJsaLTYWm/uqld7anQZuhBMzaTijRITcr88gjDJEOzwhEiyEmaPnH7kjo7OrXc/8OZTP9EQ7VGWgzOz8ZERvOSXL10cP3VeDexQudi5dM1t7/kQbepWbtsJMP3up39MkEEv5KsIw6EbAAub0Wqf+MM/dncvBzA2Ho4QjqTRQjiWAOIFxi7MBw4+/YyCGD2JTaS+Wey3Pv4+8GUTdBlzN6y4u90g3F9SgjAGGTXQlKAqSDlR6uxOwp2R8StqxAYTLdjwupnAhAhKEhtuAaEpoqiifguAF9Y3UEx/+uTP//Zv/hSfA/5SljpMSg6j5lWNuqpwFYPzGxwonUxQLjucEXhl2FuEWOG9Mm6TFBciwK/XUzRcQy1oMhWhLClbTFOglARJHR1AoLmK9DfSREuUQIrWLBUKkZHFKpUxmvJxQrlMn0qWgBaOJ/0ms42cXHLsSB5HNfWHp0Ttu0zudFm5My7HE3H1/812bVik31Snn2HjqX49YNJJee6qqczYii/wseBA2En8iPizRVfoqImNLMQuTJu6amzaiu6yN5Ae8TdWbHmrNWTlQaKAF+epetPYqzYD1xTU9uu5+Z9uXOIsiQlF1hvNlYDoQcojVlGGxHReP0F1mpC1KKy4N1BcXW4HehS9dVGOMcXp9BbDGhubburoXrKkl9qy3qUr1FpDPJZqb25yeWoRr1OTY6/v2kfRDL1048kcHneL1Xjx0imDzv3eDz82PTWIOdHW1rZ6VefM5HgmkfDORbzefFO9x+2aWZj3JvR0CzY/8MDmXHamnE16fYErQ5PpWCWaTyblhWFvZnQmUlhRe3zQp7MmVrfhv+X2BTFJ1MbUk0GBBGHBRxUirKfsXbU6UpZte+R9cX/kzL7XKXcIDI++Nvlt8EdoOsXiAxVSxedqbDXW1YVSOMXkax98pK6j4/mvfS0+Py2yINVox9jphjs++ImmDVvTMjoNJQxqfRwnDcC4gJpPTYaC/oF3DspjQSxPygJIoLznQx+oW7E8XZIbgWDBHRrznThyeGbwQjwSzGfpGE0Vp7Kusxf4084lS7r6lo0cOAjEK/4Y5Ad4SlIO1vWZETuCrJkhQVI8HGIafgRlyzA5OfONb/zgT//k973ApZeLJBZ6PB7WN4PByHRD3yxf4ucUrUrJa7ACPxdnQiNXqmKxeH19PeKY+D7uQL8vwJkZE245DXb/bLCmps1jAGooFo8HcwCGEJcR8BScBOM0V0wnlUTi8ZGKPiB47mhdAhY5xhMpJqCziGgKXp6A4Dr0BpFdwKKFX4k5Y1FiKcSDLXJH/39skvyFKfj7W2ptlbIFq7CqSATCyOG6vUbzcKP4qSKf3NZwcn1X5a9fWP7BTR5XPvuNsyJAsKm3MVEpPnnh7Loec49OO6CRhQiSXuUXcVaJ3asX+R/vXkjzqw8ptFFJzOJcZNyleSUsIaIo10/DV8x0LwS1yW7VG3SxZNRd20TdDN4IV4urobFlYODSxMhQqZilV4q7pnXthq1kUWBF0Fgom43X1Hr8vhnf3BxQsRoDgNqRhobabCp6+OBbt955c293j9naCJbDXXfdfeSt8/lUUPgC48qGWtWGlT1DE4lMpbyqoX3tht5w8NCTzxycHLP19uco6Y8lS3ggjCoRDvCnEuPT9D9PLGlxl9X1kjwSgXWos3v5CuuBd9PhOGV85SIgXzpXXSOh8YRKc9vHPzk/PxO6cJbGJJSiSE4vsrColy4qTRaZzcb0oP6jfCZy+Zb+5R/4y78ZePvNo7tfLiTjjb19Nzz6eMfqdXG6w8qKrN5tK1cdnbzioOe73//Gd/89S/8Fet7COqUC7pb+7TdtveUW+rfHKLWcmUrHQ2ODAxMXL9FmXsSchFoDxp7mypXxjpXrME2XbtsZnPOlvF7qOy9ePNnT3mWwOa7PSHUHehFeOhEn5E7FPv+gBJPZumv3m+BOfOELn/UveMmxtjtcCFBWQniMH5BjzgrAW7EgwL0SpdJPhjnlFDh4hkeG0YoA0p+YmKx6yDkxDCP1cs8lkwGBeaw1NrctC4d8zHs6HaPBAeugaFRMqp7ISaMSQR6N0w2BYYDNhC4FDE+5kkpnqFoETZ+npiuDkuQdMea4cyEXM+2zSM+Ig4VzjTH+00P/z28hXIhXELb0j4GRfiNOeP2kPCYXFXQuLBchQKoLGbcCDxjxDcnxHsTo1kYKVHFySmtdGo6WfvI3u9rX9yq1lZpkxTQfBVogIGAHrm6cXFpSr73/H/8ivarfuXqLJPbRMU/k11OTJjaOS9aTNLvS9JLSiUkxMT6pVOppy17bTKA5lsrbmjsWNTedn5mZ83un3R77xYELS5aupR9oKpmxOOuxsJntTZt3EmFCHd2za7erxti3aFE5t5BL0/TXWsxRVpEFIqGje2U+y9xk6ATscpWimdjsTJou1fFIbHouvB4w2WRhfDbd1d5FtgL3jxx3OliSUh67W23PnDrv9dibx4Pmtt4bZPt4OPEMRPaa+5e8/8tfBoitlEgNXx69++GHDPW1Ai+ERctp3vjA/b+8fAEQKCZC1BzzOxhXqVq8bdtjH/7owbcOdrR12VoaSeuIFUu6js4bF3V52tt++Z3v3fGhT7lWropQdyPkGShmmhsffmiYZW1smF59GipgxNohaIviPHLBH3r8A8997wdH9r5O2R9I6qKyVymzEZJG7+PqQhQqUSAfeu+jtR53cN5vctbc9qnPK0G8jMe7L55c0VZfkBYHIW+vzS6jIPGgsGfEGsyZBJiIoDV60z759It0Kf/TP/q8y20gUxyVsqmpkS5mXInxY7mDONPpjKTQAlRtBC4I3ZU1j4rE/v6l77x9aPfuvXOzs7T9EWjWuJdYzGjELdMAfJHLhZKZ6dRUlHE2ao0uZxtaVDIVpywRFBSy4vgJjACz4dVBm1bJtFCS8O+C5ChjfeSwCOCrVOQeGYz0oxFGoCoA0mZ0obEeFNwqW1x71N/9L4NSZTWJhrmKGI5rQ1ZdWPhC1UWM1KoOp+BbLsFPBVPmKjrd8ZHedNGLw9zc2EJ6ozwskIpvWrKibFFdCHrpVNHU5LiS8lPziwot3Z2w3QSXCzH4u93u9ZWQH4kfg4RMngUlOGgG4s4gYQmuv0qTWFWZNDkVJGEDEGjAZltYsNfWz04Ns8p19y1NRoOnTxyiKUQ8EU1FQ8cOv731xpvIAo0GF0CFIY1LrTETLr5w6RJ5VWYgr2twPdwI0KhwQcpNRFVT8ZTebF++amUscD6X0fZ01YzPXrwyEWpprkmEYqNFzdlzA7lIcVFL07LOOqunFHcrLk8Vo2nkhn7dEvOVufmuBjBpm9qW3jwwCiuhVEMxZZDUeMCpien+ZcvpgtK/aUfbkj5iZLHpsZqaZnCMFq9e1756zeTRQyjWNOtlACls2/bQo1sffPSV558femv/BbVq5W230DawvqePyvNoMtu5Zdvv9fRbHK50BorX6NHFCtkzb+xvb2r+w698NeSbe+7735scOEUZosCcb2+754mP1bW0H3/30LsvPGsBExeiVAGlLroRkRRDmIkpAxEJY9VsNMRnp7/6uc984A/+2OPYGBNrRAWkrcXrN5ZjflDjQS/9zU3MeHVjEoVTjSU1DxEJmQmGoNH+ymtvHj5y9P2PP3LfvXf2tXYg+jGL4XdQrr0zcwvzC1qwfQs0AgLjSuNwg0kjC0cAoJrYu+eNyfGx9s522hqS5kbBPryKKitTmUnb8kd8Oi1ko6B+LR4PJNPz4CMKb41CY7DWGHHegNbC2CvwJqLHoApkcOXjboFIwa/AEaHSMN5IKOaPEVDiDsgVQCrK0XG5VEjiSoZoJDEqUlh+d6qWBkdiKJYQhDSnINbEUUi6yhjXXgWniqHDKpQsQYngBS9gEstV4YQsEtUEojW0O/3xm2ct8tzlgt1Mv402DboCRcmHz1+cNZZJKVBp6jjHdSbnDJzpN+fov9rnC1xZ+A1ZCcUkCq1T+hWhef4yQ2ItlzZJYvId4S6DGlDYwFFH3ppt1OzEEuEoFn86mdS3NKzatK25rRnHwMsvvSBXLAxfPk9Ly8bGRrirYDIw9DMzU8Bfnz97dmF2vqVJ53R6KnIjcB0Xzh1tqHGGYn4ghjbvuEGusV28GCEq4WmwXhzMrF7VnUyVtmxZNj+ffvLZdywa8xMPrafsVqHPzDspWa8MepO42ExmuvcU7AZXR8fq+m7Hv/7wZZlsiXg6kshksoh3ds+Pf0xPCCWdT8vyvpUraCd16M39i5asuv39H3K0d9z1kY+/gpBOJVva2u1uD0DUXVtusDU05CPBhcvnILaxS2f0LqenvXPrfY80LV0D7LGm1gMJmlXKXCoxOTw0N3B2zzM/BwLphocfXbZ56wc+/0c4eBBGgbnZrXff5+leNHnp/KvP/tQuMsmBgSLvUG0QwTl3cHY6G/ArAf5kVigYySXffWM368X04KnFa9awwJFJw+pBXAp3a5wQhd0m5uPqJn4EqQlSQtpDa1AatyVkuTRrxdTG9cs9btvhQ4Ce/rKzvXv1mpU1dU7aPlrMNkSSUmfxkYlPE7584fylK6AtT9M2eGqKEMLSvu7Pff6zAwNnyEYVOluJdHUZOF8mq8lR2xqNhdKFJND6/oAfw08tJ4HbFQtPa3XmbD7u915SqlhRiXroQHNTa3U4i8BnsJnNwVCIgEgqEavIicLAt/pUOoQd6nQ0pkKTKrU+GAw5HBQikKhHOjTrEokpwr4XKwxPWyXM3xiC6yMh2IeHF0MgzAWDaJYAAFJKSURBVBApdC48u4yGRN+C02A4aawkeuckfJFcFIQXu0Lz45D4CZKVSK/N0Ts9D0ROoaQ3JBGogWIkk51RlSOhFGF6f1CRDiQjuUK9RwfoBZfgJhEbV08tDojp+M+bdJAXZknAlYqsgN/Y+D7/uKfrxySbX2JRjqO5KxSpVBKrUSyhmAMq5fT4yPptN5mNRppskfzbt3RdMhFKJ5IwM3r/pfOn5mam9AYzEM+4ClGveTwaA4kWSzRtNMjHxgcj87P08gsXrecukx9TWbVuU0VduHR5vsVWNznrU4q+D014bk8NDJQUttYG2/TovMvVqJF7szLjfKJstxotsXRvo2vMGw9EFFpzy7Y77hleeCO2kJG8o1epEjomDq8lipLIE28+u/c1DED4Z+zI/u8OXnz8z/6qZuna9335qw5VaXZ8DPxPmdlCCDQWDo8PXER1ozDGQPw9PD/pm54bHgX9xdzaBr9kfb7zx989dWC/d3REXcrZsYPSyb0/+Le9v3zmvb/3h+tvvyMhEtDB+WU5LBzf9ZoqGadqF/RdR0vHqvvuVUuJmlfOnBo5ejQTCqQjIbo4QEG4VkzyysGXfkWWQ/uaTTHiImj/SjWJ8hGTStbc9OvJlVQWofMK2oQARVUEL0KEgrJr0Hz5a/9nw/rVrEMuu3NoZPTrX//uP/zTv+P/ED5KgwGmyucztR5ne3sz0TwiscTKCaNzA6xvH/nQ+70LU/MLXhwtYMyh7aLMFEtJUikLck1NY68AxAwFlXqKaqgYoYNlhPYtIKIoZCkSuxgwAL/9viGjwZxPZ8LppMViH50429jQ4guMcasmnY2+9hajM69KQ3KJZFytsoP2jiOqTHovlqtG5JoyIMRv6RYu0adgMWnjCX974zjLK4EGIc0EPwjtDjNU8rlwnuq3r55GDJY4wFsIW/AebzkkHRXnIINaoTo3H08XRFHjZKqkoO92DibTTqYAPqSSF/PVYzDWJONhjvJW/EZcmrOJd+Ju/stN8LnYYH8VLjAs5+p7XrlhNj5hEztXN+moOKeQEhwT2gjyWqkg/k5+7djoCEkxVHHj2cOcUdjc5I+0tbZRRYp5mQMBiohhIDA2RqdkIkjZ9o7Ooqj/LDU2t2CQ7927K7GwsHRRfSypP378TCphT4YzOWWskC23tDsGxy7Z9JrTA2PbNq9DUlYy5VVL+3MJlOJiohiRqe3jc7Jbt6wyGM7X19kHhyZsDnfJ2DI4d/7ZH73Q3vB7w+MMA3OBOBDoL3p6xBJuF6ppWWBKS2jter0iEfJdOHG0cflyVq1XXvzZsXcO1Ld2v/fzf6StaQglEguT00aFFnAGhpb6JWoafQvzdKu/o6v7wsGDz3333xPeGYSRjfAcsV08FXK1mQYcmciRl5+dmR3bfPc9WLQApYX93tFTR02SrZYtlVtbW6zd3RESLwrlxqUrW5asoGGS98Lpd17+lUhMEyuiTJPNvbN7d/uaDSa1Jj4ze2jPq0NH3rz3n/+eWZDo7eor74g9cIRzQ08kXLBoQQwAN61c3n//PbcD3j88NrZ+7YbujpZ//ebfomnteeMo9RJwbjGf+tTHPnDXHTcvXtzFMJ0fuPyP//zNeFwQvJ1gntk8c3JWFG1rteR/U3mEyopNSAeEiiwVjc2BCqOhF7eWyKhOo9RFI0G1yix6iIqWPEalVh6M+NUaC1DyzATqJaFTOJ94LxIAKqPjoN5kp7MX08PyA1UB4Z/IZVx1dXS3ItSBas1ziRwVzEdEv5A0YpFng+KlYRC71R0+4g3iB92T7EWxfEpfYjilj8QPrm4SfVffs8ygwQvy57PrX0GPAERN9Bkh1R8RiMOXAB7PAJ5yhQCbTNRDlyJl0oaKRITcRJvZSD0EUqAgA7JAFMiLrXpG6Y5/4y3vBddCiGpyXihH/M0NNkNC/saGZ18IE+kOeWWThopuW8VQKNjQ1CYbvTI7MyHbvAWdiJb3FkfOYdYtXbkSeD3wkWh5RyKljcpr0a6c5qMK/NSpdEqvN3Ut7j118t39b7xFjnAqnW1udHjno1x7bnrM6FQt7mrTGSoU3nUvqX/j0NDomM9ur+9f20KLdbproTKlKHsP+5JRPO/OZT218yme2dDVtq5uxU0HTjy7uHW5fvmq3YfgNzG+KHqRublkOKJEOzKZQ9GoaJ7JiObpfIWVUjFadap85oVvfX308H6TVj3lP/jiN8tLb76ro68PhJx8zI/FR9Yoakq6qN5w533rb7xtZvDSz//lH5XJqAWwTmGEYUoC3SASsdANCUjMDl2eGRo6/toue2vnx/7qKxM+byIasiopSJNl+ZLVCJYo9ESjC4odaPBHI0dfMITKCY4W0QgKU3HUNnZ0UUJ9at8br/3wu7kAwNAgYUgTxARKf5kx/iMRhknjgOTZFnYUChbZT7Pe+TlfwGpz2mzR6enZK4NDy1YsveP223fveReqTybjH//we7/0+U+99dYbTptIu+3tbvvQ+x//2r98myIln9+36403m5uap6a905MncZZABUJ2URFYSObTMZ0a8HwjoZNEltxYwClbai32XDKcjSf0JprDsDziX05j+6LrCW0WpodHaAGDBgZjYLvijALbmvp1jVmrNNBYAcleY7Jn6LSc9UmZ57jSi9r2DsOtd6pyAu5VuM0E8QrbV9Ap9yStYQwCxClUTd7z5OfPlQ+fwR3LO+IcDLNYQgQZV5cTabQYL36DBSzeVUezuiuEINKUbjAS8CjigC6hBZSGHLxFSxh4G31Dj15o5n7y5FFpddWqRTwQ+rXrFa3NInlfcL9YHsXJpYtwMek2xFVEQAiBAl5dLHOVCcUDcEviPsWKV33LEWyR62/F78XaLawNqITAOoUwBH99sxPRWCyfTMBe9DBTVliQPGlc0EmCJ8hawrA5DXADegO7oUiYrCqXp47quxd/9hLpWuBy4h2g6xOPSJ0Gy2b/qo3tdY6ZmZP++QXWWEUB/TE7Mx/fspbSYbpYOqh7OnjuglvjaqszlWkNraxcmgpEw6ZtHTuaepvurXtvd0vnsy/iZ4PgpVkCipNIPcj+Rt22Bx/zRsPAbJTjCXC1LXQLkus7e/tPv7Vv6MQBs0GFLQEE+PCJo6MXL//BP/4DCBRUUSdI78DilMnb1mx46DOfzmfzz3z730uxEP0Lha4EaxmNW++6wwdK+exUYm66HI8ScsUbXw6Fwql8aHwUnELaKqOTY1nVtLX0r19DmA6rRbTJFnNSSQQWLr/7rp5FDLoG9I2v9Sy+8cEHgl7vKz/8ljLotWm0JBkQKeHrbBLVVOmKHlU8rJgdEQCkhYPI/iALVE2o8M/+7K+/8LnP0rIql053dHeo1Ka9ew5gt0MCENOy5ctpe2i2msiCmp719qiNfiEIuAZ1R+UXX34dqPVUMuafn6cGUlgQVGSWaPBOUku0mA4pSPo1OHQKIJGViUyctSMaCEECAPYgcT11NWTD5JJRAKgKJN/RW0FGU1BdjqQlUfcOr5gMWguoRt6F2QzxEnnZH1mwELIg2Q3gV2QRa0CppMdKvxkPH/ntPKJEnCiZDC9KK9wika40HHws1jw5BtrVpAZGQkSHoWlGjE/EH+lFjKA4In7HqyB4zswOnwqLmkkQNWucToRuyABUFgxKTSTNMiuqNoUnkXoK7gA0YDKPDMKgZ0Oe6tesNi5fxtrN2+uX4ybFrUol1NK1mHQuJKcPuv7seUFE4p/gVW4WXpW8MuKEYuMA2/V9waaiCguGVmYySULAa7fccO7U8UtnThXzSbxbDz72Hj3ZidR3R0L0GsEqp1sgkSi7yyO0JQQ1prmjZs3GTWOjF2cmZuwON3060lmBH6dX65f09FHChAMtLXoGg7JTPnp8eue6VfUtqsGh2aOnzpTzGA9gGUUuDc1s7mm+4YYeu1M7O1ueHJ+rVDbbO7rDkQWSeAbPnjt+pF8m28Cd82RoUEhC3J7N7R11i/uMSnWWTum57OzlwelTpxxd/TW1Da/8hApa2Bn9R9QCGjSqdDr5+o9//vgTH9HXOL779X9pb2hs7l7Uvnotk/rM1/9p4cI5Kzouwiif0zd5bnvi/craepdCvUZWOLPntYuvvU40DEomeyYXj+559ueP//5nwXeTBeYF9eFIUYFEIg2wUD3oISIHfV7SZiEDTG5ZWqF46NH3GEzW3U8+WQwESI1PlkqNHW1gw4mfCYEvbeIPpUMkT3PjCM0i8IpS4Qt6Ln4U7Rv7j9M3cs2qZZ2d7als9jiQBhcHabDFfNPo/Mc/+klrS43D6XHYnQ5Pw7HjZ37xi+dZmAgu8AW6+e7YtpOmjqdPnhobGwWPT6Wkz7SMBtzqYhxNitqpTMInz6dUBrvKLFDmjJihlJ9jCJfKUZIJZaUkcTY6OvHMeaXdYefegIzWYnTSurySylrJ0mLNzGM3UsOrLuLrp8N8Ec1EpALzUAwHiiiyiziPxCrcmBgAiaZ55SDaGYfYFf4rLGocsFQek9jAByLXnOVKGi1B5Iwb78TPxUJEsFRKaZMIW3ABFC9OJLGDgL8n3RYBWczFREsmfgXkFmKaq8GLRJgIPGkB3y5puRarEz+WagFzuND4ksQ4nE3wi2j+zUXxbklsLtRLuJsIFalSwpYQpxRqEDa98GtLB/6Ll+oDo1cIRAecNMQWdIaG1l6gKukGUdNYMzVy8a039txz3wN6jdFDo5hEBjIMa+M0v0tTNq4WvcOQJNCfy+V89aXdyAaDkZwtfTgQwhZvqG+85ZabPc2Np89dykUnc+Gg2+YsZsptrXars7C8wxQMz02NBercGCRACuYyHfqWReunJo/FAokVi3rl7o2D0xfefe05l8GIh35qWC+XbWJE+Sc1J6LlrjI4Nx0YGgoAoOaxnTl9empoRGP1PPCp38P7FZqe0+HZEgMpRo1oAXV4V44eJ6tu+W03f+lv/6Go1KWZeJn8/Jt7Rt8+bBEOSSoFK7ZFnb0bt+lq6+IMMqAHahkwPNkSQSYwHZi3PIUYI6fPXDx01GV3+QIL2D00SPSPjtcuXpICgyyZLgERl0md3v8mOoxIEq0o4uXiqltvX7p+65Fdrx179QUzvR8KJXVD3Zq771KbzGJumG5BTleniW6NggdFgoEAVhLuPsaIFx4HhItMHo8L3ee9Pv/o2DQ6CeyKuFVqNGfOXfnwR7/Q39ej06j9gdDk9DztmamZFKjSsuKnPvHRhjr3obffwRKyWE1UtxI9hz+xu5BT6N3UvZI3hOJWFM2oKN9KKVSGXNmIqqvQqhS0zShUkCMa6pchZUCAhSNCaTQBfAeGLU2y4TRcxTp9njq5WFGpTcfncRvThFoNoIsI5HOfgiPYqhoof8Wji03QdPVVMJTkXyFqLDS16jeqgyNsCDEYMGt1seKrVREmDY90ah4H0hdcIz4Um9hjicmYcmnAzZA9LPJER0voByUVXZOFvUiICUkCThRNQrUF+IbbFVcR5+XeuEOx9HEmcXKxSYeE0BBvRDhXuhhPcU0dxYYQmqf4AVY+RnD1G+Lrv97EM3MJzgO3EpmIE3efW6hvbh8cOIkaZjdrBk4d7V3UD5R69+KldMNTqwxmm2ds7EoyEdVo6c9M6VwFQ/7SpfPeaS9WIsonaht6RzFX9NQ0969dAXoXOTSqXGztqqVL+noziUImF5ueLrgbnfKQjNrzeHQ2GDTZLHjGUwqNbWQqkoso1q5ek7Y1v3X05ckrEwlL88Mf+kD74i37j4inYAMmhi5MtEysxML7nvxRipQojTJBuptS9cBnPtOwasXY2RPgFWllYvktgByC2lAsAkxk1GkWLp3cO3dl8uLp5v5VK2++nb6nc7PjpI8VSarB/VdSLNu6s3bF6ii2OAUWJABWFDW9i6xuV63dvf/ZXxpydNKSKVPZ0fMXjAY9og4HlbakOPXiK7ajx6OJSDoWK2QS6Kj0vtKRGCGTR3Ll5rXr7v/EZxf8/l1P/kDLOqBRExlqX7xEbXfRrIsnkghMUJ6Qtphqaq6MugdVF+i9LSgWRaxYwpW9bu2ir/yfL9fVOMwWC6VnB98++rdf/Wf/QkKtw9vAXGsDQbqeR3tWLmkAXEunmZnzAWhZyOU2bVjf2dZ85szxYNBHcD+ZSmZpBQcTCgAYWjG5aK1VyGWNUtdNxIdalpQVkzQMQ4+jcTKQF+lUSV1OAYiUydLVSniJU/T3Y7EqZmNpKA2E/4QqFaL6UpUPAICfNzoU5TRAuwqaMBet3IY0e4LqoGn+ScQN9YkRYB9qrvKhNB68FU/Nvvjm9f+EpJIwgoQiXY0Wiom7OobSGQTDCL4Tgyp+K/2cPmQd8fH1sbMTGveRmtWrmi7TXuFMfFEyb63JaaIKWUFNjyZ1To07gPWFSgpJpRG2GlzEeAjzD8KW7lbcK2/FH8GTXEhcsno5dgR+iXRx2EvaxJLImi4ehR+wU93nVbpNDovBEMtrsUDS2PDls031LctWbpidHGzs6rpweaQNQDg1HZ3c2dxQOJwgtEYJGUqIhcrXYsFiI3fKNTU+QtShsaHVZtVdvnRJVdH0tXavX7naaLUkFwJ1LoV/hobcGYAVnba6aN5//OycSqsq0fCwnG9pUJw7G13Winef1nZmfzxlw7bX2bwLk+Nnxxrql9506y2r1i75xUu4k6vVaHhMCqjxPDtPm0snjBQPUbGr0T3wsc/27bgtyxdpkmX1KDIEgXKWxlYwEQlzT1+5EvUuGICKSafOvvHKiXcOkMLTtW4D6hZQ3UKnZ9WCik2WGEstsZRyKR0iwVLRt2I56ohBrW25cHb+1AlKkPKysruxdnwowDCilgBSk6ff9/wUxjn/0Q9U0omElUO1fu3i5fd99LMFnSUSGiMzTq/CyVFSG/Vt3Z3siPmQJlBMRHVfFL6JTGuh8VFdjauAhCeZWBOQtl/6w893t7e9sW/XiuXLSWS75cZtFE9/7GN/QLIafkRyj//yr768+f/r7rzj5DiuOz8555ndmZ2dzYuNyFgkIhEEQBJgAoMYTImyKFGBOtE6JUvWWfroKFE+JdvynWWd7BMpH0mREDMFksiJCERcYLEANue8k3O8b00DY5j2x/f53J/XBHt7uqu7q6rfq3r1wu/d0uGtcLOwnA+GfvTcL059dE7Y0WFFVPEKncFo5XOj0kSOUSqEqZ2VPk7avo5tyDjJmf5YaBbkJx0qe3pNiF1RcFTJ8WEx6wCbQUcl5k0SsiQRlfWYmbPROSJ/6QZlJoGxw2YpC0fTjCKFZJTsuDKlLhYPo/PQiQU3m6DQIikKCqXVtJSzHBcviRJSV3CeYpAtDRO3QNHIWggsCN5aLRq8QjRO7wvXbGHLgzXFyqtI/GJZIY4F+yHa8g+VjrIsNHl7pf3UyHy3IrazordcNvjDQWcy7lQeTzkWa4etqnpTHqFigl4pTlriZl4v+EM8/wb7XOc9cap4svhOUUIULhYlnJVfxes3bhNt4KL0pOtXbjxRnOQtQhylxmC0RhJj/YnkPQ88vGxFB3ltZTo7kNtXr3T1DwyFALqenOA5OBTYHOUOl2NuZlyvMTFU+0kJmAUp2NreXG0zYNI3LVu6dvXG1SjZbA7XkkVtEZ99z8GTOvmyU2cGyjyGiZmgx4kOJqxTqj1uJ+a99atYyHmnZkf8EzONC1rUtoYKg+KTj3++pW2p2WEhfljg2ouGFedtOXm7jDmQ6smKgKlDTYbA/H1/+sXlt98bQvBXyTx1td9+7r/96offTcz7b3voQVttPfqPpkh4rvsaqSmme7pRN8USyY/2frBg9ZqGthUHteZcMkaG4Gg2PjXS19ZUF56dv3TwSPfJY4htDrcnHorhRzo9PAhZIusCJrH0tk3nTp9CZyYi5ZR5wjZYWCHSYZTFN5y4Ypb/zCzVS5d+5uvfy9vLgfrGyKbWW+SReYgLohs6d95d7dPeskZ8kxscKH0fTAicQBjDn56ZFqkG6QmlPikEr1691tZcPzE5yXqMtDDhSGj1uk3ELV690s9653NPPn7/zjsO7N2rU62e8/vtZY5777mrs/MqNuBTH5222UzkD+3tHTrfeYnlFjYP+lMMPiht4pH5qRHSb+MO423flJKpk5M92cQ8axMGAcA6WJ9lw7NoPoWElRNgGayl4tFxtMhkfMOfm5GRViBTBP0jIHuissmAs6A16cwG1qgKk1qh+RfPIInNEGVpL8wG8UgsJzVf2v8LKwoOhL4xAKB4JacIg5ESRgwEQxqBT4JrnWBlPgUiBDu4VsR8x3EU4xycAYNSQDjGxmMZOWkSNbac7r5kekah96Wu5kyT6W0P+56PT9PfBkXOiFsiMkWRiwR7FJmM6hWHieuMQ93YuMR5qQkcSwX4ScbJYhPEIMDISJibMGjTg9RNXCgOFNf5kSKigvxPTeV4GGZiEYRMh8sZiIRrl60069U+EIJnp1/+3W8i4ZTRqM+lcPONMmNUeGryclwrmY+IKAqhnoGqausb3ZVlXnel1elubGk3eezDoyPgwHd1DbY3kxjLqNHkz1/qbk20WbW5jrZ6n8cUjU7t+/Ag4rjB5JwMzaYvdqqzKpOrTWGtQmVhXFZ49eVXDQXv/Y9vmJwKyGROqW3QgauyqmHFqrHp0M6H/+TMhbN6p3PVzocCaRnI1gNnz5zd/WaZVT89Me6qrrNXeKMC4EOm1JvKV62qXrWy8/3dXXt2YxvIRcIoJ9ytrTu//NVjr+2aHRjQ20xnDx+E4q6eueAfGjKpC0mAViZGAWQOYdlUo7nWyu2mB5/5ur66ruOO7e/NzOLiF5yfik1Ps1pRmyxwrLu5tX31Wp3FlkymyutrkxYHIh8SjanMXb+0o2/vbiNzQjY7cOrU+MUL/rt3yHxVN4bNYvsAPwEGAug9DZZFUAGEiUKYc0WOUNmPf/w3ep3h7nsegiu7L3d3rFl38tT5keExdEPEVdjtNtaPZrOF7N8AqBHod+zDkwD4mkzwsuq1194CckenV9ns9mkQo/hqrB1QdSQzdo1iov8kegX0ooHJAZO3MRWbM5T5XMuWR+fHsrPjIPITH6jO4m/L0pgBEYUAsy/QFTjKmsm4AN1q1aC2KtOIMUYrcRO2qgaV1TV+8WAqNKe2VZDRWWoexCYo8ca4A+1KHCiRNZck4r7OmdIAJZZ0mAv0RT0nM3GB6FuZw0KWXkI3iSgj/4HBojdi8QFfEHoCHo8qhiNisSymQZ6dHlPrjp49N2d2ofPM6dt1ZSvlU8MxTWDTbc4TH07L1wqzsPDcZhBVEbzKJqZBuggmkarEyCVVlZ/FAmIE4Vj6KTEkx9fXhNyEgCGSVAnNDOtyniaYDeG/xK/SmMFpJnnuBFg2hbHBbHc4nKRoGhkZrqz0rVi1NjQznCSZPSHliTAsxzcmoc0UGXRTMR2+Mw472lWUui2tiyora00OEyMSeNgGq521+dXuq7VuNfl9rSbXHZtXXxsckIF7HE9uXtumN2a7+/rra63JVOHa1cm2+sEzg8NKhROzks5ZOz4d1IX9x15+Z7wv+dlPLdv97k/ffs0ik3XQsWLSgHK0+i0PPRLV2Ny1jSsbW81WcxDJDpIqpNF8DB89CN/o0BEBXAVGrUKTglygD0YpdcFZ7qJHoCNPQx2PiuVyC7fe0bSso+f8hZaFC652Xdj1q1+aQd0k/3sigtqPCDzANjGAVVXXZnT27Z97sn7RMgy4G+66r3XZclI+Ht77wbljx1d0rKlpX2SpqATRAQUOCxY1oT74BeIQJ7yk8xml8p5Pf/q3A32hkQETkIrQSyLKdelzlvZ8C3Rd/MQmkYqEUSryxRlnWVcgoALG/+ff/UF724IFDXUYSV/8/TvHT5wBn4AoQdK2/f2v/slkNrQ21+OYDDTvyy+9+dYf9zCwQizhSPj2Lfga3DU5NbZnzwcH9qK+xUgg5hDheKoiq7VO46wSSP9TXeGpHoNCFo4GGFiSM8MgIrlX3EFM6viFg67aNl1lS3T0cjo452hYXsilJ/rO+apajc6KuaunM7Fg5Yrt8Yne0bP70umYwuSMjPaZNXmjz6K5KTQBImSDank/B+xpLw1nk8iak+gyhEZKOCqI6RqPMIPNivgvdCNAL2L7t1oEj3AdhBZBw2hX4FSQOUj9Am6mvhAR5mZoH39TBomx2mWvK40JtS6pUP/k9Q+M+eyMd0HKEH/v9Mi01qLPeAvZGPEMjH7oaHiAuFX6v1gxDiU2k2rLvnRQYk7KsF1nQu4y2azJWJ7VBRZszFGCAwWqFJEvomLSRhtECyFsbiAkTGuo8PpQJfIvEmRxEGtvb7W4PPWNLedOnxaQJzJlMUlQYW5mlLZFRYharQeDwJ3uavzPy71GTN1KhcnqwGiE841Bo6mt9YDbdeKjK+vXNY6HkmitV62pvTzQD2Tz4eOn632+lsay4ETwXNfw3jMDFebYlz9/R85gC86Hycuuydi+/RdP9I++fu3kh1b1g8U6iyrjTzff00sSeO/CNmBqCasjGa06j2SqiofmpsZ61VphMsb4iQ/dW7/+Vdsta721NbNz0yxkJsPBq8cOoy/MmywL129IM04KKGDSMdnqG5uuXb7WUN/858/+HCPBuy+9dHbve3oWN3qlo7oaCJl7n3hC7/Yh/xx/643xwb5sJEaOKqvDtmbb9i8/97MQnx6wbYQOFEXUtZBP4MdUtGDjYke1U8T+lbkfeOarf/+976gSQXI50eciPdnNm1jGwH5CUwf7MQGiDGAgpdtFQi3WKgpVEu3o5HyZw0XecgTLQCBq1Dl4HcG64XDmu9/9sdNlwWBHvEssnrOXu1gTpTKp5csXf+XpLwZCMxOAVhREgAXTLPRMhAtyKbgx8pQ2lsaoalErZrXyXBKFubmSRIembGh2ou9a/xV3VZ2GZXJ3zADW6kBXPkueJg3pbtIjXVOBOZu7Zn7wfCEXj+H2FQ8ZSAA4PWjUGDUWqzIdwNAFgIRoqBhQBKdJBA35SqTMzyKpi53UH+KA3hAKSaTcQqHMMV2QObkVdS5QwnAuai8hA1JO3IKev6g0RU4Wa2mVVZ/VYZhKwn+wcBolrUvv9qwaSTBvyuSti7L5VCxLWg2l4paCVauPymRXJvzXgv5CBbNPBcwiHirIjX+CP6QainPFjTPFdwspUmpL6dJ1JqRg0XJgJe6NuBkiKXgoOLCJBMsA8VwxMRZby/00A6gS2oAYo9KZpmfmCPCdURc8FdVdXSmPx7Ft+z3zOOdPTuGbyogM6gHwywxJLPmAHGluW0w8JxkH9HogER0GnRq/AXwIU/EQaf5g/OHhQRQnnZcMPk/VpTPz0OKxk10Pbu74k+3LGIu1hmxbizMTL2xbVjPjZ+UHVJ62vaVSrW64a4snLZvZ809H+7sVJkOt1B8MfETPodF55W9+qbdXoZdvWb26Y+sW1IgsLPq6umMz02YRx4yuKSfHrnLtyumRfpXJEImFCQtXp7FVyMEaaN+0tqyhJZTLAu3QfWDPuf17ZvuH8dsyOfCILDe4XRtv3WzADlaQ1be21a9cTsoToMqDowP/67kfTfZeBS0fBRr9OAHOd1f3+kenVt33YDhNl0BkkI2wMKBpI9LKYLGgO4RcoBsSYDgXtHzm2985+OJvp/t6XJ5ye3m5+CKlTXx58Tn4LrizsBiETJmNMwKAKAM9ZLKxL37+8U88eLdBp6yprp6c8b/wwq5f//pFZFjWMXxKbLl3btu29bZ1Oq36wxPn3nnvAz4ZVHTX9q39/T1+zEfzfuCZcBxHQ0ulAPKKpYAqJPfnrCafzsrKs3o3IDGKqJ+pGkSZ+dHghasDsEgyOLd8yz3RsaFY72E9CGryfCo4Q+ImM5aU+Figb1KNrd/dnB7v0+ENKctpDWVAqyayIic1vtsmXVEcpQ20sLhJ5MuhxIolaoa4OZb2+MbQmaDdDem0P+np+2ZL80KlhqW3MDJKOGZ0d5FJYIUiVdPpCO4EV8rkLnt+LozjLbZLsH9DKRk+dEBn5ZAasOUy3SFBq3R8BD8Rv7F03fIlhlxmOpdTA9soPqO0iWla4kDeKVWMCxzwUzqQTrLElU5eZ0JJwiTpRzoeYzB2OKzA0bJEQaspPZjPLJYZojvESIIPodVmcrgq7c7KlrZyJg3/7Pjk2KBKkb06N7FuzapHnvjToYGB+ZmZcxe70NS6yqvI5wtOh9ECvK6HCZYuKC8rd9jMKM/CgdlYYMZT5tx/8LjXqdl6262Hjhw9ebL7s5++I7oyajYY2xqqVUbZwEjy9o3LDxw5qMqqG6uNO7cuvzwVT+nMZPMxW5Hv9SS9/N3vfnvlbGzt6nsK2k2Hz1HhonACrCAEimv5fA8q9pHO86f2vr/zC19qWbn+vYuXs9GEAnh5McEwE6FiBgEjnwmGDUj8WO9VMlw9Lb76dfd/IsY0oJRPd1987Ze/0OUA5FPbdJjEMD7PTw5mA9OjDzz9TO3CjrRcmWToDcVJvnHotVene3pcBp1QwQjBWKZDTPRPH3z+nxhwl+y4GwgkoWVlusbuPzn+6x/94L6HH1m05bYoJI9sKlfEC/nalat26NS/fPbZxx953OHxSF/kX/bIG9CTUARiW0KMRRlE9DoRaclUItHa1PiVp5+KR+evdl9zOZ02s/6rf/aFSCzy/PN/0MoMFrP6l7/8SUOdB9QCwLvWr19jshr/+Z9fQwofH58qL2uxOcvjyUz3lQ/wN4KxWddBCcyEemGTjDN+ov2RuxuiAb+9whWf7JHH/dmYH10bSlFUM7gV56weeXAECifPDe/IYe0Ik8c3zZq7YK+Mq0yKstpIdB5zGzb6ZNfhAnJLVW1Bg0cqbkCC/Up72iiGmKJ6Rjop0Trnpd4odrCYk3JhtLLCKQxrJ+Ic72aUKvKhEBvFc4SdQ6gYKYwv03xGEQ4F6yvdeaMu7xd2d7Ms3ZqNzCZTwTypEwygjURJi6UB+jYXiAlFD+LvSCpJpr0UonyRuaQ6iCoXj6SacyhVj5/UViojXWIRS1s4WWRC4VyrtJutnGI+wMWM9RvMFo3FwfomsBpQKZYimSCeHELpxW3M6xX43ld4yyqrCYkxmfWR0CyQJIlQ0CDAmxPlXp+1rFx4PAWC3ZevVSysY+1B+mjASASWVDZpsZYT+jIZmcffMhkNeSy2cHjq5EcnKyz6TVvbx6f9l69MJeOKNRvbT57uWrl8aXf/qV//ryM1rgbW84W8cXQ+6gqEqn0NAZUvlYm8u/uoWVW+Zfvyyb6Z++58ZPPOJd/+y2My2WMMSaLZyM9qY93S5f194+nwnDaVDvVfeeUnzy3dvP32zRuTMwO9Z0/igyx8xCkvFsp5vPeJFUjizKHS3bLzwU33fULucEezBZNK3nXipCYjJBeQzJBTxLKZZOsy2Wxfz4cffIB69sJHx0lUePrQ4bxB76312iq8sekpOfpahkyEKDhdpQiEQ/09Pct3sJZgNsobEC8i/lNvvxrr7Xrzv49MDF+tW7G6rnlxFBFGroiks+baxk9967sEfMXB7TALceo63d0gU76IdIbJEIEUDsTWB2sGyeE6PY19nqDBw0dOoI8BoHH5iqUvvPAaqpZPf/KxlR1tb7/5RkNdPZTnrkAMrwaCR60xvvzq6wODy5GKzp8/iws42h14MB5PwYSILWqLU2X25gsRTdyfCpgMZd6oP2Rq3UxSt2Qokk8OZeQFHfFrgbG8pTxrr1XjhxdP+vuuYCZifa41+qL8RbU6Nob4UPDUQxUGQhDhGbM1G5m36cFiLIqjRbKFjiV+4xdEyE+JjtmzSVRevCS6Be0wUf6VCsVftbREFYpdoZhBnnfgTologtkKFxHw11mAw9FojOBGleqtdPZ8YPanLofWArQ8A2bBrVM9trSJJZU/lQySpDGRmUgkpxL+qEw9n1PMERWiMcRVyiDYvHI5fkAsOhjvmVPFMpPqiT+widiuT9E3vhQVLo0jUkOKTChkUWU8kTCacHQS2jWFHKhmtbemFvUdLpN4luXTsSTe7rksgWg6higcJou+M3B3LBnD8wiIJ9x5+y6eYkohFy8AR6jUurq7eaC73MN/JqN5bnKc6Zzx0Wg0oMo/dPSDk0ePWa3lODF98qH7q32+9YvabOTujMXKLNaqshheFZlM9Mjxs6cVhk1rax67u8Nky5vymixm4Yzy7MWpW7euNNptlwY7X9/15l3bHpudi33uc98o95T96vnvHd0P+z1Bw4vUSq/rV915e2zvhz0npsFUIwY345879NLzhM/ede99v+g8R6wRGi4xVBVIdJXPaY2exlYdI5Ov6o4nnkoA55sVHkWpbNoPRDRyM0sgRncipFhcsYrH20ajX7NhY/fZ0y//6AeqTGzZ9tsXbNlM5Oxag238yuXeyxddRkNoeESOgjWbc3hrtj/yMBj06BGEmBML7PrFT3tPHGeNnE8nj7z88pF33r7rk59ZuWNnVHKAVmg9bYsz4Tk+YfHL8ufG+F/8zVhAdAnjfHGyUkajMVrCgmJqeu57P/irr//nL5V7vMQ5cIaEp7///ev4zhFuzCoxEolgVdIbDZAeIYuvv/lusc8I6oh9ePzEpg3r7rnnrsOHDgwN9jN7ELaL7EwDbAZtXJgN5SIvQnA0RY9YnRE0CUS6pfJKrUjEISYaUirGQkZvAxkyrJXVxPdmVRpsJ2DnWS3a1HCnEeYMJoWHsNKc1moSMaUuoyKZtppUhkUM/GLjxI6GS9Qs9UCJlKFjzvNTYkUxwbEOJlVjPk+o2l/Pzp4GVIdQDuwMLOgyaX0mZcN/Phlz5tN1YnGsLGcOUhBpqsSik48mcMBnDqXuwA4Z9Dq3Gc4tbUwZ2Vg6SwxhMJP1Z7PBWHYiFdMocmdBWdfINTVlrDsI+2aCE3Uq3ieGabEkFQMHe+lAusgxRQQT0q2Y6kidy2dFSjWRkTeWAUKTO0CbN5l10QjCg8LhFHCjVrOFZQcxQaFIQhaMK2cmpsf6gRVauGgpQHqhif6D+9/HJEXIJumWmpoXWA2W/FJ1LJKqqV9Q21ifSuZ1iKQVvsHhrkNvPj/SM2Epr2qoaTTrNCar9dNPPXniwkdDA/P3370juTVvNiujKXlltbf73IhFb3nkwU3Dk9dOHr1aX19nNJhGJphlDa4FhouvnbZpKmL50Z/81ZHPfeaLtnLd0NVTGvlm2lecMYS/OpMPYPT1qzsGBntygZCCEV2hIoPalTPH9VoZbMmwiNsVSLWZvKpm4ZIdT33BXtOgVoLdlvYj0eSZHnkeWXjU2++79zenD+dSCaylGCc4DWBwoCBr33hrU8uiX/zgL4H9sDktntraDPqBVMFEunlfZWXHiuTE9L6BF3Vk4FYpd37mSZ3XGyNHrUKOkmT3i78bOHbcqddh1ifkwKglyXb8vX/8+74rl+956mmdDfxVtHx8VlJaF1fpN82EEoGglSH4nVGcdqBEwV7P4o0vrdWbjp+42N39jfbWpoqKcqTU8xc6x8f9WpLCq/Nvv7cXd77NG9YOjc7OBfy739s30D+u15HeK1HlK//On3+trs7X2wPcW+75F4bwv8cdP06EtWiYTGUxyGNJtG/CNX1ugJySuVSm3GbP24xjPQkWUS5rld7uSpDgZeSyraIxlIyZQP6NJjC4GNTJ/MBVpn95ua8QnCfkJC3T56z28uq2ycD0wqYFWMoAm5GaVtoLEazIcpzhgKZyhmOJOSXiZi3KcDk6Ndai1b4Tjp2B2FxODCEanSGhUGNFTek0E+HIUF8/Tp+mdEGXSGxRKu9A/lKRGRNZT1GwWXMGtS5NLj/Mn6XZmHUZ1MTgorZqVTaxXi0SV7FyzKcXcrkpkCsrnEo8ExVLuSbGIIZppkQWHMWt1BCJA0v7ImkVXQcQV1gXpGMJkoVE0JEokzh2lVeUG/FJTwL5qsppcuUuJ15OVBSFQiKT0xEolk/3dl8wmO2Lli1H1PI2tljPnLly4cLqdetwoar0+RLJxNETp2cm/GW+Ko+3Mh4RSoeBwb7p4YF1Heu0mVN9w6OeZWtMFuNHZ44SoPTGG3/cuXG9YpEtOhtMRGI6jZZp1VvuGJvyq4xys966YmFNKKIknW59vUepc6DHUyUL69YtuzJ6vP/K1DuvflBX+4DNrsELCvFAdJRYFQLISa6Igs4LLv0z2eGhPS/8jgUqCzxylTK/axzluXjCU1fncFfCh9seelRX7SN0ExQ+FvmIQkjpWaRV5M9Y/P03387FSMgq0mOx6JTrDdVLF27ZtGXJ+s2TU1OBoRGdWhmLxo+8s3f7000JFFLCKVepcVXkNca7v/SlCwf319U3161bH8xlzXJFbGT4hX/89ezgFb1BzUQkrHtCQhKCq1me6z24741Q7LFvfxunapNCdubokQWZ5Qt9PrG4/Ncb4PaAuzAZsm5DxcIIy/QgpBqx5lGCPhoK+wnDSSYykbBQZDIoi4lfqd+1693X//BGTU01o38wGCVvYow0iarcV7/y3RpfedA/PTMzQb1QmwOdBhfgCsyCHmOyGPLjSbxCcdgjiADdptXqDXVfdlvU2pXL59IaW1nVXIKkuTJ1JhofvGiqWpiIhhGC8jMT8rkBwFrzZndQQG6boTXycILq6fdPWXyNDlnEbqpk3qKJpekekmUrCXJS66FtqFxiRa4WSZ0Yqfyr14YBh9kLap7NgXTAPMQKm85gLGaaicZjGhxoECZR1dhs72eSi7KZxeScyKaURkYu4UvjxmecKAKZCd/EUGgWzRcqGQE1SiI9RGNoQXQg8SEIQcKOR1izP5keGJ1eMEZ+RaE4oTK8udiE6x+LGpZ+cpW2FCuMpwLn8avC9yUasxhQxKQTCMGRMDbaZCySTZqyimwsyDKXKVsussBiekogS+Mbpyor85aXV99yy+aZudnevqtV3qqRydlbbtt+5sNDjNutiwiTSV+8cGFkYGBZx1oqIlQ78sLE2EBf7zWft7KuZWF1/cLBa32NdS2RbCrg9587fdam0C1oqBoYHHj5lbftOuv2rbctWdC+eeXKaCr44q63N65tuDIUXFi3AAVAhctNcGVkJnT//Z8q8xlP/3wPWS7Gx4Iz/rneQT/6V+k7CTZkHQ3CSTAst6swUHf2HU5FYkbC2xIFjdG2dMuOW/+0gZWu2eYwGUwsl0F6CCchOHgPIV94fbAqxjiGPKbWKtds3tx7/gw4VGAxWTzexSvX+pqbcnojEOkI8th/s3E6Va7LijRZLBtI4sPHQMmit+otxkpnTe3qu+9N4M8IkyhlH+3f23PmhFEPwrrC1ty0qKWp86OPAEmeGhjW5ROE0Q+cOXFw18t3f/oz/ZcuHtj1+0dXLZPa9bF9TU0VztBAqlF/k1EP+wlVfZFZvZXOH/3wu02N1ehlQKiYmJh97sc/37v/GFZbdDfEEH79619cuXI5/dHTM/DDH/7s/KVr69euqvJ5jp/4EAW4x+07fOikTsStgkNDsg3d9NR0M97V+OgBHkNsDFplmVyTimQme0hwDAur9TZXRW1Opc0hIcv9uEKjzUmNdBqrWxJz84rZYYRYXC5zWitW8oJIPRCQ5xJoKDAwWiOjrZXL7fYy9BS0EbqVKFXiMYmOi+cFQUs/pUvFDoHdCkSHJCurvr1nT9vSZQxHjEZwA99RRK8wchQKYRxIYLVUCvLXyNUBnXF3ONIg02sLScQNjUnLYlqmExZstngMNVbC7rSBrS7wzeQIfYvEmwVnIXMLhCdGe5JzIgkfmg1b/MENjC/UmocU14fFijHgXbepSAecLEnR1xUzej0CsCocjRDHZXc54rD17HxleYXJZGbpACwPwxJeGcKu5Y+IQELWZDrj5Mx4RU0jSlK1QTU6MmY1lYMXaijzrNx4W4KjWBLpwWSwQMtwPXLU2HBfIpGWF6IfHtiz/Z6HuxNXZudzj3zik1ab7sVXXmyrrqyvIkGvO5QBZlPnn5jtnhxQ5q1f/tL9CAfWtKHO44wGU36/dskDK4Khy1qltaDSVLndbc2N8cSU3UJUCOi7MnzlpnvTNWUN/rAw3dBZKA5ZXnadPNpi8yC+AHqNMQ/Jg+QS2x54uHrFunAWRH1ZNjCz64XfVNbUtnWsMlqsIr0fsBaKQiIIAO/VmmofAhipTizlrqf/64/tFW6lxUAwGciaCTSZGRYhhXd3vRINzAKZC2xbaHb80v59zRs2ZtUKTD7ZSHR8sL+/q7us3GtxlAcEUYAbn+vvu4YmkRVmRXPbuk8+XjCqrYsX61WG6cvdJ1/bZcqBYy07f2DvxnUb9r38cpZI9qIAJg2d0teV9pWVlfV1dd3dV8BTM5rMavyVQcrWqFOh0FNPfqm1qf6tN//QsWIFENdw0Y9++J2R0af7Bsbq6yp//Q8/SyRC0dD85csXG+oXPPOVLz751NfD4fjsrB+4nTjzyd5D4VDEXQ5WuhFPwzZ9c9elixol2VJywWwyiXt6hjB4ATqOmwpGZQ3uGQhKjDxqACx0xLyy8gVCEPJJDl9SCk8N+DaNu65MTTw0M4w4pxZZPtDm5Dfeugm/h2JijOL8cINYaSbcKBEucwgH0qx4EwcKEYJCXPJWeGVGC2pz1uxCdIbVWLMX7YR4oqNlFJjbhMwJLGOknWynVnVRllqT08xm86disUOh8GQk+T9zBZaD0Dwig0FvY9/cbD5z+kyxw4uTm5jZxAEKb9Is6NTqfdFodmB4M9ppQitQh4sqF0veaIXUBGnqltpC/SVxNI8wgxJ/dn4ubbGAfMlsC9sZbOCspDIJIniAliGbhxwnz3l/mBQJNAGN8sTANV81/kozYlmoMfT3XwG1YXx8urq6ovPoHn/Av2jxsoqq2orxGQRqFLyYj21WO8ShLqjMpDv3Tw9dGTx98XyZ03Dh+PFG1yNbbr/9ZOeVP+499qnHHv7af/rKtUvdyTirXfWx86fKbeUuLy6hQIUrcmq90+1KRA0wj9PuxKAXikTdWMHVfmbRuurmZ77yF6OT9517VsjxqDuRR4kTDY+O9Ly3m34hVwbuCCGZ/IHPP7Vyx0OzKZlZI+8hx+Jvf5Wcnjovl/WsWLXugYcdjW0FkWUBk1H+g5deyMxNWRxm4AIgGKe3ylzm1Jc5GpoX1zYvwjWW+QeXI1eZZbBAujrM6iIr8NWD+wfPnSVqJI1SCyEvlpSZrHd/4nG6DzJkmmX5XlbuCgtNGkasNENnEFnB4ibmx1JTjcKHz6smFikS2ffqq1M91wBLwjmjSAQf34FDuWTJsouXukkJumHjrbW1leMTJIognZpmZmo6FU9UeNzAhf/sp3/14IMPVtc0lLvdV64N33vPnRaTbmpsgNBdPG6R/QcHO+12y/Ts7F987zk86shu0FhXy9o+FiUHa0VLSxt+iC5X2ejIaGNzNesMNeMUswJoGrSC0RZsO6B38mSiQFbLk81Cw8qKTsTLgK8glNUAjOCLgEsdpsEkYC35VJJVpXAMVyhXrV/TUFsnUtUX+UlqJIQrcZq0l35Cx9KBeLgk+8EQQh8pgJvaF7YjuHGeSZikAEyBgjkFywi9GLyDDpm+YRDkDnT/KY3u/UKucybwx5nJ3mwurlZ5cDY38vXRMpoDwcmhoW7ixZhYbFaLVCuxFwwmHorfv3/ebyMtfCDIHCbCG4WGVLAgnMaejcrcPAdK9ec8jynOhAiKqZTDpkthAYoD6YNoJsPwkE0l8YBFjkL7QJxzEtMhyO45udPbgM9fKhpKRKNjvRdtRu3sZICZw+jQqYyWSq+K/LUGreHCiSNM5Xff/1BtIxihCvzUBgavRqLoYC33PPB4c/viQsYbDYb2vP3WZ5/81JLG+traqrmw/0LnRyePXV7Utmh1R3NVrVeMVcrAieMHxvpz7c3eaoe1ygPucHR0YtBlXBTTRsfnps68e2Lx4kU1vo4vfeH2Jas7Kqpsqdx071gQPzM6CF0Z/sYE3LkMmuGLnZAKKPO1S5amDYYF6zaFcTTBN2V8ZM/zf5+eGCX5GaFV4+fPvdjb//RzP1N4q3AMdFjsDVXV3f1UXvhSo+ya8k9PMMLmFZeUr1tr69fuvL9t3W1ylUGl1YPCZhI5zFGv4JaN5nAuOc80WZCZLGu23tmx4z5b4wJAOVniU4QYmKrqmmuAdioUU309Y5e6XKtWR3LgekA62AI0eWJcMQOqFL3dl5iSGUBY3BY/v9h9bGtvb1u7du2B/QcAXLrjjq3Pv/Ayiw6T2fL6W+/ix3vrrRuwmz306Ce9Fb59B4/29A46HGWXr1wLBCPY3rQ6C/6P2DD+94uvmi02biTEfnZubu3qVQsXtgDPo/Si3za2tbVfvtx15x13HDhwcDWs3FA3NDBMl1EToegVHihIaAVFKm7QqQIkc89E4AmJ1Bj+BCfApxiMaLrghFhOa8nJCVqXW/SmppaW1rYWpAYV3qQEQBc3iXwlNpNmv9IxB5yh1PXnX59zhN9sR0fHksWLJyYmQH4QMasClQNuYQhXIjcK+VmwB/KH8D2gIulU7jRogIHAfF5utNiyYT8p+l579eWGxjpfta8CxTIsLWOuKoAlJ1WMFosWFZlwoLcPfDp8d+G99rZWNEY3ylz/yxnqQG3hPanOUn04ZrtuomBSRj9rtlgxEEdiAfjNZbMRIZ8g+RnZPXJpvU6bicpCsazWaKHpDIcEKzW0LPKWW7HI77jvwWQ8fOjAe+Yy14Z1W1H3tyxaPjbY03vp/KX6xqULl8ZSGaPNyqJx/we7+4b677jnUavL5Slr6Lt2iVTtZWUVjz75hRn/2OTYxMTgaGB2/vSJjzzOPE63ekKFTLg6xQIT2eY7FwORUVVFT8b3n+i9tb11wbqKox/t6TrS17F4TfXCNq3SkpIFDp868L9/9T8GBzES/qjYB1BHAWBpu0nfl5nJ6813/MlTres2KMwAhLAkxptq+oWffy82M6Y1Gmze6pGhfgOe8/6Zo2+9sfVLXyZDCx5gExOk3GBZz9pSCFfkzUuKhb5cI8+Gh669/YufDXVeeuiLz9S0LjH6GpJzM1oDEb1yEhDDhiqTY8HyVSvuuadm8SJcw7F/4HYphgYZlu4sS3Dh4iHPs0zqPLjv1pYmndWOnwAYyqiO0KogpqEMKC93jQ0HSbrJh/jYBy79JFR6ZccKhuArV7sZ8u7ecfuhIx8yaOJ+8tIrr+9+f39dYx3Zl/r7B8dGJ3C3QGQFNP2HP/pZc9OCSCzW09s3MTmlRo+oUwJJaDI7lixura2qwpUXUz7Eunz5cpzJGhsbgQ/auGH9+MRUa1MT/gzDI8OCscQ0xFqIPfk18/HAqK2sLh9n3csFMUFBtMJyJkLdhQhAMG8iEXbYyrBJ1lW2NfhQrNoRTZF4ydQNyZbaRe+w8ZM9JFs6L3Fj6ScHnGGD0Akruffee7/xjW/wHFyC2JxOp4TqHwwFkWPpBxiDA7GSSCXx9lCDgGTWq5LY5HMGvSmbKnztP39Lq0OzY21qXEDmuLb2ttq6ejdyudtNglu9wYhPJa0OhwMvvvTP8/OzrAjcbk9jQxNPphrUtlgdUR/puFR/zku15QxXrytmoDPgUawuD9pIBAYkdugDztRi8NMD5gsEGqZdJCg18T1AgRTHn3xjyyLAQBYs9ECRAaaJiRGgaa02d9vChbUti90nj/oD80aiR1moywtzgVnyxdQ21J49dWTfe7tXrFoDQS1ZumLBgrU2d8XIzHD3hQuVDq3b6ZDJemPBsFln2nfigDpr6eiwhaf9W9ZsW791dSTUf+5EP3kuowFspI6COnn13JXausXzqcG3dr2qStc98ODm48f3nz4zlM8Hii0XjeXLsyL3+nzRC73bdu7c8OBDEdgAIyRpJ0iaRojL/DwTV1lD89q773Nf6Ro+fyIwMtHf3bkpGlEY7Uz7Zb6K2b5O3GeElzWuWWZr45I2sHN6jhy3ZfI6eb5z3x99ddUr73/kyed+MTs64nJaU5FU54mTdpe9YfFCO3mCc7Kezss1tbV461F1MTeSgyaT6LncjcYOV3wk0sTUZO+BPb7W1mvXrvVfuKAhLaPw7SiozUZ3bW1ff0+5y+px/2u3tSLllQgR313shG63a3bOv6i9qa6u+lLX1dGxsYRYxWd7rvXCkeBf1NTUYJlDRAdNB4VbKByhjxYubF+//hYkIKsV9G24TpVORlAtlDldlV7+q4RkoR7k3tbWVvrWU1ExMzPX3tZcXV05NjZGEjUIBokQGxnTtxlVRXre6LAb9BmM8wwfkAG8B6MQqkhkmcVscTjspOxFdZ3FPSuegP18vkqEXh5eapFEr5yB/dhD31AtV0tlpALiMxcpWzrPp2lvb7/tttsOHjxILlS20dHRixcvwpPUn422wJmEUGrJKmzjG2aDgMwBWIWxSqUkzQMOdADD4xsUigSPnzwhPEx2/QHJlsB0THHwc1lZOT2Fkxmpjnfvfp8pl9V4e/uipqZmWEeqBlWitux5nVT/0nkOOM9Gza+PN3whEYCXLcRTIRbuchn67jgyMWoJvF7wBMD4m0zm0qkEwrwIrqB0NDQ80DUql3UsW3X6o8N24hlMtujcVNe5E3hD1XmrNux46MqFk2XeqlAyk0wEZ+b9nuoFvqYld93/6PTk/OzUjElH1m9b/eLGU+dPdHderjArooHElo1bYoHM6mVLDHY7zlNnjnV2NK29Z/2K5pa2oZm++bFBzFn2WWuj1UO652DMH5wJrlhm/d2Lv71ypt9hSN+9bYunzI7MRlIJ0T6xFcVxlXLNmlVHuoeWrF6bicXmhoaudJ6vb22tW7qsqrlp6ZpbT779CtNTgjDVOf/cTIgFhMddxqdI4TSTzjDyEcKDhyFRPbd/9qlgQU52FeFtJtP2799PFj2yPh16683GVav1Hl+FfSkQ95Yq9R3tS1BLQP7hXMEiSx35w8tum2PxhltlRoOrsQngBLyxgS6jfhyJ1a5K0Xfsw8tHj6ANAJsaGZBJA3CnvFpX09a+f//u5ctacVQqNurf3/Gxkcz7+gw4qbNQmZ8PVGxdT1gGTjB8YpZDTEiEXIPgpuJlQsZCPwH9oiK6XoJRmxz1QPwS/OOr8HncbjvuGUB1FDeJdNCaICsPDAzYbHYoLxjEndKNDYDclQAcJYo+q4QpkeiL4DWDugXP3GIeRA1zInyIdI0JDShW3pqcDyEkA5PpaW612W3MYBJdlponUa205yQNlPZwY4mOpYNSGele2OPzn/98Q0MDowMJ3jCikjaTPctU9rwF9hASP1kZQX8UQcdpdDYEFmZiqYImv2LFShQcDFjAgjDEDA4MIh4GEW3kCrJQj01cgCoYbVLoh1GJG40sd/GwffbZZ+lbekJqBVViK/ac2EnHUvWkn9KAAhPyFbBvInAKTRHfKZ5IsWZFWc/EDboWq2SQo4j8QpuH6wzzp1CsFI+TkVC5txZcHVKNE5FJD/E9/eODE0MDWOVqWzvqm5pwPw2JsCb8AhOdH51oW7bWXd3g9FZjeAmHE9PRghFnTrLbo34qdzIM1vnKNuUVBN0cPfXh7ncOL/Q0V1RUV/ickOmJQ28NDaXal5SfOT+y497PbLp74x/2vu6we3P50OVzvfmEptLrMBk1dbVLar2HR4YMCWojRHZB43hcrV+3Kq40/93f/J3VZhns6Q5NTZ+1uxrXbVi7464la1ee2Pv20OULoz1XMCig1gb4ddofHBoaKVvQTG66Sxe7mFBZQ+gsJq3LoVcbRJLdXGHptq3ZRLjvw6MmhToxM/3WP/zDQ1/7usGoe+M3/7hs+XKjy22trMOugZWDIQGU//Onj13Yv9tYWfnZv3jWWlnLBLh4/S0fXO4U7hUiNiOHwh5Pb2Q6YOIBq4+wYLBY123fYfV66xtqP/3Eo0UNoPQd//09hNXS0kTYdCAY9Ho98/NM8/5QKow/TNGdJhNHxUwCQBFaz1uFjM18CHwzmNxlHpcVodCO9sGCDEz60NKcBAFJ75MOSCC5ZMmSmWmyo8URw+bn/BwkkvGcRqUDgQyGT5JbNcIIEgAWn8UACyYNqD1woBAFjQayURLNaEf9TnIh8SKxvhJb6UXST4iVA2kPyUq8J+2luYWTHLCnjFSMJ/CThy9atAgA+MHBwfHxcYBw4Ubcg1hJSawIT3LMEhHwTtSbjD7YMKEXADW3bNu6dOmylSs7gAjkFm6cn5sDaBxmZkgMEa6ODAr8aDI5MzONuEf9y8vdf/ZnfwbP07G8ncYIfdWNTpNqyE+pzqVm8pNjmFCUJbQ5if9/NEw3MWSiTkIKwkbEpBeOEpcpRh3czXTmCktZtcPlpsVz08OVVVWJXKZ/bHTRyk1E73bOzvDEZCQA9DvZ5qzuykZf2cjFcxoji2/UrXrMoZOjIxajvOvi2TKvLxJLX2WAKchJOH388IEL53O+mrqlS+r2vPtuhcdT3+SucXk3rm2biwffOXqkztNyy7pbDaoTc9Oh+YAu5aiLyLNalamxpmY2MJoJ5dYtXPvQI7drzIVK68LvfPt7xw+v/Ot/ErZ0viG1Z6/XytctaXxdkzh+7EOdyQLCWDwUvfzHd7uPHvuTr3/tzgcf3f/7F5UZkgoRTChfvGXb5gcfy6qN6XDUatLdumP76397FQCj2PjEsZdfalq/yVzpS2AEVxBkuAB3M/SYBmV+6PTxY69VYCvoO7Cnb99uhd702DPfrFy+JpLPzIwMFmJhcB9Q2mCg/92z31+5bQeO2ituvxP1wLkP3vWP4sOBThU9OgpEYvZBZTAtvGXdqtu3WspskbGe73/n6fpqNysY7Pa0i67+DzanswwqJN8LPqToLxgDZmZnoxEgxf1ovKENYrIFtzGX2e1MGsx4KG/MRpOG/BJFIv4PHi5dYlKq8HphZ0neYwROJkRmX0gcGgW3gmkN+GDkNHa8hQ1KhdA5jyj4f33+zQXgrhKbSRIpPykgUTnHEjWXak55joXAqdWi+WeRSZXgOtxoQTGGl5jAE0TEEmMVhicDHExOjjFScEszKuDmlmXLlrL6RXxF783TGLbgWDiZu2BLTvJAzkRFVMAcHdjQsGDVqlUUo1bSGMGBVA2pIRxLB1KdpZ8cU/OiOIo+XU3aWiw+WvqOQC5cBImCROoIgFxPSi2LjShd6lLX1KazEsdrs5ucJpvWjIduaN7qsldV1WuVsng6dP74YRBw58d7G5esHRvtq/KVEa0XJE0h7qB2e0tzGyiGQJCODVwZ7L0C+tC1rjMel5OAJq/L2nXh4lgyv2Hliicef+zU2ZOv7Hp3ZeuKqlq7UcMcnfntb34ve/iJhz/1hT+++ZJLb0+m8Fmr8vqqVi1bcu78wS2rVq/fsrTff+pkj2nTresqW725S7g7sAntOTuiOJkH3HbtL37+/b/+ze9myQWh0R9+/wMVCFM4O+pNq3Y+qnd4zx89EJmZXNDWevun/zQOJG1ete/11/C2bFvcctdnPrV/16sYesdOnxnpuuRtaa1dscLpLu8+cZJcv0kBsEd0aCE5PdE5OpIJh8wqOQgxL/71c9uferptzfrdb+6aGukFaRB0Gr50dGLgnV/9LfLb0rvvbtt2Z+vqFf0nDp98b09ofAyTJiZKd2vzpgcedFZXY3srl2du37CiCusxyWuK7fmPmRAmgg4EEFMm63QwrZldTrvXWwGII8sVSFbMSiJJNlMQoSBilsIhBoLjEjeKXitSDGwGlUgSYLEz/9UO6gF4lo3iPJwDNEMQNnIa5RB6eQOuqrCBeAUeq1oSzmCBF6sjCktcxF56y7969L/5QTHuYs8mEbFUpETZnOeYjWJSSakAhZl4aSmsyKwIs0GKDBMwJLzEwejoGIwEa+ELiaTH1IdU2dTc7PF4mNO4XXiYMFmpVNgq+HDUlsI8ED6kQLEhcuyxyIMIpdJLqczN1aNWUvVKJ6Wf1JPncyzZCenyVoVyCynZEILm52ZRgxMUM9TPWs6DQk5RKMvEp/RG58yEIdUbKijmGpq8NvOKgZ5pva4mrfNcPEf23pRWtZXhMJ/PzE/obGX1wTmt3ezwuR+7dPK1+Xn5oDZvM9czGiry1qXtlslBrTzjNoN6NttcsGjqGqo1ihG9sTwSr5ydnxkdnZ0fmxwwNPZOtNozmtGJYbulxaq/s2cUFKav5uOZ2Qnl4SOAP/ly8bK87N6VGzeFsvMv/+F4eKbMqHPmNd0vvdRTxB1F/4EikrmcJuNjm7Jq1d/72jM9Y1MvvroLRL6UWbdux10Fjax/tM/e4Nta8xjgMZDVwMQ41oqh4aETb7wYnw+caaq7/5OPbHt056Hd780NDmlC0cHjhwbOHdfyXWTyhio7izdCRAFJKbOSekodKDcnUXej1p1Ndn64j2WqIh5IROfRiPPtFi9vTcRj/ee7Tu5+xVNhBkLerNUsX9ja4HYeO7Dv6uUum8W2asMKh1Hmyvmb3A632aTNZkJgAaBqtf2LelD6qB/b88k5A3EU6UNg5wFAgy6hvJzlGB9dTKHsKSWVvMFxAhRWXMazobhRjAKcpOM4wfmbX8QlaXLgJFfhMUiQCUEqBmFxcOP54jnSBodLHCI9kJP8ZIOy2W5+fulYeiAlOaAkT/7YpdKLSiU/9nbKc4ZXU0kYCR6DIRGhaRoMOSvEN7kkprKn06qLG2+E2eBAzvAEqkedeYhUTxibp3GeM7yXjfL8lA6k2hZPix3VLl3ldn7yauk5VEwUFrIPOnJFmPAX8RSEYskrDtOr+AzCACrkGb6ZQg1cCEoaGoWfOh8FAzOfGIUbgcniGaIkoefch1q6iGgqxlFVnswSIrK52NfiRtGR8AWv44X0rni2+McFMBZEmDlTBC/lHNY04AfR0QGTzricwZFAhIYJSwG6Wj61aIVQi9NOFn74R4jOyisYj/XyfFlB1vf977/3l/9lJyF/rIDIIJBJCidt9P5gTg8OTfjjJNwxCVSDPBYBYEQLxG2KakGKhUI8MOcfHmEFTDoEHTo1p50MevPjk2oRdiqA9GgoaxqB+kkVRHWoubgbrYfAVEllC2qDzlvJSJien02H52krwJVap02p0yUCYZZ9jopyMVkQFg+h8IFkYFaDAiwn953datXpkEvAZwQBGq0dvQG+aa3B6BSfoLh985vf5O9Pf/pT6Sf1Z+MbQzo8lZOQkUQEEk1whgKlYw5KZziQfkoHUIlEzRLF0KucZyu+gSEHXV2UGZViFOCn9FgK3PyQm3+WjqUCPFx6BdVj4/kSXVLs5u1b3/oWzPCTn/xEYgYuUbj0BN4rFeZppbu4Wioj1VZqSOlesR4uvpEzSKe8mluYJDnJJaY7uJTWwWbcSOt4iPR8CtBkymDwKD1Teq/U/JtLSuelqnKen6WKcUDNeQJP+/nPfy7NhKxobCIc4N9uRSaFrYQHDlk5+QY8i2IQS7Fwkeau3yZ1iFSmWEScZ/SWya0IhOJJxWws4uYbNFS8U9RPemrp8cUzYifc94r9jBvUv7kq1eXm24u3pMX+xga3CBRzJC64FY7ly6NoIaEHY1lzWx11wVyv0OiEcp1KiCBmMbwwvmHII6hEKEJwMaMHREVQAdIgwXLAdDFIFLtFSLpi9BL/ikoYfH6vD1U4OsuzDAuoVvONyAh8ZVB3EF3oS0Z1jARCmwVRMUKInKHMO8LojXcRAwu6cj4TfcVIpBXhNRySi153c+99jHD5umx8WjZ6gG8vseKN3vh/+fvvvoK3sMbjcdCl9ArO/PtPv04x1y9K5Cj9kG752PM/9hCusvEKXsQG4bIvvQs+kR7IXuITbudq6S3Scak85zmmc3gIJbkFNTLsDR8yxsJvXGLPG5kzpQISE1KAeyXO4So/pYeU3nvzG3l4qWJSBaT3lqrBkznDRruoAx0ncRPn/7/bRKNHFi3+3KKFFvD6aDjKetGTQjoQ/0PqDOBiBsf8UBxbOEkviGWX4CtBVdKZG10jfon/i1cYl8S0zxNuXOav1NFSkaIphzNiZcVdxbmd3qa8EDZ4CZNBUdIQrym+StSKjXuoE5v0hXhHcZYVZ/DYIC6n9MJz585xjBm9dEbcfoMQOeYW6WepwM0HpQKlk9Lt/Cw9R3pCqYB0SbqxVPjfvqV0ifI3P4HzN1+SrlJAOnlzSemN58+fh/RpIMxQetTHniCVlK5K3CU9h2I3H5R+lk5KN0qsxTEHcBdPkEpKbyntb64bfMin4RJb6bx0zE8OSlXioFRGOmDPvVIBDrq7u/8PKyfFBrB+BjMAAAAASUVORK5CYII=", "text/plain": ["<PIL.Image.Image image mode=RGB size=300x300>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from PIL import Image\n", "\n", "combined_image_path = os.path.join(data_dir, \"combined_image.jpg\")\n", "panoramic_image = create_panoramic_view(query_image, retrieved_images)\n", "cv2.imwrite(combined_image_path, panoramic_image)\n", "\n", "combined_image = Image.open(combined_image_path)\n", "show_combined_image = combined_image.resize((300, 300))\n", "show_combined_image.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**2. <PERSON><PERSON> and explain**\n", "\n", "We will send the combined image to multimodal LLM service together with proper prompts to rank the retrieved results with explanation. To enable GPT-4o as the LLM, you need to prepare your [OpenAI API Key](https://platform.openai.com/docs/quickstart)."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import requests\n", "import base64\n", "\n", "\n", "openai_api_key = \"sk-***\"  # Change to your OpenAI API Key\n", "\n", "\n", "def generate_ranking_explanation(\n", "    combined_image_path: str, caption: str, infos: dict = None\n", ") -> tuple[list[int], str]:\n", "    with open(combined_image_path, \"rb\") as image_file:\n", "        base64_image = base64.b64encode(image_file.read()).decode(\"utf-8\")\n", "\n", "    information = (\n", "        \"You are responsible for ranking results for a Composed Image Retrieval. \"\n", "        \"The user retrieves an image with an 'instruction' indicating their retrieval intent. \"\n", "        \"For example, if the user queries a red car with the instruction 'change this car to blue,' a similar type of car in blue would be ranked higher in the results. \"\n", "        \"Now you would receive instruction and query image with blue border. Every item has its red index number in its top left. Do not misunderstand it. \"\n", "        f\"User instruction: {caption} \\n\\n\"\n", "    )\n", "\n", "    # add additional information for each image\n", "    if infos:\n", "        for i, info in enumerate(infos[\"product\"]):\n", "            information += f\"{i}. {info}\\n\"\n", "\n", "    information += (\n", "        \"Provide a new ranked list of indices from most suitable to least suitable, followed by an explanation for the top 1 most suitable item only. \"\n", "        \"The format of the response has to be 'Ranked list: []' with the indices in brackets as integers, followed by 'Reasons:' plus the explanation why this most fit user's query intent.\"\n", "    )\n", "\n", "    headers = {\n", "        \"Content-Type\": \"application/json\",\n", "        \"Authorization\": f\"Bearer {openai_api_key}\",\n", "    }\n", "\n", "    payload = {\n", "        \"model\": \"gpt-4o\",\n", "        \"messages\": [\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": [\n", "                    {\"type\": \"text\", \"text\": information},\n", "                    {\n", "                        \"type\": \"image_url\",\n", "                        \"image_url\": {\"url\": f\"data:image/jpeg;base64,{base64_image}\"},\n", "                    },\n", "                ],\n", "            }\n", "        ],\n", "        \"max_tokens\": 300,\n", "    }\n", "\n", "    response = requests.post(\n", "        \"https://api.openai.com/v1/chat/completions\", headers=headers, json=payload\n", "    )\n", "    result = response.json()[\"choices\"][0][\"message\"][\"content\"]\n", "\n", "    # parse the ranked indices from the response\n", "    start_idx = result.find(\"[\")\n", "    end_idx = result.find(\"]\")\n", "    ranked_indices_str = result[start_idx + 1 : end_idx].split(\",\")\n", "    ranked_indices = [int(index.strip()) for index in ranked_indices_str]\n", "\n", "    # extract explanation\n", "    explanation = result[end_idx + 1 :].strip()\n", "\n", "    return ranked_indices, explanation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Get the image indices after ranking and the reason for the best result:"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["ranked_indices, explanation = generate_ranking_explanation(\n", "    combined_image_path, query_text\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**3. <PERSON><PERSON>lay the best result with explanation**"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Reasons: The most suitable item for the user's query intent is index 6 because the instruction specifies a phone case with the theme of the image, which is a leopard. The phone case with index 6 has a thematic design resembling the leopard pattern, making it the closest match to the user's request for a phone case with the image theme.\n"]}, {"data": {"image/jpeg": "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", "image/png": "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", "text/plain": ["<PIL.Image.Image image mode=RGB size=150x150>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(explanation)\n", "\n", "best_index = ranked_indices[0]\n", "best_img = Image.open(retrieved_images[best_index])\n", "best_img = best_img.resize((150, 150))\n", "best_img.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Quick Deploy\n", "\n", "To learn about how to start an online demo with this tutorial, please refer to [the example application](https://github.com/milvus-io/bootcamp/tree/master/tutorials/quickstart/apps/multimodal_rag_with_milvus)."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}