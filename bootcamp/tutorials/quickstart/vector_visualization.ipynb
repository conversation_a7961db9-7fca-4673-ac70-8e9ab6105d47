{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/tutorials/quickstart/vector_visualization.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/tutorials/quickstart/vector_visualization.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>"]}, {"cell_type": "markdown", "metadata": {"collapsed": false}, "source": ["# Vector Visualization\n", "In this example, we will show how to visualize the embeddings(vectors) in Milvus using [t-SNE](https://www.wikiwand.com/en/articles/T-distributed_stochastic_neighbor_embedding).\n", "\n", "Dimensionality reduction techniques, such as t-SNE, are invaluable for visualizing complex, high-dimensional data in a 2D or 3D space while preserving the local structure. This enables pattern recognition, enhances understanding of feature relationships, and facilitates the interpretation of machine learning model outcomes. Additionally, it aids in algorithm evaluation by visually comparing clustering results, simplifies data presentation to non-specialist audiences, and can reduce computational costs by working with lower-dimensional representations. Through these applications, t-SNE not only helps in gaining deeper insights into datasets but also supports more informed decision-making processes."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Preparation\n", "\n", "### Dependencies and Environment"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"is_executing": true}}, "outputs": [], "source": ["! pip install --upgrade pymilvus openai requests tqdm matplotlib seaborn"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We will use OpenAI's embedding model in this example. You should prepare the api key OPENAI_API_KEY as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-***********\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prepare the data\n", "\n", "We use the FAQ pages from the Milvus [Documentation 2.4.x](https://github.com/milvus-io/milvus-docs/releases/download/v2.4.6-preview/milvus_docs_2.4.x_en.zip) as the private knowledge in our RAG, which is a good data source for a simple RAG pipeline.\n", "\n", "Download the zip file and extract documents to the folder `milvus_docs`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["! wget https://github.com/milvus-io/milvus-docs/releases/download/v2.4.6-preview/milvus_docs_2.4.x_en.zip\n", "! unzip -q milvus_docs_2.4.x_en.zip -d milvus_docs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We load all markdown files from the folder `milvus_docs/en/faq`. For each document, we just simply use \"# \" to separate the content in the file, which can roughly separate the content of each main part of the markdown file."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from glob import glob\n", "\n", "text_lines = []\n", "\n", "for file_path in glob(\"milvus_docs/en/faq/*.md\", recursive=True):\n", "    with open(file_path, \"r\") as file:\n", "        file_text = file.read()\n", "\n", "    text_lines += file_text.split(\"# \")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prepare the Embedding Model\n", "\n", "We initialize the OpenAI client to prepare the embedding model."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from openai import OpenAI\n", "\n", "openai_client = OpenAI()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Define a function to generate text embeddings using OpenAI client. We use the [text-embedding-3-large](https://platform.openai.com/docs/guides/embeddings) model as an example."]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["def emb_text(text):\n", "    return (\n", "        openai_client.embeddings.create(input=text, model=\"text-embedding-3-large\")\n", "        .data[0]\n", "        .embedding\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Generate a test embedding and print its dimension and first few elements."]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3072\n", "[-0.015370666049420834, 0.00234124343842268, -0.01011690590530634, 0.044725317507982254, -0.017235849052667618, -0.02880779094994068, -0.026678944006562233, 0.06816216558218002, -0.011376636102795601, 0.021659553050994873]\n"]}], "source": ["test_embedding = emb_text(\"This is a test\")\n", "embedding_dim = len(test_embedding)\n", "print(embedding_dim)\n", "print(test_embedding[:10])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load data into Milvus\n", "\n", "### Create the Collection"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["from pymilvus import MilvusClient\n", "\n", "milvus_client = MilvusClient(uri=\"./milvus_demo.db\")\n", "\n", "collection_name = \"my_rag_collection\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["> As for the argument of `MilvusClient`:\n", ">\n", "> - Setting the `uri` as a local file, e.g.`./milvus.db`, is the most convenient method, as it automatically utilizes [Milvus Lite](https://milvus.io/docs/milvus_lite.md) to store all data in this file.\n", "> - If you have large scale of data, you can set up a more performant Milvus server on [docker or kubernetes](https://milvus.io/docs/quickstart.md). In this setup, please use the server uri, e.g.`http://localhost:19530`, as your `uri`.\n", "> - If you want to use [Zilliz Cloud](https://zilliz.com/cloud), the fully managed cloud service for Milvus, adjust the `uri` and `token`, which correspond to the [Public Endpoint and Api key](https://docs.zilliz.com/docs/on-zilliz-cloud-console#free-cluster-details) in Zilliz Cloud.\n", "\n", "Check if the collection already exists and drop it if it does."]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["if milvus_client.has_collection(collection_name):\n", "    milvus_client.drop_collection(collection_name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Create a new collection with specified parameters.\n", "\n", "If we don’t specify any field information, Milvus will automatically create a default `id` field for primary key, and a `vector` field to store the vector data. A reserved JSON field is used to store non-schema-defined fields and their values."]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["milvus_client.create_collection(\n", "    collection_name=collection_name,\n", "    dimension=embedding_dim,\n", "    metric_type=\"IP\",  # Inner product distance\n", "    consistency_level=\"Strong\",  # Strong consistency level\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Insert data\n", "\n", "Iterate through the text lines, create embeddings, and then insert the data into Milvus.\n", "\n", "Here is a new field `text`, which is a non-defined field in the collection schema. It will be automatically added to the reserved JSON dynamic field, which can be treated as a normal field at a high level."]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Creating embeddings: 100%|██████████| 72/72 [00:20<00:00,  3.60it/s]\n"]}, {"data": {"text/plain": ["{'insert_count': 72, 'ids': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71], 'cost': 0}"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["from tqdm import tqdm\n", "\n", "data = []\n", "\n", "for i, line in enumerate(tqdm(text_lines, desc=\"Creating embeddings\")):\n", "    data.append({\"id\": i, \"vector\": emb_text(line), \"text\": line})\n", "\n", "milvus_client.insert(collection_name=collection_name, data=data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualizing Embeddings in Vector Search\n", "\n", "In this section, we perform a milvus search and then visualize the query vector and the retrieved vector together in reduced dimension.\n", "\n", "### Retrieve Data for a Query\n", "\n", "Let's prepare a question for the search."]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["# Modify the question to test it with your own query!\n", "\n", "question = \"How is data stored in Milvus?\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Search for the question in the collection and retrieve the semantic top-10 matches."]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["search_res = milvus_client.search(\n", "    collection_name=collection_name,\n", "    data=[\n", "        emb_text(question)\n", "    ],  # Use the `emb_text` function to convert the question to an embedding vector\n", "    limit=10,  # Return top 10 results\n", "    search_params={\"metric_type\": \"IP\", \"params\": {}},  # Inner product distance\n", "    output_fields=[\"text\"],  # Return the text field\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let’s take a look at the search results of the query"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "    [\n", "        \" Where does Milvus store data?\\n\\nMilvus deals with two types of data, inserted data and metadata. \\n\\nInserted data, including vector data, scalar data, and collection-specific schema, are stored in persistent storage as incremental log. Milvus supports multiple object storage backends, including [MinIO](https://min.io/), [AWS S3](https://aws.amazon.com/s3/?nc1=h_ls), [Google Cloud Storage](https://cloud.google.com/storage?hl=en#object-storage-for-companies-of-all-sizes) (GCS), [Azure Blob Storage](https://azure.microsoft.com/en-us/products/storage/blobs), [Alibaba Cloud OSS](https://www.alibabacloud.com/product/object-storage-service), and [Tencent Cloud Object Storage](https://www.tencentcloud.com/products/cos) (COS).\\n\\nMetadata are generated within Milvus. Each Milvus module has its own metadata that are stored in etcd.\\n\\n###\",\n", "        0.7675539255142212\n", "    ],\n", "    [\n", "        \"How does Milvus handle vector data types and precision?\\n\\nMilvus supports Binary, Float32, Float16, and BFloat16 vector types.\\n\\n- Binary vectors: Store binary data as sequences of 0s and 1s, used in image processing and information retrieval.\\n- Float32 vectors: Default storage with a precision of about 7 decimal digits. Even Float64 values are stored with Float32 precision, leading to potential precision loss upon retrieval.\\n- Float16 and BFloat16 vectors: Offer reduced precision and memory usage. Float16 is suitable for applications with limited bandwidth and storage, while BFloat16 balances range and efficiency, commonly used in deep learning to reduce computational requirements without significantly impacting accuracy.\\n\\n###\",\n", "        0.6210848689079285\n", "    ],\n", "    [\n", "        \"Does the query perform in memory? What are incremental data and historical data?\\n\\nYes. When a query request comes, <PERSON><PERSON><PERSON>s searches both incremental data and historical data by loading them into memory. Incremental data are in the growing segments, which are buffered in memory before they reach the threshold to be persisted in storage engine, while historical data are from the sealed segments that are stored in the object storage. Incremental data and historical data together constitute the whole dataset to search.\\n\\n###\",\n", "        0.585393488407135\n", "    ],\n", "    [\n", "        \"Why is there no vector data in etcd?\\n\\netcd stores Milvus module metadata; MinIO stores entities.\\n\\n###\",\n", "        0.579704999923706\n", "    ],\n", "    [\n", "        \"How does <PERSON><PERSON><PERSON><PERSON> flush data?\\n\\nMilvus returns success when inserted data are loaded to the message queue. However, the data are not yet flushed to the disk. Then <PERSON>lvu<PERSON>' data node writes the data in the message queue to persistent storage as incremental logs. If `flush()` is called, the data node is forced to write all data in the message queue to persistent storage immediately.\\n\\n###\",\n", "        0.5777501463890076\n", "    ],\n", "    [\n", "        \"What is the maximum dataset size <PERSON><PERSON><PERSON><PERSON> can handle?\\n\\n  \\nTheoretically, the maximum dataset size <PERSON><PERSON>vu<PERSON> can handle is determined by the hardware it is run on, specifically system memory and storage:\\n\\n- Milvus loads all specified collections and partitions into memory before running queries. Therefore, memory size determines the maximum amount of data <PERSON><PERSON><PERSON><PERSON> can query.\\n- When new entities and and collection-related schema (currently only MinIO is supported for data persistence) are added to Milvus, system storage determines the maximum allowable size of inserted data.\\n\\n###\",\n", "        0.5655910968780518\n", "    ],\n", "    [\n", "        \"Does Milvus support inserting and searching data simultaneously?\\n\\nYes. Insert operations and query operations are handled by two separate modules that are mutually independent. From the client\\u2019s perspective, an insert operation is complete when the inserted data enters the message queue. However, inserted data are unsearchable until they are loaded to the query node. If the segment size does not reach the index-building threshold (512 MB by default), <PERSON>lvus resorts to brute-force search and query performance may be diminished.\\n\\n###\",\n", "        0.5618637204170227\n", "    ],\n", "    [\n", "        \"What data types does Milvus support on the primary key field?\\n\\nIn current release, Milvus supports both INT64 and string.\\n\\n###\",\n", "        0.5561620593070984\n", "    ],\n", "    [\n", "        \"Is Milvus available for concurrent search?\\n\\nYes. For queries on the same collection, Milvus concurrently searches the incremental and historical data. However, queries on different collections are conducted in series. Whereas the historical data can be an extremely huge dataset, searches on the historical data are relatively more time-consuming and essentially performed in series.\\n\\n###\",\n", "        0.529681921005249\n", "    ],\n", "    [\n", "        \"Can vectors with duplicate primary keys be inserted into <PERSON>lvu<PERSON>?\\n\\nYes. Milvus does not check if vector primary keys are duplicates.\\n\\n###\",\n", "        0.528809666633606\n", "    ]\n", "]\n"]}], "source": ["import json\n", "\n", "retrieved_lines_with_distances = [\n", "    (res[\"entity\"][\"text\"], res[\"distance\"]) for res in search_res[0]\n", "]\n", "print(json.dumps(retrieved_lines_with_distances, indent=4))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Dimensionality reduction to 2-d by t-SNE\n", "\n", "Let's reduce the dimension of the embeddings to 2-d by t-SNE. We will use the `sklearn` library to perform the t-SNE transformation."]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TSNE1</th>\n", "      <th>TSNE2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>-3.877362</td>\n", "      <td>0.866726</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-5.923084</td>\n", "      <td>0.671701</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>-0.645954</td>\n", "      <td>0.240083</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.444582</td>\n", "      <td>1.222875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>6.503896</td>\n", "      <td>-4.984684</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>69</th>\n", "      <td>6.354055</td>\n", "      <td>1.264959</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70</th>\n", "      <td>6.055961</td>\n", "      <td>1.266211</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71</th>\n", "      <td>-1.516003</td>\n", "      <td>1.328765</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72</th>\n", "      <td>3.971772</td>\n", "      <td>-0.681780</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73</th>\n", "      <td>3.971772</td>\n", "      <td>-0.681780</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>74 rows × 2 columns</p>\n", "</div>"], "text/plain": ["       TSNE1     TSNE2\n", "0  -3.877362  0.866726\n", "1  -5.923084  0.671701\n", "2  -0.645954  0.240083\n", "3   0.444582  1.222875\n", "4   6.503896 -4.984684\n", "..       ...       ...\n", "69  6.354055  1.264959\n", "70  6.055961  1.266211\n", "71 -1.516003  1.328765\n", "72  3.971772 -0.681780\n", "73  3.971772 -0.681780\n", "\n", "[74 rows x 2 columns]"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.manifold import TSNE\n", "\n", "data.append({\"id\": len(data), \"vector\": emb_text(question), \"text\": question})\n", "embeddings = []\n", "for gp in data:\n", "    embeddings.append(gp[\"vector\"])\n", "\n", "X = np.array(embeddings, dtype=np.float32)\n", "tsne = TSNE(random_state=0, max_iter=1000)\n", "tsne_results = tsne.fit_transform(X)\n", "\n", "df_tsne = pd.DataFrame(tsne_results, columns=[\"TSNE1\", \"TSNE2\"])\n", "df_tsne"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Visualizing Milvus search results on a 2d plane\n", "\n", "We will plot the query vector in green, the retrieved vectors in red, and the remaining vectors in blue."]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Extract similar ids from search results\n", "similar_ids = [gp[\"id\"] for gp in search_res[0]]\n", "\n", "df_norm = df_tsne[:-1]\n", "\n", "df_query = pd.DataFrame(df_tsne.iloc[-1]).T\n", "\n", "# Filter points based on similar ids\n", "similar_points = df_tsne[df_tsne.index.isin(similar_ids)]\n", "\n", "# Create the plot\n", "fig, ax = plt.subplots(figsize=(8, 6))  # Set figsize\n", "\n", "# Set the style of the plot\n", "sns.set_style(\"darkgrid\", {\"grid.color\": \".6\", \"grid.linestyle\": \":\"})\n", "\n", "# Plot all points in blue\n", "sns.scatterplot(\n", "    data=df_tsne, x=\"TSNE1\", y=\"TSNE2\", color=\"blue\", label=\"All knowledge\", ax=ax\n", ")\n", "\n", "# Overlay similar points in red\n", "sns.scatterplot(\n", "    data=similar_points,\n", "    x=\"TSNE1\",\n", "    y=\"TSNE2\",\n", "    color=\"red\",\n", "    label=\"Similar knowledge\",\n", "    ax=ax,\n", ")\n", "\n", "sns.scatterplot(\n", "    data=df_query, x=\"TSNE1\", y=\"TSNE2\", color=\"green\", label=\"Query\", ax=ax\n", ")\n", "\n", "# Set plot titles and labels\n", "plt.title(\"Scatter plot of knowledge using t-SNE\")\n", "plt.xlabel(\"TSNE1\")\n", "plt.ylabel(\"TSNE2\")\n", "\n", "# Set axis to be equal\n", "plt.axis(\"equal\")\n", "\n", "# Display the legend\n", "plt.legend()\n", "\n", "# Show the plot\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}, "source": ["As we can see, the query vector is close to the retrieved vectors. Although the retrieved vectors are not within a standard circle with a fixed radius centered on the query, we can see that they are still very close to the query vector on the 2D plane.\n", "\n", "Using dimensionality reduction techniques can facilitate the understanding of vectors and troubleshooting. Hope you can get a better understanding of vectors through this tutorial."]}], "metadata": {"kernelspec": {"display_name": "zilliz", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}