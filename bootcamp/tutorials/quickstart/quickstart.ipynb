{"cells": [{"cell_type": "markdown", "id": "cde9cd90b57efaaf", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/tutorials/quickstart/quickstart.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/tutorials/quickstart/quickstart.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>"]}, {"cell_type": "markdown", "id": "a7ab72552b8d7e3a", "metadata": {}, "source": ["# Quickstart with <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "markdown", "id": "fa50f50f8c78cfe5", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["Vectors, the output data format of Neural Network models, can effectively encode information and serve a pivotal role in AI applications such as knowledge base, semantic search, Retrieval Augmented Generation (RAG) and more. \n", "\n", "Milvus is an open-source vector database that suits AI applications of every size from running a demo chatbot in Jupyter notebook to building web-scale search that serves billions of users. In this guide, we will walk you through how to set up Milvus locally within minutes and use the Python client library to generate, store and search vectors. "]}, {"cell_type": "markdown", "id": "bc44bb33cc4950e3", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## In<PERSON>l Milvus\n", "In this guide we use Milvus Lite, a python library included in `pymilvus` that can be embedded into the client application. Milvus also supports deployment on [Docker](https://milvus.io/docs/install_standalone-docker.md) and [Kubernetes](https://milvus.io/docs/install_cluster-milvusoperator.md) for production use cases.\n", "\n", "Before starting, make sure you have Python 3.8+ available in the local environment. Install `pymilvus` which contains both the python client library and Milvus Lite:"]}, {"cell_type": "code", "execution_count": null, "id": "initial_id", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}}, "outputs": [], "source": ["!pip install -<PERSON> pymilvus"]}, {"cell_type": "markdown", "id": "f5fdf63111967483", "metadata": {}, "source": ["> If you are using Google Colab, to enable dependencies just installed, you may need to **restart the runtime** (click on the \"Runtime\" menu at the top of the screen, and select \"Restart session\" from the dropdown menu)."]}, {"cell_type": "markdown", "id": "43237e11f0c52425", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Set Up Vector Database\n", "To create a local Milvus vector database, simply instantiate a `MilvusClient` by specifying a file name to store all data, such as \"milvus_demo.db\"."]}, {"cell_type": "code", "execution_count": 1, "id": "640b036a572d02fd", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["from pymilvus import MilvusClient\n", "\n", "client = MilvusClient(\"milvus_demo.db\")"]}, {"cell_type": "markdown", "id": "492e2ca1652be245", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Create a Collection\n", "In Milvus, we need a collection to store vectors and their associated metadata. You can think of it as a table in traditional SQL databases. When creating a collection, you can define schema and index params to configure vector specs such as dimensionality, index types and distant metrics. There are also complex concepts to optimize the index for vector search performance. For now, let's just focus on the basics and use default for everything possible. At minimum, you only need to set the collection name and the dimension of the vector field of the collection."]}, {"cell_type": "code", "execution_count": 2, "id": "566d03c34d7ca94", "metadata": {"ExecuteTime": {"end_time": "2024-05-22T07:53:48.940418Z", "start_time": "2024-05-22T07:53:48.423742Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["if client.has_collection(collection_name=\"demo_collection\"):\n", "    client.drop_collection(collection_name=\"demo_collection\")\n", "client.create_collection(\n", "    collection_name=\"demo_collection\",\n", "    dimension=768,  # The vectors we will use in this demo has 768 dimensions\n", ")"]}, {"cell_type": "markdown", "id": "9d1498d5e5dc46a7", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["In the above setup, \n", "- The primary key and vector fields use their default names (\"id\" and \"vector\").\n", "- The metric type (vector distance definition) is set to its default value ([COSINE](https://milvus.io/docs/metric.md#Cosine-Similarity)).\n", "- The primary key field accepts integers and does not automatically increments (namely not using [auto-id feature](https://milvus.io/docs/schema.md))\n", "Alternatively, you can formally define the schema of the collection by following this [instruction](https://milvus.io/api-reference/pymilvus/v2.4.x/MilvusClient/Collections/create_schema.md)."]}, {"cell_type": "markdown", "id": "18e6a979541b1254", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Prepare Data\n", "In this guide, we use vectors to perform semantic search on text. We need to generate vectors for text by downloading embedding models. This can be easily done by using the utility functions from `pymilvus[model]` library."]}, {"cell_type": "markdown", "id": "865920755266b514", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Represent text with vectors\n", "First, install the model library. This package includes essential ML tools such as PyTorch. The package download may take some time if your local environment has never installed PyTorch."]}, {"cell_type": "code", "execution_count": null, "id": "c63363f1d4b8532e", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["!pip install \"pymilvus[model]\""]}, {"cell_type": "markdown", "id": "2f6df7d93414560f", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["Generate vector embeddings with default model. Milvus expects data to be inserted organized as a list of dictionaries, where each dictionary represents a data record, termed as an entity. "]}, {"cell_type": "code", "execution_count": 3, "id": "a02a7ef763ef98a7", "metadata": {"collapsed": false, "is_executing": true, "jupyter": {"outputs_hidden": false}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dim: 768 (768,)\n", "Data has 3 entities, each with fields:  dict_keys(['id', 'vector', 'text', 'subject'])\n", "Vector dim: 768\n"]}], "source": ["from pymilvus import model\n", "\n", "# If connection to https://huggingface.co/ failed, uncomment the following path\n", "# import os\n", "# os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'\n", "\n", "# This will download a small embedding model \"paraphrase-albert-small-v2\" (~50MB).\n", "embedding_fn = model.DefaultEmbeddingFunction()\n", "\n", "# Text strings to search from.\n", "docs = [\n", "    \"Artificial intelligence was founded as an academic discipline in 1956.\",\n", "    \"<PERSON> was the first person to conduct substantial research in AI.\",\n", "    \"Born in Maida Vale, London, <PERSON><PERSON> was raised in southern England.\",\n", "]\n", "\n", "vectors = embedding_fn.encode_documents(docs)\n", "# The output vector has 768 dimensions, matching the collection that we just created.\n", "print(\"Dim:\", embedding_fn.dim, vectors[0].shape)  # Dim: 768 (768,)\n", "\n", "# Each entity has id, vector representation, raw text, and a subject label that we use\n", "# to demo metadata filtering later.\n", "data = [\n", "    {\"id\": i, \"vector\": vectors[i], \"text\": docs[i], \"subject\": \"history\"}\n", "    for i in range(len(vectors))\n", "]\n", "\n", "print(\"Data has\", len(data), \"entities, each with fields: \", data[0].keys())\n", "print(\"Vector dim:\", len(data[0][\"vector\"]))"]}, {"cell_type": "markdown", "id": "d118f4fea6418e94", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## [Alternatively] Use fake representation with random vectors\n", "If you couldn't download the model due to network issues, as a walkaround, you can use random vectors to represent the text and still finish the example. Just note that the search result won't reflect semantic similarity as the vectors are fake ones. "]}, {"cell_type": "code", "execution_count": 2, "id": "8d41beeb9dfb3f84", "metadata": {"ExecuteTime": {"end_time": "2024-05-22T07:53:05.724689Z", "start_time": "2024-05-22T07:53:05.718610Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data has 3 entities, each with fields:  dict_keys(['id', 'vector', 'text', 'subject'])\n", "Vector dim: 768\n"]}], "source": ["import random\n", "\n", "# Text strings to search from.\n", "docs = [\n", "    \"Artificial intelligence was founded as an academic discipline in 1956.\",\n", "    \"<PERSON> was the first person to conduct substantial research in AI.\",\n", "    \"Born in Maida Vale, London, <PERSON><PERSON> was raised in southern England.\",\n", "]\n", "# Use fake representation with random vectors (768 dimension).\n", "vectors = [[random.uniform(-1, 1) for _ in range(768)] for _ in docs]\n", "data = [\n", "    {\"id\": i, \"vector\": vectors[i], \"text\": docs[i], \"subject\": \"history\"}\n", "    for i in range(len(vectors))\n", "]\n", "\n", "print(\"Data has\", len(data), \"entities, each with fields: \", data[0].keys())\n", "print(\"Vector dim:\", len(data[0][\"vector\"]))"]}, {"cell_type": "markdown", "id": "b65418003fe51a1d", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Insert Data\n", "Let's insert the data into the collection:"]}, {"cell_type": "code", "execution_count": 4, "id": "49e17b12831018f3", "metadata": {"ExecuteTime": {"end_time": "2024-05-22T07:53:57.061772Z", "start_time": "2024-05-22T07:53:56.995602Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'insert_count': 3, 'ids': [0, 1, 2], 'cost': 0}\n"]}], "source": ["res = client.insert(collection_name=\"demo_collection\", data=data)\n", "\n", "print(res)"]}, {"cell_type": "markdown", "id": "cc955d6f66fa7b26", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Semantic Search\n", "Now we can do semantic searches by representing the search query text as vector, and conduct vector similarity search on Milvus."]}, {"cell_type": "markdown", "id": "9c51c7dc6c1bec2f", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["### Vector search\n", "<PERSON><PERSON><PERSON><PERSON> accepts one or multiple vector search requests at the same time. The value of the query_vectors variable is a list of vectors, where each vector is an array of float numbers."]}, {"cell_type": "code", "execution_count": 5, "id": "9331e044d109d8a9", "metadata": {"ExecuteTime": {"end_time": "2024-05-22T07:54:09.960354Z", "start_time": "2024-05-22T07:54:01.056095Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["data: [\"[{'id': 2, 'distance': 0.5859944820404053, 'entity': {'text': 'Born in Maida Vale, London, <PERSON>g was raised in southern England.', 'subject': 'history'}}, {'id': 1, 'distance': 0.5118255615234375, 'entity': {'text': '<PERSON> was the first person to conduct substantial research in AI.', 'subject': 'history'}}]\"] , extra_info: {'cost': 0}\n"]}], "source": ["query_vectors = embedding_fn.encode_queries([\"Who is <PERSON>?\"])\n", "# If you don't have the embedding function you can use a fake vector to finish the demo:\n", "# query_vectors = [ [ random.uniform(-1, 1) for _ in range(768) ] ]\n", "\n", "res = client.search(\n", "    collection_name=\"demo_collection\",  # target collection\n", "    data=query_vectors,  # query vectors\n", "    limit=2,  # number of returned entities\n", "    output_fields=[\"text\", \"subject\"],  # specifies fields to be returned\n", ")\n", "\n", "print(res)"]}, {"cell_type": "markdown", "id": "aa7edf57d1b67710", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["The output is a list of results, each mapping to a vector search query. Each query contains a list of results, where each result contains the entity primary key, the distance to the query vector, and the entity details with specified `output_fields`."]}, {"cell_type": "markdown", "id": "229fc50d34ca6130", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Vector Search with <PERSON>ada<PERSON> Filtering\n", "You can also conduct vector search while considering the values of the metadata (called \"scalar\" fields in Milvus, as scalar refers to non-vector data). This is done with a filter expression specifying certain criteria. Let's see how to search and filter with the `subject` field in the following example."]}, {"cell_type": "code", "execution_count": 6, "id": "a8bf39cbc3ee9d13", "metadata": {"ExecuteTime": {"end_time": "2024-05-22T07:54:18.582486Z", "start_time": "2024-05-22T07:54:17.405245Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["data: [\"[{'id': 4, 'distance': 0.27030569314956665, 'entity': {'text': 'Computational synthesis with AI algorithms predicts molecular properties.', 'subject': 'biology'}}, {'id': 3, 'distance': 0.16425910592079163, 'entity': {'text': 'Machine learning has been used for drug design.', 'subject': 'biology'}}]\"] , extra_info: {'cost': 0}\n"]}], "source": ["# Insert more docs in another subject.\n", "docs = [\n", "    \"Machine learning has been used for drug design.\",\n", "    \"Computational synthesis with AI algorithms predicts molecular properties.\",\n", "    \"DDR1 is involved in cancers and fibrosis.\",\n", "]\n", "vectors = embedding_fn.encode_documents(docs)\n", "data = [\n", "    {\"id\": 3 + i, \"vector\": vectors[i], \"text\": docs[i], \"subject\": \"biology\"}\n", "    for i in range(len(vectors))\n", "]\n", "\n", "client.insert(collection_name=\"demo_collection\", data=data)\n", "\n", "# This will exclude any text in \"history\" subject despite close to the query vector.\n", "res = client.search(\n", "    collection_name=\"demo_collection\",\n", "    data=embedding_fn.encode_queries([\"tell me AI related information\"]),\n", "    filter=\"subject == 'biology'\",\n", "    limit=2,\n", "    output_fields=[\"text\", \"subject\"],\n", ")\n", "\n", "print(res)"]}, {"cell_type": "markdown", "id": "25c791ec71c7ec7e", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["By default, the scalar fields are not indexed. If you need to perform metadata filtered search in large dataset, you can consider using fixed schema and also turn on the [index](https://milvus.io/docs/scalar_index.md) to improve the search performance. \n", "\n", "In addition to vector search, you can also perform other types of searches:"]}, {"cell_type": "markdown", "id": "d45b9796b5a84931", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["### Query\n", "A query() is an operation that retrieves all entities matching a cretria, such as a [filter expression](https://milvus.io/docs/boolean.md) or matching some ids.\n", "\n", "For example, retrieving all entities whose scalar field has a particular value:"]}, {"cell_type": "code", "execution_count": 7, "id": "b1421d2f94b591c5", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["res = client.query(\n", "    collection_name=\"demo_collection\",\n", "    filter=\"subject == 'history'\",\n", "    output_fields=[\"text\", \"subject\"],\n", ")"]}, {"cell_type": "markdown", "id": "d1c5fd1eac35e5d7", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["Directly retrieve entities by primary key:"]}, {"cell_type": "code", "execution_count": 8, "id": "8f87707ba48df498", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["res = client.query(\n", "    collection_name=\"demo_collection\",\n", "    ids=[0, 2],\n", "    output_fields=[\"vector\", \"text\", \"subject\"],\n", ")"]}, {"cell_type": "markdown", "id": "881cb50a42d7f506", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Delete Entities\n", "If you'd like to purge data, you can delete entities specifying the primary key or delete all entities matching a particular filter expression."]}, {"cell_type": "code", "execution_count": 9, "id": "32f93de9bd68aa74", "metadata": {"ExecuteTime": {"end_time": "2024-05-22T07:54:32.290105Z", "start_time": "2024-05-22T07:54:32.271384Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0, 2]\n", "[3, 4, 5]\n"]}], "source": ["# Delete entities by primary key\n", "res = client.delete(collection_name=\"demo_collection\", ids=[0, 2])\n", "\n", "print(res)\n", "\n", "# Delete entities by a filter expression\n", "res = client.delete(\n", "    collection_name=\"demo_collection\",\n", "    filter=\"subject == 'biology'\",\n", ")\n", "\n", "print(res)"]}, {"cell_type": "markdown", "id": "dfba75a81b8a80b1", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Load Existing Data\n", "Since all data of Milvus Lite is stored in a local file, you can load all data into memory even after the program terminates, by creating a `MilvusClient` with the existing file. For example, this will recover the collections from \"milvus_demo.db\" file and continue to write data into it."]}, {"cell_type": "code", "execution_count": null, "id": "8bd995809ec6e25d", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["from pymilvus import MilvusClient\n", "\n", "client = MilvusClient(\"milvus_demo.db\")"]}, {"cell_type": "markdown", "id": "ff2ba854e8980606", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Drop the collection\n", "If you would like to delete all the data in a collection, you can drop the collection with"]}, {"cell_type": "code", "execution_count": null, "id": "5f1480c2c9bdfb35", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["# Drop collection\n", "client.drop_collection(collection_name=\"demo_collection\")"]}, {"cell_type": "markdown", "id": "72a3e10144cc6981", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["Learn More\n", "Milvus Lite is great for getting started with a local python program. If you have large scale data or would like to use Milvus in production, you can learn about deploying Milvus on [Docker](https://milvus.io/docs/install_standalone-docker.md) and [Kubernetes](https://milvus.io/docs/install_cluster-milvusoperator.md). All deployment modes of Milvus share the same API, so your client side code doesn't need to change much if moving to another deployment mode. Simply specify the [URI and Token](https://milvus.io/api-reference/pymilvus/v2.4.x/MilvusClient/Client/MilvusClient.md) of a Milvus server deployed anywhere:"]}, {"cell_type": "code", "execution_count": null, "id": "9d821310ac435b64", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["client = MilvusClient(uri=\"http://localhost:19530\", token=\"root:Mil<PERSON><PERSON>\")"]}, {"cell_type": "markdown", "id": "1d32ff4fe58e97eb", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["Milvus provides REST and gRPC API, with client libraries in languages such as [Python](https://milvus.io/docs/install-pymilvus.md), [Java](https://milvus.io/docs/install-java.md), [Go](https://milvus.io/docs/install-go.md), C# and [Node.js](https://milvus.io/docs/install-node.md)."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}}, "nbformat": 4, "nbformat_minor": 5}