/*
 * This source file was generated by the Gradle 'init' task
 */
package org.example;

import java.util.Arrays;
import java.util.Map;

import ai.onnxruntime.*;
import ai.onnxruntime.extensions.*;

@SuppressWarnings("unused")
public class App {
    public static void main(String[] args) {
        var env = OrtEnvironment.getEnvironment();

        try {
            var sess_opt = new OrtSession.SessionOptions();

            // NOTE: ONNXRuntimeExtensions for Java on Apple Silicon isn't currently
            // available
            // Hence I'll comment out these lines for my machine
            // sess_opt.registerCustomOpLibrary(OrtxPackage.getLibraryPath());

            // Depending on how you call App within Visual Studio, may need to add app/ to
            // filenames below
            System.out.println(System.getProperty("user.dir"));

            // Try out tokenizer
            // var tokenizer = env.createSession("tokenizer.onnx", sess_opt);

            // Get input and output node names for tokenizer
            // var inputName = tokenizer.getInputNames().iterator().next();
            // var outputName = tokenizer.getOutputNames().iterator().next();
            // System.out.println(inputName);
            // System.out.println(outputName);

            // Try out embedding model
            var session = env.createSession("model.onnx", sess_opt);

            // Get input and output names
            var inputName = session.getInputNames().iterator().next();
            var outputName = session.getOutputNames().iterator().next();
            System.out.println(inputName);
            System.out.println(outputName);

            // Since I wasn't able to run tokenizer via ONNX on Apple Silicon, hardcode
            // token ids from Python
            // This is for "The quick brown fox..."
            long[][] tokens = { { 101, 1996, 4248, 2829, 4419, 14523, 2058, 1996, 13971, 3899, 1012, 102 } };
            long[][] masks = { { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 } };
            long[][] token_type_ids = { { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 } };

            // Wrap native Java types in OnnxTensor's and put in input map
            var test_tokens = OnnxTensor.createTensor(env, tokens);
            var test_mask = OnnxTensor.createTensor(env, masks);
            var test_token_type_ids = OnnxTensor.createTensor(env, token_type_ids);
            var inputs = Map.of("input_ids", test_tokens, "attention_mask", test_mask, "token_type_ids",
                    test_token_type_ids);

            // Run embedding model on tokens and convert back to native Java type
            var results = session.run(inputs).get("embeddings");
            float[][] embeddings = (float[][]) results.get().getValue();

            // Print the first 16 dimensions of the resulting embedding
            var result = Arrays.toString(Arrays.copyOfRange(embeddings[0], 0, 16));
            System.out.println(result);

            // Comparing this to our Python notebook, we see it is identical! I.e. calling
            // the ONNXRuntime on the same model from Python and Java produces same results
            // (up to numerical precision etc.)
        } catch (Exception e) {
            System.out.println(e);
        }
    }
}

// 0.146325, 0.32853213, 0.266175, 0.5182375, 0.20214303, -0.17958449,
// 0.15232176, -0.39807054, -0.037162323, -0.057262924, 0.12987728, 0.13251846