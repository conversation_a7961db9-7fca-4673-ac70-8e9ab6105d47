{"cells": [{"cell_type": "markdown", "id": "2b982406", "metadata": {}, "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/tutorials/quickstart/full_text_search_with_milvus.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/tutorials/quickstart/full_text_search_with_milvus.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>\n", "</a>\n"]}, {"cell_type": "markdown", "id": "32257120-dd18-430b-9d80-14bb75bc7d8b", "metadata": {}, "source": ["# Hybrid Retrieval with Full-Text Search\n", "\n", "[Full-text search](https://milvus.io/docs/full-text-search.md#Full-Text-Search) is a traditional method for retrieving documents by matching specific keywords or phrases in the text. It ranks results based on relevance scores calculated from factors like term frequency. While semantic search is better at understanding meaning and context, full-text search excels at precise keyword matching, making it a useful complement to semantic search. A common approach to constructing a Retrieval-Augmented Generation (RAG) pipeline involves retrieving documents through both semantic search and full-text search, followed by a reranking process to refine the results.\n", "\n", "![](../../pics/advanced_rag/hybrid_and_rerank.png)\n", "\n", "This approach converts text into sparse vectors for BM25 scoring. To ingest documents, users can simply input raw text without computing the sparse vector manually. Milvus will automatically generate and store the sparse vectors. To search documents, users just need to specify the text search query. Milvus will compute BM25 scores internally and return ranked results.\n", "\n", "\n", "Milvus also supports hybrid retrieval by combining full-text search with dense vector based semantic search. It usually improves search quality and delivers better results to users by balancing keyword matching and semantic understanding.\n", "\n", "> - Full-text search is currently available in Milvus Standalone, Milvus Distributed, and Zilliz Cloud, though not yet supported in Milvus Lite (which has this feature planned for future implementation). <NAME_EMAIL> for more information.\n", "\n", "\n", "## Preparation\n", "\n", "### Install PyMilvus"]}, {"cell_type": "code", "execution_count": 1, "id": "779da698-4ce5-4555-8f0b-11dc90408525", "metadata": {}, "outputs": [], "source": ["! pip install pymilvus -U"]}, {"cell_type": "markdown", "id": "6856cf93-e297-40a1-83f2-e71bc99a3ca9", "metadata": {}, "source": ["> If you are using Google Colab, to enable dependencies just installed, you may need to **restart the runtime** (click on the \"Runtime\" menu at the top of the screen, and select \"Restart session\" from the dropdown menu).\n", "\n", "### Set OpenAI API Key\n", "We will use the models from OpenAI for creating vector embeddings and generation response. You should prepare the [api key](https://platform.openai.com/docs/quickstart) `OPENAI_API_KEY` as an environment variable."]}, {"cell_type": "code", "execution_count": 2, "id": "c95ab9e1-a221-4840-bf0d-b1801eaa14ed", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-***********\""]}, {"cell_type": "markdown", "id": "352e32be-2085-4c88-a05a-cce7303cf254", "metadata": {}, "source": ["## Setup and Configuration\n", "\n", "Import the necessary libraries"]}, {"cell_type": "code", "execution_count": 3, "id": "c24491e3-1334-4ce5-92a8-1aa13e73a137", "metadata": {}, "outputs": [], "source": ["from typing import List\n", "from openai import OpenAI\n", "\n", "from pymilvus import (\n", "    MilvusClient,\n", "    DataType,\n", "    Function,\n", "    FunctionType,\n", "    AnnSearchRequest,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", ")"]}, {"cell_type": "markdown", "id": "229d49f5-4721-425f-8848-ed2b3bb8be57", "metadata": {}, "source": ["We'll use the MilvusClient to establish a connection to the Milvus server."]}, {"cell_type": "code", "execution_count": 4, "id": "c54de4d5-3ca5-405d-9595-c49e602f1822", "metadata": {}, "outputs": [], "source": ["# Connect to Milvus\n", "uri = \"http://localhost:19530\"\n", "collection_name = \"full_text_demo\"\n", "client = MilvusClient(uri=uri)"]}, {"cell_type": "markdown", "id": "78e0083d-ed6c-4227-817d-9f2ad676c5d4", "metadata": {}, "source": ["> For the connection_args:\n", "> - You can set up a more performant Milvus server on [docker or kubernetes](https://milvus.io/docs/quickstart.md). In this setup, please use the server address, e.g.`http://localhost:19530`, as your `uri`.\n", "> - If you want to use [Zilliz Cloud](https://zilliz.com/cloud), the fully managed cloud service for Milvus, adjust the `uri` and `token`, which correspond to the [Public Endpoint and Api key](https://docs.zilliz.com/docs/on-zilliz-cloud-console#free-cluster-details) in Zilliz Cloud."]}, {"cell_type": "markdown", "id": "fd699087-524a-4bbe-9c34-f57654195224", "metadata": {}, "source": ["## Collection Setup for Full-Text Search\n", "\n", "Setting up a collection for full-text search requires several configuration steps. Let's go through them one by one.\n", "\n", "### Text Analysis Configuration\n", "\n", "For full-text search, we define how text should be processed. Analyzers are essential in full-text search by breaking sentences into tokens and performing lexical analysis like stemming and stop word removal. Here we simply define an analyzer."]}, {"cell_type": "code", "execution_count": 5, "id": "b4f9eb4b-4930-4ef9-bbc5-69c55315f0cc", "metadata": {}, "outputs": [], "source": ["# Define tokenizer parameters for text analysis\n", "analyzer_params = {\"tokenizer\": \"standard\", \"filter\": [\"lowercase\"]}"]}, {"cell_type": "markdown", "id": "71d1faf1", "metadata": {}, "source": ["For more concept details about analyzer, please refer to the [analyzer documentation](https://milvus.io/docs/analyzer-overview.md).\n"]}, {"cell_type": "markdown", "id": "98e87b23-09c4-469b-b2de-5ee569490743", "metadata": {}, "source": ["### Collection Schema and BM25 Function\n", "\n", "Now we define the schema with fields for primary key, text content, sparse vectors (for full-text search), dense vectors (for semantic search), and metadata. We also configure the BM25 function for full-text search.\n", "\n", "The BM25 function automatically converts text content into sparse vectors, allowing Milvus to handle the complexity of full-text search without requiring manual sparse embedding generation."]}, {"cell_type": "code", "execution_count": 6, "id": "bbe123b4-c991-4de7-973c-04df0adffc6e", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'auto_id': False, 'description': '', 'fields': [{'name': 'id', 'description': '', 'type': <DataType.VARCHAR: 21>, 'params': {'max_length': 100}, 'is_primary': True, 'auto_id': True}, {'name': 'content', 'description': '', 'type': <DataType.VARCHAR: 21>, 'params': {'max_length': 65535, 'enable_match': True, 'enable_analyzer': True, 'analyzer_params': {'tokenizer': 'standard', 'filter': ['lowercase']}}}, {'name': 'sparse_vector', 'description': '', 'type': <DataType.SPARSE_FLOAT_VECTOR: 104>, 'is_function_output': True}, {'name': 'dense_vector', 'description': '', 'type': <DataType.FLOAT_VECTOR: 101>, 'params': {'dim': 1536}}, {'name': 'metadata', 'description': '', 'type': <DataType.JSON: 23>}], 'enable_dynamic_field': False, 'functions': [{'name': 'bm25', 'description': '', 'type': <FunctionType.BM25: 1>, 'input_field_names': ['content'], 'output_field_names': ['sparse_vector'], 'params': {}}]}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create schema\n", "schema = MilvusClient.create_schema()\n", "schema.add_field(\n", "    field_name=\"id\",\n", "    datatype=DataType.VARCHAR,\n", "    is_primary=True,\n", "    auto_id=True,\n", "    max_length=100,\n", ")\n", "schema.add_field(\n", "    field_name=\"content\",\n", "    datatype=DataType.VARCHAR,\n", "    max_length=65535,\n", "    analyzer_params=analyzer_params,\n", "    enable_match=True,  # Enable text matching\n", "    enable_analyzer=True,  # Enable text analysis\n", ")\n", "schema.add_field(field_name=\"sparse_vector\", datatype=DataType.SPARSE_FLOAT_VECTOR)\n", "schema.add_field(\n", "    field_name=\"dense_vector\",\n", "    datatype=DataType.FLOAT_VECTOR,\n", "    dim=1536,  # Dimension for text-embedding-3-small\n", ")\n", "schema.add_field(field_name=\"metadata\", datatype=DataType.JSON)\n", "\n", "# Define BM25 function to generate sparse vectors from text\n", "bm25_function = Function(\n", "    name=\"bm25\",\n", "    function_type=FunctionType.BM25,\n", "    input_field_names=[\"content\"],\n", "    output_field_names=\"sparse_vector\",\n", ")\n", "\n", "# Add the function to schema\n", "schema.add_function(bm25_function)"]}, {"cell_type": "markdown", "id": "9bb17020-0350-47e9-8eec-4a6b14e3f4b6", "metadata": {}, "source": ["### Indexing and Collection Creation\n", "\n", "To optimize search performance, we create indexes for both sparse and dense vector fields, then create the collection in Milvus."]}, {"cell_type": "code", "execution_count": 7, "id": "7d3474c3-c131-443c-8237-862344a7c625", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collection 'full_text_demo' created successfully\n"]}], "source": ["# Define indexes\n", "index_params = MilvusClient.prepare_index_params()\n", "index_params.add_index(\n", "    field_name=\"sparse_vector\",\n", "    index_type=\"SPARSE_INVERTED_INDEX\",\n", "    metric_type=\"BM25\",\n", ")\n", "index_params.add_index(field_name=\"dense_vector\", index_type=\"FLAT\", metric_type=\"IP\")\n", "\n", "# Drop collection if exist\n", "if client.has_collection(collection_name):\n", "    client.drop_collection(collection_name)\n", "# Create the collection\n", "client.create_collection(\n", "    collection_name=collection_name,\n", "    schema=schema,\n", "    index_params=index_params,\n", ")\n", "print(f\"Collection '{collection_name}' created successfully\")"]}, {"cell_type": "markdown", "id": "e34998de-df3f-4fcc-b80d-eb2c4bfeff8b", "metadata": {}, "source": ["## Insert Data\n", "\n", "After setting up the collection, we insert data by preparing entities with both text content and their vector representations. Let's define an embedding function and then insert data into the collection."]}, {"cell_type": "code", "execution_count": 8, "id": "ed9e5327-6212-41c8-ad79-d808c36a8d90", "metadata": {}, "outputs": [], "source": ["# Set up OpenAI for embeddings\n", "openai_client = OpenAI(api_key=os.environ.get(\"OPENAI_API_KEY\"))\n", "model_name = \"text-embedding-3-small\"\n", "\n", "\n", "# Define embedding generation function for reuse\n", "def get_embeddings(texts: List[str]) -> List[List[float]]:\n", "    if not texts:\n", "        return []\n", "\n", "    response = openai_client.embeddings.create(input=texts, model=model_name)\n", "    return [embedding.embedding for embedding in response.data]"]}, {"cell_type": "markdown", "id": "168db250", "metadata": {}, "source": ["Insert example documents into the collection."]}, {"cell_type": "code", "execution_count": 9, "id": "ab9083ab-2f11-443f-9919-c2c33c427186", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Inserted 3 documents\n"]}], "source": ["# Example documents to insert\n", "documents = [\n", "    {\n", "        \"content\": \"Milvus is a vector database built for embedding similarity search and AI applications.\",\n", "        \"metadata\": {\"source\": \"documentation\", \"topic\": \"introduction\"},\n", "    },\n", "    {\n", "        \"content\": \"Full-text search in Milvus allows you to search using keywords and phrases.\",\n", "        \"metadata\": {\"source\": \"tutorial\", \"topic\": \"full-text search\"},\n", "    },\n", "    {\n", "        \"content\": \"Hybrid search combines the power of sparse BM25 retrieval with dense vector search.\",\n", "        \"metadata\": {\"source\": \"blog\", \"topic\": \"hybrid search\"},\n", "    },\n", "]\n", "\n", "# Prepare entities for insertion\n", "entities = []\n", "texts = [doc[\"content\"] for doc in documents]\n", "embeddings = get_embeddings(texts)\n", "\n", "for i, doc in enumerate(documents):\n", "    entities.append(\n", "        {\n", "            \"content\": doc[\"content\"],\n", "            \"dense_vector\": embeddings[i],\n", "            \"metadata\": doc.get(\"metadata\", {}),\n", "        }\n", "    )\n", "\n", "# Insert data\n", "client.insert(collection_name, entities)\n", "print(f\"Inserted {len(entities)} documents\")"]}, {"cell_type": "markdown", "id": "6d4876bb-f819-4b4e-abbb-cc153fb3a230", "metadata": {}, "source": ["## Perform Retrieval\n", "You can flexibly use the `search()` or `hybrid_search()` methods to implement full-text search (sparse), semantic search (dense), and hybrid search to lead to more robust and accurate search results.\n", "\n", "### Full-Text Search\n", "\n", "Sparse search leverages the BM25 algorithm to find documents containing specific keywords or phrases. This traditional search method excels at precise term matching and is particularly effective when users know exactly what they're looking for."]}, {"cell_type": "code", "execution_count": 10, "id": "c746bd25-6c7e-4068-ae2f-79d63dc4726d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Sparse Search (Full-text search):\n", "1. Score: 3.1261, Content: Full-text search in Milvus allows you to search using keywords and phrases.\n", "2. Score: 0.1836, Content: Hybrid search combines the power of sparse BM25 retrieval with dense vector search.\n", "3. Score: 0.1335, Content: Milvus is a vector database built for embedding similarity search and AI applications.\n"]}], "source": ["# Example query for keyword search\n", "query = \"full-text search keywords\"\n", "\n", "# BM25 sparse vectors\n", "results = client.search(\n", "    collection_name=collection_name,\n", "    data=[query],\n", "    anns_field=\"sparse_vector\",\n", "    limit=5,\n", "    output_fields=[\"content\", \"metadata\"],\n", ")\n", "sparse_results = results[0]\n", "\n", "# Print results\n", "print(\"\\nSparse Search (Full-text search):\")\n", "for i, result in enumerate(sparse_results):\n", "    print(\n", "        f\"{i+1}. Score: {result['distance']:.4f}, Content: {result['entity']['content']}\"\n", "    )"]}, {"cell_type": "markdown", "id": "e2a723fd-be86-4e84-a24c-3be051e37cc6", "metadata": {}, "source": ["### Semantic Search\n", "\n", "Dense search uses vector embeddings to find documents with similar meaning, even if they don't share the exact same keywords. This approach helps understand context and semantics, making it ideal for more natural language queries."]}, {"cell_type": "code", "execution_count": 11, "id": "ea10d2a6-f378-4654-b1e9-765de3508902", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "<PERSON>se Search (Semantic):\n", "1. Score: 0.6959, Content: Milvus is a vector database built for embedding similarity search and AI applications.\n", "2. Score: 0.6501, Content: Full-text search in Milvus allows you to search using keywords and phrases.\n", "3. Score: 0.4371, Content: Hybrid search combines the power of sparse BM25 retrieval with dense vector search.\n"]}], "source": ["# Example query for semantic search\n", "query = \"How does <PERSON><PERSON><PERSON><PERSON> help with similarity search?\"\n", "\n", "# Generate embedding for query\n", "query_embedding = get_embeddings([query])[0]\n", "\n", "# Semantic search using dense vectors\n", "results = client.search(\n", "    collection_name=collection_name,\n", "    data=[query_embedding],\n", "    anns_field=\"dense_vector\",\n", "    limit=5,\n", "    output_fields=[\"content\", \"metadata\"],\n", ")\n", "dense_results = results[0]\n", "\n", "# Print results\n", "print(\"\\nDense Search (Semantic):\")\n", "for i, result in enumerate(dense_results):\n", "    print(\n", "        f\"{i+1}. Score: {result['distance']:.4f}, Content: {result['entity']['content']}\"\n", "    )"]}, {"cell_type": "markdown", "id": "20fc1454-96fd-4a4a-bdeb-4c577248de8b", "metadata": {}, "source": ["### Hybrid Search\n", "\n", "Hybrid search combines both full-text search and semantic dense retrieval. This balanced approach improves search accuracy and robustness by leveraging the strengths of both methods.\n", "\n", "Hybrid search is especially valuable in Retrieval-Augmented Generation (RAG) applications, where both semantic understanding and precise keyword matching contribute to better retrieval results."]}, {"cell_type": "code", "execution_count": 12, "id": "0f05fcb4-25bd-4478-9e55-4f485e3c5e22", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Hybrid Search (Combined):\n", "1. Score: 0.0328, Content: Hybrid search combines the power of sparse BM25 retrieval with dense vector search.\n", "2. Score: 0.0320, Content: Milvus is a vector database built for embedding similarity search and AI applications.\n", "3. Score: 0.0320, Content: Full-text search in Milvus allows you to search using keywords and phrases.\n"]}], "source": ["# Example query for hybrid search\n", "query = \"what is hybrid search\"\n", "\n", "# Get query embedding\n", "query_embedding = get_embeddings([query])[0]\n", "\n", "# Set up BM25 search request\n", "sparse_search_params = {\"metric_type\": \"BM25\"}\n", "sparse_request = AnnSearchRequest(\n", "    [query], \"sparse_vector\", sparse_search_params, limit=5\n", ")\n", "\n", "# Set up dense vector search request\n", "dense_search_params = {\"metric_type\": \"IP\"}\n", "dense_request = AnnSearchRequest(\n", "    [query_embedding], \"dense_vector\", dense_search_params, limit=5\n", ")\n", "\n", "# Perform hybrid search with reciprocal rank fusion\n", "results = client.hybrid_search(\n", "    collection_name,\n", "    [sparse_request, dense_request],\n", "    ranker=RR<PERSON><PERSON><PERSON><PERSON>(),  # Reciprocal Rank Fusion for combining results\n", "    limit=5,\n", "    output_fields=[\"content\", \"metadata\"],\n", ")\n", "hybrid_results = results[0]\n", "\n", "# Print results\n", "print(\"\\nHybrid Search (Combined):\")\n", "for i, result in enumerate(hybrid_results):\n", "    print(\n", "        f\"{i+1}. Score: {result['distance']:.4f}, Content: {result['entity']['content']}\"\n", "    )"]}, {"cell_type": "markdown", "id": "d838fcc8-57a9-435b-bf44-2ae549d083cc", "metadata": {}, "source": ["## Answer Generation\n", "\n", "After retrieving relevant documents with hybrid search, we can use an LLM to generate a comprehensive answer based on the retrieved information. This is the final step in a RAG (Retrieval Augmented Generation) pipeline."]}, {"cell_type": "code", "execution_count": 13, "id": "85ab46df-3d85-4638-9ab2-586a02838fa8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hybrid search combines the power of sparse BM25 retrieval with dense vector search.\n"]}], "source": ["# Format retrieved documents into context\n", "context = \"\\n\\n\".join([doc[\"entity\"][\"content\"] for doc in hybrid_results])\n", "\n", "# Create prompt\n", "prompt = f\"\"\"Answer the following question based on the provided context. \n", "If the context doesn't contain relevant information, just say \"I don't have enough information to answer this question.\"\n", "\n", "Context:\n", "{context}\n", "\n", "Question: {query}\n", "\n", "Answer:\"\"\"\n", "\n", "# Call OpenAI API\n", "response = openai_client.chat.completions.create(\n", "    model=\"gpt-4o-mini\",\n", "    messages=[\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": \"You are a helpful assistant that answers questions based on the provided context.\",\n", "        },\n", "        {\"role\": \"user\", \"content\": prompt},\n", "    ],\n", ")\n", "\n", "print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "id": "99dcea39", "metadata": {}, "source": ["That's it! Now you've just build RAG with hybrid retrieval that combines the power of BM25-based full-text search and dense vector based semantic search."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}