{"cells": [{"metadata": {}, "cell_type": "markdown", "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/integration/rag_with_milvus_and_bentoml.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/integration/rag_with_milvus_and_bentoml.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>\n", "</a>"], "id": "8b7c2d1c50f316b4"}, {"metadata": {}, "cell_type": "markdown", "source": ["# Retrieval-Augmented Generation (RAG) with Milvus and BentoML"], "id": "114565a1859d0c32"}, {"metadata": {}, "cell_type": "markdown", "source": ["## Introduction\n", "This guide demonstrates how to use an open-source embedding model and large-language model on BentoCloud with Milvus vector database to build a RAG (Retrieval Augmented Generation) application. \n", "BentoCloud is an AI Inference Platform for fast-moving AI teams, offering fully-managed infrastructure tailored for model inference. It works in conjunction with BentoML, an open-source model serving framework, to facilitate the easy creation and deployment of high-performance model services. In this demo, we use Milvus Lite as vector database, which is the lightweight version of Milvus that can be embedded into your Python application."], "id": "3a753053793afd65"}, {"metadata": {}, "cell_type": "markdown", "source": ["## Before you begin\n", "Milvus Lite is available on PyPI. You can install it via pip for Python 3.8+:"], "id": "b77f4482b49d7154"}, {"metadata": {}, "cell_type": "code", "source": ["!pip install -<PERSON> pymilvus bentoml"], "id": "90a2e9fd1b0b69b", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "markdown", "source": ["> If you are using Google Colab, to enable dependencies just installed, you may need to **restart the runtime** (click on the \"Runtime\" menu at the top of the screen, and select \"Restart session\" from the dropdown menu)."], "id": "86ed1bfa3dcba484"}, {"metadata": {}, "cell_type": "markdown", "source": ["After sign in the BentoCloud, we can interact with deployed BentoCloud Services in Deployments, and the corresponding END_POINT and API are located in Playground -> Python.\n", "You can download the city data [here](https://github.com/ytang07/bento_octo_milvus_RAG/tree/main/data)."], "id": "e17efda5ae808c2b"}, {"metadata": {}, "cell_type": "markdown", "source": ["## Serving Embeddings with BentoML/BentoCloud \n", "To use this endpoint, import `bentoml` and set up an HTTP client using the `SyncHTTPClient` by specifying the endpoint and optionally the token (if you turn on `Endpoint Authorization` on BentoCloud). Alternatively, you can use the same model served through BentoML using its [Sentence Transformers Embeddings](https://github.com/bentoml/BentoSentenceTransformers) repository."], "id": "8fa6de0f0c710c87"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["import bentoml\n", "\n", "BENTO_EMBEDDING_MODEL_END_POINT = \"BENTO_EMBEDDING_MODEL_END_POINT\"\n", "BENTO_API_TOKEN = \"BENTO_API_TOKEN\"\n", "\n", "embedding_client = bentoml.SyncHTTPClient(\n", "    BENTO_EMBEDDING_MODEL_END_POINT, token=BENTO_API_TOKEN\n", ")"], "id": "53b8dc84eaa9552f"}, {"metadata": {}, "cell_type": "markdown", "source": ["Once we connect to the embedding_client, we need to process our data. We provided several functions to perform data splitting and embedding.\n", "\n", "Read files and preprocess the text into a list of strings."], "id": "bc9fc4a83cb30651"}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-30T09:00:45.231255Z", "start_time": "2024-05-30T09:00:45.228138Z"}}, "cell_type": "code", "source": ["# naively chunk on newlines\n", "def chunk_text(filename: str) -> list:\n", "    with open(filename, \"r\") as f:\n", "        text = f.read()\n", "    sentences = text.split(\"\\n\")\n", "    return sentences"], "id": "c875c865b4f03cbf", "outputs": [], "execution_count": 6}, {"metadata": {}, "cell_type": "markdown", "source": ["First we need to download the city data."], "id": "56cf89a31307dd9"}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-30T09:03:04.829125Z", "start_time": "2024-05-30T09:03:02.749073Z"}}, "cell_type": "code", "source": ["import os\n", "import requests\n", "import urllib.request\n", "\n", "# set up the data source\n", "repo = \"ytang07/bento_octo_milvus_RAG\"\n", "directory = \"data\"\n", "save_dir = \"./city_data\"\n", "api_url = f\"https://api.github.com/repos/{repo}/contents/{directory}\"\n", "\n", "\n", "response = requests.get(api_url)\n", "data = response.json()\n", "\n", "if not os.path.exists(save_dir):\n", "    os.makedirs(save_dir)\n", "\n", "for item in data:\n", "    if item[\"type\"] == \"file\":\n", "        file_url = item[\"download_url\"]\n", "        file_path = os.path.join(save_dir, item[\"name\"])\n", "        urllib.request.urlretrieve(file_url, file_path)"], "id": "22279ff9d4181675", "outputs": [], "execution_count": 10}, {"metadata": {}, "cell_type": "markdown", "source": ["Next, we process each of the files we have."], "id": "c9c1bd74212091c4"}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-30T09:03:08.342067Z", "start_time": "2024-05-30T09:03:08.330758Z"}}, "cell_type": "code", "source": ["# please upload your data directory under this file's folder\n", "cities = os.listdir(\"city_data\")\n", "# store chunked text for each of the cities in a list of dicts\n", "city_chunks = []\n", "for city in cities:\n", "    chunked = chunk_text(f\"city_data/{city}\")\n", "    cleaned = []\n", "    for chunk in chunked:\n", "        if len(chunk) > 7:\n", "            cleaned.append(chunk)\n", "    mapped = {\"city_name\": city.split(\".\")[0], \"chunks\": cleaned}\n", "    city_chunks.append(mapped)"], "id": "616ee4d005a73a32", "outputs": [], "execution_count": 11}, {"metadata": {}, "cell_type": "markdown", "source": ["Splits a list of strings into a list of embeddings, each grouped 25 text strings."], "id": "19a39247b3a6d144"}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-30T09:00:57.547822Z", "start_time": "2024-05-30T09:00:57.543888Z"}}, "cell_type": "code", "source": ["def get_embeddings(texts: list) -> list:\n", "    if len(texts) > 25:\n", "        splits = [texts[x : x + 25] for x in range(0, len(texts), 25)]\n", "        embeddings = []\n", "        for split in splits:\n", "            embedding_split = embedding_client.encode(sentences=split)\n", "            embeddings += embedding_split\n", "        return embeddings\n", "    return embedding_client.encode(\n", "        sentences=texts,\n", "    )"], "id": "9585e8a71f9582a7", "outputs": [], "execution_count": 8}, {"metadata": {}, "cell_type": "markdown", "source": ["Now, we need to match up embeddings and text chunks. Since the list embeddings and the list of sentences should match by index, we can `enumerate` through either list to match them up. "], "id": "2f0c0258604b680e"}, {"metadata": {}, "cell_type": "code", "source": ["entries = []\n", "for city_dict in city_chunks:\n", "    # No need for the embeddings list if get_embeddings already returns a list of lists\n", "    embedding_list = get_embeddings(city_dict[\"chunks\"])  # returns a list of lists\n", "    # Now match texts with embeddings and city name\n", "    for i, embedding in enumerate(embedding_list):\n", "        entry = {\n", "            \"embedding\": embedding,\n", "            \"sentence\": city_dict[\"chunks\"][\n", "                i\n", "            ],  # Assume \"chunks\" has the corresponding texts for the embeddings\n", "            \"city\": city_dict[\"city_name\"],\n", "        }\n", "        entries.append(entry)\n", "    print(entries)"], "id": "70e248dd9b053db3", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "markdown", "source": ["## Inserting Data into a Vector Database for Retrieval  \n", "With our embeddings and data prepared, we can insert the vectors together with metadata into Milvus Lite for vector search later. The first step in this section is to start a client by connecting to Milvus Lite.\n", "We simply import the `MilvusClient` module and initialize a Milvus Lite client that connects to your Milvus Lite vector database. The dimension size comes from the size of the embedding model, e.g. the Sentence Transformer model `all-MiniLM-L6-v2` produces vectors of 384 dimension. "], "id": "86a7c03a4d816adc"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["from pymilvus import MilvusClient\n", "\n", "COLLECTION_NAME = \"Bento_Milvus_RAG\"  # random name for your collection\n", "DIMENSION = 384\n", "\n", "# Initialize a Milvus Lite client\n", "milvus_client = MilvusClient(\"milvus_demo.db\")"], "id": "f4fc2677daaefe9b"}, {"cell_type": "markdown", "source": ["> As for the argument of `MilvusClient`:\n", "> - Setting the `uri` as a local file, e.g.`./milvus.db`, is the most convenient method, as it automatically utilizes [Milvus Lite](https://milvus.io/docs/milvus_lite.md) to store all data in this file.\n", "> - If you have large scale of data, you can set up a more performant Milvus server on [docker or kubernetes](https://milvus.io/docs/quickstart.md). In this setup, please use the server uri, e.g.`http://localhost:19530`, as your `uri`.\n", "> - If you want to use [Zilliz Cloud](https://zilliz.com/cloud), the fully managed cloud service for Milvus, adjust the `uri` and `token`, which correspond to the [Public Endpoint and Api key](https://docs.zilliz.com/docs/on-zilliz-cloud-console#free-cluster-details) in Zilliz Cloud."], "metadata": {"collapsed": false}}, {"metadata": {}, "cell_type": "markdown", "source": ["Or with old connections.connect API (not recommended):"], "id": "93efd6c5e8017da2"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["from pymilvus import connections\n", "\n", "connections.connect(uri=\"milvus_demo.db\")"], "id": "6cea5eca775e374a"}, {"metadata": {}, "cell_type": "markdown", "source": ["## Creating Your Milvus Lite Collection \n", "Creating a collection using Milvus Lite involves two steps: first, defining the schema, and second, defining the index. For this section, we need one module: DataType tells us what type of data will be in a field. We also need to use two functions to create schema and add fields. create_schema():  creates a collection schema, add_field(): adds a field to the schema of a collection."], "id": "a5250d6521f8a81e"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["from pymilvus import MilvusClient, DataType, Collection\n", "\n", "# Create schema\n", "schema = MilvusClient.create_schema(\n", "    auto_id=True,\n", "    enable_dynamic_field=True,\n", ")\n", "\n", "# 3.2. Add fields to schema\n", "schema.add_field(field_name=\"id\", datatype=DataType.INT64, is_primary=True)\n", "schema.add_field(field_name=\"embedding\", datatype=DataType.FLOAT_VECTOR, dim=DIMENSION)"], "id": "5b24edf5600e9bc9"}, {"metadata": {}, "cell_type": "markdown", "source": ["Now that we have created our schema and successfully defined data field, we need to define the index. In terms of search, an \"index\" defines how we are going to map our data out for retrieval. We use the default choice [AUTOINDEX](https://docs.zilliz.com/docs/autoindex-explained) to index our data for this project. \n", "\n", "Next, we create the collection with the previously given name, schema and index. Finally, we insert the previously processed data."], "id": "77a6aadcd5d4f7ca"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# prepare index parameters\n", "index_params = milvus_client.prepare_index_params()\n", "\n", "# add index\n", "index_params.add_index(\n", "    field_name=\"embedding\",\n", "    index_type=\"AUTOINDEX\",  # use autoindex instead of other complex indexing method\n", "    metric_type=\"COSINE\",  # L2, COSINE, or IP\n", ")\n", "\n", "# create collection\n", "if milvus_client.has_collection(collection_name=COLLECTION_NAME):\n", "    milvus_client.drop_collection(collection_name=COLLECTION_NAME)\n", "milvus_client.create_collection(\n", "    collection_name=COLLECTION_NAME, schema=schema, index_params=index_params\n", ")\n", "\n", "# Outside the loop, now you upsert all the entries at once\n", "milvus_client.insert(collection_name=COLLECTION_NAME, data=entries)"], "id": "cceb127ea97eb5e1"}, {"metadata": {}, "cell_type": "markdown", "source": ["## Set up Your LLM for RAG \n", "To build a RAG app, we need to deploy an LLM on BentoCloud. Let’s use the latest Llama3 LLM. Once it is up and running, simply copy the endpoint and token of this model service and set up a client for it. "], "id": "a77cc2fe778feb53"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["BENTO_LLM_END_POINT = \"BENTO_LLM_END_POINT\"\n", "\n", "llm_client = bentoml.SyncHTTPClient(BENTO_LLM_END_POINT, token=BENTO_API_TOKEN)"], "id": "e8409c4076fbd383"}, {"metadata": {}, "cell_type": "markdown", "source": ["## LLM Instructions \n", "Now, we set up the LLM instructions with the prompt, context, and the question. Here is the function that behaves as an LLM and it then returns the output from the client in a string format."], "id": "c077e35528b984a2"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["def dorag(question: str, context: str):\n", "\n", "    prompt = (\n", "        f\"You are a helpful assistant. The user has a question. Answer the user question based only on the context: {context}. \\n\"\n", "        f\"The user question is {question}\"\n", "    )\n", "\n", "    results = llm_client.generate(\n", "        max_tokens=1024,\n", "        prompt=prompt,\n", "    )\n", "\n", "    res = \"\"\n", "    for result in results:\n", "        res += result\n", "\n", "    return res"], "id": "6070b2e7c2462fb4"}, {"metadata": {}, "cell_type": "markdown", "source": ["## A RAG Example \n", "Now we’re ready to ask a question. This function simply takes a question and then does RAG to generate the relevant context from the background information. Then, we pass the context and the question to dorag() and get the result."], "id": "89d25152a70af9ee"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["question = \"What state is Cambridge in?\"\n", "\n", "\n", "def ask_a_question(question):\n", "    embeddings = get_embeddings([question])\n", "    res = milvus_client.search(\n", "        collection_name=COLLECTION_NAME,\n", "        data=embeddings,  # search for the one (1) embedding returned as a list of lists\n", "        anns_field=\"embedding\",  # Search across embeddings\n", "        limit=5,  # get me the top 5 results\n", "        output_fields=[\"sentence\"],  # get the sentence/chunk and city\n", "    )\n", "\n", "    sentences = []\n", "    for hits in res:\n", "        for hit in hits:\n", "            print(hit)\n", "            sentences.append(hit[\"entity\"][\"sentence\"])\n", "    context = \". \".join(sentences)\n", "    return context\n", "\n", "\n", "context = ask_a_question(question=question)\n", "print(context)"], "id": "a6ad810aa7dc5edd"}, {"metadata": {}, "cell_type": "markdown", "source": "Implement RAG", "id": "fe5876426fe2e13a"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["print(dorag(question=question, context=context))"], "id": "752e6dc9a9d8e0e7"}, {"metadata": {}, "cell_type": "markdown", "source": "For the example question asking which state Cambridge is in, we can print the entire response from BentoML. However, if we take the time to parse through it, it just looks nicer, and it should tell us that Cambridge is located in Massachusetts.", "id": "6099649f74a3cf3f"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}