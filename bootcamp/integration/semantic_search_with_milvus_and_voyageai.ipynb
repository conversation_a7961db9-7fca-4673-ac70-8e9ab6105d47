{"cells": [{"metadata": {}, "cell_type": "markdown", "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/integration/semantic_search_with_milvus_and_voyageai.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/integration/semantic_search_with_milvus_and_voyageai.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>\n", "</a>"], "id": "7a79772ef944c961"}, {"metadata": {}, "cell_type": "markdown", "source": ["# Semantic Search with Milvus and VoyageAI"], "id": "a69173aac7235d33"}, {"metadata": {}, "cell_type": "markdown", "source": ["This guide showcases how [VoyageAI's Embedding API](https://docs.voyageai.com/docs/embeddings) can be used with Milvus vector database to conduct semantic search on text."], "id": "16d6e22ee4ff3b9a"}, {"metadata": {}, "cell_type": "markdown", "source": ["## Getting started\n", "Before you start, make sure you have the Voyage API key ready, or you get one from the [VoyageAI website](https://dash.voyageai.com/api-keys).\n", "\n", "The data used in this example are book titles. You can download the dataset [here](https://www.kaggle.com/datasets/jealousleopard/goodreadsbooks) and put it in the same directory where you run the following code.\n", "\n", "First, install the package for Milvus and Voyage AI:"], "id": "a67540e44c0221a7"}, {"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true}, "source": ["!pip install --upgrade voyageai pymilvus"], "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "markdown", "source": ["> If you are using Google Colab, to enable dependencies just installed, you may need to **restart the runtime** (click on the \"Runtime\" menu at the top of the screen, and select \"Restart session\" from the dropdown menu)."], "id": "11d0ddab29c7ee2e"}, {"metadata": {}, "cell_type": "markdown", "source": ["With this, we're ready to generate embeddings and use vector database to conduct semantic search."], "id": "88b478036d3df2da"}, {"metadata": {}, "cell_type": "markdown", "source": ["## Searching book titles with VoyageAI & Milvus"], "id": "ae68028dfc5ab583"}, {"metadata": {}, "cell_type": "markdown", "source": ["In the following example, we load book title data from the downloaded CSV file, use Voyage AI embedding model to generate vector representations, and store them in Milvus vector database for semantic search."], "id": "1fa47c845c7ee303"}, {"cell_type": "code", "source": ["import voyageai\n", "from pymilvus import MilvusClient\n", "\n", "MODEL_NAME = \"voyage-law-2\"  # Which model to use, please check https://docs.voyageai.com/docs/embeddings for available models\n", "DIMENSION = 1024  # Dimension of vector embedding\n", "\n", "# Connect to VoyageAI with API Key.\n", "voyage_client = voyageai.Client(api_key=\"<YOUR_VOYAGEAI_API_KEY>\")\n", "\n", "docs = [\n", "    \"Artificial intelligence was founded as an academic discipline in 1956.\",\n", "    \"<PERSON> was the first person to conduct substantial research in AI.\",\n", "    \"Born in Maida Vale, London, <PERSON><PERSON> was raised in southern England.\",\n", "]\n", "\n", "vectors = voyage_client.embed(texts=docs, model=MODEL_NAME, truncation=False).embeddings\n", "\n", "# Prepare data to be stored in Milvus vector database.\n", "# We can store the id, vector representation, raw text and labels such as \"subject\" in this case in Milvus.\n", "data = [\n", "    {\"id\": i, \"vector\": vectors[i], \"text\": docs[i], \"subject\": \"history\"}\n", "    for i in range(len(docs))\n", "]\n", "\n", "\n", "# Connect to Milvus, all data is stored in a local file named \"milvus_voyage_demo.db\"\n", "# in current directory. You can also connect to a remote Milvus server following this\n", "# instruction: https://milvus.io/docs/install_standalone-docker.md.\n", "milvus_client = MilvusClient(uri=\"milvus_voyage_demo.db\")\n", "COLLECTION_NAME = \"demo_collection\"  # Milvus collection name\n", "# Create a collection to store the vectors and text.\n", "if milvus_client.has_collection(collection_name=COLLECTION_NAME):\n", "    milvus_client.drop_collection(collection_name=COLLECTION_NAME)\n", "milvus_client.create_collection(collection_name=COLLECTION_NAME, dimension=DIMENSION)\n", "\n", "# Insert all data into Milvus vector database.\n", "res = milvus_client.insert(collection_name=\"demo_collection\", data=data)\n", "\n", "print(res[\"insert_count\"])"], "metadata": {"collapsed": false}, "id": "5e5bffb900bf075e", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": ["> As for the argument of `MilvusClient`:\n", "> - Setting the `uri` as a local file, e.g.`./milvus.db`, is the most convenient method, as it automatically utilizes [Milvus Lite](https://milvus.io/docs/milvus_lite.md) to store all data in this file.\n", "> - If you have large scale of data, you can set up a more performant Milvus server on [docker or kubernetes](https://milvus.io/docs/quickstart.md). In this setup, please use the server uri, e.g.`http://localhost:19530`, as your `uri`.\n", "> - If you want to use [Zilliz Cloud](https://zilliz.com/cloud), the fully managed cloud service for Milvus, adjust the `uri` and `token`, which correspond to the [Public Endpoint and Api key](https://docs.zilliz.com/docs/on-zilliz-cloud-console#free-cluster-details) in Zilliz Cloud."], "metadata": {"collapsed": false}}, {"metadata": {}, "cell_type": "markdown", "source": ["With all data in Milvus vector database, we can now perform semantic search by generating vector embedding for the query and conduct vector search."], "id": "d0e23c54d75520a9"}, {"cell_type": "code", "source": ["queries = [\"When was artificial intelligence founded?\"]\n", "\n", "query_vectors = voyage_client.embed(\n", "    texts=queries, model=MODEL_NAME, truncation=False\n", ").embeddings\n", "\n", "res = milvus_client.search(\n", "    collection_name=COLLECTION_NAME,  # target collection\n", "    data=query_vectors,  # query vectors\n", "    limit=2,  # number of returned entities\n", "    output_fields=[\"text\", \"subject\"],  # specifies fields to be returned\n", ")\n", "\n", "for q in queries:\n", "    print(\"Query:\", q)\n", "    for result in res:\n", "        print(result)\n", "    print(\"\\n\")"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-05-30T09:11:31.175432Z", "start_time": "2024-05-30T09:11:30.503896Z"}}, "id": "40d0cd0218d44802", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query: When was artificial intelligence founded?\n", "[{'id': 0, 'distance': 0.7196218371391296, 'entity': {'text': 'Artificial intelligence was founded as an academic discipline in 1956.', 'subject': 'history'}}, {'id': 1, 'distance': 0.6297335028648376, 'entity': {'text': '<PERSON> was the first person to conduct substantial research in AI.', 'subject': 'history'}}]\n", "\n", "\n"]}], "execution_count": 3}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}