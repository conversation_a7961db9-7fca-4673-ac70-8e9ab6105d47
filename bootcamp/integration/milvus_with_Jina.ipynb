{"cells": [{"metadata": {"id": "e17219815f8987d9"}, "cell_type": "markdown", "source": ["# Integra<PERSON> with <PERSON><PERSON>"], "id": "e17219815f8987d9"}, {"cell_type": "markdown", "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/integration/milvus_with_<PERSON><PERSON>.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/integration/milvus_with_<PERSON>a.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>\n", "</a>\n", "\n", "This guide demonstrates how to use Jina AI embeddings and Milvus to conduct similarity search and retrieval tasks."], "metadata": {"collapsed": false, "id": "20180cc31eaf0495"}, "id": "20180cc31eaf0495"}, {"cell_type": "markdown", "source": ["## Who is <PERSON><PERSON>\n", "Jina AI, founded in 2020 in Berlin, is a pioneering AI company focused on revolutionizing the future of artificial intelligence through its search foundation. Specializing in multimodal AI, Jina AI aims to empower businesses and developers to harness the power of multimodal data for value creation and cost savings through its integrated suite of components, including embeddings, rerankers, prompt ops, and core infrastructure.\n", "Jina AI's cutting-edge embeddings boast top-tier performance, featuring an 8192 token-length model ideal for comprehensive data representation. Offering multilingual support and seamless integration with leading platforms like OpenAI, these embeddings facilitate cross-lingual applications."], "metadata": {"collapsed": false, "id": "990b149ce5c688b2"}, "id": "990b149ce5c688b2"}, {"cell_type": "markdown", "source": ["## <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>'s Embedding\n", "In order to store and search these embeddings efficiently for speed and scale, specific infrastructure designed for this purpose is required. Milvus is a widely known advanced open-source vector database capable of handling large-scale vector data. Milvus enables fast and accurate vector(embedding) search according plenty of metrics. Its scalability allows for seamless handling of massive volumes of image data, ensuring high-performance search operations even as datasets grow."], "metadata": {"collapsed": false, "id": "d27fd9a9cf451a02"}, "id": "d27fd9a9cf451a02"}, {"cell_type": "markdown", "source": ["## Examples\n", "Jina embeddings have been integrated into the PyMilvus model library. Now, we will demonstrate code examples to show how to use Jina embeddings in action.\n", "\n", "Before we start, we need to install model library for PyMilvus."], "metadata": {"collapsed": false, "id": "4ff70dd614666672"}, "id": "4ff70dd614666672"}, {"cell_type": "code", "source": ["!pip install -<PERSON> pymilvus\n", "!pip install \"pymilvus[model]\""], "metadata": {"id": "f748781570cc911f"}, "id": "f748781570cc911f", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "markdown", "source": ["> If you are using Google Colab, to enable dependencies just installed, you may need to **restart the runtime** (click on the \"Runtime\" menu at the top of the screen, and select \"Restart session\" from the dropdown menu)."], "id": "a20be817dcf2d3f1"}, {"cell_type": "markdown", "source": ["## General-Purpose Embedding\n", "Jina AI's core embedding model, excels in understanding detailed text, making it ideal for semantic search, content classification thus supports advanced sentiment analysis, text summarization, and personalized recommendation systems."], "metadata": {"collapsed": false, "id": "c9251246e4ce2edb"}, "id": "c9251246e4ce2edb"}, {"cell_type": "code", "source": ["from pymilvus.model.dense import JinaEmbeddingFunction\n", "\n", "jina_api_key = \"<YOUR_JINA_API_KEY>\"\n", "ef = JinaEmbeddingFunction(\"jina-embeddings-v2-base-en\", jina_api_key)\n", "\n", "query = \"what is information retrieval?\"\n", "doc = \"Information retrieval is the process of finding relevant information from a large collection of data or documents.\"\n", "\n", "qvecs = ef.encode_queries([query])\n", "dvecs = ef.encode_documents([doc])"], "metadata": {"id": "541e01d196bfb8fd"}, "id": "541e01d196bfb8fd", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": ["## Bilingual Embeddings\n", "Jina AI's bilingual models enhance multilingual platforms, global support, and cross-lingual content discovery. Designed for German-English and Chinese-English translations, they foster understanding among diverse linguistic groups, simplifying interactions across languages."], "metadata": {"collapsed": false, "id": "76bf2f06073c10c"}, "id": "76bf2f06073c10c"}, {"cell_type": "code", "source": ["from pymilvus.model.dense import JinaEmbeddingFunction\n", "\n", "jina_api_key = \"<YOUR_JINA_API_KEY>\"\n", "ef = JinaEmbeddingFunction(\"jina-embeddings-v2-base-de\", jina_api_key)\n", "\n", "query = \"what is information retrieval?\"\n", "doc = \"Information Retrieval ist der Prozess, relevante Informationen aus einer großen Sammlung von Daten oder Dokumenten zu finden.\"\n", "\n", "qvecs = ef.encode_queries([query])\n", "dvecs = ef.encode_documents([doc])"], "metadata": {"id": "7877da246e95292b"}, "id": "7877da246e95292b", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": ["## Code Embeddings\n", "Jina AI's code embedding model provides searching ability through code and documentation. It supports English and 30 popular programming languages that can be used for enhancing code navigation, streamlined code review and automated documentation assistance."], "metadata": {"collapsed": false, "id": "5086f6f097d5de36"}, "id": "5086f6f097d5de36"}, {"cell_type": "code", "source": ["from pymilvus.model.dense import JinaEmbeddingFunction\n", "\n", "jina_api_key = \"<YOUR_JINA_API_KEY>\"\n", "ef = JinaEmbeddingFunction(\"jina-embeddings-v2-base-code\", jina_api_key)\n", "\n", "# Case1: Enhanced Code Navigation\n", "# query: text description of the functionality\n", "# document: relevant code snippet\n", "\n", "query = \"function to calculate average in Python.\"\n", "doc = \"\"\"\n", "def calculate_average(numbers):\n", "    total = sum(numbers)\n", "    count = len(numbers)\n", "    return total / count\n", "\"\"\"\n", "\n", "# Case2: Streamlined Code Review\n", "# query: text description of the programming concept\n", "# document: relevante code snippet or PR\n", "\n", "query = \"pull quest related to Collection\"\n", "doc = \"fix:[restful v2] parameters of create collection ...\"\n", "\n", "# Case3: Automatic Documentation Assistance\n", "# query: code snippet you need explanation\n", "# document: relevante document or DocsString\n", "\n", "query = \"What is Collection in Milvus\"\n", "doc = \"\"\"\n", "In Milvus, you store your vector embeddings in collections. All vector embeddings within a collection share the same dimensionality and distance metric for measuring similarity.\n", "Milvus collections support dynamic fields (i.e., fields not pre-defined in the schema) and automatic incrementation of primary keys.\n", "\"\"\"\n", "\n", "qvecs = ef.encode_queries([query])\n", "dvecs = ef.encode_documents([doc])"], "metadata": {"id": "b54e81a1863eadbc"}, "id": "b54e81a1863eadbc", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "markdown", "source": ["## Semantic Search with <PERSON><PERSON> & <PERSON>\n", "With the powerful vector embedding function, we can combine the embeddings retrieved by utilizing Jina AI models with Milvus Lite vector database to perform semantic search."], "id": "3fb7ecc7c0bb19ef"}, {"metadata": {}, "cell_type": "code", "source": ["from pymilvus.model.dense import JinaEmbeddingFunction\n", "from pymilvus import MilvusClient\n", "\n", "jina_api_key = \"<YOUR_JINA_API_KEY>\"\n", "ef = JinaEmbeddingFunction(\"jina-embeddings-v2-base-en\", jina_api_key)\n", "DIMENSION = 768  # size of jina-embeddings-v2-base-en\n", "\n", "doc = [\n", "    \"In 1950, <PERSON> published his seminal paper, 'Computing Machinery and Intelligence,' proposing the Turing Test as a criterion of intelligence, a foundational concept in the philosophy and development of artificial intelligence.\",\n", "    \"The Dartmouth Conference in 1956 is considered the birthplace of artificial intelligence as a field; here, <PERSON> and others coined the term 'artificial intelligence' and laid out its basic goals.\",\n", "    \"In 1951, British mathematician and computer scientist <PERSON> also developed the first program designed to play chess, demonstrating an early example of AI in game strategy.\",\n", "    \"The invention of the Logic Theorist by <PERSON>, <PERSON>, and <PERSON> in 1955 marked the creation of the first true AI program, which was capable of solving logic problems, akin to proving mathematical theorems.\",\n", "]\n", "\n", "dvecs = ef.encode_documents(doc)\n", "\n", "data = [\n", "    {\"id\": i, \"vector\": dvecs[i], \"text\": doc[i], \"subject\": \"history\"}\n", "    for i in range(len(dvecs))\n", "]\n", "\n", "milvus_client = MilvusClient(\"./milvus_jina_demo.db\")\n", "COLLECTION_NAME = \"demo_collection\"  # Milvus collection name\n", "if milvus_client.has_collection(collection_name=COLLECTION_NAME):\n", "    milvus_client.drop_collection(collection_name=COLLECTION_NAME)\n", "milvus_client.create_collection(collection_name=COLLECTION_NAME, dimension=DIMENSION)\n", "\n", "res = milvus_client.insert(collection_name=COLLECTION_NAME, data=data)\n", "\n", "print(res[\"insert_count\"])"], "id": "83dc520d0684b82e", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": ["> As for the argument of `MilvusClient`:\n", "> - Setting the `uri` as a local file, e.g.`./milvus.db`, is the most convenient method, as it automatically utilizes [Milvus Lite](https://milvus.io/docs/milvus_lite.md) to store all data in this file.\n", "> - If you have large scale of data, you can set up a more performant Milvus server on [docker or kubernetes](https://milvus.io/docs/quickstart.md). In this setup, please use the server uri, e.g.`http://localhost:19530`, as your `uri`.\n", "> - If you want to use [Zilliz Cloud](https://zilliz.com/cloud), the fully managed cloud service for Milvus, adjust the `uri` and `token`, which correspond to the [Public Endpoint and Api key](https://docs.zilliz.com/docs/on-zilliz-cloud-console#free-cluster-details) in Zilliz Cloud."], "metadata": {"collapsed": false}}, {"metadata": {}, "cell_type": "markdown", "source": ["With all data in Milvus vector database, we can now perform semantic search by generating vector embedding for the query and conduct vector search."], "id": "774929336febc81d"}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-30T07:44:54.881785Z", "start_time": "2024-05-30T07:44:54.396756Z"}}, "cell_type": "code", "source": ["queries = \"What event in 1956 marked the official birth of artificial intelligence as a discipline?\"\n", "qvecs = ef.encode_queries([queries])\n", "\n", "res = milvus_client.search(\n", "    collection_name=COLLECTION_NAME,  # target collection\n", "    data=[qvecs[0]],  # query vectors\n", "    limit=3,  # number of returned entities\n", "    output_fields=[\"text\", \"subject\"],  # specifies fields to be returned\n", ")[0]\n", "\n", "for result in res:\n", "    print(result)"], "id": "19e8c0e5e7b49c5b", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'id': 1, 'distance': 0.8802614808082581, 'entity': {'text': \"The Dartmouth Conference in 1956 is considered the birthplace of artificial intelligence as a field; here, <PERSON> and others coined the term 'artificial intelligence' and laid out its basic goals.\", 'subject': 'history'}}\n"]}], "execution_count": 10}, {"cell_type": "markdown", "source": ["## <PERSON><PERSON>\n", "<PERSON>a Ai also provides rerankers to further enhance retrieval quality after searching using embeddings."], "metadata": {"collapsed": false, "id": "c067c5218388d11f"}, "id": "c067c5218388d11f"}, {"metadata": {}, "cell_type": "code", "outputs": [{"data": {"text/plain": ["[<PERSON><PERSON><PERSON><PERSON><PERSON>(text=\"The Dartmouth Conference in 1956 is considered the birthplace of artificial intelligence as a field; here, <PERSON> and others coined the term 'artificial intelligence' and laid out its basic goals.\", score=0.9370958209037781, index=1),\n", " <PERSON><PERSON><PERSON><PERSON><PERSON>(text='The invention of the Logic Theorist by <PERSON>, <PERSON>, and <PERSON> in 1955 marked the creation of the first true AI program, which was capable of solving logic problems, akin to proving mathematical theorems.', score=0.35420963168144226, index=3),\n", " <PERSON><PERSON><PERSON><PERSON><PERSON>(text=\"In 1950, <PERSON> published his seminal paper, 'Computing Machinery and Intelligence,' proposing the Turing Test as a criterion of intelligence, a foundational concept in the philosophy and development of artificial intelligence.\", score=0.3498658835887909, index=0),\n", " <PERSON><PERSON><PERSON><PERSON><PERSON>(text='In 1951, British mathematician and computer scientist <PERSON> also developed the first program designed to play chess, demonstrating an early example of AI in game strategy.', score=0.2728956639766693, index=2)]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "execution_count": null, "source": ["from pymilvus.model.reranker import JinaRerankFunction\n", "\n", "jina_api_key = \"<YOUR_JINA_API_KEY>\"\n", "\n", "rf = Jina<PERSON>erankFunction(\"jina-reranker-v1-base-en\", jina_api_key)\n", "\n", "query = \"What event in 1956 marked the official birth of artificial intelligence as a discipline?\"\n", "\n", "documents = [\n", "    \"In 1950, <PERSON> published his seminal paper, 'Computing Machinery and Intelligence,' proposing the Turing Test as a criterion of intelligence, a foundational concept in the philosophy and development of artificial intelligence.\",\n", "    \"The Dartmouth Conference in 1956 is considered the birthplace of artificial intelligence as a field; here, <PERSON> and others coined the term 'artificial intelligence' and laid out its basic goals.\",\n", "    \"In 1951, British mathematician and computer scientist <PERSON> also developed the first program designed to play chess, demonstrating an early example of AI in game strategy.\",\n", "    \"The invention of the Logic Theorist by <PERSON>, <PERSON>, and <PERSON> in 1955 marked the creation of the first true AI program, which was capable of solving logic problems, akin to proving mathematical theorems.\",\n", "]\n", "\n", "rf(query, documents)"], "id": "1953c1d0c63f53b"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}, "colab": {"provenance": []}}, "nbformat": 4, "nbformat_minor": 5}