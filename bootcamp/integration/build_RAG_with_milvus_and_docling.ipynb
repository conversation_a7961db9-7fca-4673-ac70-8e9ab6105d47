{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/integration/build_RAG_with_milvus_and_docling.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/tutorials/integration/build_RAG_with_milvus_and_docling.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Build RAG with <PERSON><PERSON><PERSON><PERSON> and <PERSON>ling\n", "\n", "[Doc<PERSON>](https://github.com/docling-project/docling) streamlines document parsing and understanding across diverse formats for AI applications. With advanced PDF comprehension and unified document representation, <PERSON>ling makes unstructured document data ready for downstream workflows.\n", "\n", "In this tutorial, we’ll show you how to build a Retrieval-Augmented Generation (RAG) pipeline using Milvus and Docling. The pipeline integrates Docling for document parsing, Milvus for vector storage, and OpenAI for generating insightful, context-aware responses."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Preparation\n", "\n", "### Dependencies and Environment\n", "\n", "To start, install the required dependencies by running the following command:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["! pip install --upgrade pymilvus docling openai"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> If you are using Google Colab, to enable dependencies just installed, you may need to **restart the runtime** (click on the \"Runtime\" menu at the top of the screen, and select \"Restart session\" from the dropdown menu)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Setting Up API Keys\n", "\n", "We will use OpenAI as the LLM in this example. You should prepare the [OPENAI_API_KEY](https://platform.openai.com/docs/quickstart) as an environment variable."]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-***********\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Prepare the LLM and Embedding Model\n", "\n", "We initialize the OpenAI client to prepare the embedding model.\n"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [], "source": ["from openai import OpenAI\n", "\n", "openai_client = OpenAI()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Define a function to generate text embeddings using OpenAI client. We use the [text-embedding-3-small](https://platform.openai.com/docs/guides/embeddings) model as an example."]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["def emb_text(text):\n", "    return (\n", "        openai_client.embeddings.create(input=text, model=\"text-embedding-3-small\")\n", "        .data[0]\n", "        .embedding\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Generate a test embedding and print its dimension and first few elements."]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1536\n", "[0.00988506618887186, -0.005540902726352215, 0.0068014683201909065, -0.03810417652130127, -0.018254263326525688, -0.041231658309698105, -0.007651153020560741, 0.03220026567578316, 0.01892443746328354, 0.00010708322952268645]\n"]}], "source": ["test_embedding = emb_text(\"This is a test\")\n", "embedding_dim = len(test_embedding)\n", "print(embedding_dim)\n", "print(test_embedding[:10])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Process Data Using Docling\n", "\n", "Docling can parse various document formats into a unified representation (Docling Document), which can then be exported to different output formats. For a full list of supported input and output formats, please refer to [the official documentation](https://docling-project.github.io/docling/usage/supported_formats/).\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this tutorial, we will use a Markdown file ([source](https://milvus.io/docs/overview.md)) as the input. We will process the document using a **HierarchicalChunker** provided by Docling to generate structured, hierarchical chunks suitable for downstream RAG tasks."]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Chunk 1:\n", "Milvus is a high-performance, highly scalable vector database that runs efficiently across a wide range of environments, from a laptop to large-scale distributed systems. It is available as both open-source software and a cloud service.\n", "--------------------------------------------------\n", "Chunk 2:\n", "Milvus is an open-source project under LF AI & Data Foundation distributed under the Apache 2.0 license. Most contributors are experts from the high-performance computing (HPC) community, specializing in building large-scale systems and optimizing hardware-aware code. Core contributors include professionals from Zilliz, ARM, NVIDIA, AMD, Intel, Meta, IBM, Salesforce, Alibaba, and Microsoft.\n", "--------------------------------------------------\n", "Chunk 3:\n", "Unstructured data, such as text, images, and audio, varies in format and carries rich underlying semantics, making it challenging to analyze. To manage this complexity, embeddings are used to convert unstructured data into numerical vectors that capture its essential characteristics. These vectors are then stored in a vector database, enabling fast and scalable searches and analytics.\n", "--------------------------------------------------\n", "Chunk 4:\n", "Milvus offers robust data modeling capabilities, enabling you to organize your unstructured or multi-modal data into structured collections. It supports a wide range of data types for different attribute modeling, including common numerical and character types, various vector types, arrays, sets, and JSON, saving you from the effort of maintaining multiple database systems.\n", "--------------------------------------------------\n", "Chunk 5:\n", "Untructured data, embeddings, and Milvus\n", "--------------------------------------------------\n"]}], "source": ["from docling.document_converter import DocumentConverter\n", "from docling_core.transforms.chunker import HierarchicalChunker\n", "\n", "converter = DocumentConverter()\n", "chunker = HierarchicalChunker()\n", "\n", "# Convert the input file to Docling Document\n", "source = \"https://milvus.io/docs/overview.md\"\n", "doc = converter.convert(source).document\n", "\n", "# Perform hierarchical chunking\n", "texts = [chunk.text for chunk in chunker.chunk(doc)]\n", "\n", "for i, text in enumerate(texts[:5]):\n", "    print(f\"Chunk {i+1}:\\n{text}\\n{'-'*50}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Data into Milvus\n", "\n", "### Create the collection"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [], "source": ["from pymilvus import MilvusClient\n", "\n", "milvus_client = MilvusClient(uri=\"./milvus_demo.db\")\n", "collection_name = \"my_rag_collection\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["> As for the argument of `MilvusClient`:\n", "> - Setting the `uri` as a local file, e.g.`./milvus.db`, is the most convenient method, as it automatically utilizes [Milvus Lite](https://milvus.io/docs/milvus_lite.md) to store all data in this file.\n", "> - If you have large scale of data, you can set up a more performant Milvus server on [docker or kubernetes](https://milvus.io/docs/quickstart.md). In this setup, please use the server uri, e.g.`http://localhost:19530`, as your `uri`.\n", "> - If you want to use [Zilliz Cloud](https://zilliz.com/cloud), the fully managed cloud service for Milvus, adjust the `uri` and `token`, which correspond to the [Public Endpoint and Api key](https://docs.zilliz.com/docs/on-zilliz-cloud-console#free-cluster-details) in Zilliz Cloud."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Check if the collection already exists and drop it if it does."]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [], "source": ["if milvus_client.has_collection(collection_name):\n", "    milvus_client.drop_collection(collection_name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Create a new collection with specified parameters.\n", "\n", "If we don’t specify any field information, Milvus will automatically create a default `id` field for primary key, and a `vector` field to store the vector data. A reserved JSON field is used to store non-schema-defined fields and their values."]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [], "source": ["milvus_client.create_collection(\n", "    collection_name=collection_name,\n", "    dimension=embedding_dim,\n", "    metric_type=\"IP\",  # Inner product distance\n", "    consistency_level=\"Strong\",  # Supported values are (`\"Strong\"`, `\"Session\"`, `\"Bounded\"`, `\"Eventually\"`). See https://milvus.io/docs/consistency.md#Consistency-Level for more details.\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Insert data"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Processing chunks: 100%|██████████| 36/36 [00:18<00:00,  1.96it/s]\n"]}, {"data": {"text/plain": ["{'insert_count': 36, 'ids': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35], 'cost': 0}"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["from tqdm import tqdm\n", "\n", "data = []\n", "\n", "for i, chunk in enumerate(tqdm(texts, desc=\"Processing chunks\")):\n", "    embedding = emb_text(chunk)\n", "    data.append({\"id\": i, \"vector\": embedding, \"text\": chunk})\n", "\n", "milvus_client.insert(collection_name=collection_name, data=data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Build RAG"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Retrieve data for a query\n", "\n", "Let’s specify a query question about the website we just scraped."]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [], "source": ["question = (\n", "    \"What are the three deployment modes of Milvus, and what are their differences?\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Search for the question in the collection and retrieve the semantic top-3 matches."]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [], "source": ["search_res = milvus_client.search(\n", "    collection_name=collection_name,\n", "    data=[emb_text(question)],\n", "    limit=3,\n", "    search_params={\"metric_type\": \"IP\", \"params\": {}},\n", "    output_fields=[\"text\"],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let’s take a look at the search results of the query\n"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "    [\n", "        \"Milvus offers three deployment modes, covering a wide range of data scales\\u2014from local prototyping in Jupyter Notebooks to massive Kubernetes clusters managing tens of billions of vectors:\",\n", "        0.6503741145133972\n", "    ],\n", "    [\n", "        \"Milvus Lite is a Python library that can be easily integrated into your applications. As a lightweight version of Milvus, it\\u2019s ideal for quick prototyping in Jupyter Notebooks or running on edge devices with limited resources. Learn more.\\nMilvus Standalone is a single-machine server deployment, with all components bundled into a single Docker image for convenient deployment. Learn more.\\nMilvus Distributed can be deployed on Kubernetes clusters, featuring a cloud-native architecture designed for billion-scale or even larger scenarios. This architecture ensures redundancy in critical components. Learn more.\",\n", "        0.6281254291534424\n", "    ],\n", "    [\n", "        \"What is <PERSON><PERSON><PERSON><PERSON>?\\nUnstructured Data, Embeddings, and Milvus\\nWhat Makes Milvus so Fast\\uff1f\\nWhat Makes Milvus so Scalable\\nTypes of Searches Supported by Milvus\\nComprehensive Feature Set\",\n", "        0.6117545962333679\n", "    ]\n", "]\n"]}], "source": ["import json\n", "\n", "retrieved_lines_with_distances = [\n", "    (res[\"entity\"][\"text\"], res[\"distance\"]) for res in search_res[0]\n", "]\n", "print(json.dumps(retrieved_lines_with_distances, indent=4))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Use LLM to get a RAG response\n", "\n", "Convert the retrieved documents into a string format.\n", "\n"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [], "source": ["context = \"\\n\".join(\n", "    [line_with_distance[0] for line_with_distance in retrieved_lines_with_distances]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Define system and user prompts for the Lanage Model. This prompt is assembled with the retrieved documents from Milvus.\n", "\n"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [], "source": ["SYSTEM_PROMPT = \"\"\"\n", "Human: You are an AI assistant. You are able to find answers to the questions from the contextual passage snippets provided.\n", "\"\"\"\n", "USER_PROMPT = f\"\"\"\n", "Use the following pieces of information enclosed in <context> tags to provide an answer to the question enclosed in <question> tags.\n", "<context>\n", "{context}\n", "</context>\n", "<question>\n", "{question}\n", "</question>\n", "\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Use OpenAI ChatGPT to generate a response based on the prompts."]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The three deployment modes of Milvus are Milvus Lite, Milvus Standalone, and Milvus Distributed. \n", "\n", "1. **Milvus Lite**: This is a Python library designed for easy integration into applications. It is lightweight and ideal for quick prototyping in Jupyter Notebooks or for use on edge devices with limited resources.\n", "\n", "2. **Milvus Standalone**: This deployment mode involves a single-machine server with all components bundled into a single Docker image for convenient deployment.\n", "\n", "3. **Milvus Distributed**: This mode can be deployed on Kubernetes clusters and is built for larger-scale scenarios, including managing billions of vectors. It features a cloud-native architecture that ensures redundancy in critical components, making it suited for extensive scalability.\n"]}], "source": ["response = openai_client.chat.completions.create(\n", "    model=\"gpt-4o\",\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": SYSTEM_PROMPT},\n", "        {\"role\": \"user\", \"content\": USER_PROMPT},\n", "    ],\n", ")\n", "print(response.choices[0].message.content)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}