{"cells": [{"cell_type": "markdown", "id": "eb9e3f09-7e8f-43f2-b9dc-753917c2445b", "metadata": {}, "source": ["# Multi-agent Systems with Mistral AI, Milvus and Llama-agents"]}, {"cell_type": "markdown", "id": "30ebd78c-4b8a-4d41-ae79-e4dd36237908", "metadata": {}, "source": ["# Goal of this Notebook\n", "\n", "In this Notebook, we will explore different ideas: \n", "\n", "### 1️⃣ Store Data into Milvus:\n", "Learn to store data into Milvus, an efficient vector database designed for high-speed similarity searches and AI applications.\n", "\n", "### 2️⃣ Use llama-index with Mistral Models for Data Queries:\n", "Discover how to use llama-index in combination with Mistral models to query data stored in Milvus.\n", "\n", "### 3️⃣ Create Automated Data Search and Reading Agents:\n", "Build agents that can automatically search and read data based on user queries. These automated agents will enhance user experience by delivering quick, accurate responses, reducing manual search effort.\n", "\n", "### 4️⃣ Develop Agents for Metadata Filtering Based on User Queries:\n", "Implement agents that can automatically generate metadata filters from user queries, refining and contextualising search results, avoiding  confusion and enhancing the accuracy of information retrieved, even for complex queries.\n", "\n", "# 🔍 Summary\n", "By the end of this notebook, you’ll have a comprehensive understanding of using Milvus, llama-index with llama-agents, and Mistral models to build a robust and efficient data retrieval system.\n", "\n", "--- "]}, {"cell_type": "markdown", "id": "fab70f78-08f4-4ab9-a10f-b6094a643772", "metadata": {}, "source": ["# Milvus\n", "Milvus is an open-source vector database that powers AI applications with vector embeddings and similarity search.\n", "\n", "In this notebook, we use Milvus Lite, it is the lightweight version of Milvus.\n", "\n", "With Milvus Lite, you can start building an AI application with vector similarity search within minutes! Milvus Lite is good for running in the following environment:\n", "\n", "* Jupyter Notebook / Google Colab\n", "* Laptops\n", "* Edge Devices"]}, {"attachments": {"ad459431-95ac-4cbd-a931-453d08d5fdef.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "2cd012d0-7440-466a-8dad-6ecbbc1dc655", "metadata": {}, "source": ["![image.png](attachment:ad459431-95ac-4cbd-a931-453d08d5fdef.png)"]}, {"attachments": {"7bd73318-7929-4675-8998-c2e9ef091906.png": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABgoAAANfCAIAAACHThFwAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAGCqADAAQAAAABAAADXwAAAABDYzZlAABAAElEQVR4AezdB3hUxdrAcUih19Ah9N6E0AVCAKmCoICCioBKERVFsICi4hUUC4KgFAEVQbAgCII0EUInlNB7D70k1ABp3DdOGA6bZLNJNpuzu/88eXLnzJkzZ+Y3y/2+fe+UjHfv3s3ADwIIIIAAAggggAACCCCAAAIIIICAuwp4uGvH6TcCCCCAAAIIIIAAAggggAACCCCAQKwA4SE+BwgggAACCCCAAAIIIIAAAggggIBbCxAecuvhp/MIIIAAAggggAACCCCAAAIIIIAA4SE+AwgggAACCCCAAAIIIIAAAggggIBbCxAecuvhp/MIIIAAAggggAACCCCAAAIIIIAA4SE+AwgggAACCCCAAAIIIIAAAggggIBbCxAecuvhp/MIIIAAAggggAACCCCAAAIIIIAA4SE+AwgggAACCCCAAAIIIIAAAggggIBbCxAecuvhp/MIIIAAAggggAACCCCAAAIIIIAA4SE+AwgggAACCCCAAAIIIIAAAggggIBbCxAecuvhp/MIIIAAAggggAACCCCAAAIIIIAA4SE+AwgggAACCCCAAAIIIIAAAggggIBbCxAecuvhp/MIIIAAAggggAACCCCAAAIIIIAA4SE+AwgggAACCCCAAAIIIIAAAggggIBbCxAecuvhp/MIIIAAAggggAACCCCAAAIIIIAA4SE+AwgggAACCCCAAAIIIIAAAggggIBbCxAecuvhp/MIIIAAAggggAACCCCAAAIIIIAA4SE+AwgggAACCCCAAAIIIIAAAggggIBbCxAecuvhp/MIIIAAAggggAACCCCAAAIIIIAA4SE+AwgggAACCCCAAAIIIIAAAggggIBbCxAecuvhp/MIIIAAAggggAACCCCAAAIIIIAA4SE+AwgggAACCCCAAAIIIIAAAggggIBbCxAecuvhp/MIIIAAAggggAACCCCAAAIIIIAA4SE+AwgggAACCCCAAAIIIIAAAggggIBbCxAecuvhp/MIIIAAAggggAACCCCAAAIIIIAA4SE+AwgggAACCCCAAAIIIIAAAggggIBbCxAecuvhp/MIIIAAAggggAACCCCAAAIIIIAA4SE+AwgggAACCCCAAAIIIIAAAggggIBbCxAecuvhp/MIIIAAAggggAACCCCAAAIIIIAA4SE+AwgggAACCCCAAAIIIIAAAggggIBbCxAecuvhp/MIIIAAAggggAACCCCAAAIIIIAA4SE+AwgggAACCCCAAAIIIIAAAggggIBbCxAecuvhp/MIIIAAAggggAACCCCAAAIIIIAA4SE+AwgggAACCCCAAAIIIIAAAggggIBbCxAecuvhp/MIIIAAAggggAACCCCAAAIIIIAA4SE+AwgggAACCCCAAAIIIIAAAggggIBbCxAecuvhp/MIIIAAAggggAACCCCAAAIIIIAA4SE+AwgggAACCCCAAAIIIIAAAggggIBbCxAecuvhp/MIIIAAAggggAACCCCAAAIIIIAA4SE+AwgggAACCCCAAAIIIIAAAggggIBbCxAecuvhp/MIIIAAAggggAACCCCAAAIIIIAA4SE+AwgggAACCCCAAAIIIIAAAggggIBbCxAecuvhp/MIIIAAAggggAACCCCAAAIIIIAA4SE+AwgggAACCCCAAAIIIIAAAggggIBbCxAecuvhp/MIIIAAAggggAACCCCAAAIIIIAA4SE+AwgggAACCCCAAAIIIIAAAggggIBbCxAecuvhp/MIIIAAAggggAACCCCAAAIIIIAA4SE+AwgggAACCCCAAAIIIIAAAggggIBbCxAecuvhp/MIIIAAAggggAACCCCAAAIIIIAA4SE+AwgggAACCCCAAAIIIIAAAggggIBbC3i5de/pPAIIIIAAAggggIApBSIv302wXZGXYxLMjwpLMNsyMyI02jLLHNeZfDyT2xCvvLY+4Z0v6f9J2DtfRluroxwCCCCAgCsKEB5yxVGlTwgggAACCCCAgAkEjCEei7BO/GiOIwM36/esNgFP+jShYdUmKX6x9RhWkuEq61Eq4lMpHhceRAABBOwiQHjILoxUggACCCCAAAIIuJpAYGCg9S5FXI6dibN+w3pjsciwhGf3SJmtB4OMJW1P7z62w/bCySpZrXSNZJV30sJpB2gjSOqdvfNYToCqXbGefrtnbp1MYcLf3z+FT957LCAg4F6S/0QAAQScUiDj3bsJT9x1yt7QaAQQQAABBBBAAAEbBNS8nlWBK6OvZNi0Lza+s/VAXOwmODjYhgookiH1IQ+XR0z3sJR5hPv16yeNkSAUUSTzDAotQQABCwHCQxYgXCKAAAIIIIAAAq4pICEhWeG1em1gRGjMxAVjpZPGb+8q2FG7Qr16lRrKrdQsQXJNPpkn5cZL0pxxTM3wGVafmUw+HmvXr81SyiMoKEiFX1W0aNiwYc4IS5sRQMBVBQgPuerI0i8EEEAAAQQQQCBOQAJDK+avlC+osrzLIiTUv8NAKWSGL9KMFgIuLyCbN8kOTRKi3XJm/eTJk6W/EiciSOTy404HEXAWAcJDzjJStBMBBBBAAAEEEEi2QPjBaPku+vWPX+mokHGWEFGhZIPyAAJ2EshW1vOTz0dOXzpF6mMykZ1QqQYBBFIlQHgoVXw8jAACCCCAAAIImFNAzRhSgaGerftII+WLqMSGZLoQUSFzDhmtckMBFSTaeWGLLDpjJpEbfgDoMgKmEiA8ZKrhoDEIIIAAAggggIAdBCQ29NE7H0s8SAJDspeQ2mmIwJAdZKkCgbQR+Pbfz6f+wnKztMGlVgQQsE2A8JBtTpRCAAEEEEAAAQScREAWlHXu3VEaO2Po3Oc+7SQJAkNOMnQ0060Fxv4xSq81Y0Mit/4o0HkE0kmA8FA6wfNaBBBAAAEEEEAgDQSWz/33q7FfyQFkUreaPTSw85A0eA9VIoCA/QW2nF3b58OeUi8LzeyPS40IIJCUgEdSBbiPAAIIIIAAAggg4BwCEht64fWeOjb07es/EBtyjpGjlQj8J1CnSOM9c477+fnJuWYjRoxABQEEEHCkALOHHKnNuxBAAAEEEEAAgbQSkDVlFR8ppXehltgQW1CnlTX1IpCWAuv3rH7l6+flDTNnzgwICEjLV1E3AgggcF+A8NB9C1IIIIAAAggggICTCqi9qFXjZU0ZsSEnHUeajYASUKvMZBrRggULMEEAAQQcI8DiMsc48xYEEEAAAQQQQCANBeScMlU7saE0VKZqBBwlIKvMenfrJ6fdd+jQwVHv5D0IIODuAoSH3P0TQP8RQAABBBBAwNkFZI8SiQrJAfbEhpx9KGk/AlrgjS5DZfaQRIgCAwN1JgkEEEAg7QRYXJZ2ttSMAAIIIIAAAgg4QqB48eJqyyF5GXtRO0KcdyDgEAGWmDmEmZcggECcAOEhPgoIIIAAAggggIATC8jUodvHY6QDWw8GzRg614l7QtMRQOBBgUw+nt1HPiETiEJCQh68wxUCCCBgfwEWl9nflBoRQAABBBBAAAGHCaxbtlHeJcvK1Hn2DnsvL0IAgbQWiAiNHtBroLyF9WVpTU39CCAgAoSH+BgggAACCCCAAALOKiBTh3Yf2yGtl8VlLCtz1lGk3QgkLlC/WIDsQDR69OjEi3AHAQQQsI8A4SH7OFILAggggAACCCDgeAFZVlatdA2ZOkRsyPH4vBEBBwioCURsUO0Aal6BAAKEh/gMIIAAAggggAACziqg1pTpfamdtRu0GwEEEhdQE4jWrFmTeBHuIIAAAnYQ8LJDHVSBAAIIIIAAAggg4HABWVkmgSGJEAV/d8ThL+eFCCDgIAGZQCRvCgoKctD7eA0CCLirALOH3HXk6TcCCCCAAAIIOL+AxIaYOuT8w0gPEEhCQG1QnUQhbiOAAAKpEyA8lDo/nkYAAQQQQAABBNJJQGYTyMZD6fRyXosAAg4VkO2HHPo+XoYAAu4nQHjI/cacHiOAAAIIIICASwjI10U5toxNqV1iMOkEAtYEZPshuc3x9taMuIcAAqkWIDyUakIqQAABBBBAAAEEHC6gviiysszh8LwQgXQQkO2H5Hj7dHgxr0QAAXcSIDzkTqNNXxFAAAEEEEDAVQQ4xshVRpJ+IGCTQO2K9UaPHm1TUQohgAACKRIgPJQiNh5CAAEEEEAAAQRMIMDKMhMMAk1AwBECDR9u6IjX8A4EEHBjAcJDbjz4dB0BBBBAAAEEnFaAU66dduhoOAIpEYi+kpKneAYBBBCwXYDwkO1WlEQAAQQQQAABBEwkwLFlJhoMmoJAGgtEhMak8RuoHgEE3F2A8JC7fwLoPwIIIIAAAgg4qUDtCvWctOU0GwEEEEAAAQTMJkB4yGwjQnsQQAABBBBAAIGkBeRU+6QLUQIBBFxIgH/1LjSYdAUBMwoQHjLjqNAmBBBAAAEEEEAgSYF6ldiqNkkkCiDgIgINqzZxkZ7QDQQQMKsA4SGzjgztQgABBBBAAAEErArwddEqDzcRQAABBBBAIBkChIeSgUVRBBBAAAEEEEAAAQQQQAABBBBAwPUECA+53pjSIwQQQAABBBBwcYHAwEAX7yHdQwABBBBAAAHHChAecqw3b0MAAQQQQAABBBBAAAEEEEAAAQRMJkB4yGQDQnMQQAABBBBAAAEEEEAAAQQQQAABxwoQHnKsN29DAAEEEEAAAQQQQAABBJIpsH7P6mQ+QXEEEEAgeQKEh5LnRWkEEEAAAQQQQAABBBBAAAEEEEDAxQQID7nYgNIdBBBAAAEEEEAAAQQQQAABBBBAIHkChIeS50VpBBBAAAEEEEAAAQQQQMDxAn5+fo5/KW9EAAH3ESA85D5jTU8RQAABBBBAAAEEEEAAAQQQQACBBAQIDyWAQhYCCCCAAAIIIIAAAgggYB6BoP3rzdMYWoIAAi4pQHjIJYeVTiGAAAIIIICAKwusWbPGlbtH3xBAAAEEEEDA4QKEhxxOzgsRQAABBBBAAAEEEEAAAQQQQAABMwkQHjLTaNAWBBBAAAEEEEAAAQQQQAABBBBAwOEChIccTs4LEUAAAQQQQAABBBBAAAEEEEAAATMJEB4y02jQFgQQQAABBBBAAAEEEEAAAQQQQMDhAoSHHE7OCxFAAAEEEEAAAQQQQAABBBBAAAEzCRAeMtNo0BYEEEAAAQQQQAABBBBAAAEEEEDA4QKEhxxOzgsRQAABBBBAAAEEEEAAAQQQQAABMwkQHjLTaNAWBBBAAAEEEEAAAQQQQAABBBBAwOEChIccTs4LEUAAAQQQQAABBBBAAIHkCGw9GJSc4pRFAAEEki1AeCjZZDyAAAIIIIAAAggggAACCCCAAAIIuJIA4SFXGk36ggACCCCAAAJuIRAUxDwCtxhoOokAAggggIDDBAgPOYyaFyGAAAIIIIAAAggggAACCCCAAAJmFCA8ZMZRoU0IIIAAAggggAACCCCAAAIIIICAwwQIDzmMmhchgAACCCCAAAIIIIAAAggggAACZhQgPGTGUaFNCCCAAAIIIIAAAggggAACCCCAgMMECA85jJoXIYAAAggggAACCCCAAAIIIIAAAmYUIDxkxlGhTQgggAACCCCAAAIIIIAAAggggIDDBAgPOYyaFyGAAAIIIIAAAggggAACCCCAAAJmFCA8ZMZRoU0IIIAAAggggAACCCCAAAIIIICAwwQIDzmMmhchgAACCCCAAAIIIIAAAggggAACZhQgPGTGUaFNCCCAAAIIIIAAAggggAACCCCAgMMECA85jJoXIYAAAggggAACCCCAAAIIIIAAAmYUIDxkxlGhTQgggAACCCCAgBWB4OBgK3e5hQACCCCAAAIIJFeA8FByxSiPAAIIIIAAAggggAACCCCAAAIIuJQA4SGXGk46gwACCCCAAAIIIIAAAggggAACCCRXgPBQcsUojwACCCCAAAIIIIAAAgg4WoBVpY4W530IuJkA4SE3G3C6iwACCCCAAAIIIIAAAggggAACCDwoQHjoQQ+uEEAAAQQQQAABBBBAAAEEEEAAATcTIDzkZgNOdxFAAAEEEEAAAacSmPr3N4+912zU7OF37951qobTWAQQQAABBJxJwMuZGktbEUAAAQQQQAABBNxJYO+JXd/+OUZ6/OvKGZ38u1bwrWyq3l+9EfbTP1M9PTxf7jDIYQ3bf3JP8OEtF66cK+JT7An/rt6e3g57tZO+6Nad8G2HN5cqWLZYAV8n7QLNRgABBBwgQHjIAci8AgEEEEAAAQQQQCAlAut2r9KPeXqYLg6ydMui7/+eJC1sVK1pjTK1dFPTIhF2I3TB+j/mBM46dfGkrv9s6OnXO72jL0nEFzh1MaTvV8+evXy6fYMnPn7hy/gFyEEAAQQQUAKEh/gkIIAAAggggAACCJhRQFaTzVv7u25Znhy5ddokieiYaNWSoH3r0ig8FBUdtWbXv/PXzwncviJ+r4+fOxo/0/E5l65dyOKdJUfWXI5/tfU3yryhAeOfl9iQ9WLcRQABBBAQAcJDfAwQQAABBBBAAAEEzCiwef8G4xf73Nnzmq2VOjwUfifc7m2LjI6UJXVTFn5zLfyqsfJc2XJ3CXg6k3emY+eO9nn0VeOtdEl/O3/01EUTsmTKOqrP2IAaLdKlDYm9dNgPg4+fO5bYXfIRQAABBIwChIeMGqQRQAABBBBAAAEEzCLwx9rZuikSffDyNN3/46rDQwdO7pWJPKcvh5wLPSOrwLJmypY9S/bG1Zrmz11Qd8H2xLGzR/7Z9vcfq385H3bO+JRvgRLPt+n3WMPOptpvaN3uQGnk7YhbQ6YMnPnuvLJFyxvbnI7p6cum/LttWTo2gFcjgAACziVguv8r61x8tBYBBBBAAAEEEEAgLQQOnz64bPPfuubsWbLptBkSN29dP3T64PbDW1RjNuxd+8QHLS0a1rRmizEvT7bITPJy4cZ573//pkWxBlUaPfPI8xJvypgxo8WtdL+sWbbOvhN7pBkSIXrl616LR601QyPl8zN2zqh0x6EBCCCAgBMJEB5yosGiqQgggAACCCCAgFsIxNyN+XD628aummRl2cx/vp+14sew66ESCjE2L8F0jXIp2ax69c4H9hh68dH+TzV5tqBPkQRfkXaZK7YtDrl0smvAc1kzJxGYe7vbBw9X9Z++dMrWg5tkutO1m1dy50j/ZYB/rvst7XCoGQEEEHBJAcJDLjmsdAoBBBBAAAEEEHBiAdlzZ+/xXcYOeHh4Gi9Tk5YVYbKbT94cPsmtRNZ8jf5tpJWnqpSqXrt8vYfK1CxXrJIsBEvZarhc2fIYXyF7DOXLk5IVajK/ydPDK0vmrMbajOmDp/b9G7z07OUzsslRobyFyxWt2Kxmy2xZskuZGf9M++q3TyRx+uKp95792PiUTh85c7j7J49n8sr083vz/as3k1+11M4zoZGSLaLDroflyZFH1a8rSSyR4jHSFQ7sPKRpjRbSmAtXLwz57jWd7+yJwMDAgIAAZ+8F7UcAAXMKEB4y57jQKgQQQAABBBBAwE0Fzl0+M27uF6rzX/Qb/9bkAZL28PBIPcfeE7vGzf1s074NUlW+XPnkNPpOTbpZOXFMghS/rZopB8mXKVqus//TVmYM1a3UYPyA7zN7Z059I/u1e3X9ntV6T+6J87/+c+2c3o++3K7BE7bUfyfi9qyVP/64+Du1oXXNcnUaVw/o0aqPxXZFgTv+GfhtP4vWygZP7zz9weONntp9bLu6tXHvWosy+vKbP78QEPldEbykZ6s+kp9gYGj74a3Tl323avs/6kFZJde/4xsPlfbT9VgkkjtGFo/rS4nN1anYQC5lsZ7OJIEAAgggYEWA8JAVHG4hgAACCCCAAAIIOFRAznEfNOklFYjp0LCzf/Xm6vWJzcS5E3lHwjcS+/AtWMIjY6IhJJkgM/zHd/7eNF935vK1ywvW/yG/Y1+ZrM7bunv37puTXw69FjqwyzsSM5IFbu99P2hp0EL1yPVb13q16tezdZ9fV86sWa5W05otG1Vtuvv4jqFTXpcCPrny2xK70W+3kiiQt/BPQ+Z8+ftI/WoJFX08472v//j82Za9ZLWXlaVbG/euGfb9YOmarl92R5LfTfvWjX91WuZMWVR+8KHN8WNDckvYP5o+NHDHitz3ZjCJra7KmNh3YreO+NT9LwpjvKvTElz7dNaH+lISG/euk99vX/+hYdUmxnxJ2zJGFo/Ychl++4YqlthHyJZKzFCmdoV6u4/tMENLaAMCCLiqQKL/R9RVO0y/EEAAAQQQQAABBEwr8NkvH6l9jovkK/ZOtw89751W5uVh+T9qXr0RNmr28AavVOkyvE3H9x95dIi/TLpJsF8Schr4bV9jbEjOhtcldfzifOhZOehKgikzlk2Vuz8smaQDNHL59ZzPZVqTLFna8M3uiQN/6tr0Od8CxSsVr6LquXkrLgahq01NQs47G9X7618/WNiqbjtdj8wGkplETQfVEaLw2zd1vk5s3r+h/9heOjZk7OPm/RuXb1usS34z/yud9n+oWa82fXu06l25ZFWVKXGfrYeDdIGLD56epvKnLv5WJeTxKiWrS2Rt7e5VsvhOPyUJWaGmbY35kv7i1/9Z5Ng4RhZP2XJ5806clY3r2mypkzIIIICASwoQHnLJYaVTCCCAAAIIIICA8wlIBGdO4CzV7lF9vzZ+n/f2ymTsz+Kgv9oM8ZctinSmbIo8eOLL+tKYkElA63fHRY46NuwSOGZr4NhtMoFFlZEHj5w5JOn897b4uXHrmmzM/M280cZKJL08+H6ERd3KkTWHSly+dtGicOovK/hW/qzPuIUjA7s17yHLvnSFv/z703OfdpJgls6RxP6Te/p+1V3lSGDom9e/lz6u+mpLQM1HVOaq7ctVQjq77WBc9OeVJwaNe3Xq653eeaPL0FnvLfj5vT9lMZoUO3Xh5BONn1Lldx4LVgn9V+aw6APj3+k6XPIlEjRg3Iv9x/aQKVeq2KKNf6rdi9Rl/46vzx62YGCXIery+LljgqzS6q+NY2R8xMa0jtxluTd5ysYHKYYAAgi4mwDhIXcbcfqLAAIIIIAAAgiYUUDmnrw3bZBqmcQR1PY0tyPCVY6Hx/0D3X9dNePdqQPVAjR1V/aB1l1auX3ZwAl956/7XeVs2rd22eZFKt2n/SvDe32WK3vs3s/R0VH6kQwZYiu/c+8wMtmc6M1Jr6q7tSvUb12vvUoHbn/gTDHJ1Bv6RMfEhUVUSTv+LVbAV2ZRSaDn4xe+rFa6hqr56NnDT/2vnWzTo1+k5+nInkoS6GlUNXb34kzemSMi7ugyKiHTfFRC0GS5nPGuzAP64e1fJXw273/LH/ePCw/tenBBkywBe/+Hweqpvu1fleZJWtFJoO1i2Hm5lMlEY+bE7mwtPxKr+mvkqr7tXqtUoqpsUdS0ZguVHx0drRLy1/Yx0o/Ynrh1J+6MOWO00fbHKYkAAgi4j4DlNF336Tk9RQABBBBAAAEEEDCJgGyo/OakuLk/j9RqLXEEOetKNrhZu2eVaqEsj3ryo0db1WnbuFqzUbOGq0yZUzP0meEtaz8qh6/LeqvIqAhZCTVoQn+5K6Gc+pUaFfQp/Pkv9w/emrLw25XB/5QpWvbc5bM7j8ZNipF4SukiZeSR0Ouhqlr9t1aFet8MmCYzYtQqs1MXT+hbKhEZE6kSMWkWHlL1y7ZB7Rs8Ib/Tl00ZO2eUZMpasxEz35NIkKT/2fq37o4sLuvyUZs6Fetny5Jt9Y6VOoim9/o5F3pG1dmxcZcEt+OR8Jn8Sq+FVx5fsW2pLKlTj8jfCfO/krk/kihVuPQLbeOGLPO9yU0hF08U8ikiexvpNW7jXpsqq/D040O6DS9VuEz5YpX0DkryItvHSNdje0I+WqqwcQaW7Y9TEgEEEHAfAcJD7jPW9BQBBBBAAAEEXEFATrZ2hW482IcPpr8lM2JUnsQjHn61mo5r6IKHTx+QTDmIXeXIt/1pb82WCS/q8r+5Idm/nvuZLi/xkZXBS3W1Kl8qkV9dRhIf9vxM7WkdeuP+js6SL5NrJDakDoaXg8kkPiWzY4wPxqbv3lU5Fq2VeT3TFk94vOGT/g81t3zE6rXEMq7cCJMIS2KlJHBWqlBptbG0bNJ0Pfxqzmy5Jy742lheGrN21ypjzkNl/Do2elLlhN2Ii4J5ez6wXs9YXtJi0qRGc5l4JbtTbzmwUZ0CJnGoH5d8p0p+3vcbvRt3/lwFVOah0wek5KHT+9Vlq7qPWhwMJ12TtWzqrvqbrDEyPmhjOvzeBDTve/tY2fggxRBAAAF3E2BxmbuNOP1FAAEEEEAAAQTMJTBl0fhlm/82tski2qJuvfL4G79/8Pf5K3Exmrw5fcoWrWB8Sram/uXfuN2IKhSvLFNglm2Nq3bwU+999fLEMkXKGcvL/JdJb0z3r95MZYZeu2S8O+blyTIpSeVULlFNJU5dDDGWyZMjn7q8dvOqMX/c3M9kd56RP79vzEwyLWuyuo1o32ZI464ftz996VRi5SsVj9tDWgqE3w4/fu6oCoHJNKhFn6zuEvCMxYOd/bt9N2imPnVe78VjUSz+pTrQTfK/+G2ETOY6eGrfW5MHqGJvdXu/vG8l/YgOD209uCk2M2PsYj35CblwQk/eUTnx/yZrjOI/nmSOtFyV0ducJ/kIBRBAAAH3FGD2kHuOO71GAAEEEEAAAQRMIfDjsskT5o+1aIocW1a9TE05S8s3X/H3f3hbRYs6+T8tc3kaVGms9pmW4977jn720QYdC+YptOvY9k371u89fn8vnhfaxi4xO3zqoKr50fodfXLma1qjpURSDpzal8nTq3SRCmWLPhAtOnXpfujnjSeHlit2P/ZUukhZVc/2I1uMS6VkcZbsrSPrvORX4iDq5Pg7kXd2HNlu0SNbLqWbat3WwZB97d8NaFy9adv6HauXqpHJK1NUdPStiPCzl09tP7p10YY/VW0S7fLJnX/1jn/UZdOarYrmL/besx+/9sSbMn3pXOjZYvmLVy5RNXvWnMa334mMW2x16coFY378dKvaj34z70txlva0eaex9FGVkdPKnmney1g+X+642UPq5PXyRSuquzK/6ZmRHSXQVqJQKWN5YzpZY2R80MZ0RFSEKqkDZDY+SDEEEEDA3QQID7nbiNNfBBBAAAEEEEDALAJj/xg1fekU1RrZcqhepYaF8haWDZjz3VusJLemL5uqgg67jgbLfJYn/Z+Zu3q2CqPIhjt6zx1jl6SSFrXaSM7N23Hnze85tkPWeWXMmLFs0fLyayys03r2kMwqsgh/1ChTWxUL2rdeNgDSj0iicL6iKm4ikaP6lRvLAe2fzR6u4lmN781LMpa3kpbJSrITdtwEnAwZZIGYxRoxi2c/7/eN7I19894h93tP7FQFZLmZtMSisL7Uu2ifuBC7hZCVHwl+DXjiLdkFXMro2JBE7j558SuLpwreO/TNw8NTbsmeTRLak9iQpCUeJ5OhWtdp17B6QN2KDfLm8LF4NlljZPGsLZe37+09ZH0xnS1VUQYBBBBwbQEWl7n2+NI7BBBAAAEEEHBBAT8/PxfolewTpGNDjzd6UoIdTzXtLgEgY2xIunnxynnV2f0hseEGmUD0zYAfyxWLm5+SoMPTj/RSU0V8C5RUBSYtHCdHbiVYWGc282ul0rITs8WezTJ7SGJGcjdwh+XhZUXyFVVPvTSmp5zs7v96zXlrf1M5z7d+SSVs/zvyhdESIUqyvHRfDqFXcS7f/HGntkk4Rk5tS/JZXSDkwnGdTizRtt5jMij6ruzH9P2bv+TImkvnqIQEpNTGz7LiT+VMfH26RIhUWoJl89fPeWfygOaD6rZ4s94r456fvPBrWQkoGydJgWSNkaowWX9PnDuiynt5eSfrQQojgAAC7ibA7CF3G3H6iwACCCCAAAIIpL/AtZtX9CbHPVv3MZ6NZdE4vSH0xSsX1S05TH3mu/NWbFty5nKInEEmi7kK+hQq4lNs9G+fqGk7MktFlez9aH81GUfWnb09+dVPXhyjtxMyvkU2+jkfekZWSE0fOkd2F9J77hjLfNhj1POfd21QtZExU9KdG3fTB95v3LtO3x36zEfq0HedY0tCdm6e+uYsacO/25eu371Kunzp6kW1PCp7lmw5s+WqWKJq42pN29R9TAew/MrXlZ2n1SwqObVNthmqW+nh+O+SLXhkl6KrN8Nklta2g0FSoE7FBIrFf/Dtbh9ERN7ZtH+9rLb7qNfn8af/qEe6NX9OBlRaoi7lYLIf3vptxj9Tp/09SQ2KypcTzWRtoFoeKBGlv0b+m9wxKlkoNk5n+0+pwuXUMWq5s1lGtWyvhJIIIICAOwhklD3w3KGf9BEBBBBAAAEEEHANATm5bPTo0cHy813ctAhn7NeRM4e6DG8jG/eMeOFL68d7/bnut4+mD5U+vtxxYJ92cVsjx+9ydEx0nZfidgvaPPGAjp4MntRfdolW5WXz5r7tX6tVvl6x/L6h10IPn9m/fs+awB3/qAiULJv6+9PV8WvWORJhSTC61PerZ+VcM12sac0WL7Z9WZbI6Zy0Tsim0V3/116/pXW99h0e7lyxeGXZtCjk4slthzav3fXvpn0bVIGRvcecDzsTfGjLB8+NzJ+7kH4q9YmT548XyFPQgujmresrgpfuPBK842iwxZlx8sYFI/4tXrCkHccofi82798w5e9v5JM2qs84/amIX8z8OWol5syZMwMCAszfWlqIAALOKEB4yBlHjTYjgAACCCCAgPsKuEZ4SMZPtk8uls9XppkkOZZyuNXlqxc7Ne6q9n5OsPzpi6favxf7tVkmsEwfMkeXkZjO+z+8uWLbUp2TWEIWQ816b0Fid63ky/SlX1fNOH72SKUSVWWTHVt6ZKW2lN2StVqDJ75snKeTWD3jXp1iPR6X2IOpz79955asEJStwQ+dOiDj8mTAMzXL1ZFqHTBGqW98utegwkP9+vUbNmxYujeGBiCAgEsKsLjMJYeVTiGAAAIIIIAAAmYXqFKyuo1NlCO0kiwZcvGYKlOjbC1jYZnM8kW/b2f8M23aogl6f2VjAUnLvKGmNVr07/C6Rb6Nl5m9M/do2dvGwmlUrGHVJr++v/CTWcP0LKH4L6pbqcGzjzyfXrEhaY/sGyXxIBUSMjbPAWNkfB1pBBBAAIEEBQgPJchCJgIIIIAAAggggIAzCZy8cEI196EyNS3aLQeWSfima9PnFgfNl1VOUvL6rWs+OX1KFipTqUSVhpX9C/oUsXjEGS/l8PhJb8w8fPrg30F/njh/TA6k9/TwKlageOnCZepWePihsrXMvLTKTcbIGT9XtBkBBNxHgPCQ+4w1PUUAAQQQQAABBFxW4OS9c7geKp3wsW4yx+fxRk/Jr8sS/Ncx2UD6tSfedtI+uskYOeno0GwEEHB5AQ62d/khpoMIIIAAAggg4GoCsi21q3Upmf2RfYvkcHSZKaOfO/bf+eWy+bRrTAXS/SKBAAIIIICAYwQIDznGmbcggAACCCCAAAII2Edgy4GNz458fNKCcc992ulAyF5V6YGTeyRRLd7KMvu8kloQSG+BepUapncTeD8CCLi4AOEhFx9guocAAggggAACCLiYQNCB9apHclBX39Hddx/bsTJ42eVrlyWzRIFSLtZZuoMAAggggIBjBNh7yDHOvAUBBBBAAAEEEEDAPgKtarefsvBbVZccRiZziHS9svOOTpNAAAEEEEAAAdsFmD1kuxUlEUAAAQQQQACB9BcICAhI/0akawskBvT9W7+UKlw6fita1GobP5McBBBAAAEEEEhSgPBQkkQUQAABBBBAAAEEEDCXgF/5ur9/uOS5li8am9W/4+vZsmQ35pBGAAEEEEAAARsFCA/ZCEUxBBBAAAEEEEAAARMJeHl6DXry3Wlvzq5WuoY0q2frPr0ffdVE7aMpCCCAAAIIOJUAew851XDRWAQQQAABBBBAAAGDQK0K9WYMnRt++ybzhgwqJBFAAAEEEEi2ALOHkk3GAwgggAACCCCAAAKmEiA2ZKrhoDEIIIAAAs4oQHjIGUeNNiOAAAIIIIAAAggggAACCCCAAAJ2EyA8ZDdKKkIAAQQQQAABBBBAAAEEEEAAAQScUYDwkDOOGm1GAAEEEEAAAQQQQAABBBBAAAEE7CZAeMhulFSEAAIIIIAAAggggAACCCCAAAIIOKMA4SFnHDXajAACCCCAAAIIIIAAAggggAACCNhNgPCQ3SipCAEEEEAAAQQQQAABBBBAAAEEEHBGAcJDzjhqtBkBBBBAAAEEEEAAAQQQQAABBBCwmwDhIbtRUhECCCCAAAIIIIAAAggggAACCCDgjAKEh5xx1GgzAggggAACCCCAAAIIIIAAAgggYDcBwkN2o6QiBBBAAAEEEEAAAQQQQAABBBBAwBkFCA8546jRZgQQQAABBBBAAAEEEEAAAQQQQMBuAoSH7EZJRQgggAACCCCAgGME/Pz8HPMi3oIAAggggAACbiJAeMhNBppuIoAAAggggAACCCCAgLMKNPVv5qxNp90IIOAkAoSHnGSgaCYCCCCAAAIIIIAAAggggAACCCCQNgKEh9LGlVoRQAABBBBAAAEEEEAAAQQQQAABJxHwcpJ20kwEEEAAAQQcJxAYGJjYywICAhK7RT4CCCCAAAIIIIAAAk4qQHjISQeOZiPwgID6Kjt69GjJDQ4OfuBeci5s3Ou0Xr16Ntbq7+9vY0krxVzv27iV0IMVB7vcWrNmjV3qSbCSoKCgBPPtmJmaj3fKmmH8R6HfrjLVPwT5kLveRzRlVjyFAAIIIIAAAggg4LwChIecd+xoubsLWISE5PvqxVt3S1Zr0Khs/dI1GyudsrVtmuZwZGuiEyVUPce2rzVy775svLJMF8wWm7M5aKP8XRS4Sd8ukDWjThsT+iu3MZN0mgoYQx5WXpSyobFSecoqVC30rVRTEupv/DbLJz9+psrR/xwSLJDkv5Ei2TMU/u8jXbtQXAUjRoyQlMTCJk+eLAn1t1+/fsOGDUvwFWQigAACCCCAAAIIIGB+gYx37941fytpIQIIGAUkMCQTheSbtnwPL1W9fhm/xuVrB5y9aSziZGn5Bm7lR305tyiwZo1lSKtwtqT/22z9unWqnoJZky5sfKMD5sUYX5fctA67qNCMBAqTW4OV8lYiL1aeSuxWKoM1iVXrmHwVKtJxInmpChWpCJFcSpBI/hIncsxwuPlbOnToIP/wg7874uYOdB8B9xHI5ONZtUupmTNnMmXVfQadniLgYAHCQw4G53UIpFxAR4WkCplD0bT7m0lOfEj5y3gSAQQSF0gwTqSnEUmCyUSJ43HHPgKEh+zjSC0IOJWAX9+yhIecasRoLAJOJsDiMicbMJrrngIEhtxz3Om1aQVksp78Bl/M4Fcgg5pMJDOG5EcmE6nYkAoVSY5pu0DDnF1Atr7S0wadvS+0HwEEEEAAAQTMIEB4yAyjQBsQsCagvnBKCWYMWWNyznthZ0/M+qCHd5Zsjw8eU7BUJVN1Ijoqctb7z0kLW/YeVrlxO1O1zTyNkQiRRZBI2qYiRJKQ+R0LFiwwT2tpCQIIIIAAAggggAACiQl4JHaDfAQQMIOAfL1U0xAadXnpxTF/sZrMDINixzYc3LT80qmjZw/v/nv8u3as1i5VXTpx6Oj2dWHnT/028qXoyAi71OmqlUiEaOruDFvPx/ZPZgzJzH+1WZXM7yhevLjan8hV+06/EEAAAQQQQAABBFxDgPCQa4wjvXBBAVlQJl8s1doBiQ21ePE9F+yk23fJw8NTGdy8ZvU0uPSA8vCKa5u8/Hb49fRogpO9UweJZNNQNWlIgkSyCZFEeOWfs5N1huYigAACCCCAAAIIuJkA4SE3G3C66yQC8mWye/fucgqVLCjrPmImsSEnGbdkNzOjR9x/Cd++eS3ZD6fxA7pt8p6I8Btp/DbXqV6CRHoakeqVRIjkqEHX6SE9QQABBBBAAAEEEHBFAcJDrjiq9Mn5BSQ2JJ2QqUMsKHP+wbSpB3fCb9pULp0K3bnJ7KFk0OsIkcwhUqvM5GGWmCVDkKI2CPj7+9tQiiIIIIAAAggggICtAoSHbJWiHAIOE5CvkTJvSF4n84Yc9lJelC4CUff29Im8cytdGmDlpVER9/cbirht6uiVlV6k1y2JEC06FvvywYMHy+Iy+StxIpaYpddw8F4EEEAAAQQQQACBJAUIDyVJRAEEHCqgpxjIfkNsRO1Q+vR4WUx0lN1fG3HzetSd26mv1rgddcZ7eySlvlr3qUFOvpdVZrIPkVpcJhEiNSvQfQToKQIIIICA3QXk/6zYvU4qRAABBJQA4SE+CQiYSEBiQ+qcsmxl67PfUOoHJirizomdGy+FHEp9VWlUQ1REXBzHO3PW1L8i8nb43M8HfNqlysjHyy+eMCz8Wlhq6hQ9/XimLNl1moTtAmqVmZxlJkeYrVmzRmYF6viv7ZVQEgEEEEAAAQQQQAABBwh4OeAdvAIBBJIlsChwk2w5lKxH0rHw3eiYjJ4mDTT/8FanMwd3Cs6AaWt8ipZKR6XEXh15LwSTOZsd4i87lv+2a+Wf6l1Bf02X31qtu5Wu0ci3cu08hYsn1obE8mOiIvWtTFmz6TSJZAlIhEh+JEIkBxHKgffsUZ0sPQojgAACCCCAAAIIOEyA8JDDqHkRAkkLqKlDJas1SLpo+pW4eOLA+aP7vDNnyZQ1x4ndGwNnfd3qxfce7vJS+rUo0TdfPBE3b2jDH5PaDRiVaLn0u6FDMHaZnhP/+LNtS3+RX+lfjrz5y9YKKFurSdk6zbLlymtLj6Mi788e8s5CeMgWs4TLSISocI4MMnVIJhBJCdmBiKUBCUuRiwACCCCAAAIIIJB+AoSH0s+eNyPwoIBadSJbDplzWVl0VOSGORO3LZkddv7Ugw3PsGzayLodnvfKlNki3zyXp/YFm6cxxpbo/X3sEh4qVrGWsXJj+kbYpR0r/pBfySxTs1G1gA6VGrXLmjO3sYxFWm+bLfmZs9phcpNF/W51uf1C7B7VsveQmkBEeMitRp/OIoAAAggggAACTiFg0iUhTmFHIxGwr4CaOmTfOu1V27nDuye/0nrF9C/ix4bUKw5vWWmvd6VFPaY9eEuHYLJkz5n6jpes/nBV//ZJ1nN0+7oFX7/z+VPVlkz64EbYf2ufEnom5t6panLTK5MdtkZK6CXukifbVJerE6AmEAUHmzRY6S6D4Sr9JMjoKiNJPxBAAAEEEDCLAOEhs4wE7XBzATV1yLdSTRNOHTq9f9v3b3a6eNLaBs+BM8fcjblrtkHUp8XfunHFbG1T7dHbP3tmtsPcKw8vzy7vTrSIEMmm14nte71p/g+jn6n1z7SRt64l4KP3RZKmZvTIaE5AJ2qVmkAkUWAJEnHCvRMNHE1FAAEEEEAAAQTcRIDFZW4y0HTTOQRMuOvQmYM7pg/ppuMs4likXLVytQNiYmLW/T5Rs547tnf/+r8rN26nc0yVuHX96j/ff+Lh4XH51NErF0/fCL0YEx2ZJXsu2VKn0sOtmjzzRnq1VoeHMme1w+wh1QvZFkp3Rwar95iFGT08zhzcvn/j0gMblsUP862bM2nvmoU9Rv1msX11dFSEqsf6GjT9LhLWBWQCkV+l2AlEcoqZbFDN1A/rXNxFAAEEEEAAAQQQcLAA4SEHg/M6BBIWCAoKkhtmmzp088ql2cN7GWND7V4ZUad9T9WHklXrzRr+vO7PyhmjTRIekmZfOLb/zOHYM8v0jzGYpTJlOx5JnD28u1bb7jnyFtAlHZm4GxOtXpfZfkeDRdy+pbvg33WATCmSy2KV/OT3kV5Drl44fXz7umM71x/ZFqgE5K6sGZw2qEPPz37LX7y8fjbmXtu8M7MvtVZJVeJceNwORBIkSlVFPIzAPYH1e1Y3rNrk3hX/iQACri/A+QauP8b0EIH0EyA8lH72vBkBg4BsRyKbUhsyTJGUZUc6giAN6jLkm6oBHXXLytdv0eCJ3hvnTVU5Mi0l8nZ4eh1xdXTr6hU/fSbTgq5dOqtbaEsiV/4imdLvWC49QyeT/fZ+vnjyoO54WT9/nVaJ3AWL1Wj1lPzKYsDgJT8v+e5/KvwnA/394Cd6fT6nYKlKqqRsRq4SmbPlsKiEy5QJyASiHDfvSmyI7YdSBshTCCCAAAIIIIAAAmknwN5DaWdLzQjYKmDOjUhkWdn2f+boPrTu874xNqTyKzdsowtIIv7CJePdtEufO7JnxrBnzxzcaWNsqFCpSrVad2vVe9izH/348qQViYVmrl0+d3p/sASepP7wa2Fp0f474TdUtfYKq0lMR49CgRLlMyW+47VsJ1Tr0e79Jy73KVJStUGW4C2dPFx3M+JWXNsS89ElSdgukLNyU1XYnP/qbe8IJRFAAAEEEEAAAQRcTIDZQy42oHTHKQXWrFkj7TbbyrI1s8dpzTqPPtugU199qRPFKtWSbY/16rPrMnOnQg19N7GEbLgji6rsFRCRt5w9tCOxd+n8UtXrl6rRsESVer6VayX56iNbVi2Z/OGlU0f145LIkTf/Q807NezSP3ue/MZ8i3Syenfn1k31uL1Ojr908rBuj2/FmjqdYEJW4R3a9I9e4CZl5FCzu9ExGT1j/5cDHbqyV9sSbINdMmXa2oENSy+fPhpx51bu/EWLlKtevGpdu9Rs90pkAhE/CCCAAAIIIIAAAgiYUIDwkAkHhSa5o4CcWWaqbssONfs3LlNNkgBQs57vJNg8Ty/v8nWb7127SN318Ez0v1JioqIPbFyydcmsk7s3q3CSVJu7QJFKDdv4termU6x0gvXHz5QlUf/+9Nn+dYtlhx3fSrUeHzzGO0vWbLl84pc0xq3k7jP/myEl4xeLn3Ny98aZ7z8XP1+WX63/47vNC2d0eP3zas0eNxZIce/u3Lyu6smU1T4LuM4f3aMbljW3j0x6kqVhMkySKZODQk8fkfjR5TNHL4ccOXtkt2w5pAurRJVGbVVsSC51eChTluwWxeJfSlDpxtWL0RF3suXMa2XKUvwH4+dcOL5/8bfDrl48ndHDs3XfDyo0aBW/jM6R2VLBS2b9+9MX0judKYn8vmU6vT2+SPmHjJkmST/z0qDgfs+xO7VJhsPZmzFxwVj2HnL2QaT9CNgoIHuN2ViSYggggEDKBBL9Lpey6ngKAQRSICD7UpvtzLLdgfN1Rxo88WK2XHn1pUWifsdeJ/dsktCJLGWS6TkWd9WlBFwWjn9XL3pSmRIkkuk5a3+bIL/FK9eq3/EFi8Vr8tSfX74RHR3d+Mn+dTvE7YG9eMK7mxfNVDVIWOrOrevP/m9mxYZtGj/1stQjISHZbadqk/YV6rWQIMWPgzud2LtZFfbytunkeFlHNnNYD/VIgn+l2X98PuDw1lXtXv1EzUJKWe9U5bduxJ0ob0sIJsH2WGRK0EfnrJ8zWX7lUo4ei4qI0JO8dAGLhKwy6zDwS515+17oKnPiK9SksMw/WjVrjKzs0w9KQmZaPdypr8y0MmbGT8/7/LV965cUq/BQ5yETcvgUlAIXTxz48e0uOtYz+6MXe332e8mHGsR/VnLOHtwxZ9QroWdPxL8rH60f3urSfcSMEtXqx7+bvjl6fVn6NoO3u4CA7GMVeSXGBTpCFxBAwBaBoP3rpRgHX9piRRkEEEiZAOGhlLnxFAIuLhCyJ/YkNfVTvekDM2XuZcf9Z4lqDQbPCr4eeiFHngKynY3FXZm9snTShztXzrPIt7gM2bdN/bbu86GevbJ92W9qesvy7z9V4aFNf07VsSFVw5Gtq7cv/9WvdbdHnh/6cOeXsmbPrR+XAjIpSYeHIiNu2bKHzsqfPrcIo+Qt5CtTe27duGrc2GjHij8kkNR56Ldrf/kmZb1T7ZeojUpkzm6f2UPnDOEhVbP81dEWnRM/Ubdd95a93zcuu4u8HbcOKrHFZdGREXM+6a9nmRnrlHDh8mmfFK9cx8oiL5mhpuiO79q0b+0iGeKIm9dnDnvOorXzv3rj1amrPf6bAGV8hfj/8PaTFoNlLCC35n81eMD3a42ZZkjHnnDPyWVmGAmXaMPuY0kvrXWJjtIJBBBAAAEEEEhzAcJDaU7MCxBIUkCOMereeXCSxRxZ4PTBuK8ccrBXgZIVk3x1zv+mflgUu3zqqByXbvFtX8qoOT4eHh7Hdm0w3t00//sr507IRBIVpMh078As+Z4vkQgJfCyZ/JHFK+Ry88LpEh6SRPwpTlly5tblr18+n8+3jL5MMBF29uTBoH/1rQr1mj828Et95v2lkEPbl/++7veJqsD54/unDnws4la4Lq8SNvZOFdbRjcz2Wlx2fL9FexK7/G9kyxcsWbH0Qw1L12zslTmLRcmIe/sieSd0qprcnf1hT4nsWDxlvPz+zU4NO/eVqJMxU6eN6+muXjory9PmfTnQGINTJSVEeDR4Tbm6zfWDkpBJRj8N7ar1JKd0jYfrtOsh+yjt/HeuHkSZWCSjlr94eeOzZkhfvHW3QFbLWKoZGkYbnEtg8ODB3bt352x75xo1WotAagT4XxdSo8ezCCCQpADhoSSJKICAWwtkz5Mvxf2XVVfG6I+qR77Gd3l3sg7lnD20M/Dnrw5sWqHuSmLGu08//+WfMhEph2EH6NMHtiW25uvs4d3XQ8/n9CkUv53GaS+3b1yNX8AiZ+vimTpHlkc9/uY4WZalcyTK0OKFd+u267F61tiTezeHnQ+JHxuyvXdSrWxirSs3TtvRmclN3Lx6OT64qkSW7xUpW02mU/kUKZW3qPwtEX8+jsXrZJtnlZMpSzaLW/KimUOfOXdsr86v0rCNbCMl9ecu6Js9d/5PO1dSsRvZralig1YJrvAy2kr4b8HYwQlORJJXHNi4zBgeksIWk4wee21UrbbPqsbIEsW/xry5bdmv6vL4jvUmDA9pNxIIpF5A1puw/VDqGakBAfMLbD0Y1KhVwqutzd94WogAAk4hQHjIKYaJRrq+QNnaAabqZJbsuWR9kDRJzoRKccNkU2SLZ+UEtDb9P1Y7Jatbsnlwt+E/SiBJoj8qoCCrzHavmle9eaebhrPkf3jrSV2VTM95ZvgPy6aNkMCQyjyyeVXN1l11gfuJjMmboHF6/zb97CM9hxjjFzo/dyHfx96I3aBn7+oFv3/6is6XRLJ6J+Ujb8fFXyRtnEpjrDNZ6cshh3R5afzDT/QpVaORzBLK6VPYw8tT37IxoZuXJVtO4yMyzefXj17QsSF5UbcPphkDQKGnjxnn9az86Yuen88x1qDSenaSXG78c5qxQK1WXYtXqTt/7JsqU2YDtTPc3jR/mnGSUZe3x1c17BR+dGvgrsAFuniegr46bZ6E7DW2bs4k87SHljipgNqCRL4xOmn7aTYCCCRLQBaTNspAeChZZhRGAIHkCXgkrzilEUDAPQRkIyHVUdniNyyhrX9tYShYuqqxmF/Lp9oNGGWMDem7soFRt/en6ss9q2O/3odfuaxzdEJiQy98ObdUzUatDEuWws6f1AWMiajIuJ19JPPu3aQ3cA09e7+eqgEdjFXFT6eyd1KhbIekq7VlXyRdOLHEheMH9K1m3Qf7P/168Sp1chcsloLYkNQTpWcPPbjwLeiv7yWEp1/03MhZxtiQ5O/fsETflYQsQJMpPMYclQ6/msD4yi35nLR//QuJ95W6t6u0BIPkbDj1lExcWjVzjLG2OZ8P+LZPwKLxQ5ZPGyGbkc8Y1t0YnCpaqZaxsEnSsprPJC2hGc4uICtN5BtjJp9kx3+dveO0HwF3Exj7xyjpsr+/v7t1nP4igIAjBQgPOVKbdyHgNAI1WtyfrbNk0gfGiR629yHjg5N32r78sZVny9RuomfrnD0Su2rp5tXY6UsWP70+/71wuWqSWaJqfQkVqbs3ryRQUm7FREfpx2OiI3VaJWRi1MGNhr6f/wAAQABJREFUy87dm4Ikmbeux50jJmkPryQmV6ayd/IKPT0n9nWedvh2d8VwUH3BUpWl2tT83A6/rh63aNvWxbN0tU8MHmNxeLys/Nqy8CddQCWWTf34bsxdi8ybCYX/qvq3f+z1L9Qe5xUatNSPhF+LiyWtmT3OGP1RBSSIueXvn+WYNr0Tucpv3mOwXsaoqyKBgCsJyPZD0p0xcz51pU7RFwQQSEyAY8sSkyEfAQTsIkB4yC6MVIKAqwlUa/a4DtbI0p5J/VvIZsCp6WSRctWsb69zYuf9jYry/rcg6GaY5ewS2WKmaIUaqhkyI6ZU9foqfeXCqQTbZlwVdfvGNYsyssmRnJs+eUBbOXZNbsn8FGPcIcFZThY16MsU9E6e9fC8H4G6ZVhJp6tNbuLa5XP6kWx5fHQ6ZQnvzHFbDoVfC9U1yFF0F0/GLWGr36HXQy266FsqsXr2WHXenDFflgHu+vcPY46kb165aJHjU6Rkx0Gj9dlzJavfn0J//dJZKRwdFbnTUE91w5oyi6rkssYjnRs++cDqv/hlyEHA2QXi1pcdYH2Zs48k7UcgCYHpS6f069cviULcRgABBFInQHgodX48jYCLCnhlytzqxfd15+QL/5TXHzuw/oFFQ/pu6hMykWfFj/f/1++CpStKnVcvnTbWXLXJY3r7YZWfv0TciVRn7p2zZiwv6dwFiumc8KthOi0JiaToFVLHtq2WnKjI28YCMgvGeJmadIK9i32jYXFZYiutkvVeFUNRj8RE3p85laxKdOHIO3HbTt0wTM4KN0zpyl+8nC6sEpv/+nH17PEWmepy+fcj5dx6460b/0XljDlPDZtijCEa6z/137ZQp/Zu1XtvV2ncrtPb4/tPXC4TjoyVSLpw6SodB375+JtjkxXjs6gkTS/NttdYmnaWytNaQL4xyvGXW86uTesXUT8CCKSXgFpZNmzYsPRqAO9FAAE3ESA85CYDTTfNKxAYGGjOxsnmL7I2R7dNZtb88nGf7wa03bJw+h3DTBxZNCSzb2SJlkwvuvPg9/9c+Qvr9V8yf2TnP3N0bcZEyN4tE196RAdrZNZSk2cHycIrHQiQwpL52GufGZ+SdIF74QkpeSnkiMVducyZr7DOtAg2nT2wXd/KkiOXpCMjHggPGTc/1iWNidT0TtXj4emtK7wRZjmVRt+yPWEUu3PrgViM7ZXELxluCA/JnCddYNP872Uykbq8cu7kn18O/HvC/ZCizKjqMuQbXVh2Op86qOPVC/dDfqHnTuq7kmj54ruFyjywIE72Y5Lz41SZE7tjJ0cYt8Gq9HArySlYqlKXdye+O+9A7zHzO705tueoXwf9vLXfhKUJb1Wu6uIvAq4loL4xTvhzrGt1i94ggMB9AaYO3bcghQACaSlwf2lDWr6FuhFAwCkF/J8eGBUZuXr2ON16ifIsOjxs0bfDytZuEhMVefXCmdAHN66WFT0ya0OVl5kgtds+ow+lmjf6jTOHdpSu0ahAqUoeHp4SKTi5Z9Oe1QvPH9+v65fEk0Mn5chbQMINxszWvT/InD2nMUfSvlXq6Bw5+yx/8bL6UiVy5i+kcyQIpdMyjWXpdx/pS98qdSVt3KhILq+eP5W3SEldJn4iNb1TtWXLdX/914WTB+O/Irk5t25c1Y+c3h9ssWO0vmVjIkfegiredN6wrtCnSCkJ2ahT7WTHn4kvNfetWOta6LkzB3caqy1Qonz3kbNk359rF88umzZS3ZJVaZNfbd3jk1/U7lHXL91fCifLyho83ttYg0qXqFJ377rFkj6xe6P8Db9+f5mbUUzGolilWvIbvwZyEHAHAdmgWk0gqlOEXc/dYcDpo3sJqKlD7tVneosAAukkQHgoneB5LQJOItCsx1sV6j2yfMoIi01/j2xdnWAPzh3ZY8yv3/FF2clY7+mzacGP8mssYEzLFCF9RLpXpiz6lqwVesiwVbbOz+9bXh5RIYy9axdZLD2TYtnzFNSFj2xbs37OpDJ+/qf2bdkwd4reH8ev5ZNq9+KYqAdWY53cu1nOR9OPJ5hIce9UbVly5NbVHtuxTqdTnDBura1WY6W4Knkwe24fNaNJQj+yN7k6W022fHq4U7/l9yI+Eifav3GZxVuKVaz59Ec/KtWHu7wkO0MF/TVdlZHBks2emj03uGGX/mrSlspv2XuYh9f9uVS6wtI1GqrwkLzo9P5t+kA9KbB92a9yOluCT+nHSSDgJgKyQXX37t1lAtHM9wIiQqPdpNd0EwF3EJDYkEwdkhAwK8vcYbjpIwLpLnB/mUC6N4UGIICAOQVkUkav0XN7jJwlX/utt1CWkjV5eoCxTJ7CxTu/fX/ykfGWMS0PNuzc96UJy/WElxw+Bbu+P0WVadV7mDrKyviIpCWzSbfXVGbo6WMWd+VSgke58hfR+RLUmPxqG5n6ZJzxFPDsIFUgU5bsuqQkjPEpY74xneLeqUqk/bICS6UlBJPYDkrGN1pPyxwcXSBPkRI6nbJEUcNwb1k0Q1dS//EXrXwSZL7YC1/OzZ47ny7f5qWPaz64g/XKGaM3/DGpxQvvqaPr5W+lhm10eWOievPOen1Z6JnjRSvGbUwuZSRgNO/LgfEPRDM+ThoBNxGQDapnzpwpE4jGLf+UQ+7dZNDpppsISGxIerpgwQI36S/dRACB9BXwHD58ePq2gLcj4OYCJ06cmDt3btPucUEK02rISqtabZ6RmUR5C/tmuBsTFXE74nbc1sWFSlWqWL9Foydf6jDwy8Jl4+IduiOywbBfq66eXl4R4Tfin0AvgYZm3d/o9Na48vUeyZztgeVj8mDFeo/UfayXb5XaujaLRNHyNY9sW3X98jn/bq8W/2+NmEUBb2/vQ5tXWmTqy27vTylWuZa69M6c5dqF0+eOxs1+evTlEbaciZ7i3sW9NFOW/RuWqnT+YqWLG5bL6Ubanrgeek7t0SOPdHzjK1vab6XyPAV9g/76URXw9PSs3ryTSsvCwBotnvTy9j62Y73xcVlv+OTQCbUf7Z7Rw9OYnzFjxgr1WoZfvXjm0C6dL7PS5BNVtUn7YhVqNO3xpofnA4/oYrJFegHfcrtWzZcwX8s+7+cuWOxmmNSzUxW4cOLAreuhZWo2TuxxKRZ29uS5w7vO7N+WKWs243Qt/Yr0SgT+PGbQILP/q08vHN6bAoFSpUqFh4dPnjy5TiO/It6pjQ6noAE8ggACdheQqUM7jmyT4K/8A7d75VSIAAIIxBfIePfu3fi55CCAgMMEZGtqWRTw4eIQh73RXi+SiRsR4de9smS1/XwoWaMkc0Bkl5+7MTGZsmWXk8XUkqXUNEmOGJMKjSdeGWuTo9C/7d1ELyXTt+TcqxYvDLXYXUiqWjR+yO7VCxs/1b/JM2/owjYmUta71bPGnNy7JZdPYf+nX8+buik/0tkFXw2+EXbBv9trpWo0tLHZVortXbNw179zPTy96rTrUdrPck+TqDu3JUBz+/qVnAWK5CtaVtadWalKbp0/svdgkKxEy1jF/7F8vmWsFzbelQ2wdahLlqeNf7GRWlGoyuQt5Nuy9/tyFpj+LEVF3AnZHXRwy78HNy7XM8WKV671wlfzjdWmb/qjtsVDQpzvX336ovH2JAVGjBghEaIpH01nE6IkrSiAgMkF1LIyOZqQZWUmHymah4ArCRAecqXRpC9OKeC84SFn4Zb5Iwu+GnR89yZpsCzmqtniyWrNntDhBmfpBe3UAsd3rJ/1YS+9oZXOlzhRtjz55Zy1+NFAKSNz3F6auFwXTvcE4aF0HwJXbUCHDh2ka798OS/8CJsQueog0y/XF3ju0067j+0gNuT6I00PETCZAFtTm2xAaA4CCNhbQKbk9PxijswouRsTndgkI3u/k/rSUEAmRvWfuHzWBz3k6DTjayQqlGBgSMrI5lYBzyZ7OpixctIIOIuA7FEic4gqPlKKOUTOMmS0EwELAWJDFiBcIoCAwwQIDzmMmhchgEB6CsguNun5et5tVwFZFdh3/OKN877bMG+qcaFZ/JeUreVf1q9JFf/2uQv5xr9LDgIuKaCWovT5sGfvbv3e6DKUs8xccpTplEsKrN+zeuKCsd55PGS/Idly3iX7SKcQQMDMAoSHzDw6tA0BBBBAIGEBmQjm//TAhl1e3vXvvNMHgy+FHA49ezImOjJnvsI+RUvnK1a6VPWHS1Sr5+mdKeHnTZArC0v5//5NMA6u2QQVIZJ9iDxzZxj01FAWmrnmMNMrFxJQgSFZUCZn2HNOmQsNLF1BwMkECA852YDRXAQQQAABLSDRn5qtu8qvziGBAAJKQCJE/v7+cvSBBIl6tu4jmQM7DwEHAQTMJmAMDDFpyGyjQ3sQcDcBwkPuNuL0FwEEEEAAAQTcQkCmp8kBeeo4M+nw9KVTJE5EkMgtxp5Oml5AokJB+9dvPRikZgwRGDL9iNFABNxCgJPL3GKY6aSZBTi5zMyjQ9sQSCMBObmMLwNpZEu1CQroIJHcVZOJ6lVq2LBqkwQLO3VmJh/P1LTfK29qnk7DZ6PC7Fk5O1LZUzM5dUlUSIrLBkMSFZKEnE0ms/xYaJwcQsoigEAaCjB7KA1xqRoBBBBAAAEEEDCDgKw1kx8VJJJpRNIk9dcxoaIEQzYJBmK883lY4fLOl9HKXW4lRyAlQbTIy3dtf0Xk5RjbC9sS/HLSkJaOB4mGmigkiSEfvyV/iQrZ/gmhJAIIOEaA8JBjnHkLAggggAACCCCQzgI6SCTtCAoKCg4ONoaKVOOqla4hidoV6tnYVu+8lgEd2Q87ZT/SpJQ9aM6nhNecDbNjq2QfZTvWZpeqIq9YC0s1qN8gMsxagdS3QdaLqUrU/CBFJIeRDR48WPIJCaVemBoQQCDtBAgPpZ0tNSOAAAIIIIAAAqYTkCCRbpMscF6zZo2+tJJINHZzwcpDKbzlDoGVFNKY7DG7j1Tq400SiLGCtPWAnUOQ9epZBlIblWog68WkDUSCrAwEtxBAwJwChIfMOS60CgEEEEAAAQQQSHMB+QbLl9g0V+YFCCCAAAIIOIOAtfi6M7SfNiKAAAIIIIAAAggggAACCCCAAAIIpEqA8FCq+HgYAQQQQAABBBBAAAEEEEAAAQQQcHYBwkPOPoK0HwEEEEAAAQQQQAABBBBAAAEEEEiVAOGhVPHxMAKpF7BxT9DUv4gaEEAAAQQQQAABBBBAAAEEEEhQgPBQgixkIoAAAggggAACCCCAAAIIIIAAAu4iQHjIXUaafiKAAAIImEcg9Yc3m6cvtAQBBBBAAAEEEEDABQQID7nAINIFBBBAAAEEEEAAAQQQQAABBBBAIOUChIdSbseTCCCAAAIIIIAAAggggAACCCCAgAsIEB5ygUGkCwgggAACCCCAAAIIIIAAAggggEDKBQgPpdyOJxFAAAEEEEAAAQQQQAABBBBAAAEXECA85AKDSBdcQeDI1kBX6AZ9QAABBBBAAAEEEEAAAQQQcEIBwkNOOGg0GQEEEEAAAQQQQAABBBBAAAEEELCfAOEh+1lSEwIIIIAAAggggAAC9hYYMWJE8eLFAwOZaGxvWepDAAEEEDAIeBnSJBFAAAEEEEAAAQQQQMAsAhIYmjx5smpNQECAWZpFOxBAAAEEXFGA2UOuOKr0CQEEEEDA9AJr1qwxfRtpIAIIpJuAmjGkY0N+fn4dOnRgAlG6jQcvRgABBNxAgPCQGwwyXUQAAQQQQAABBBBwEgGJAUkkSAJDEhLq16+ftFoSwcHB9erVYwKRk4whzUQAAQScUoDFZU45bDQagTQVOJq6Y9SObE/tnIiTu4PStIOq8lP7gx3wFjO/wreSX9o1r0S1enapvGxN/xTXU6Y2CzFSjMeDCCCQDgISGBo9erREgiQeNHPmTGlB9+7dJSF/JWfYsGHp0CZeiQACCCDgNgKEh9xmqOmo2wuooI/EblTwxYTBEYtohZX4Qvn6lWU8vTKHpWZUPTM3Vo+XrROXsLE2r8yhNpY0Z7ED6/amdcMObTyQIcOxo1sO2vKiE7uOWym2fk7cphtWyiTrlvqYqU9Xyxf5rpUsPAojgEAaCqhthlRgSM0SkjlEMntILURdsGBBGr6bqhFAAAEEEJCvVyAggIBrC0hUaOXM0cZgkHw99vQO6/nFWAmvVGhURbqfgniHjSGG/8IENgH/F0p4INxz+sDSBJ+UaML6OQneSTSzZPVSid6LvfGXlbtl6lSwctfpbtkYskmsX9ZDOfGfSko+9gkpYx25fIOK8WvWORX/+wzrSysJ/aFV0auVPyxTsaeGXWLXbjgyVFQku5VmcgsBBNxOQE8akolCevmYRItkNZm/v79MHVJLzNzOhQ4jgAACCDhWgPCQY715GwIOFFCBIf1C+RLetMfAzLmOGL5OF86QIYUTYQyV6DckkLCxWAJP2i9LBwWSW6Xtsa3k1pyO5VUgxnrAxXrzzDCm1luY2F3dcpVoP/iJqDs+S76ZFh1xZfXPv0qoSOJEjgwSJdZO8hFAwK0EZIqQrCaTAJBxfpDEhgRBVpPJefYq4VYmdBYBBBBAIF0ECA+lCzsvRSBtBZZPGyHfdWWWkKygkaVkMleo7+QB974bx04Xcqufex1PdqdT/GCy38QD6SQg8+YkSCQv7zi06cLR8w5tCPqobXEJEsmGR+xblE5jwmsRcCMBmTSkNhUyThqS/ktsSPalDgkJkciRXDJ1yI0+E3QVAQQQSFcBwkPpys/LEbC3gJoxJFEh+YorgSFZn/XoG48R5rA3M/W5oICKE0mQ6M71DDOGdWcmkQuOMV1CwDQCajWZNMciMCQ5cktiQ5KvFpfJxCJZX2aahtMQBBBAAAFXFuBge1ceXfrmbgIyaUi+1papXcEr8xWZPVT+4QKvzX6H2JC7fQzob2oEJEjUeXjNZs+3kn9B097ooDZ0T02FPIsAAghYCEjcRyYNyb5CsppM7zSky6idhmQ76qCguEM845fRhUkggAACCCBgRwFmD9kRk6oQSE8B+Sorr39x3IdHgzfInruG1WTp2SrejYAzCkiQqIzfw0snLJB463MjZrLQzBkHkTYjYEIBK5OGVGvVUWWS1ovLWFlmwnGkSQgggICrCjB7yFVHln65l4DEhmTS0ICfh0hsSI6mGr17IpOG3OsTQG/tLVCleWH5ByVLzCRCJPPy7F099SGAgNsJWJ80JBxqNZksJZPYkIoKycoy2Z3a7aToMAIIIIBAOgkweyid4HktAvYTkO+uso6s/WDZW3ea1CoLyuxXNzUh4L4CsnG1LDQ7faCULDQTBQ41c9+PAj1HINUCMm9IFovF32lIVyyxISkgy83UBCKJCkkOU4e0DwkEEEAAAQcIMHvIAci8AoE0FJDYkOw/LWthZEtdmTekttdNw/dRNQJuJiDx1pLVYyNE7EPkZiNPdxGwp4DsH5TgTkPqHRIJkhlDUkBNIFIzhvTeQ/ZsB3UhgAACCCCQuADhocRtuIOAQwRS8///SWxIvrXK19cD6/bKfkPMG3LIiKXJS84dOTvno1nbF29Jk9qTWenNKzf/Hjt/yfi/kvmcyxZ/+cfPpG8rZ4522R7SMQQQSD8BdVSZTBSS2JC0QsWGJJOVZek3JrwZAQQQcFMBFpe56cDTbdcQkNiQHLEkffmu33iVcJZ+Rd6JvBxy8Wbo9ZDdISF7T5w7eNori3fFhlWqNq9e8qEyztILO7Zz2beLdizbuuWvTZWbVs+cNbMda05BVTuWbF0xdYk8WKlJ1VI13HE4LNBklZls+j7ttY8kIMsSMwscLhFAIJUCo0ePVovI1HbUqjY5uYyVZamE5XEEEEAAgeQKEB5KrhjlETCLgHxTlZCQWlamEmZpWVLtuBF2Y9pL357cc9yi4Km9JyUqUaJqqa6f9ChctojFXTtehuw+EXr6co3WtexYZyqrio6Mkhoib0ec2R9S2q9cKmtL5eMx0TGqhkMb9hMeUhSyU7VaYla2pj8HmaXyA8bjCCCgBdRqMtmOWs6zl52JdL6EioyXOp8EAggggAACaSfA4rK0s6VmBNJWQKYOqdiQLCtzoi2HLp24MK7rqPixIY0lt77o+L/1v6zWOXZPBE5f8dPgKcGLNtu95hRXGBMTF5GJCI9IcSX2ejAmKlpVZYbG2KtTqa+n9auPSSUsMUu9JDUggIASkNiQLDCX1WQSG5K5QrI/kcqXlWWS0JdwIYAAAggg4BgBZg85xpm3IGBnATV1SFXqRMvKrpwN/bLzSJkjozm8s2Rq8/JjvlWL3wi9fnTbkXWzV6lbf4yYLRN80jTstWfVLr92dXVL0jlx965qwIkdx3IVzHP55IWr56/cvnE7a86sWXNmq9Haz8Pbcf91rWcPyVSmi8fOyUCEnQ27GXYjU5ZMmXNkkRVnufLnTmeu9Hh9xUZVmjzbdfXPv8oe1UwgSo8R4J0IuJSAxIbUajJ9VJnuHivLNAUJBBBAAAFHCjju+4Yje8W7EHB5AbXrkJxWJlOHRu+e6Cz9/ePj2RaxoaGLhuculFe1v2bbOgE9m099acKF4+ckR7pWrJJvWkRwvLxjJ06e3H1cvTd9/167dPXcwTPnj51XzVg6YaH8WjTp5tWujZ9tapGZFpe3b9w6d/jMseAjqvL96/eOeuwjixdVa1bj+fEvWWS6yWW7QZ0kPCQTiAgPucmI000E0khAbUcty8fU4jK1HbV+l0wpqlevnr4kgQACCCCAgGMECA85xpm3IGBPAZk6JNug6F2H7Fl1Wta1Y+m2vat3qzdkzZWt8dNN6z7eQMeGVH4+3wKvzX57St9xJ3Ydl5yZ73xfuEKxIuWLqrv2+uuZyVuqkr2xoyKjvNJ4Vo684tLJi2Ehl7L75Chaubh6XVRE1B//m3Vww/4r58OS7JRMsCpcLg13YpIGrJ6+Yu3PK6+FXjcG7xJrWGm/sondcvl82aNa/umd2BXs8j2lgwggkKYCajWZzBKSSJCcZ2/xLjmzLH6mRRkuEUAAAQQQsLsA4SG7k1IhAo4QKFOngnNNHZJpKb9/9LOiKVymSJ/Jr+Yp4pOglCyn6jmm76fth6tQxYrvlnT/4gVV8tqFK9sXbz1/7JysusqZP1eBEgWqNa+Ru3Dc5COL2iQuc/3iVe+smXPkzWFxK3O2uKPBbly6llgzLB6xuJSj3zNnz2wltBR6+tLOpdt3Lt+q4lzqcT3vJmju+qA/N1jUqS8lHlTGr1wpvzIla5QuVKZw7sJ5MmZ8YJ+4ZDnoahNLnD96bv4XcxK7K/myWXjpOuVK1Sgtcbq8vvmt9NpKJfpWxK07u5ZtP7H7uKxWy5YrW94iPhUaVfGtUlwXMHlCdiCSgwJTub6scDaT95LmIYBAGgqo1WTyAuNRZfp9Mp+IM8u0BgkEEEAAAUcKEB5ypDbvQsA+ArKyTBaUSXjIiXYd2jJ/061r4dJ/iX1YiQ0pIIn4tHm5/V9fzZXLY9sOq8zLpy6OeWqUqkTlyN+5n/wa0LNF29c6eGeOnRCkfmSHoxVTlm5esFEFmIqUL9asV8vaHevfu59BdvNR6WsXryYrPHT90tXF4/7aNHedPC4dKV+/Yq22df3a39/A6M6tOzsXb922aPPBTfv163TiwIZ9Kh156/7uS/quSrTs27b1q+0zejwQDzKWsd3B+JSVtJXGlKtXsffEV4y2CdYjgyL7fJ/cdez6petZc2f3KZbvoVa1Eoz4RNyOmPrSt0e2HjLWs+jrPys1rPLU/7onFukzFk73tOxAJBOIWF+W7gNBAxBwUgG1miz+UWVO2h2ajQACCCDgSgKEh1xpNOmLewk4165DR7fGRXlkI2pbIjL5iudXw1m0YjFJ3Ai7MaHnGIvYkCoQOP2fA2v2PDemT+GysSuwjm8/MvXlCcaSZw+dnvXej6FnLrfs/6h6xNPLUyUs1lLJnKBbV2/KqxOMzqz6frmKWOln9wbukt+zh888OrCjyvxt2IztS7eqtP4rK+nyFMorM2VqtKmtMut3bbx/7Z6QfScrPly5RutapWuVXT55idqWO2/RfAm+XT1ou4OUl+5Im3cs2XL5zGW5VM1o/XK76i39VG3qr2/VEhJnXDt7lUxZqtr8oUr+VUJ2nZjx1jS5mzNfLuuxoZjIqMCf/l06cZGF5IqpSyQq5/9ss/pdGhnfNWPQFIvYkLormxzJfLHunz1f7ZGaxvLmTNtlApE5u0arEEAgTQUkNqRWk6kJRImdTWaxFVGaNonKEUAAAQQQ0AKEhzQFCQScQ0BtPORcU4dE9ui9CSM129ayBfrckbOqWL7iBSSxftYqvU2PWuvk5eV5el+IhBXk7rmjZ8d2HTXw1yG3rt385rnRCda/5Nu/GjzZKOd/R2553ttvKPJOlCosK9Hm/m/2pnnr5dK3SomXpw/KnDVuAZoq8M/kxYvH398eQuIm1y9fU7ckFNKwq7/EvKQ9xtiQTC9q2uORqs1rFK9WUpXUf7Nky9Jv2uv6UhJ6fyVjYMtYQKVtdJBI2dlDZ77tOdpYm6Tl99cPZ2bJmbV8g0rGymUfK+MhcTGRcQfb37l5y1jMIn0s+PBv7/+s9hG3uCWXEpX7bfjMY9sOdfmou1qPdnTLIb35lMTLqjZ7KEuOrHIs2u6VOyS6JL8/vD75mZG9jPO84ldLDgIIIOCkAta3o9adIjakKUgggAACCDhYgPCQg8F5HQJ2EJCNh+xQiwOriImK1sEUW6YOSdNkH2vVwLJ1ykvsZvXPK9VlhfqVjIEVWe216Kt5mxdskuDCkaCDf42OXY+mfvza1m3aq8XF4+f/GPmLipLImfFqcop3lriVaGrOi8yy+WHAJAl2qAdP7T25fMKi9oM73aspw8bf1+rYkCws6jqyp2wJJDVP6TteTczZu2pXw6cD8hb1kRk6OiLjW7lEyRpl4seGdLXGRJbsWdTljbDrxnxj2kYH2Qb79vXwSb3Hqd5JDRUfruJTPF9+3wJLJiyU5k3q/XXfyQNkkZSxcmM6c86s6vLapbgQmPGuSsuYWkTiJJD3xJCn8hbzCdl5XC+vk6EJPR3ae9IrmbJmXjMzbhBF6c0/35dNplRVkXciV/2wfMXUpdLgM4dO1c5wfxlg/PeaIYf1ZWYYBdqAgNMJ6O2opeXEgJxu+GgwAggg4A4ChIfcYZTpo0sJnNwdVP7hAs61sszDyzNf0XwqkiJ7VMucEetDIhvZyNwTVaZC4ypnD5zWMZfOHzxtfFZmA3X7pFen958OOxv2z6TFOiDywvj+MjlFSsrKKYn+zPv0V0nfjbmrntUzgyJuRUiAafxzo+UUM2O1K39YXveJhhIDkkx5/M/Pfld3jXvxeHh6REfHqHxVb7bc2XuN6Tv1lQmqGRJvmvrK4bK1y8vEnBIPlTbWHz/tlSnuv41j7tUZv4yNDtERUaO7jFSPV2lS7elPe0nD5PLu3Zh/pi5Rbfv76/lWwkOeXkk3xqJ5EoHqMaa3GtnC5YrW7dRQ5gp9/9okGThZTSZr3Fq90n7f2j3qqSfeeUrHhiRH1q+1fOnRZi+2uno+TG8LZVG/S17KGhOX7BedQgCB+ALWt6OOX54cBBBAAAEEHC+Q6O6njm8Kb0QAAVsETu2PPVRb5rDYUtg8ZdQaMWnPkc0PbEscv4USqZH5Pipfpv9IKEe2mlaXMnUof8mC8R+RmSl5i+Td9e92deuxQZ1UbEhd1uvcsO2ADvJbuWl1leN1b/ZQ2JlQHRuStWCyV478VWXW/xKoEku/+UtHnQ4HHRj16IfTB3437eVvP2n7gV7vVq5+RVVYEkMXDZcTytSl/JXgyNfPfC6zk2Tpmc6Mn4iKiFvPZSU8ZIuDhLR+//BnVX/ZOhV6ft1PxYYkRyZP6SibzJDavSKOK35jYqLi1tzpgFr8MrEhv//W/cktOdTshQn9LaJ+ZeqU7zvpVfXgloVB4VdvasYEl4/JArR8vgV0a+O/0VQ5sv2Q+pdoqlbRGAQQMKeA3o5ajiqbOXOmORtJqxBAAAEEEGD2EJ8BBJxSwOnWl+Utlk9Byxyfyv5VJbiQoPvJncemvzFFRTFkfx+ZciLFwq/GHnkmP57eCT8lt2QLGxV9kIVLTV9o+V/xuD+ZsmRq0a+tMSdjxrirxd/EbSckW+G8Mn2Qj2/+YhV9f/lghtxWe2nfCL227l6cSD0jISEdFVI5j7zYWs0zUpe5C+V9fvxLMndG1lLt/Cc2lic/sr2O/MpcHpkmk/BMortxM5uiIuJCM+rBbX8F7Vi27bE3O0lczBaHq+fCTu45Ls9Kj3qM6WM8hH7p+IWqTvV3wZdzKzWpZiyg7+qokBw0pjMlEbL75Iopi+s/0VAF2jw944ajee82CdaTr0RcLE9OiAu/ctNYlWukU3m8vWsg0AsEELAuoLejLl68uJxYn9h21NYr4S4CCCCAAAIOEGD2kAOQeQUCdhaQlWXGjYTtXHvaVCfnc6mKJXghASBZYmbxHtmAZuHouTLRRgdfnh/fT60C03NeLB554PJeyEcKh5669MCteBe3b9w25smMoVd+io0NSWbVR+Im/pw5cOr/7J0HmFXF+cYXRVEpShWBRQRpigqCoOCy2BUNsSYWjDGKSBIr8Z8isQU1RrFFjT0WYoyxYsVOUREhoIKKSFdBERu9CP/fue/ueLhb2IW7yy3vPOc5znzzzVfeuSrzMjOHr9S///K70tytcI/Bjw/h++vxgVBRP73o+L4XHB0Xqs7emdNuPOvS1/962KAjw44kLmYmwUeveGjd2qJTaWFgvSb1VF/6zZIg5LKhRy7/F7zSy3c8h7AiOCz45HMN3//kPnXq1wmmXrr9uY/f/ig0qbBL661/j45LQn274oFs+QlCKs/d8MT7r0x+9C//jgup12u8Q5JETc6UqdKsbbPli5NnvNQhmSLU9UMzJo/JlIAdpxEwApsFAbghdgyNGDFCh8t85dBmmQU7NQJGwAgYgQoi4N1DFQTKakbACGwSAiynm7VvAeeCFfiOvx552ZHnH925bzeIkgXTP/9g1JR3nnwrEEPwKaffNHDnPVvL5epVq1X5buF3ZQXBtdCM0gaivx39F+4A6lCwe1nKy79fj6o4+arTGjSPuCEKh5v4chlnr6jPmvBJYFv27rtPs3bNB9x5Die86F25ZGXjXZo065hfctcMdxWtXr5SN3DXa7Q9G6B6n3bQmAdfe+We6OplzL71yJilXy859boz4luo6jUpYli4fyeKI1HmT/s8HMhCUBEcikbm5U14aty+J+wPgfX1Z189d+OISc+/o679TihYNHehqKIn//Zf8i151IukGAgbxcMGIrZfRd5Xrp717sxgn0qjlo302TK2FP1i2JnxdADh6WsffeepcagxL8dcfOKa1UWTiGTFshV8uy1uKkPrXASWoZE7bCNgBKoBgQp+qqwaIrELI2AEjIARMAIVQcD0UEVQso4RSC8EMu7iIcF3yt9+dfuvbtQnzHg//OcHeEoiu0uXXU+55pf1mxUdRkNh7ZqivTYL53zJ/co1amxRchT7jPY/qZArpemCUrlr0C3s9+Fp17MDN9ok6X8198sggS7Z89CinU0Sdjm8m+ih6W9/tHJp0T6jTz+c2+XIfVCA9yn/42vPXPfY+Cff4hLrE4eeqiy4lOeQQX0LTj3g1btfHD38VcLj0Nk7T47rcXyvEEa9HYvooTnvz2bXEunAsDxyaXTMjdKhoBPviuCw895tRJMtmDn/8gP/yAfRwjfjsHDA6Yey74ytWzed+DcxOw9dfN+iTxcSXo0t1kO1/k4NtFlp9qQZ7fbryD6mJ678j7iqjr2jYChsUNKH6tlSdP0JV7fr2bFRi0ZLvl48638z4juVBtz2az7fNmP8NI3i/dXshS12yw/NDK1wwHP6WwszNHiHbQSMQDUgMGzYME6TjRkzhnvo2UBUDR7twggYASNgBIzApiBgemhT0PNYI7B5EMi4i4cEU9M2O537r4vuv/AukS+lYtf33J8eeOahSVRF0ISe4OxVnQZF57CCXJW+5/2UXqgZNT8Y9T4PdegSvjHfcs9WO++5y857toLcgcKQDt9T6/f741UP7/1O6s0XviBHPv1g3q77tJP8jYdH9zqpMGwyCsolKwvnRJQBl1gPPXRIz5/17nZ0j+0b71Cr9jbs/WmzT9tpb36g9D9+68M4PQQfFHY//eWgPzVr12LGhI9lnBug9zykS9xROTiwK+eQs4547uan0EdNhI7GcmO3bmWCrhp073m39L9O35Ib+Y9n3x35v3Meuih+tzTElrZ63THgZr5KNnPSJ8HUgWccKoPtenXkEm72gtHkS3PhY3Pq5c39R8f/+aQ23Yvu7Q7yRXO/zAJ6qO2+7U0PhTl1xQgYgSQEdB01Qg6XzZs3L6nXTSNgBIyAETACaYiA6aE0nBSHZASyFgHu9zn/4d9Pfe29Ka++O+/9Od99FR0WY6NKo/zGHfbfvWOfTpzGKpl8p4P2evH2Z5E3adW0LG6IXg43/XzoLzjF9twNT4r4kCl4Db4xz6Pmmbf+Zr+fFejS6BOvPk0np9SlN0wNnzl7/MqHv5n/9X4/31+HwjBy+5k3D7rnvPi2pjCKG6wXJS484kBcpwP2DL7efGQ0T1CLV+o2Ts6014m9X7/vZXRgpgI3BGd06rVFx9AqiMOBAw7laNiI6x4LhA6fMOt7fr9WnduEADjLdva9598+4GZuIEK49LulNbfeKvRS2e+4XlMTvA/1aW99ELqOG3KSLmlCwjau/tedMf6xN6Dk4pQfMbfusmvPEwvYvRVOnLXcqzV3jWsrU6surYPBjK7442UZPX0O3ghUHQJwQxgvKCjo37+/P1VWdTjbshEwAkbACKQWAdNDqcXT1oxAdSDAtoXqcFM1PtgZ1OmgzjwVN9+8Y/6v/j7ojYdGHTzw8A2O6nxEt04Hd57+xoefTPh4zruzA1MTBnLdctd+PS599eqVy1Y2brVjkMcr7Ovhm/f1d6oPG/WTC495/Kr/0AuTwoaggwccAY21Y+udVq9ctXDmgunjpk0d9b422qBzyStXsUOHi5BeuOUZnd6Kmw11eK7Df3tUaKrC97/efvxNHelCAsnSrV+PQ8/uG64lqiAOsDY9T+y973E9v5jNWbx1jXduslWt9agfuYPl+b8Rl3w0ego3Me12wJ41t17vfwd8m4zzceyBCkGyUeigAYclfXYNy71O7sPDFUV8NO2HH9bWqV+7VAoPTQip5298ijNofNwtmHXFCBgBI5BlCOg6anYM6Tpqf6osy+bX6RgBI2AEshiBGiwesjg9p2YE0h8B/vg4adKk/kOHt+laWJFoLz8if9iUf1RE0zogsHbNDwtmLvhs6tz5n3z+3RffdujZcZ9je1YWmVfvGvnsTU9WZNTlo68ROcKV2++9OOmjsVO/mDH/yzlf8rn6eg3q1q5ft2HLxh33332vvl1L3mmNfRiWN/8zBgJr1x7td+uzR6m0TkXCSIkOd1G/+dCoL2Yt4K7uvQ7vWnuH2ikxm01GBncadOnzG3NmpEvjvMsH9AMKX0eSTb8H52IEQIDrqLVjiCuHaPpTZf5VGAEjYASMQAYhsN5fF2dQ3A7VCBgBI1ARBDjcxBfHeCqiXJbOgQMOa96xxZN/fbSsDUEc5mq/X0cueA4bZ9gkBaXCU5bNUuXbN61/xHkRa5AOBXKq8PSD0yGStI2BS+JnThzVumLEbtpm4cCMgBFIIQJwQ7qOGpvmhlIIrE0ZASNgBIxANSBgeqgaQLYLI7BhBGZNHluR3UOsRTdsyxpVgED7/Xf/v6c7Thv7IRuCFs764vtF329bZ9uG+Y2a7tq8bc8Om0g/VUG8NmkEjIARMALVjYBOk+HV11FXN/T2ZwSMgBEwAqlAwPRQKlC0DSNgBHIAAe706VCwO08O5OoUjYARMAJGoHII6FNlvo66cqhZ2wgYASNgBNIJAdND6TQbjsUIGAEjYASMgBEwAkYg0xCAGxo/fjy3ieXn53O4zNdRZ9oEOl4jYASMgBGIEDA95N+BETACRsAIGAEjYASMgBHYSAT0qTI+YK/DZb5yaCNx9DAjYASMgBHY3AhssbkDsH8jYASMgBEwApmKwIzJ0ceJKlua1qnsCOsbASOQvghw05Cuo+7evbu5ofSdJ0dmBIyAETACG0LAu4c2hJD7jUDVI9CiQ+eqd2IPRsAIGAEjYASMQIoRCNdR63BZiq3bnBEwAkbACBiBakTA9FA1gm1XRsAIGAEjYASMgBEwAtmCgK6jJht/qixbptR5GAEjYARyGgEfLsvp6Xfy6YDApEmTKh7GFrW+qriyNY2AETACRsAIGIEqQkDXUfOpMrghLh6qIi82awSMgBEwAkag2hDw7qFqg9qOjIARMAJGwAgYASNgBLIBAV1HPW/ePB0u86fKsmFSnYMRMAJGIOcR8O6hnP8JGICMQmD62x9mVLwO1ggYASNgBIxAtiEwatQo7RjS4TJfR51tE+x8jIARMAK5ioDpoVydeeedTgjs033fCoZTs9a3FdS0mhEwAkbACBgBI1AVCAwbNkyfKsO4uaGqQNg2jYARMAJGYLMg4MNlmwV2OzUCRsAIGIFsQGDulPHZkIZzMAJGoMII+DrqCkNlRSNgBIyAEcgwBLx7KMMmzOHmOAJbbv0NCEx744Mcx8HpGwEjYASMgBGofgTghnDq66irH3l7NAJGwAgYgWpAwLuHqgFkuzACRsAIGAEjYASMgBHIbATCddT5+fkcLvN11Jk9nY7eCBgBI2AESiDg3UMlILHACBgBI2AEjIARMAJGwAjEEAjXUetTZb5yKIaNq0bACBgBI5AlCJgeypKJdBpGwAgYASNgBIyAETACVYRA//79dR119+7dzQ1VEcg2awSMgBEwApsXAR8u27z427sRqAQCNWt9VQltqxoBI2AEjIARMAKpQEA7hrA0fvz4ESNGpMKkbRgBI2AEjIARSDsETA+l3ZQ4ICNgBIyAETACRsAIGIE0QcCfKkuTiXAYRsAIGAEjUNUI+HBZVSNs+0bACBgBI2AEjIARMAIZiQDcEDuG/KmyjJw8B20EjIARMAKVRMD0UCUBs7oR2HwIbFFr0eZzbs9GwAikEgGuL0mlOdsyAkagChDQddSDBw8eNmyYP1VWBQDbpBEwAkbACKQXAqaH0ms+HI0RMAJGwAjkCAKTJk3KkUydphHIUAR8HXWGTpzDNgJGwAgYgY1DwHcPbRxuHmUEjIARMAJGIO/Tj0zx+GdgBLITAV9HnZ3z6qyMgBEwAkagbARMD5WNjXuMgBEwAkbACJSNQOtu7ea8P7vsfvcYASOQqQj4OupMnTnHbQSMgBEwApuAgA+XbQJ4HmoEqheBmtv47qHqRdzejIARMAJGIPcQ8HXUuTfnztgIGAEjYAQiBLx7yL8DI5BJCMyc8DHhTh83rX2v3TIpbsdqBIyAETACRiATEIAbuuOOO+bNm5efn+/rqDNhxhyjETACRsAIpAwB7x5KGZQ2ZASMgBEwAkbACBgBI5C5COhTZcOHD9fFQ0OGDMncXBy5ETACRsAIGIHKImB6qLKIWd8IbGYEdt6j1WaOwO6NgBEwAkbACGQjAvqA/ZgxY7p3725uKBtn2DkZASNgBIxAeQj4cFl56LjPCBgBI2AEjEDKEWheO+UmbdAIGIFNRSBcRz1+/PgRI0ZsqjmPNwJGwAgYASOQaQh491CmzZjjzWEEatb6Ooezd+pGwAgYASNgBKoKAV1HjXUuHjI3VFUo264RMAJGwAikNwLePZTe8+PojIARMAJGwAgYASNgBKoSAV1HzZVD/fv35zrqqnRl20bACBgBI2AE0hcB7x5K37lxZEbACBgBI2AEjIARMAJVikC4jloXD/nKoSpF28aNgBEwAkYgnREwPZTOs+PYjIARMAJGwAgYASNgBKoQAe0Y8nXUVQixTRsBI2AEjECGIODDZRkyUQ7TCBgBI2AEjIARMAJGIKUIcKxMp8l8HXVKcbUxI2AEjIARyEgETA9l5LQ5aCNgBIyAETACRsAIGIFNQQBuSMO5jnrevHmbYspjjYARMAJGwAhkAQI+XJYFk+gUcg6BmRM+zrmcnbARSFcEZk4cla6hOS4jkOsIcK9QWRDoOuqCggK4IS6lLkvNciNgBIyAETACuYOA6aHcmWtnmiUItO7WLksy2ag0Rt//ylWHDlm+ePlGjfYgI2AEjIARyCEEuFeoVIYoXEeti4cKCwtzCBSnagSMgBEwAkagDARMD5UBjMVGwAiUi8BXc768tt8Vj17+0Ox3Z5armOLO6eOnLfp80aK5C1NstyrNLf126eKvvqtKD7lle9GnC1ctX5lbOTtbI2AEKo+Azo6VSv2IFfKnyioPqkcYASNgBIxANiNgeiibZ9e5ZRMCNWt9lVbp1K5fZ/XqNW/9d8zfT7n2mqMuH3XfK5Ag1RDhFzMX4CWz2IF7f3Prg4PvqQZwcsHFmtVrrjr8ktfvfSkdkm3dZb90CMMxGAEjUBYCunY6qbdfv36Sd+/e3Z+xTwLHTSNgBIyAEchlBEwP5fLsO3cjsPEIbFtvu/8bccnJV/6yxW4tv5y9YMR1j16y/++evf7JlZu8rePD19+/7Zc3sDupZHBr1/ywaF60b2j1ytUle6tO8sglw996ZMxG21+z+ofPp39WweETn3p7+EX3VlA5B9XW/rCWrOdP/zwHc3fKRsAIVAoBLhUqyf6wpQhWCDt8qqxkb6XsW9kIGAEjYASMQJYhYHooyybU6RiB6kOg5lY1u/60R8tOrXB54hWn7rxHq1fvHXn14ZfMmvTJpgTx8dsfzZjw8Z1n37JqxaokO98u+EaS7bavndRVpc1Vy1e9N/J/G+GCrS6chOKmpOXfL/vnObff8LOr2Wl1//l3lmOKIZOefwcirBydnO2CfFzwcUS0fTh26j2/ue26Y4ZywvHVu0bmLCBO3AgYgbIQgAYquXUIIayQrqMePHhwWWMtNwJGwAgYASOQmwj4w/a5Oe/O2gikDIF169Zhq8tPuu9zbM/3Xvzf41f+55ZTh/W/5owuR3bbOB8/GXxsm25tp789DVJm6222jhtZsWSFmjvstENcXmodnuWzD+fOmjDj6/lfF5zSp3GrHUtVq4hw3dq18z+p3HaVdevW3tL/utnvzgr2p7z27lbbbL3znrs03bUZvTVqlM7OK+Vvv/imQfNGYWxaVVYsWzFn0swZ70xft3bdwYOOqLVtrWoI7+vPvrr1F9cDi3ytXrHqg1Hv77Bj/WYdWtTfqUE1BGAXRsAIZBYCbB1KoofghvSRMl9HnVlT6WiNgBEwAkag2hAwPVRtUNuREchOBEQPbVkzIjv2PHTvNt3b/+P0G4b//p76LRq02qu1cl67es0b/x795iNjfvjhh90K9+h7Xr+tizmFNx56fexDoxZ//X3nw7r2+/3xkCNb1Nyy00GdeQJe387/euXyVTu2brpmVXSmrGnrneo23L4cywtnLXjh1mcnvzAhWKjboO4hg/qGZskKLv73zDv7HLNv3UZFloPOE1f9Z/LIiaFZauXzjz9bvPC77bav07x9sy22iv67SsCfTYv2udRtWG/F0hXQGX96/oqG+Y1LHR4XbrXtVjS/X/hdWfQQoX4564utt9t6x9Y7ccQvPpb6zAnTR93/yvY77hAxYrs0Db1lyVHgNN9Ldzz33ZffterShqlRkHjZYacG8GKj73/1+0XfHX5Ov61qbTVt7NRX7n6RvV3BbMfeu7fu1jY0S1ZWLFkOf7ht3W05DvbGv1/f8+Au7Xp2DGoLPvn8uZtGzH1vVrN2LQp/eVD7XrvRxc6pkbc+C70IjxY050yeJW6I8Dhg2Kx9i4F3n1enfp2g4IoRMAJGICAAE0Q96eyYCCNfRx1QcsUIGAEjYASMQBICpoeSAHHTCBiByiGwekVE2axasVq7SGrvUHvgXedc0++K+869408j/wLdwz1Bdw28BU6BvTM1t645ZvircCUnXHYKo7jT5+3H35A/brnOq5F3/CUn04QvaJDfmMX/su+WDv/dvdPe+gBhpwP2KjztICq7H7CnhpRl+eEhD2jbTkH/A3fvs8cOO+5Qf0M7cZ6+7nE4INirQ85ej0WaOOLtsQ+93mCnhl/PX8R2pC233OL5m5+eOfGTI845CiKMMKA/7j/vTg7EKaT2++126vVnwoZss902Fz05ZMstt4RkeegP90185u0KbnLZsmb0n+VVy1bxnvTsO8DVrd++PU+KPrrMnqNnhj3x+n0vyxfE02/uuyDOAXFBOJdAqfeNh0dxMxSn/2iWJafruRuefOWe6HAWO3Eg1OZNnQON9flHnw47/srTbxo4e/Ks1/75Ir1bbLHlQWcdxok/6nBSfU47uN1+HWo3qNOwxQYIr3//8f5Fn3515m2/vvW0YZywmzDi7T+/fBU/EuxAS93929uoNGzWkCnm+fNLVwLX158tevmu5yeMGDf48Ys5Rchl5M8Me5y9Qmj2PfenB511+GWFv9+2zrbmhgDExQgYgbIQSNo65OuoywLKciNgBIyAETACAYHSTzeEbleMgBEwAuUj8MPqNSisXLw8qLEB54hz+i1e9P2UlyYjfPLqR+CGOh/e7Yqx1w5+9E9IZv9vBm8ue4YbYjPIBY/8aeibwyA7FkyfLyP3X3DXS/94lvrDQx6ENdily66HnHUE7NKEp8YhTJxmixTLstz5sKJzbWwzqduoHgQKtFQ0oOyiC4+3rbsdFMynH8zjje68KXMe+tN98CZ7HtaF5orFy+759W2v3P0Clys9eNG9nLHi4SQd3NB+Pys476H/6/e744n2sSv+LT9QJ5Ad1Nnpw5v9RJKX/946sXsI5uulfzw3/Pf3znl/9mNXPjxvylxGPfLn4XBD7Xp0GHjXuef86yIQvv3Mm9mZJYNc1SRuCKB+dnn/ndo2f+ji+z4cNaUsOaMmPTsBbojdWJe8fNUfX7iiSaumIAaJIzRG3vYs3BDcDZofj/twmzrbwtBRR2HJ14sbtmyyQW4I5bU//DB/+md3DPg7o3AEMzh38kzkX3/6FdwQof7mvgv/9OLQHsf2QvjpB1GaDVs22q13J/YK3fPrW1+6/bm/9bscbmj/k/tc+urVcEMoME3Lvl9GxcUIGAEjUCoCbBSKy9lM5Ouo44C4bgSMgBEwAkagVAQ2sGQqdYyFRsAIGIGAQJ2GdanPeOeT+GVDP6yKOAtIBLZ+jHt0LHXtTOHMFPXdD4xYholPj+d91u2/bbRzEypn33NejS2KCOstt6457/3ZnG+a+tq7bbq1O/vuczlxtmDG/Gt/egWaUBVH5h1djuWCXxzYep+2L/x9BLQCT9cjux808AjOpjG2rFIrweDs1qfT3QNv/ejND352Wf8O++92x1k3o3/63wdxqIrKPYP+MXfqbCgSSA1uj35/5KSFc76E+zhuyEk9T+yNwuv3vcKbrgN+dUjzjvnUi0p0OxNESUQ5UThRFT82JWF4b1krOlz28u3P4ws+pfMR3V649ek3/v3a3kd1H//kWxzBO3XYmSi8+fBo3nAonNojX+oQLrwpB595eI/jerXdt/2Vh/355Tufq9+86GqeJHnbnh2euf5x9BfMnH/Dz//K3igYHO4XZ3PQ0m+XIP982qfUz/7n+aMfeHXMv15D4fS/nz3llcnP3zSC5rjH3ujd/8DCXx6srUCR49LK0m8iU3zb7oDTDz3gjEP5vN2nH87r2GePZ298CjlsETuS6jWou+jzRaDaqktrhNzKRI5/T9zcxC4w6Llf3TIojiebvIIreLTFX5V5EC+ouWIEjEDuIJB0sowm11FDD8EZzZs3L3dwcKZGwAgYASNgBCqLwBaVHWB9I2AEjEAcgdZ770pz3tTZEnLHzYOD737q2kfZDbR3vx4fvj4F+clX/RKOZtWylWxF+fkVpx76myOelzEAAEAASURBVCMRrlwW3TMdbpuGMQkMzg5NdmDXDDfsoLD3kfvADUEG3XXW32m26dqWPSY0y7GMGmzCGbf95vyH/wCbM/HZ8exAefq6x7QniN6yyozx0+GG6J34zPi7Bt0KXXLqtWe02C0fFgMhfE2vk/pAkRz26+gA2qz/fQKHRZriht554q13Xyy6ouipax6FTAle2O1CfcWSaMMLp9WuPfovixdFNFmpZU3isB6+WuzW8rz//P7gsw/Hxcx3ps99bzb6R154NG/2N4247jENH3n7s0u+/p76onlF9NDKZStpcnURAxcv/L4s+fxpn8MuHXTGYQf+6rCta23F2UC2aA244xzGfv/FtzIOeYedXbu3ozk/cZUSd0L97skhnDtrvHMTNlJB97z7QlHWGpL05kojJHsc1PnIC38KkcSeprnvz+J2oamvv7dr9/bQcI3yGy9bsnyffj3YWVanQT0Nh1hc/FWUFKXHsT3j3BCS2vVqL19ctHvo8b/8my1UCcXN8Fqzsv5m8GqXRsAIbAiBLl26SAVuCFaIj5TxHj58+IbGud8IGAEjYASMQE4jUDOns3fyRiBzEFizslG6Bctlw99/8Q23C7Hv43/PvjNjwnRYGzEpkDg//cMJ3A6z4JNPCXvnvXbp2i+6BCdeIIzYn3LDz6+GnuCG42a75XNfjxQ4EUalbuPolmiu/mEb0esPvIJl9ulw5c3VR1769qNjlyV2uJRqmVFwSd1+um/nI7rC5hAnx9A4ltVkl6Zsq5GLpPd2O0SXHD/85wck1+3Lh//2J2zeQaJNT/A1R//fcTQ5rcY5rE/entau126cLONz9VvU2AJCh4NybHR64sr/fDB6yj1n33ryNb/URddcFM2oue/OAatX7x7Jlpw69evKUcn39wk+BUhPu36ALvCGW3nzkdGMQvnmk69t0TGfz7qjMOie8z+dOu/p6x+74YS/nnbDgO8WfCNrr947Evpp1fLVnD6DHStLvuDjaGoatGi07wn7i3UKwSxetJg6bFHLPXehQuK8Z4z/eLsdtnti6H8O+XVf3R3O5UHc2/3A7+7+Q4cWZX0YDgaKUE+4vL++1Ja/e8tJL0zkdiFms06Duj2O78UT/Kry9qNvPH3943BzB515+Pgn3hz5j2e5EenIC48JW67qNdketL/74huMY639fj/edZ1kyk0jYARyEAGYoHDxkOq6jrqwMLrErWQZNWoUCmwvSrrKuqSmJUbACBgBI2AEshsB00PZPb/OLjsRYGfNZk+ME1LXHfOXEAarfb7P1aJjy533arXHwZ1bdW6jrpZ7tn5nxNuPXPqvM//x6/C1MnbWsD+l96kHwJv859Lh0Bk86MO5tO/V8eg//oxPaM2ePHOnts3YbMLpLR4ohlOuPn3vn3RH7YDTD2Gbzz7H7FeWZe6injNlNvuAnr/pqZZ7td6qVs0lia0osyfNKIse4gbrUQ+8AgVz+o1nzZsy782HX+95Yp8+vzpYWdRpGNFVJ//1dH2VjPqhZ/cd/od7DxpwGMyFbszhm1zHX3YKG2ROvX7AP3/zD2gjsj7j1l+j3KJTRK/wNTfelBOvODUco5Mk/q5dP7q2+bg//RziRnKOqkEPNWzRiC02pAw3BF9zwmX92da0a4/2y75fyi6e2351Y99z+qHPfp+Rtz7zzlPjQAwC7rjLTh7/3+jy75Lybz5bhPyZG55o17ND/Ctp0C57HNJ54ZwvDhhwmALgBiUuPPp+0fdcQ453npa7t9qxzY5rf1jHxh902NlUFj0Eo8dF3eEAGrPGEblatWuxs4nzht2O6s5BM3nhzU3k4x97C8KL+ll3nMOHzApO7fPg4Huh27ifO9BDLfdoNeW1d684KLrHitL3gmhHlYsRMAJGAAR0sqygoID6Bq+jFjGEJtuLyiKPjKoRMAJGwAgYgdxBoIY+Sp07CTtTI5BuCOTn5x/zi7O/XJZ38BkXlx9bvfxnbj7pmsN++5M7B/592JR/lK9c1b2cD3rlrpH8B4RDRg3yGzZo3jB8bD7uGjXO/rAZhxtk2Muz9Ta15k2Z/cmE6dBJ/a85g+uK2CTCNpw5782a+/6czz/+dJfOrTkUhgW+CMZdyBwHm/f+nDWr1jTr0IKmLCNc+u2y7epuW47l1l3bvHznC++MGIcjjdrr0K5HXXh04FziQar+6dS59Xbcvl6JD9urd+m3SwPHgYQYPnh9CncVsSmGu29q1dmWnUFxm3zqvkmrHcOV2CNveYaNMG26tWWrTvlfgsdIki8kHNnbcddmBACBArUE4RL39fVnX2293TaTnh7/5N/+y/3N9ZrsgNrW29WquVX0FwBjHni1VDldL9w84qU7n6fCzdDMINusYNDYkrPv8fvr03LBC/PIET+afFSOXHQEjyYkFMpHXXhMyDQMKasiUx+NmXrXoFvQYX9Tfqedv13wzayJn3ALUr3G2y9fvLz/NaezQaksCyuXr3xkyIOc7Ot0SJdeJ/WuyA3ZZZnaRPl7z6+8/6LzTx06vHXX0ncllGX/zE7RIpZ9Db4JpSyILDcCG4cAlNCkSZP4N0s8kYyUui1ImuwzKrV347x7lBEwAkbACBiBjEbAu4cyevocvBHYbAhAFhwyKLqCp/yC2ll3nTP6vlfefel/4YvsfImMG4X2SnwOjA07exzShSfJjsggyBedb4r3ItRHzcuxjN/jLjmJB6qlRg2+dbVtORt2ZLzF7tEen7JKnBtChxh2P2BPKesEWdLAZu2axyWH/fYonriknHqSLzQDo8SH3ksO1N6fGltGd8npAuy4WllylA8/t1/Tts24NYlPyMksl2FzpIs7pJO8iBtCyHQMuPOcdWvXcj0Q3FDg7JL0y2nKVIeC3c9/+I+j7n+ZfUA86LOfqPvR+x145mGNdm4MvOVY4Jok3c9djo67jIARyE0E4IZgfOCGyrmOWuQsaiNGjMhNlJy1ETACRsAIGIFSETA9VCosFhoBI5AyBNjDcuCAw3iWfLNkxffLGjRrEI5obaKPilguSbVsotO0Ha5MuTaIs2DxIMuSS4fLlXhWLV/5zfxvdtipPsxLfGxZdbi2UkmxsvRLled3atn/2l+xmWjh3IW169cR5VeqpoVGwAgYgYogoB1D7MtDmYuo+/fvn3QddThNhtynySoCqXWMgBEwAkYgpxAo729ocwoIJ2sEjEBVI8D6n2/Yp4obikdbdZbjXtK8Xr9FQyLkzqakOMuSx9W4ForPxlWQG4oP3PQ6m4lwbW5o05G0BSNgBAIC4obYHxQ4IIghTpNBGHEFNZuGgjwMccUIGAEjYASMgBEwPeTfgBEwAkYgGxDgvmpOe417ZExSMmXJk9TcNAJGwAhkOgLaNwQrpE+VhUuF2FUEMUR20EZBmOnJOn4jYASMgBEwAilHwIfLUg6pDRqBSiDA32dWQtuqRqBsBDhq1/Pnvae8Gt3jEy9lyeM6rhsBI2AEMh0BnSyDGyKR8JV6XTOExKfJMn1+Hb8RMAJGwAhUAwKmh6oBZLswAqlBYM3K6E6Z6eOmpcacrWQdAv0uOu6Ic6PP2yeVsuRJam4aASNgBLIAAS6l5viYrhnSTdXeMZQF0+oUjIARMAJGoBoQMD1UDSDbhRFIGQKtu7VLmS0bykYEtqq1ValplSUvVdnCCiKwdmWjCmpazQgYgSpFQLuEunTpwvkyNgqFpjcNVSnsNm4EjIARMAJZhoDpoSybUKdjBIyAETACGYMAGxx8RW7GzJYDTVcE+PdItw6xVwiGiIuHvGkoXefKcRkBI2AEjEBaI2B6KK2nx8HlAgL8WTYX0nSORsAIGAEjYASqAgH4oGBWDJE3DQVAXDECRsAIGAEjUHEE/OWyimNlTSNgBIyAETACyQi07lqYLHLbCBiBakRg8ODB8sZft0AM+bv11Yi9XRkBI2AEjEBWIeDdQ1k1nU4muxFYs6Jhdifo7IyAETACRsAIVBYBTmjywbKCggIf1awsdNY3AkbACBgBIxBHwPRQHA3XjUAGIDBzwscZEKVDNAJGwAgYASNQXQj422TVhbT9GAEjYASMQDYjYHoom2fXuWUrAjvv0SpbU3NeRsAIGAEjYASMgBEwAkagggisXrSugppBbfWitaGefZWtGqby9pitGtbIPoicUTkImB4qBxx3GYH0QmDtSh8uS68ZcTRGwAgYASNgBIyAETACpSJQFnFTkp1Z802pBiLhqq9/KLPPHaUiMCONENu6wZalxli+sGb98vvL7N0Iaqw6+S++s1lm6Hl5aXI+2vRQOXPkLiNgBIyAETACRsAIGAEjYASMQC4iwGp2zJgx48ePD8nzccBQd8UIGIFNRKDTLnvJwpRZ76rCNxa6d+9OfXMdmjY9tIlz6uFGwAgYASNgBIyAETACRsAIGIGMR0C7G4YNG0YmSUyQ1rGnHTage4ee9PbcvXelsn1z6uhK6VdcefxHb1ZcuTo1BVR1etwsvir7S9gsQaaPU/2LoB/txI/HixXiXy7+zTr/uD/Ee/Pz80UVVTNPZHoofX4tjsQIbACBNSsbbUDD3UbACBgBI2AENisCny3Na167qiKw8apC1nZzHgGIIVih1d9Gl/J0bdf9/pF3UWHVOqjf+SlZ/6fESKmzVHWWS3VnoRHYFAT0c0360d742F+x2eWsNvwbx7998EQ0eSNfMXstPFF1fp0zlTdXbQpS2T2W/+DGS3Yn6+yMgBEwAkbACBiBchCY+EVyJxKIj3hBkqSGQpIE/QpKqs54yagWLEmOqqQOkSeFpFyShCUHVr/xpJA2OvIKThbukjxW8JeA/Q2WknhucIgVsh4BVij9+vVj/fnXP1+7Z5Nu5Mt2BjY13HrePyfdOePBPz6etI7NekCcoBGofgRggnj4Nw5uCO/wRKdefSzcUJBDEvXv33/o0KHVEFuNdesqfdl7NYSVWS74D6sC3ujTueGQYUFBQZrcSpVZU5C50fLj4e9qOGI6ZVGNg8+4eIOJjH7kDH3Y/tx//36DylYwAkagShGY/GTd14YPO+OGEZXyslPtvCN3yeP/8Xfcccfw4cP93/xKoZehyizL2VDDpPN/eaUwf2n0p6+dakdfhNFvABagaZ319t2I+um6449JYwd+JC6hD7UkybOz8jo32YCpjTZekThLGi81zpKRlxxYUlJBEBi4QTyr2bhAKBlV0vSVjArJ9Imv69eiX8OkhXlNt1sX//1IHv5EWtZ/WEoa10C/cxMBiCES79q+e4+OPW+673rqqdorlJt4OmsjkCoEtJ+ITXw6dCazCLdptUVVnzXz4bJKTyL/643TQDqXC7+DIRb5FFkMfwQs6YDhcSH3vWFEdlgtqIstZFSqevrjYbi+eRGYM2VcRQL4YVX9Oe/PPuD0QyuibB0jYASqFIEZk9f7j3mV+rLxNEdAy3Lo/sGDB+/arTCJxIEbuvHRUfwvPvxfPikd/qd/+gVDGBUGahk/Zsyoyx+ILgFJKvxh48vlNW6+MvpLBcgFlCEdKDrVVWfuqME3j+3UcF38jyIQCtO3W3di3z6RXvEoBgZuYsyo15FPT5BWkAuitIJxLEtZEuIMNIckikEBIGF43DiW0U/ireLGUZAFLIeBGAnGl3w0Ck4tUCQPT4zqQB08MoqCvgr6/41xKBK+MDsv7/DCoCNhcMckPvbiWIT35+U12Xb9vz0dMCSOsKIi1PtvvnL2+2/LzsLl6xpvG/F9KvwS6nQoJGXo4FAYyC9h2AX9gyRe0c9AxhWV3uRy0cBT45rxemCZiZ+/Xo53qR6/vSJuvKSmJbmDgH4tWnmy5hxw6WnxVWju4OBMjUB6IsC+IQWmY55q8ubfVvb6hf/sV0Xwpoc2jGr4Mx+qInGo8P/aHnvs0qsXd7Od0qdjvR+tLF1QXJ9ZXIn9s3ZTGoWndI2J8vIG9A3NUWPGjp00I2ouXzhu/Dv5d9yh/6l7V1GAKMcrbXt0GP2vHMfA6RuBdEGgZq1v0yUUx7GZEOBPCEl3uPI3QGE3EEtxFRb5JxxR2HS7B2lCaoStQ1SefOkNeJw6HQqgOUTBoKzC8K7HFy75aMyzo96O8w6Jv1GapL+XkibMxeQvox1DlMARvJGXVwobdceDYojEO8C/iAbSdjZZS3rzx9AFHQqDfXoJDNbj0RdGDR9SChmBArFdetcIopIXJHiBTIEo2e+Qn3z60eQkF2pee8eDXfv2iYMgAC8f0C/86StpILE1LyyMC5VOACHepfrUx6LYsCycqTCEOhRMqdyKRvHXdaRMaV7M9QiEL5ep/8d3EklEyoEUwwuYnB/N6UC2DDfZLhoVeCgkdTrsH2e7GNg0ocMvikxRht7asXZel8ZF7qD8vliaB0emNpUnJ8xjn9GCZTX42SCEQ8Q+fw3JT2jIkKJRIWUqLrmJgP595/gY6eu6E0615CYUztoIpDMCOlwGJcS/pyJwxRPxfyv+OqGK9pGYHirlJyE+SFuEwp9IOnfIR3X40NMKe/fK+5EDSvy5YGmJPx2UYjUh+nFgTCMmLOxQt7BD56K+k6LKlfe8mLdo6nVDn+vff56pohhqrhoBI2AEjIAR2GwIiBjSHxL4vzPUBmtyVv4ExKoeUoB1PrwDBVqEQhNeRpRE3WU/7ihp27XPgmVFLABqrNjRQRK2nPDnvy6nRAphMS8jKIsKoSLmJRq4JDqqJo5APBRRcQoJHe27YT9L3L7ICAI+YkAxeYAql48kyAsxFxhkCAFgXPr4pXn84YWzJp8Nt4W+aAgNFNtFHWWeaGyCvVJGRxb2yCvsgX0NRE0FguzEvhF6FIGgOm+24WhTD1yMRvF3ZjAjsCSkIxDwEgYSJ0QJM6KU4eAAgV6xKkpHzIsgVVLPNyycN29eZCVRMIiLIxLziEApU0GuUVTg45r+4ccNXPTShSYBKAVpkjjK4tfkK5BNYZYZG8gg6iWLwr66MDJFkf2CDhG2z8+KUqMiv1BskU5BBCZCeUSSlLKmRqYiiy45gwAHyrh/Gj6INafuGPLtQjkz+U40IxGIk0SwujT5KN5vbjq9iraPmB5a71cS/9MeHVBCPL/rf2Bh1zY/6sXYnB+FVVa7+IwfjxFBFY0b9Zz+MlBU0RB2Hu24d5U5t+G0Q2DNyvppF5MDMgJGwAjkGALanNKiQ2c4CPgdsQBgwCI8zvWwAo+vyVEIi/myAINcoIhQkFks6CIhjCORgroidwnuSU3egQUIh8gUUhINEfceiA9Ot4lKwOOQBKMhcgELFBmnHmgIhBclyBFZUwzUk1JGfveUHze8wIxgpEuCEQtD5IK3JPIi/gJ3cD2ndYhOhBEqVAiUDaWw+ONoKMO8BPoMNSTAEhBQSNGQ4o0/UQrLoicU9M/sFEUeoKYLiUxhU8wLQiQ8FIGjICVR8Eh4SJnhSQUjdIVCzHGPuEYBU8oUNWaNlNGJ/x6UYBxkAIkYosQcyTg6KFAUEhWlrF695Yi3volTRX8LHffoejogwL4h7p8+f1B0RIV4uHk6HaJyDEbACGwQAe0bghWCIYLSZTMRm5f5m4MNDqysgr9cFiHGn/OuvPg8/gfJTi3+JlCs0NnH7//0DQN41uOGKgtwSvWhioiHHUzERpzwRPndfnrlBScRfN4X/8tbOj+l3mzMCBgBI2AENoBAy07dN6Dh7mxEgD+QsdPk0SefrtuxT1iBh0RZiqsE5kX0QZzdkERvFv8M0XoepkMGedMrFkCcAhKaQQEXSJJojhBDvCIaQhIsQEPwYJx4eER8RKYS3ARyeimSKCrqkhAnTISakvAmhiBRykiUGr1YEKEjCQoUpSwJb4bzYJy6hCFl8lWEjEII54KEJ5SAdpCoIjvUqYCAUg46BIAEEod4UFD8CLGsgZLoTQqqMJyKMA9EjyTBsjySMpaVrAxiXF1602QgMYCnQMagvKBPGKojVJzR4PULYaigw+6hOOZYIGUeuSYMMEwKQLjhhRMK0Z8n8/MhDtb34Fa2IcAUv/HiOBaZ4oa02sy2JJ2PEcheBPh3FlYIhujNqaOpsw2wKv67ndv00NL5ox69+SeH9IQVuv2Bx2GFYF7mPX8FFAw7htL2pwVdBU9EnJBEPLc/OpbgI55oyIVQRRFPxOOSpQisXdkoSzNzWkbACBiBTEIgLLlZgSexAOJZSEaL/LDmj7Z4NI44DiQszuEmKEksAFxAUkmiP8KaHzVcox8kNOEIYAEYIl6AN/Z5RDFAQ6BMAIqB8AINoUiSiI8k1+iQmvbUKGWFinEsIxEmuAtMBAHIo9KMIiz+8ppiEDMiO0giFzGySXLeSkcVQSSJPJaaspSlhneMkw7ukMgRb+27wWMoGI83JQ8IyyZN1OIhKV/lIjnGeUhWQqWvN/MegEWHn8T8BGUmX+gQAC5CwQifO0Qoy0Iej8SAKYQ8jAoxSA2J3BFAwDxIMI5rFXaN3T56Xq/jzzZJFDDPygrLSKaY7ULmhrJyfp1UjiAQZ4j415l/qdnmktrct7zssstSazEDrLHL5rOxo0bcf86frrntX883bbT9decfc9NFx518eNdWzRoofiq9944dKEvLrIiQ58L+ByxfuXrCB3P13HDnw8u//nTMyMd6d2kd7Seqs1Naxu6gihCYM2fOW2+91bx58+lzPtv78JM3iMs38+e89+rjhw7q26hl4w0qW8EIGIEqRWDa2Nlrf9imzd69K+Wl7tZ57ernjR49euLEiccee2yrVq0qNdzKmx0Blt/rakTESmGLKJZmdfKmfxPdU1Nnq6ImEhb8SLRil85zs6NFvoYgWbw675NvIwmm6m0dDRzzWV5Bi8js4lWRTS3y0cc41pCwzocRaF8/auphFE25k8cT20emMCj9XesXGUeybl3e9G/zuuxYJEGNJjSEjNPEIynwSAINQTCkIF8KSfwCdUVFFxKM85Mm/rx1RZQE1tABgSWrf0wZCfrqiv5R7JGKPIaUhTBmSZkANFBhoEzKuFMTDAVpyZQ1ClYlYI4O2CKpvXURCGTBFBCkMMc4EvRlHAtEApUjzEkHNYpSVi+aARN6NcuSoEkvxoEUZRUkeAQrpgMhIC9ZlXdU6/U8Mmt1to50SDNgQl0BxFPeu0kRyPRSMMicxmc5SLjQmvQpJUNSyvsV9F7RtOvXn898beSzy5Yt6927cv9Zi0y7pDcCp5xyCvsOxn04ljC9byi958rRGYHyENh3t/1XrFp+1b8u2bN1lyP3PfrWh2886aSTyhtQyb4co4cSxNCVV//t2ntGvDV98e/O6HdsQds/nn5wYIUqiV66qAeSqNtuLZN4ot4tFkd/BjFJlC5zlRzHxtFDXX/Sw/RQMpRuG4FqR+DF2/5Tr9GupoeqHfjN7JAlN0t3mBqtt4kGigdmJM5EsJJPYl4gBVj2sxQXKcBOIrauwDsgYcXOA4kQiI84sYI+vTKIAgVJXEEBEBIuRBMggVbAHdtGRD+JZYAbitNPKIj4UACMgtfAOEOIChpC9FYIAP2QsmKIEx9iXgLPgkd6eQhVUcH1wLxgPE58yKMSRDlQLQTAwAByyZSRwMW03SEKWEUeYVuUspgXYsad+CAU4Hq6NokkxIlTpRyYFzyKS8JmCClgLuYFKicUdOJkE80aQPdt9HExgMIdvUn8mjyiQAz86YxoQ8oMF7uER4YTFYgFTBioOPldxVMmQX57jJUQ72Ai40iIAYW6WxVJUKNoFvRT1KQgxOPBnVvtVHDyN4uXP/HgHWaIIqSyqLB1qFOjortKzQ1l0cQ6lRxFQAzRiDcfO+/Y/3v05YfzWzdP4d815tDhMnZe/eTo4/J7nzVuypzBF5w34qZB0WfC4ndOZ/gPjBNn4dCZUuHcWf4Rl1z512vzpvzTh84yfHrXC799r93Wa7thBIyAETACVYMAf3igsMzWoR6cUGfND/NCRU15Zt0eJPAaWvaz/NZAmhzn4Y1OOGXGQCSQCDzSlyneWCurYJBeXMgdapA+nFEiKiT0Si53xEkMFAUQRb6sSKLjRSUDQI5OyQAwFQoupCBfvLGDcQp1HmWEEDXOQBEDAVMwjloIKRIlLk5WJf5mYPColJFoIGq40JVGktCk4C5KJzE1YEJd7qhLTSkLhDjmksS9q45OKHArgEwumlA8QjZFA4vvbEKTpg6sCQGaFMKgxFMmDM7owUYlFQg+hMpFKaMgd7IgDOMpY0r2GYUcjzSJgchpEqEUiBzLIWWEdJGLDCoMeeS6cQ6aJQXmZkYjsOzjHziBAit0/8i7zA1l9FQ6eCMQENC/y5wVHdTvfO6oDvJNr+QEPcQf7PiIIxcMgRe3C3G1UJ+Wy2Ifp990GNPLQiCJuJmIyAJJFPFEs573zUTpNVuOxggYgUxGoE3ngkwO37FvAAH+/MAfHsaMGcOSO7AAWo3rrRU4XTQlYclNYflNQQKjARnEUjwUMQ5BErEMsVuERSuUZAFY9jNExIHcaYWPRBbknTdUiFwHj0kV3GkXieRiHxDKOMJgkC4eyRVAnJjAl5wKBFlDQoIwEWJGECKB9cAjYYeCO4os44KY4wlKLuOqB4RBRmQTw0V8YEqYSx85FQKQi8gNH6Jd/0YnPNLLI+MoUJFECaJAiUJKEFI0kcO5YFnsj9TCLCtlBYBQXExkouyCBRiiMKeKBI8aS1P0FjbJnQCQkEU85cD14EQpB2+oETklYI4jZoEiR+rCl4zTxEUAGYboiAFDIm2XrEDgqr9dybEylpG8syIhJ2EEjECEAMQQnC9fMeOLVSlEJMvpIRFD1w39Y482tXXndDZtFyr/d6DNRPrMGZrRDdZsJup9VkQS+frq8rFzrxEwAkbACBiBvDz9xZK++R0t1Nf/GroQEh0T0NIGEPELLMUZwiIchkjLct6s2+EFWIpTlxDLPBQoA/EapbIAEkKFSBn9iAVIHDQLEvyKFwjuAlMTJAwkALnjTfzwDiGAEBJd2Fd26qUJfxFXiKwkSgCBXu2ykRqdVAgJVoVIJOSNQdmkzljMUpSgmBf16o1EFemAZ5zeoiu+6QYd9HFHkTsRH3IhSeBBpCMhdngIVSHJI2/AiQi+xJXe6BNkUgDYj6dMk0zjAcg+AWBNdd5irLBGYU4D14MOY2lKgV4kQIREykjE9eBU1pBQISqGYIoAJI+njAV6ZZNeKWCZJzBE1FUiYfEWsyKR/5GxCLB1iAUk4XvrUMbOoQM3AqUjoM/bn3r1sZ122QvSo3Slykuzlh4KxNDg4/ZkuxBcSeXByYYR8c+cKZ+IJOIzZyKJsiHFbMhh172jfV4uRsAIGAEjkD4I6Huxx/yi6KANK2o4AtEHBKkFttbbrKWDhDU55Aur8cD1sNiO+KDi9bYW4VqBwyOoqaxZ8MN0BAksACWJBcAUrqEAKFRwFCQIk4iPwFPIZhLzQi6wCYH4QIc6Q+QX+xhEIkfRPxKFRHhCAMhIWcQE8TAcniK4E0o0oTYCCME+cvTjBf2SHpHIDpqqYCrUkRBAoJ9oEgzuonQS+62ECfohKlUkUTpxCSGBuRJEh+Fx+kly5aJgII/wLgtImBEQIACiwjgS3nH7moU414OX+CyLysEvY3HHGwTiINCFcbwQKr08ykUgMzx4xC8gMMvCBAWaSjk+C9GQ2N4uDKKPR4UaWXfJWAS0dWjix+O9dShj59CBG4EyEdARs67tuqfwfFkW0kNJxFDubBcq84eT6Agnzjp3yEdQRBJdcJLPmpWPm3uNgBEwAmUhMOf92a27FpbVa3lGI8CfJbitY+DAgaedezGrZZECLLa13g48ixbqrKWRhDU5ibO0TuZ6Eiv8OCYosMLX2l5vmiz7MYU7HvEOceJDS3piwK884poiCYt8Kipowh3AayTREEXdxf8Q8aHsFAPutNcJIV4wKJvqxSkRRsRH7EojjOFFcQZ6S6OIIdAQqAmWYudR7qQcaAiawjB4RMJwmrwFC2PVqzdCVRSAiI+QMhUkSSW4Q67ENX0BBBIhKoGgrMk38EHChLERCAnKD2V5JBK5U0joYDlCIEGxKYwoo0RT7hBqljESuB5GYVBG6MUmdR5AYDiPungTKr1SiNvHhSJHSMAY57chjzQpKFCRDm/9ikITBQUQRyYa5pJpCGjrEPuGWD361qFMmz3HawQqhABHzOB/V3+7NlUbiLKKHjIxtMEfESQRe6k4cSZNk0QbRCw9Faa98UF6BuaojIARMALZgQD3DZEIx8q0LGeVrjU5QnEBWswHobLWApt1OCt21uRhlY4ciYgS6cBrsLbXcEmwoCa8A4t5PIp3wJ24CSzItXzBLlGCJLIfIwU0PEmCZVzInd5I8IIy8WCB3uAunjJyETTiEfCrXBSJ3sQcT1lCGY97jHJJUEsSYplHNAQBEI8KQpo8SpA3OoSU5DeeIGZpUmRZFUZhCgmTwpvhNFHTISwM4jEYBwQKTXSInCkWJpE0wQchKVnQUWE4lpWLJNhnlnGqkPTGOI/YGYbgKMkjTRUGQreFjUVBXtRdfLUQTslOjwLGPgV3wSNpyiMK1KVAPYq5+PpqhggB9dLEI+kwsy4ZisAbU6LzJhw8ydD4HbYRMAIbRIAjZuhMmfXuBjUrqJAl9JCIIe4I4I4h6A/vGCp/+sGHW6t1cbU0o+Nm3klUPmruNQJGwAgYgZxBQFuHlK6ID620kVBhgc2qntW1hGIZEKqXdbhYCS2zYSLQZA0vFgCd+JEuFuf0wjuENXlkJS/iBULBC8MpcsebIQSAMEjkUToI6cUgD/ZZ8/PAU6ggjLMANMVByEWRUl5kQcYlISmK3hH/lTjfhCQpAKxJiAvREPgluySPkalivow6yIjtCh6pEE+camEWoJZ4410ueMsdxjWQJk6VsiSiVJCL4whNomLWAsjIyVcxRNYTRb3xkAiAIglvIozcJWaBGKjgCFMIafJgk6ac0qQoYFVENgUJwrhH0iRmTGEErHAXeUzQWwogmoUY10M6+h1GbhKO6OUJ9qnE6a0g15wyBOMUdIQwdTliFm58NGIZXDIOgVHPjeVMGetGbx3KuLlzwEagsgik6nxZxtNDcWKIy6dz9o6hyv6AACocN2MPEcN5+0KiysJofSNgBHIWAW/iy+Kp161D+nhTtCZPLPtZTlNnka8Ka2mW7hQkceZFa3IxAgEieAetvSWBBdAiPFrhJ/a8YEFLcRTkUcapa62O0+AOnRASZsU7yKPW/HFSQPI48REcBRaA3sBBYByDcbJJIfEmAOUObSFHescDQA0hAYRCAEnMixIkBegMBSMMsc/AeMqMRUivQlIMICaPIQZMgWdAWCljKkjkkeFyRwUjzAKJS6J3YH9oYhDX4IAdgEIS3DFclF+QoJM0xejEizSTmBdgIfJ4AHjkoSAMXA9jUcMFRakhAQEUCE9FcjKSNYSqoCCskFCJeyQpzMoyyjTBBO/i4JDIApY/mTBq2AX9zRAVYZ05/+BkGcFyssy3DmXOpDlSI7AxCHC+bGOGlTEms+kh/gDHjqF1y7/itJSJoTKmeAPiQBJxJ1HRWbOLz8tbOn8Dw9y9WRGYPm7aZvVv50bACEQItOjQxUBkKwLDhw8PzIvW9sqU5bTW21qQS8i6WoUVtZbc0qHJqp41OY+oEC25WYRLoi0tjMUaXuhlIEPkkTf1wMWgJr4jToXgGo+hRAHUWW/TTSQp/lYaFACPiI/IeOL2GbEGNEWFoE9F2SkM5aK6HGEQO2UVeURHAyPeYdmPd/fQqwQxCAgEgxrK8ihyJJ4yXuilBJAD6aMAZF+7gTBOQcIQnjjmuFMK6EhNVAjBqKle3kiISpQfUYFGmCZ55C1qSSBoeJhiepHgXbmol3eYdEbRDCCQTmjKPjEQAAZlPzilgiQIiSFgAoYEjFOlg0HNMvHjl6ZAxjIPHiN6q/hiKUZRwIouFYZgRxEiKSws7NKly6sPDMOOS2YhwI0kmRWwozUCRmAjEOB8GWdIU/V5+5obEUE6DGHTkD43ywkpE0ObPiPCcNTEGWMnz7j9gcd5uI9Tn/LddOO2kHIEflhVP+U2bdAIGIFKIWCWtlJwZZay/vfHYjjaEpKgQlhml1wbS6Lld+hl7S1lLcgZrlU3clbgLOnRp0jIGp4iSVRLFC3XQ52BmBJlw5KeXh5MQR+oyBQBsJ6nqNk8th1JEt7YISMRH9KEJiCkoiHQDevvUgk6kd1EwYKijdzpuFniE+zUkWBHOMggRIbsawhvBaBNOthDjZgFcpGDxD9IUNZoySBjqfCABr5CEwtAIYO8sY8ONuWR4SAcMKepCOMKYn/imBOPmBfmkSLMqQTv2Iy4mCVFE0qX3GmKMY41ISB3ATSa0mFIACFykRhCRQVHmlxoKWZERS7ooiCXC5o8Gk604VeqCItGFv8D1yqoYV+/PULiIUIkmEKHXiZOgCChl9Jqjx5PPHD7ko9GfVanEAWXTEFA15H4ZFmmzJfjNAKbiAAMCYT+JhrJvN1D4TQZu128aWgTpz9pOHcSwROBKqQb1y5od32SjpvpgMCc995OhzAcgxHIcQRad22X4whkZfrancFba2yWx+I+SFYLbC3UWVFTtHiOdBJbTpBo8SxNrcAjvUSBdKCw5NabUUhYhwcJ+rAVSToYRAgJIjUsM1DLe5wqgGhMomh4cStBHBSTAhKK+FAdg9iJB4A80BDCgTShQjAL0UM92A8pM5wIaRIYOpQQEvJ4yhiEecGjoEOTABguuiqAwHBZQE1CNfUOG4uCTmC7FHycaiEkAsCp7MgjCmSh7JCIbKKiqNAkwjDFIYDgnTlCQSnLOGNVENKFu6QSEEaOO5UQEjOLO+wTAFHFPTIQd/FZRo1mKKGp8JCTMkM0y8pRBnGB6yBBH34KU/TiF0AYyBN+6ijIpub9uEP3xzgXW5CjS6YgMHpsdGOUT5Zlynw5TiOwKQik8HxZhtFDOk3G1in4C19BvSm/oXLGiiQSQ5Sfn2+SqBysNlfXD6vrr1nZYHN5t18jYASEwJqVOxiK7EOABTBrZhbt8A5aDLNO1pqcxbPWzMo6LNS1AmdNzspcK3A0RXxoBY6+WIDAO2CHUeIF6KUe5x2QsGhnua4i3gFTPAqJLtxR5FrDWeSH5T0SlEUKSCcyGDvxJCFh8GCNXoqykwRTGoJHBQDxgVBFwxVDkShx5AqnQgAhOnGPgjQynjh3FjxiPB6DrCkSrAUQkNMkRw1Uk3f85BceUaAoPLpAgHlkIBICA2QkyghJlGACZEUVp7ckQT8EQAUEQsoMV5O33PFGP9AuIQzlQq8CgEIKOgQQ6C0CUy7SZzge47Msg6DHgyMM8qgZMMEC9gWCZpDcZZB30ZwW/66in0eCbBIgvClIwiwzRIRXnQ7R+bKo2yVzEBj7ZnS1aPcOPTMnZEdqBIzARiKg75fpo6sbaaJ4WMbQQ9o0xJYWbxoqnruq/We4k8jbiKoW6Epab9012jH46UeTVn3nbQuVxM7qRiClCLz2zxfb9uiYUpM2li4IsEJmVRwKi/CwkEZIEwUkPCzLeWiySqfJUh8SBAUKa3vW2xoYZwGirsSH1eMsAKt9PAYmQjphlR6xAGzuSPAC1GEZRDaJBcC4huNONkOE2JEEHYarzpuAGaImOsERdRVMoROKMgqkhppKmcAUkrgY8qVChNIJHnEH0yECQkJYlXghBgxSNBALFBIkU9WR0xSk1JUR7hBC6wSPAQSE9FI0hDjFT0Wi4iKPxa0oQuJUABJqQjHOI+aFISCDjjDHBbBQNAviehDiLplsSgTALCt9dISwMMECNuMIIBGkwSMKDGE4D3aohyY6uJMFegUCvYAsm1GIxSVMt/RRoCKQUQnuqEcKdYrIrAN/MZi/neVP48Vm/M/MQECLxsyI1VEaASOwCQhw/dAmjP5xaGbQQ9409OOMVW9NJFGNRVO9jah6gS/Pm27D/WSi/4hWHkruMwLVgEC7nol1YTV4sotqRIB1MowDa2xW0SyPeajzFtdAhVW36gSFJoUVNToUluVh3R6W/fRGS+7EvhuGs96moCZSgzpdNCnQASjwUDCIpppU0EEiL6iJhsAFXVAADJdZBlKBuSinYJMhWIDRoJCvMsI4EtEE6BSFvaxITa5FaqBPkURMhOiqSJo4nkaE6EgBCWajCIsvq1YASAIXg4QifQbSlEGMEyfWsEBeAdJIu7gghKBJ8ogR7AeE0UUB9gcYQ4II8YgEdzwMIbsQFRJ6fzSeYK+QKEgix5rq2NEsBP4LCWZpUuSOigIIEuwLYSoUBRDs0+TRzwwhPzPCw2n44UVjEj88DUcHoOLzjlw/DNSko7csUFczeARwgYwk+rkWz7swZ1TT7dYlfPqVMQj4XuqMmSoHagRShMD48Sm4jT4D6KF+/fqxgQXQfNNQin45lTYDSeTbiCqNWhUPWLuykc+XVTHGNm8EykTgmWFP7LxHq7UrG5ap4Y6MRYD1sJbErJNZJPOwkKZO4R1fgSOBBWAtTdFiW++kZb8G8oatgEdgiCQwEVqTq6llOfbxKHcKI4ohdqiNJvbhGlSoYAezVMREUIEpgFAIITEkWt4Xb/PBBQoYRwgjgL4oAwxSwRcDFYPCgGUIBRfKN0ioYASDjIozL6iFAKhjSgmirABoitFAQpEv6aAgbgU5cTKcGIQbEplVzHEXoQuhCD7sgzBNXGBEQ8g3ySPJ4lFMX4gKa6EwFp1QaBIeb3kna7xEs1DMfwkHuaOLJg/2kWA/HoCyFibSxwtC3MU9IhTXg0dM8aYXfeaUogTBh3yRS4eMCEn2pcNbzWA/9FIhowBykEfWi0vbrn2ocv1QscD/zAAEUrWbIANSdYhGIOcR6Nque0owSGt6SAfK9JE2uCHuxElJzjayEQiEbUSwdd5avBEApnBIy07d2UD02vBhPl+WQlRtyghUFoHW3dqtWdmosqOsn+YIaO2tIFlmJxWW8azAWedTCZqspbWcRqjlelyChbDYZgUOWRAkyFnAx8kXhmuVLtd6QzpQgoQmA3nrMJF4EHEo4h3oosmDmngHER9IKCIyVKeJd/TjmTIKZgEhhRypECQFHZriaGQQ45LLIwNFLTFcPAi90qFLhUo8X8JIQkBqgEDBHUWx6QAXdR4YEBnUWwrURa/IoxLkLcYEO/Ehkd3iwnBCwmOgn6iElNFSGDKuAJgmNdUrfezzAAXzwkQEd5gCc56AubAt9h9pChM5Qo4XEOZRakg0a7KJ8UBmYROhiCTeNAmMCjr4DTFgQToBUmLAvrCiFzkShmAZp2pqlhUDb6zxTVs+YRaiYqBLmiOQquVimqfp8IyAEUghAulLD8FB8Ol6uCFdNmRuKIWzvtGmIIl6tKnNvPi+6o3GcNMHtulcwN1Dm27HFoyAEdhoBLh4aOXixL0mG23CA9MSgX/eMJTFv1bIrKhZM7PeloS1NE1W4FqEswKPFBLbN0IqgRdAoi4ty7WiZr0NDaEVOAoI6WVNzlsKEA24E6MRlu7Y0TpfkrDmRw1TgdSQPE6+sMgX8RHCk7vQxCBGNFAB0CWhwlM6UkCCL6UjVgI0oEIoQYEKIYnciTpKFIyTL07hO4LHQP2gLmGEORt2EvuYEMo+b7xjn95QJAzNkhWMYIoiyyI+kGgWEFIhpDDLSAS4nBIwIGgq5YsYSJ9CE3nSz0CjgF3uIr3Ejd28JeGNBaUgSRwTwpNQPzPs04uECGUZieY0zDu9YdI1luwi9GLbnfAuEAQpXVjDBT9IZhCnyJHw6LeneS+a5eJLlDAyZMiQm6+8WJHQdElzBPRV+zQP0uEZASOQKgRSdQ99mtJD4oYAC27IXyhL1Y8mJXZgiNjJ5fuqUwLmRhth9xAMETsXfL5sozH0QCOw0QhMe+MDxh55wQkbbcED0xMB/uaD/7uxKmYRLlpBK2EkKmFhrHU4Qq3SaTKEFbh2ZLDeVpdojsChoIxEtA7LflbpDMGmyALUgruixXxihR+8Kwa98cgaHmIFIwomiiGxAUQSYkBBxAd+6cWj3OElNGVcfhHKCEIkCk/upICEQqahiO1SvgjFdOBFpgKVQ5Mi45jCviJHX5b1pkmJS+RR9nnDg5CR1CLVhMcI5OJLmpAIzOARCxgMQhEfSLBDF09gXgQFElUS5td7EQCp8cQDkEQIKx7mF5tx+olfhSadqAImIWUsUBcmhKeA5ViRMCRgrjllSNw+7iIQEsKAIU3qxIxT7PPISGQtwZfhAvsEBlUXijwSsABETmpKR7MQNF3JCARStVzMiGQdpBEwAilBIB3poSRuKCV52kgKEWAn17znr+C+ag6apdCsTVUQAX28DOWX7hnq82UVBM1qRiCFCEwfNw1rq75vn0KbNpVWCLAkjhctlVmBU9F6W2tskQ5akDNEi3yErOER0qSwJheHEiSswFlva8eHdFCDFGChLvtah4c1vDyiIHfo8NBkDU+Jx4A1Hg0kBiqyzxt9ippRrUSRDmrSwQWWRbugS1PkAjZFhSChoEPY+KIZ6SQ8yhQQ0UWRwcCRRaJEQZlHdoplUZxxGkI5ymM4YMWoQHxIIYlGCR6JQfhgHyHNUKQTmlRCRlQC5gpSExpBmtjJhVPS0bxjJ5hCgj7BICF33PEw10h4YMRolrO1SgCGSFTBV0SllSD4MAtWPFQUAO+4cZoEA2joqBCD9OMusEyoyJU+KYAYmhRACDniRRu+4rMjs36nMwL+bFk6z45jMwIpR0B38myi2bSjhwI3xF3I7BvaxPQ8vOoQ0EEzf9Gs6hAux/IB/QfTO3fKeDYQrfiubTma7jICRiC1CLB1iJNlPY8fmFqztpYOCPDJjy5dumidzOqah3pYIRMhEjiC+JpcYSOhKxRxCoxlLc3ym/U2q2v4IFnmLQuoBQksgNbkkX3OQyV26DCQChJoBdEc8kJTNETwGK+ESGSELnmJu0Mog+qSDgqEQb4INZaQqCCRTYZQQUL8xCAhOsiRBBDImmShKkgklKhZzFMUuYsdmsMjvdghBvKlyYN9jMujiBV55E2c2vMi0MRZBBoF+yH3QGdgEB4E+zA7CGkqXzTljlHBvmYZBSQSis6TO95MKCUkiE48u6ivRAFGRmETdxS9wywoTuwECQrYlMdgX8EwXAkqKrmSQewH41SAPfzwCEAZaSz6PNRxoRlEAWBVcAQIFCnTFOEVUi7S8z+MgBEwAkYgPRBIFR2cXvRQnBuCfUgPqB1FmQgwR/6iWZnoVGWHNhBxvmzmxFHewlCVSNu2EUhGQFuHfLIsGZdsaS9cvo5UkhbhIbnANUiiJktuHpbWKlpyywJraSpIeOs4FXWKVunS0baawALQCxXCyly9NEW1aCAeoQzETUSGEls8RKxQVwzosKSPFvzFW07ooolB5NKRcb2xrERoEgaxxZkOjEAZKMjIX+JEEiERg0JCQkUgyJS2+cgjEh5FKDv4QiI7vBUDKYuYoIkpmkkxQHOgoIGMIirxTZIEuEJGigFGQ3tegkcUiBwh9qmTr5CPu4sALD5+hS/GagoCz4IkBCzLvIUA+gKTCHHEpMsCsKOAR0UIngqAXiTMCyGpSxJ6cQGGKsG+3CHEPgZJkIKQB325EKGjicYCYWAtBIB+SBm56ggZS4KYVQkewTZkFNwVKfkfaYwAS6o0js6hGQEjkL4IpBE9xJl/7jwGKhgHc0Pp+5NZPzIzROvjUX0trh/S98tw6Q1E1Ye7PeU2AnzPXluHTMtm5Q+BXdlHFvYgNa2HqbA21hI9LMshKYJEa2lW49EKP7EtRWvysOTGQrQsL76sh4EsyHkCEYACNAcLe9nnjU7Y8UEYsAasyTEuHeIRBYAFlHlQUDwhBiQKAAm+ojV/8bYdBRyaeJdO4CYUBgZFCtAkeEbFN+aQURQSdy0V77tRRpiiMBA+QvWonWC7iJlCVNhBgRJXIEJRNvJOb0CAegiJMIJH5ATJg2viEQIIKVgGKyxQkUd0FKEUeOMucD0SEiE6+AJzoSSbGEeOPk2ypo4E+0gwriyQUKiryXCxaWryZhT2CT5eCCD8zIQwdiQhDGGi3HljIdinyYM1JMQgswpJHoMQdxhEKPzj3jUKeXBBbyCzFIY8osPPQzmGlDUqbtB1I2AEjIARyA4E0oUe0n2QbOo2N5RxPywzRJtlyjhfxu6hsIHId1Rvllmw01xDAG4o11LOwXxZFbNcZwHMklgcQbTATuy7Qc4KmRLpJEgH5EEi0kGIMRwdCqtrESLUEcIuIdFiW2tsXDCQ5T1ChiCUBPssywMNQUUsg8xijYp4kMhNcYEWocgyb7EMktDEAsZ5pFA8KPKCdwllHx3CjnIsPukmOWrET8o8dFGIAeV4YZQKLAO98S0nSBiOQggAg3jHOGoCgbEYDFwMynLNWx6RiN2QFwwGa0ioYxCcsawmCkGHXoUXEV6JMFDDMg86BFCSvcKIAiBldCjinqJa8dkr1XlHQS6LFCgiWfCoUVhGEjAhSOaXpgJAX5ijTxjChDS1zyggLPsoxEuSR7oASkNwKhyiH14xnRdXoC4F5Sj7gS6kFwnp8EMKmEtT2aHgYgSMgBEwAtmEQFrQQ2yA5FshcEPrln/lfUOZ+POKGKJfHOvPmVXn3HG+jN1DeJwxeQxv31FdneDbV24iwNYhJX7IGUNyE4Hszjp+FkOL5Hi+EUsS2wAS1vwiLNDUalmkA+ttLcWRY4qVOYt8hqggwVTSBhOtwDVQo3iLZSgaVvyPYAdlOBRMScIb1oCmiBU1i1iGBCNDb5xlQIGYeROegoRloKJCF6YoCMV/FXXE/kGO2CTIYIpOXCg7LMTpLbpQAy5cKAyaPCjLKV6IHBpCRVxMxEokwpAQTeJBgh0NV/AyzluzgBrD8RKID7mgKYjURAFroSAU4BLKFL5wgS8VeUdIrx74F6UjX9JUzASJkCFY5kEIIHFMyJdmvGBKkCrCpF8XmkKMaQ1kEzpYVgwSoqPhegtSAOFRIaq4goQhR5rYD83gkVCFCf+mMDxYKzLqfxgBI2AEjEAaIBD/w8zGhZMW9NCwYcOIHm7Id1Fv3Cymw6iLT+pshqiaJ+KMG0bAEL356B3cQOQ7qqsZfLvLNQR0rGznPVr5UursnvqCggIS1JI48AI0I14gsVGIOo8Ka3LWyWqyMteSm7U6jxb2krCQZr2NBR4tqjEFEwERoLG88YVOIEdYwIc1P2tyUQ+4Y6DCkwLW5IIm1rBAE2ESy8AoWdbyXsGjScwhJAJmFJGI9YhIB46Prb/vhoHoKADeYjrQoWCKomB4i/ggR0nUha9QkAuQoIBfnMbPN5ERdigKiQppApSKhgtPGYk22iToJymEZAUyQkCIhyTYiV/woq9ceNOFKQqWBbKQoQuJ3JEyj+JBQoUAiFk6oKd8ZVOmIouxggumjBIipIJEARCPEABzNGlGUSUwxB1CYc5wxcObgXG+SWYxiB3V0ZEFNbFJU3OqMICCpgyK00SBJo/m9NyLr9Qf2qO4XYyAETACRiDrENj89BDHyjjt37lDvrmhTP91mSGq/hnUJ8weHBJd2sVlKD5iVv1TYI+5gEDghpq3P8xbh7J1xgsLC4cPH16nQ2FYSMMLsMgX88LiX0wE6YcluqBg5cxCWjSBxgZqQAoIGcLSXct+hKzhWYRr3S5uQmSE2BAkWNMSnTcsQKCNRFXIbHjjLon4wEJgGVDDApaR4EURIqQSQqIe3KGDPk0qvCXHoGgCBiIRLBiUAl2KMBjHAr3yEv0j4Y6UGSgdvQMgKMgdFWxiEIiIWfnSxUMM6McRiA9nINQPA6E8VBgSmA65EybYEdWCmqxhR9QSEgVJBVNEGy8kFSQMVMp4UQmVoIMClikhZQIgKZE1EpIsHqUTJAEB9HFEQYL3KKTizVYIqSNMKgyRfXKkIgSCEBf8tBQVCgyXd+wTNvqhIBGDKWXkRIKd2e+/HXRcMQJGwAgYgfRB4M2po1MSzGamh3TlEJnUE7ozAABAAElEQVT8rv+BKcnHRjYvAhefefjAgQN9yqzaZoEjZqcOHY67ey7ox3vZlz3NEFUb+HaUIwjoS/bsG5rz/mxzQ9k96XwfY8yYUSzCWRuHIuZFq3Qt4LVgVh016AbW/OIvaDJWxIS6UAurepmVvjRZb4tlUBMvjEXCWzQHwxUARuRR73gMKGOHZX9QELskdxAfKGOZppgL1IhBHqUTNWIFfRRCV+A7ggoeQ5FTAlChyaMA4sSHjBAGFYyTjpgLRime4A6JUoYhEgh0oR+KPOKCgjV5hNpAwqNRuAhhU2E4D3Z4yA4dStxjERWS4IOUHWpU5EtvAo5LSBmdwMVgEAm7n+Bc0Be9FblL3OEdAAcBfi0UxRPVihmxuEQIqJc3BvEVLwpJIKiuNx4Jg6gUgIYgjH6ixfvXlDgK2BTCqJEaICCRHQ0ULHH7yPm6XxglNb+NgBEwAkYgaxDYnPRQ4IaGDz2tsGubrME0pxNZumDIgL5cIwVDtOlHH3MayQonD0PEaRfuqH7pnqEM8iVEFUbOikZgwwjADd058O9n3Hwp3JCPlW0Yr0zW0P+zWFrDAoQVMkv9INEKnxRZXWuBjRoPK2qxHsoeiXr1ZqGOguoosESnaNOH1tjaThLGoqADVuiIRlEAGMFUYBmCwSARAaQm9VC0wpcvxRl4E3Si+Iu/QaYmliNJjCaAR4CGABaMY4chFJE7SEKCskMXkhAA1gIrEQ1LFLgP7CskKRT3RP/EplLmjWvcRUEm9l5hWd4ZpXxF5SBHGQkPeDKKEmJAh+GkoLHEWZL1oFfuBCDDcaGChBQYFYTSQYGHUfRqQjUEZIgHFwqAt3qLzBX/I45wUWDFmAcE0KVLQFHHMmEg4cEpBeNyGkJCiATvFIET1RJ1PFJkjeFELrkMUsd+JEkQkRhEH4l+n3H7jbetEbccjXExAkbACBiBbEFgs9FDgRviU2XmhrLl55TIY+G7I+66DIaIv4bNqrzSOBl2NLBw5RIiGCIuIVr25b5pHKxDMwIZg4C4odOuvfHD0Qv4V8xbhzJm5jY5UBbYrJm1QtbyW+wAK2c18aBKkLBgZi1NMxTW4Sywk5gILbkhI3SaCSMMFB+EO2gFuoJxmZIj3piCWAlFcpqKjQoxq6iiN1GJ+FATQkHbRtCMEkxcqISQCiFhU2pUkFBHB4IABQqmYDoIUq7pjW9yQYdc6EU/MBrokDIPdijI8Y4CFlCmiQIpS0FN6rLPW9yEQKYpMCNDxYUg6RXTIaeYVRPLFL2VpjJCgUTAnIGKCjWCxz4DyUjuECKhLsyFQGRx/YJOUsEmdsgiBIAFmhQi5AFDRmkgyjyad0m020t1vdEnctVDGAElzNIbLxikF+goikHIYIHEKcGFBpJjPGXG0qQEj4IlEhUXdRW3/E8jYASMgBHIHgQ2Dz3E39GxuwQU/Rn77PkpxTNZ+O6FZ5+CoF+/6MSTSzUgwML10ufn4UgM0ffzjvIps2qA3S6yFQGIoZtPuua5G8bCCr1yz/2kaW4oW+e6/LzCwhtOgWUzj9bbjFIFaoMKDwtm3lp+q4shFCTiOFi0U+JLbhbhCMNKW1RLfP0fDSh2pBW+iA/V5SVazBd/xivOMsTd4UIxMAQ5dSSiABQkXqgg1yjFECIJFYYTJDFQIQYsUA8pIxQ3JJs0eSj44iFOjMMNMUQGEUJ8YIQmdZDUrh/q8SJSTKaQM5whoYkvLFOYKeoha0WlJr3yqDfDBaCYOJryKJvYp6jOW5MSl1DHOO6kgykCUAzU8Ugdg8GmMFGTCElZfrEjoSBVbAhFgSlCmhQMMkoSOZU86GBWIeEdBQUgVNFEgoJ80aSOCwrDg1P9jCNpccFISBCZGMy492JF/9MIGAEjYASyCoHNQw/pqwdcR+3P2GfVrymWTJ+O9bjjk0vH2SYWE7tatQhoBXv5EfmQRNxDtOK7tlXrz9aNQDYiADHEgbI+vzifq9/ZlNeyU3dzQ9k4z+XlpGUwS27W0tTVZIGtNbYkohXCAjviBYrPH1FRU/roQDrAAoSCBbpEE1BnoY41dFiTs7ODJkKMRGv+BPdBE+qEJqOQExX2GUJdLqhDQ2AhxBN8qYKcFT46FOqR/QSpwUC5Q45ZuUNCQYeCU1UQBqaDUdGWltg2n3gAhIQpCkIiVFGc1IMEg4pB9hkCbcEQNTVKWYedPiCGnYCbNJHII2NLFoTBNaBRD9OqfIkBO8GpFLCDhLFAGp+FYA0MSQQdcKAwikf1kCAVECMpTQoKNHGHX7kTzuKDkEiIO/IVHyQJdhAiIX4VuZMddGhSopASJxBDAAjlNCATXNAFtjIoQNCklwdlJLJJE7+q683FQ5EzFyNgBIyAEchSBDYDPRT4Al9HnaU/qkRaSxcUdqira6p9CVF1TjTrWB00gyR69ob/miGqTvDtK9MR4AtlgzsN4vNk7MWb/vaHfBOQq9/NDWX6tFYq/vlL12kJHZbErJZ5wpJbctb5LKEpKIeuuCOxAJJoyS2OgzoPRnhTopV5jGpBDh/Buh2hChLs00QuNkFcQ5xsQiG+yUXuQnhFhhKhYkRsCEIsR8YTO1NCghLCO4g4oClNmkTFcHlnVLwQWAhAcgZGNhNUiMImKpoiPqgjxIvcMQT7glSACByEbGhSYSyP+JeIB0lgEsJGR7MQqRXfzhMJE6OUCzbFKyFXeAwvWY9ExQqqhKbIHaWDkPiDBZqKGRdibZAIMSTKlzfpKE7po0mEpAywGI/sF7NpCElW9oW5gkeiScGCsqCign16A8JBGDlNbN0SDhrOG1oq/Ipoarhy1FiENF0yGoFUfckoo0Fw8EYgpxAYM2bMJuZb3fRQ/FiZrxzaxMlL9+EL39U11doslu7RZlF8rGZZ3IokurjXwY9dNplFbxbl51SMQIoR0FEyiKHpby2ED2rTuYBPAc6dMp46V7+n2JnNpTcCO9WuoTW5wmTNTGF1zUpeBXaANTNLbrrUq66I1yjeasTCnnW+ltzooE8vJdJJkE0IUYhTLXGnWrcHj5G1BOsR7KMMoQBfQBemeNPEODbpCu4IAEkgRwgACY9iUDooULCvmCUs9U1UKspa7lQnsBBAcKeUGUIvQpzimiI57tSUzTi7JAWGEJUgkhf0kWh3lcChSUV8DQY1UF24ozAkjKIpa1QwKLaFOsOxE5kqPnQmBQnlDgmFHCnyiAX00cEpdaIV7HJBFxL08Y6EN+FRqKsZNRK7q+iKFwWPJB6hFOhSSHSpBB0qPDgVAnpLiGbwCMiaboQCjZ8xYaNJoSvMgiSkEKglFCTkXmo2EKkeDXNJVwQ2fYmYrpk5LiNgBKoWgZpVa76EdTEFPdrU9rGyEthkoyBxCdGpA3/HlrEhQ4ZkY4bpmxMkEQ+nzDgdQ5Sv/fNFvszduls76kcNPiZ9487LK+fWpDUrGpYT+dqV5fWWHMg13iWFmSKpWeuryoa6Ra1FFRxSc5vyNGvW+rqCdtJZDUqI8Ebe8jSfJGvRocsB/Yfm/TxvxuQx7BhKNAebGErn6au62Ng9dGIx9cOCmcWzFvksp1kSa52PhCU3JaJUEvtotMjnzYqaNXZQwAJbSOLECvos4zF7RIKX0TJbFANMhIghhkNeMFCFIbIPQcBYyo+ShDUkUqAi7wqVZsRHJG4UCkPoCu5wRFEzCgz7cBCJrNmEgqbs8NZwKiQYTEXGa0d8DXYUQARLk0hHDAVChhAzQ5IKEhkHWIGMX0mSNBFSZF9vISOn9DILk4tZp7hHDGI8Cqn4Qh8kNBFSIVMlrklEQsEyhIhA0EAJmZeQtTwiDxLqgVihTgz6GYSsGaI6FaWAO/wyy1FX7GfGQCQqIcIgQZOoaAYJTR65Y8pCUQw0NRGkrB8GCYIbdpQpY8lXeDJN+j3ICDaJWfZxF2a5e/fuKCiL4M4VI2AEjIARyBoEqpUegiPgMhpfOZQ1v56KJMIlRDpiVlBQUFjov4evCGap1AkkEUbhiVgJU4Eq4n3A6YfyprTdtz3v9r12S7Q25lWS0EnicUolbspiZ2ZOHFV2EAvK7qJnvV6W+uUqb7iT/SMbVqqkxqcfTarkiIxRh1Uh1i23+qaCEYusLEdZv8xSFSr7cxUZhKnp46bNnPAxFbFCLTsd1vvnBa8NH8bD1JCCdwyVCnjuCNk9RLKsflkSay2tlbDWyWGRjA6rd+3fibMAcZoAHVmgEtbz1NHnoStOOsgLRAlC1Vnts0SnaIVPBbkW7ZE0VnCKfXphHMIKPzAjVFSoELOM6y1eRmOlhoWIukrsdUIHoTxKX3ZEUanOWPEpaAIRRbAwCnAkITyKqBDsyB0SKjyKQfbRV8rgI+6mpPfIVqwwUENkVlngizCwhlDsD9boEubqDRmF6UMi3gTzIu+oSI0ggxo6cQQwqxRQVgy8KUwEavQSCUV26FIvXQpDHlGWAnEqHXqFHnKMKIsgkU74YYj6QRhGMUQB4y5OBqm+07Jo1mSNN780UZOKQe6ioIsLcgKOQKiT57/qK0bF/zQCRsAIpBcC4z96MyUBVR89FI6V+cqhlMxcxhj5f/bOBE6n6o3jg+yEsmYrSwlliciSLUqLUqJFaadVWqi/Nm1SIrKHEmknZUvIFiFrkl1C9n2n+H/v/b3v43pnjGFmGDPn+dzPnXOe82znuXfemef3nnvunvUvtXpwxowZLBxz8NCZumqARLjWGfAF3ATgQyARfGvQZoUR57jQf4dyRBdLQOxDWEN0FyfFYVPhk5KPEJY6jxpF8OPfTX4rU2IF9WJIGDfhgV1H+TEiccE786horK0Yb2ChohF63GC6XXHNheYqJ7+LEjFl1409A/yF4o0Kwb9TIDVBotIW+KKan5oZjlAMq/lV5yMAh4rayn6r+Q2gYVS4iVygAiEPH10ZtwpfdTt8cajS5ZGGJNHCBaMmiSMOBEAWhCMohuCMtHJE0xRYMGqPh0+hqK5nIbxASTOiK+/mlwbC3mj4+Tu6LFkSB0xBSARdFAVhKAYUg6iEmObO4mRqRoqBLGEKrEeECoSjICk8xEydqJReSwjWRBKmzRSUDcuAZJQoBJCE5JEGo8KGxKerq6CE08UOARiOgxgCQnO8LPlrr+SRG4AuKsoAQbIOK8TxVzYxFxGSyNBFWAaPuc18LfxaSMIoMQVxxh0B5A1fYpjYYYKoyCANTTDogingF1NIyo73w5HLgMuAy4DLQLLLwOmDh/RYGW+yd1sOJbu76EQTWjn62Wefbdq0KRBh8D/vE6m58UTJADVw9DI4xto+jqtvgoX98TCdk4KNjmckLumIJx4Uo4sY8xBPzCjGhMfoPRZmjIHFIn+yQ8Ere7K6Jh/3Sx/9ukfnYDaOl/iYC9TEwjmmEf0X4Zjh09Jhewj3qXhaMh0nJ1wLVcicIVX1VrfDUcktCECSYsKBKKolQBFOeU8RzmoOkdX8BhzAV6FuduBYSS8Xwa5MGQfQYWivt3/5YfCNN9RnQcf6vdlCnvwfiGF2+aL5E6Ny1yiRFx5dLBgEwKQkQwzEphhwCtAgggPhBS0kGRKooQAYAtmB7IEmycAxKARwQat1BExIwMtDGJggBgE9mBKEBEdgCt451IYpAc9fIMnRV1chDxFw0KPN2hvz1RHDOOFxEIxmpPBsjsxXGUBFAnKn64IiLhQhCZSAb97LkhFi3AZKr6kggIoIC5ZwIpEwQ+SQIWVP8dttgy+LGUkyY9iQFInQHrWDw2Rxp4U/ipl4sKYJYo30imjjVBPUjOhCcIgBQl1JwIIy7HEduQy4DLgMuAwkrwycJnhIj5WROrflUPK6f+I6G73FzC0gimu+TrtcjHVyjMzEDi0+uEmiwiURWElEN7HTEkf7cYROTsraMThLHDWPFTsjN9KxIbiey0BcM6AiWcCEdKiEqZAhK9Gplq2SlwwFc5Coq5G5IbymBmhAZmmo5MY+KjA9QMTfuRl51eTYQQwBI/gEoDUgxjywb++Pg3vS/eKLLyZOmdb58zEX5sqEKewLiVj1y5BnW7ZE4KvCRZq/M+Dy4hcyEQ4EVPArEk0KjqcYhrdoEwBAhlAbSRqogU0cQQZt0GYiSgLCmggpkoA4nCVAeAgrgZIU6LDefym7FqcgYxkmZpjCSixCPMogkgSvWTBK2MSGigTgIyZSzpFXPtVVAAgIJbE5oohZMkBsRiH74T22xYepCWJBS6XgKySmoMAQICrvCC8oQxgZm04wSGExBIawBzaFjWBWkUuALgbJMGJY05RhYhNSSJzNBe6AjayrzCgG5E3dUw6T3IV73k8cyX7QY1DAtV0GXAZcBlwGzmwGeGQnngGcDngIbKh3b29zXJYOxTNcp362ZoC3mLV6sMGdM9we1WfrFTxdcccHSoiP7uman/PjMuAycBZkgBpYBTaxqnSHQ0lMQQ5xVnVtRThMamkEJEzFTldMKnAIFXFoMGpVOmW52VSJjhHBCphCV46wYGtAkPcepFo207Pr07o1q4b3fqN9+/ZYQMtzsTuqR98BGl27akXnpxr3HsZebOnFwbucKiSYHicASIGh4EJYiRAEBYxxDo1q0Yq6moWsWRJsjtLCiwToKgPYMWKaLITBo5JgGUAYLZggRDCN8ALJILiSFtHAATphapZDyegSaI6oYBOxCGt0I+aILnPUJaCNFoQMpJzQIGx84REOUA7yip9n64IrnuRUGQ7FHH5qD3VUYCpIbGINDu7wxcGFUBIYoqH7QZcPjqx5uoEn0aSuICWApEwpfrpGRIVNiEjwBRyGF3nkLHnccWBKZ4QZ0kzNjmu4DLgMuAy4DJzxDJQr520DGk86fS+2BxtyS4fiebXObvWNc3nEDKCQR8zO7om46F0GXAZcBlwGkmkGKJIpg1VUUwOrZqZ+hmOAgnANODRU1UteBTP1tq3vwAK6wgVIGF2IkhsOlsUxeUzBxIvKdc++D3xQtNOACfrAIhrkafy3YZGnHyZ2TVq/3tubX1Ghsm7t3+HBqG2b1m1fOR/vCgAZCwkZmHSxL3d0iQEXXpzh7YQ0QZ21jsk4yBC5ulhTEgjAODSwH/QIkuJx/LVOChKn8kgDa0ogisqwF6G/Ebii5UzX7NMVdkYD7/CF9SjDnJmIp+LPUdcLdSVZXUbxKIM6M0dxlBPEFD+jEvAuig9vySNiQtM8T35KwbPwohjE5IwYc/GmA0SYxYtBMgqSUe8ILzHz5LN4eA3C0pJHAoDDoUgQkx3NRfmXR2REqENaCaW2zvKIIgEwZS3dklngLYg2h/zCMY/W8IQcuQy4DLgMuAwkowwkOjxkS4ccNpSMbptTmsqe9TxiBqipXahOyYRTchlwGXAZcBlwGUjEDFh1TQFMdW01My6ppVWWmwy1PRQswkENgBXgWz1Pw0NeAjU/lq2e91AGfycXmDRwx/l4pCrde+CIxUPLlkWIffn9aHHwnjPtgZ1bNwcF5i70XthHqDhSSDTABTiIgXggOGA0dNXGXXBqHtenIA4iDjELbqBLAy0tQhE4InekhQYH9oEhbJpiooh3Dnm0DNNA0pgIQMSshtwRJzKKXNZkHJCILiQBAkDRrMHHCF1QDzJmHuHjzsAdZJgvORdaxCg2tVCIi2UT9Lz4F86mg0F0g3eCkoxxMoA1eUSGOBGDaEtF84UjGe9u8fEgdUOTCuTcUw4QAsijpdkxa7pYhnSGI5JHxJgLCZR9DTHriDAQMMWQvvvhMuAy4DLgMpDsMpDo8JB7rCzZ3TPxmNCmec+0uHvOnDluAVE8kuhUXQZcBlwGXAZOUwYoianeIavJqaXV5qw6nzqcslld1dheke/X85T9wnQUrlXpdOGDMlCEq2KXAOrYlH1BD3Bs/QjWgC3U/X1JuMSXJu+gHDMSReletP+YtUWIHNq5Ee+CSMwjXcUQtuH9FBoiO0wWGU3BjHu4Q3jZFExBDChKQF7oekkILCwCekCL2VmKZFaJUkhYwCOHwRBwEIDDWQnBMl348sgZL4qB5GAfSXWlxagEPAUf+mFGkCzQUM7NOK5RJFrFr5CQAUChzYELHDHKGaYn7+9nBAeyALyOkuDfCeoiqYQTkgiDQn9C/fAPZmGkGIgKX8SJC1TkTolS8DAhRag2MkSOR3SNlHaMyAJ8fAkMkh3OcLBAorAGeQH4c8TOB9+4BeCWS9dwGXAZcBlIhhlIXHiIpUPkrGyJgm7pUDK8d05pSjUvPRc9t4DolJLnlFwGXAZcBlwGEjcD1MYqiSm8KY8poTnTppyG6KqoFgeAQAsukEHAsB6vnPYLcspsCQgOUL2tIhwOpTtiERW40AqYUpE7anVMmTW6q1Yuj0jErOnT9uzcgQyV/MiRIyNGt+0+gJZBXYpBa0YsBpjoIsa8sIMwXeLXFDAIU/HAYS6su0FAJD5JwFqOVHs3bNhw5MgRhsw4bQwyO0hTllnAC6VFzEuz7i0QtaFMziNkWBwsYNzLgA824VG+EBCpSxsBLgEkRRro6sIxNUbpctAwC0qXgB60OOTOsxIAd5BXTrDGFBQ5Z9rEj0GjiABsCsgQgzLAfCGGmALTocHs1PAC8BdDEYZkZIEAFLMumTfmh6ergFmNoqv7UAJ4pKvIZQe+8kDkXD4EvBj8Rxd1e8DBBW2ICWJWedaU6b7fqulTbd+SfXd2GXAZcBlwGUh+GUjcram1dKhy6cLJL3FuRqeYgT3rgQu1gMi9zvkUc+jUXAZcBlwGXAYSIQOsbOUPE/UzJTGFt0piFf8q++XTinAJwAwCH4yGxMIVvmkhz0GhjrzBEHRVossaFTgWEIBUpdNARngBkjC/mrd997Zjnh2Ti29G/NTszkYE/OXQ78Wxcx4fh5IRmMgoZsEKigG+kAsaSIIslDV9vwETSalgAYjBi9NP1Ljp8/t9M2rL8nndVi3bsnGd9GrfdPuAHp1Q4fCAmMCb0eAojSyG+mri/F/Hj/rj93nb1ixbt+6obvXHOgFtWJJlE79w0MWgwBrxwTUgZQzjuoIKEkkjmy9MLeSRfcJAxSxI3gsyfBHxiHyQGCUAKdIWEYDaOtus6ZIrywDC6nJWzIRBtBBnC9Lr+nnzBnziPsQpwShszgZIoRXC+8Jvc6OrBLJVNgEoJNlBGCPe5fNhJjGJBMwoeKujQszEQHrlUZIRXTHd2WXAZcBlwGUgGWQgEeEhLR1yO1Ing7skYafwXNPaTV8a4F5yn7BZddZcBlwGXAZcBuKTAbChpk2bssdzlhI1vJI4sB+NqnoV2MEiHA5dzoYl0VbZTyQMQXCEYlh1TVl+QsKjdGWf+lwusEYBf3DRrzFa2DJnVNSdjUaPHr1l3ZoIgRU7U8GxkDAYIxHkj/PWTpgwYf+W1bkzHhmWt1CGoleVuaSIcBC8EzxnEdZWrVrV4e1Bk0YPM0goaHb8D1/PefieK8qVk4q8I6BJDZ66ausvg658YJhBQhG69W+7Z32ucoJLZEHgi9pKEYHJoOEsMsKosBL5gsmUBcRIAKwHeEujOqOCDMbpQvJCQ6OEYfIawjXgC4TZHqPmzZk188JUGyZkyFCkSJESFWuuOJSDIWzKmifnxxBqhN9choA8cia3eDGyqOAwSmyypgU+FoxuDLAtDjGR16vT6NpdhzUEbNbImC8FAEcW5A55IW6KHxlT4cYwsx7XkcuAy4DLgMtAEshA2hypo3zQP56xJCI8pKVD7rGyeF6h5Kde44qibgFR8rusbkYuAy4DLgPJIAPU/NVLxAAQMDWV68cU4Zm9ipqiPaJaNiRCNT+vmYWjBReq8ynpKc5VcmMZfIFRunKBsIFBVtKbCxZ39P5xTIypnjR+TIs9u77r0z76qB71kguNErZhPcwaKCHTv9sbt+w6bchHEepFr7i63bvdo6KyKzxG1+0+vPjH/m+NHTlz5swI4YhulixZbMryfvjw4f79+382ZOSy30+ge0WhLMV99A1FSDgFZ3ICEb/gHrpfzt12Zbbt27Zlz5Yt25xNoW0TtNBG10KXSXgHujCZL6SQZFDW4MidNxx2SgMVPIqDvHAWLsrSpUvbt2//008/eWMBqtP4oUaPvRwVlRqnAmLMvknpNsCywkNMcyQAQUXWRQZSF6dasiQtuzHMLA0sMEG0dNeJgzsqB661jDNqIWnKTNA8iqOuXINLCg4LOnJtlwGXAZcBl4EkkoFfp/+aNnsCbByUWPCQLR1KIvlyYSSpDLgFREnqcrhgXAZcBlwGXAaUgbyZjlhJDMdWiATzY1V3qKjOEirCKcUpvCHOKt1pB2t+Pcsj6IeqHnUr0elyBHEHeQSGAOaQWbmrX/jw89OPwkOFCxdmCY+EOf/vwVvX/73CutbIdmgzRggAF5DhArSJAShh+cSv/tf2pQP795mKNZbPmvR0s5vf6/f1NSVzkxws/NDjlZFfDTCBiEb6DBnPy5E9feZsZSpV33lu8eAcmcLQD18ZMCA23UxZs2fKmq3G1dWLFy+OZYUtaIYuSSaBTASzixcvZiXyqFGjggFkyZqtYo26zdu8dcNFmZRPzsH5ejaPxUGE9RBbwVQbi2fMMHmLt0liUIauWdDtwUWZvf7w4A9e+2rQx0Hv1h73Vd+Me9ff17br3I1psQ+gI2I6LGvyrqmPKtpFZ9SDb3xSkkOd8A/kidBuTrpkQARf0dLgEEmSrsAp3ULMAg4BQIb1mDvNkSFxsGmEFvIyZUzXcBlwGXAZcBlIUhm48sorZ8yYEc+QEgseckuH4nlhkrc6C4iYoNuBKHlfZTc7lwGXAZeBsy4D6/emUszUw2BDVPU0IKE5NCihQShU4VP2UzNrCMCC8t7AoPV+9S4kSAY5C+tB3kjlulXyEtAoXgTc0MX4XJ+L/PTpM3ft3GEW7n7qle8G9lo4N7QS569lke8sk+Q///yDX4zgQgtn4BvyMu77Lz98/TmzGb2xec2Kbh+8n+ONDgxVzbG9RUzYUN2b76h5w22ZC112W6nQDMFlIvC1oum3x4gNNWh0xz1NbrvssssmbAzpGjZBSpVzXQhiFuedzj1adIphndTuXTt+Hv7NVZdfnL5hi7K5U7FehimjS5INBzHjAmuObFjcvVOHSZOnCB07P3e+Wo0erNPkkaioVHKqC8TcabCIhgSWz33k/ieeH/f9V9FzZZzhw4dfXPnai6++BTBIAXjX1H9IzZAdOBYMQ7o3EBZT3jGo+4SG7IgvJkmWAGebIEMi4zDT4I3HaNAdXTwajqYGsQXdoS4LG/eFfkdCPtyPJJyBGYumVil1dRIO0IXmMuAykOQykCjwkFs6lOSuc9ILiOfLUmXM6XYgSnpXxkXkMuAy4DKQcjOwe9HkWRtqUEsbrkGBTZEMQAOp6qZIBiBAgLLfSm6qa+pthCFV73A8LX/n5iDWYyW3hGVfeAFm1TULkqFcN1gnuFgmXYaMF5Stdc/BjS+G4SHPfZhKlCixaFEILfprzXpikBF8KQYCBsD66pvvIrChu+66q0jFustXrZk2/DPDm5b8MmLdnnfyZU61a9eusIejP8ePH89iH8wGgQ/ckSKDIZCetjJm3SXnFC+YO2qRHw9JC4IjygB5xo4IaOaNN97s06fPUffRWm+//Xap0eNe7vJp2dyZ2GAbm0qpIDyuHYTlKUu3/fpxhx+HfBY0wD5K3/R4c9+WNevvewO+rbKhTQ61iOaRVv8LYkMZM2Z89NFHy5cvP2z89J+Gfb59S2jjcPaBatzoFiJXzrmvOOgK2VFImNV8aSiBdIPXXSuP4EheZwmgoiTTgDSEuh4EI2lw6OKdDLAhEfEzd91m3FSShyMXjKLIJSNLEOociFk8HtcnFC2AMM/9dBlwGXAZcBlIDhlIgOfToqfBLR2KnhPHicgAz5fB0QKiiCHXdRlwGXAZcBlwGThTGaDu1eIgauMgUSSLrMaGQ52sGltDtBkVYQcUACwAJmfV2CqqKbmD1TWQganQYIh6nsNkDNaZ+vf+wYMHh6SjoqrXu6lAtrQZyjYwTrDxUNuO+fIXEGf1yqUjlv4nXEATAQiA9qxd0umlJ02LJ7O+//77Dh063Fj/mhq33tdp0GgeXtMoS5b+3biM9sZ0BevVq2cqajRu3PjVTh/lTH8IZEG4AznEHRlgdkoR58PZY9b9Z+xHv609hClBJ5YQ8yILdGf989+jTz0TxIYuLlX2kdZvvNH7y19++aXbuBWXVfb+u4D+mD19/Nd9hHF4F8JPKXyySmBgIh+NW9z+wWsjsCFf1VefM8PaahCDB7Jkjlo09rPR3w6y0UIXl+457JdWrVqxqfndj7UeNHS0Df09bwryeCTnBAAxQV1WzuLApO3JhG8DqXMjcd1Bo2zukhG2xSh82ghgnKtJF4PMFDGY5N8UEeCgS/y6B3ChnMiCPBIqozg1QgVFyO5q2mxNbQKu4TLgMuAy4DKQzDKQ8PCQWzqUzG6RRJoOz5cd2ed9vcYCokRy4cy6DLgMuAy4DLgMnGwGKIkpklVOo0thrKqbtuptAR8q6RnioLC3LhU4ZKU+fMnQMBKGYl0gAzjmUTW5dUFbqPZRhzNx5Lf79h3dHuiBJjfDz5j53ArXNzVrapQsV6laxTJlLitt/DQ7/lJbIbFIhCD/90o7E2DPoLf7fnv4Au9FY8yCTY6GfNo9uLFRvcvzaxYN2vQsWbW+KdLYvHlz/86v33vzNb+NHYYioIPBEMJHsAkTSKJnz55X1Y7Uff3119978JrhPwxj42qsoSIiQuEg6uJ94bfvjfjum9BwVNQbb7wxbvQP1ze+77IrqqQ5v1CD4uesWjLfRgd+1ENrnXThSDKphrCTasvy9s1vinjhWq4LCgM2sRTIE0qXlSkU2jH7pnq1q9eq2+6DfroN9uzZw17UnoBPJUuWHP3dV/sz5OIyIbB/394ub7wYHozKk7+gtbl8ujfEISQBNCagWSMGMcptxnWHSRIUP3zvuviIGw0lWeklVLrSRQymOIQEMV9dOLU9ln9ja+URjoT+yAsricgSbXWlG+Sga9Z8S+7kMuAy4DLgMpB8MpAoD5eRHvfCsuRzjyTyTFhAlMgenHmXAZcBlwGXAZeBOGVA+6qo+lWFTNVNQQ7BFFRhwAdMCnIq8KOVvF/bw7H6GSNCJWjA5Ey1T/XOkz4ACgZYeBz/USCY2JQ67tRlSAE8/UU/r+VTgQIFqlev7hnMFNWq9YsPTf5h366jexJVa3g/WgULHoUn9q5dnLdCUWxS6gtHGD120vRfJoXtRbHzzlNNrileplKOQiUuz5P2zdGj16xZY6PgIJkyedMj/jmb0j3Vvs+WWcPffe2FHTuOOl2xYsUTTzzx1jvvNX746X/r3TxrQ1qbiNLoe0/X5t0+62YOf+GFY3RX/bWib7snRvTzdKvXu/mctJ6u0kU+CRi/8+bN696dd6iFiO8jmzVrJhkhLxuXzN251fvmSQSUxnr2Wvc8Z0gKcyelhw4e+KjtA4cOHAXaipYo/fhL7xUp4aFpvOVt7qJl5+XOz2KoJ557fNOaNTyL1vf91+rWrhkVVfTDDz8MTnnhwoVkplKNelHZC6Q7cuC3n4YE8bvSV1xFeBDB6yychRmBDTHEWVdZXUsXd5Q9uqh7g7Ahbhi6qAjZ8Vg+Kb0kAZsygsf6/jbYutYKAy/YkRGEdSczJHdISpczYrjQQ2d4EIfG6tWr5dGdXQZcBlwGXAaSZQYSfvUQf4lbNKqWLJPlJpWwGeD5snI+TZw4MWEtO2suAy4DLgMuAy4DJ5WBGjVq8BepStWqqpDRpUHVbVU6HGEQZlYlN8U2ZTYFtgp4RuFQwEMIAARAsikBYT0wqdUxGMSSPFH/VWKhhr/URRbgTJ069a/lSzXEueEdzWZv9DYJxvjVRc99+/2uNpSvQOF6devhrlChQsbctnoR2IR3hEMa2NXbWyeCls6bPuOHAX379g1iQ8i0a+etM/LxHW++QAkHS9zY8/tptzZ7lGVHQSPr1qzq8mqrp2+/eszQz6av8R4ZExLBxG2+N95447Rp09ivJ0bd5rdezQNchw4dYmrKuWL+5puj64bqNGhc+roQNsSkyDn0UuePg5HQ7t27z+I1G4WD0NWFmDh29NpVR9/vdukVVZ/q8i3YEKOex82p8hUufmvJTF8PGRZMwuYDqTZu3BjEp8zX9Iljpg/rP/n7z4LYULZs2d584Ski9w7/UpIB7NMle1wdBQOT4Hk6TF1sMmVvbZf/OJi5oAvJDmddAkxxj2GKLna4oyA4HHQRE76DcdAi2UcGPioYFIcz6sFnygQ/wcSOiAbyxGmc0ID74TLgMuAy4DKQvDKQwPCQnixzS4eS102SWLPR82W8gc89X5ZYKXZ2XQZcBlwGXAbinAG23aleo6YVwDSokCmJqaUhurSppTngiEnVrZqfFUYU2JTrjMLhQAB0CRXaRggYyZGhAHSRp0sBT1ujtDkwtWrbwRdfPPrUEkYurHmH3MngReVrd/jwoxy58lWq23D4sCGVCqTF1NrzKpm7VUv+oC0XNHqN+WP5ktDG1bxIvvxVNU0yeoNnuNJeVFnYEKNKAslZvDdrvQf/98eC3zt27Fis5OVBRYCVHm+98NCNV7buNCBPxiOoCDKz+WbNmvXaB/83cPzvT74Sqbtl3RrWFvHvwZvdPV3MogWoMWHyL3JRunTpPp29J7zIAHyIJM+fOWX+z0MlYOf9+/eN6d8B7yKyymXaMHuMCfCesje6DUyfMZMW+CDgPb2VKWrmusM/f3sM2JQnX8Hug4aZIjEUKVLEuhENsKGPP/54+YHsmIJIuwj73lz820CXGD5TwClXmRRJgItu8jQkKaZuPF0CM2UTpGHGjQnYhAvlP2T32B+oeM+U+WATYloNJ3VcS5EuSeZsYR9rw/VcBlwGXAZcBs58BuL/XE4Cw0Nu6dCZvynOtghmzJjhNqg+2y6ai9dlwGXAZSB5ZoACGJiAAlg1sEpiIUQUzwb0qOY3DsKU38AlkBQ5A0NQlovENKxHKADqwpuQEZaEBdXkcBgVYEEbxTfbvcqjW7LGudaNjTKfm52GuUP4rluuGzV5xovtu64+4q+lYRfhC0tkzJpNWhMnTV6z45CCxPiKGUchknsef/6mVwbe3e4Tdt4xF2pcVb3m119/fd999xGYrf3REB6ZINNcsD19kyZNOn464qHO35erfHXQAu/w+rzzSx+80pKlQIAOTEQTJGyBDunSpa/ToMnPP45475NIXfYz6v2Op1uvgLcECWLxjhoXXnjh79vS0SaBzAWbg8fOfrvVAxqNOPOKMf27jEdgGoCY4gVymgzbD61a/icXlNkJeeEqF8+4s0Ore+bPP7qNUaNGjUDcxo/90RS7du36008/NX/hTQAmY9JgPVT5ek06Dhp9QcmK4mOctAfXl8HhIBjyoCuIU5ITzLDdCYYHBb0E25iSEZg0lBMJ0NVtRpcA6IqDLwlzxr66OhMD0epWJwauMhwLBnnaunaeCUcuAy4DLgMuA8krAwkJD2npUPLKj5tN4mZA7y9jPb9bQJS4iXbWXQZcBlwGXAbilgEVxgIdpKH1KbRVFVNgaw2OynK6lNAclM1WdaNOjU0XXQQgK7BhUm/LgnyhKyxJMpIP6vbo0SP4qiysXd3kMQQIAEcGaqCIQRnheSJcXF80Vd1aNTz3Ud7WQpv/nGZhrPxrlficH7m9PudiV9Z5d8APYCK8HA3s44Xe3w+euLBN54GsG2LUm2b4BVhSnPDnBoAb0BbFgLuH6pfr+clnHQcOr1C1tmR0njhq6LWNH6iTd7+6xLB18wa9qgyOQrqjTrnm73q6tWtH6j7wwAOlsu3Hi9Hw4cNXLFpA3kjv1Tl3tXi5Y5v7b2ahkATYW7plp8HpAo+83d64yffjpoKPCPi4+upjMKyXHm7088COcyeN/m3WnOkTRj///PMVKlQI7sqE2WuatiTPuzb8LRds/FS8eHEgqvqNms2dNYPn/l7s+NEjr/d65ZOxC/9Y0KNrxzXnFOAqEyHEBDlwzVXWLIxDl0MZiJ5h8eWRew8BhLnuEG0OyLsNfC90dSeQExAimLoHuGq6KwiAa8QhCzKOCsJGCs+6NLCGPGJ289PQfRsUc22XAZcBlwGXgTOeAXYkjH8MCQkPuffZx/96pDQL9nxZ/BfCpbTUufm6DLgMuAy4DCRGBqzqVoNimIqaEpoiWVgPJTSVtip82t6x+2jBDB95SOU354iSW2W2LSxCEnUM4kJFOPKmyyhhjB8/3rMYppLVbqhfsTg9iYmNmHWFF4hfq1atsF7UypljNCkggwM7ws87RUV9OnExAXBAKw7m4P/LQlUa3lKjXKbMWaVLYJqFXGDk6/k73mpWp1SpUh+89nzRDDsEb4EacDSpWealLgMAeoqUrWqul/42oUOXbpo7Wzu3bFzn2RtKvffq88UyhHa2xgUZLnZpmafe9XQrVj6qO2HChJc6dCPmKtWOwjrPNK3/xIPN2j/eqFz5K0Z80sUcgQ2x3KnW1dWffG+gMYHGHr+vyY6Z3yr+mjVrVqwYWtqDDBsGdenSpddLD/dt1aD9cw9/8cUXwS2EELjrrrvyFriQhr3pjEfnJq/YBUep3piuYLpS1z3Q+IbypS8BMyIJSqatu9HEgdJIlJF0mRf55NB1twxLDKawHkGKQWTH7hPcoaULpDMcbirdY8GbQTZ1D1gYWDY+wcgFlwNCUvHo5oeJNYw7chlwGXAZcBlIahlYsHJegoSUYPCQe599glyPlGmkWvniLCByG1SnzKvvZu0y4DLgMpB0MqAyXiUxZ8pjlpxQ1askhhOEfhilnI4gmMjDZ+0G1uiq3qYBwfEWlYSLcJjiYxkXAAG442ykSPam9teHhLmNHnpa8WANUxyAAtTtCh4pLZMhDBrZSx6FVIYNHyEbmC1eKG/YXtTk7wbCkS+YggZwIY4QB/jq6rxm+Z+7du4ARgFMaXhtzZ2/DSmd4xABWAy7cpZp3fWLV9t3Ni/jv+zD3BEYNtXTBbL5ZcQX1WrUHDJkCDtYa9YYx12WwmXadvuic+ejukMH9WHo6cebmzUav/0yfvr06dgxZvbzc4IN/Zu3DJfp9rqVnnu7uw3R+OCVpx9t+ez69evTpEnT4vUeCAdHY2lfVOdeRrmOufMVMLHPvvyWNlliRrgDD1LGuBAkkGh1aXR9kfSmFt4rGiZadLEp+AYLdt2RhJDBDgKYRRJcCRey5un6y4gQoA1xljuvox3E/Q2w7S31CChI7goIOzKFfQ7scEByzZlguIGjE5E4chlwGXAZcBlImhngJRvxDyzB4CGF4jaljv8lSWkWeL6sU6/PmPXkyZNT2tzdfF0GXAZcBlwGklQGIqpfinCWYBiTGpsSnTqc0lplOcFTSAsRoK2SWzCB0BnNzgp+uqhbEY59DnUZAj1Bi7PsSBfjtW67X23OvEO9fOkSEiAw06XNQVTU+QQJnzCw9tfh3AWKlZQ6OwH9MnOW8Ai+lTGbsyeN6t5/ELpmjSGboBa8ME051fniHKYdtXvb5s4vt7yl1pVvtXv13Q96vtN78MeDvtw7bcCQzm06v/eWyYEl8TAaswsSIbVs2ZIdrPu8+2rPnp7uH2O/nDNyQI+32rz11lHdg/v3ASGVKVOm4T3HIERBU+XLl+/02eg153rYkMCalvc0YN/roMzwIV899uTTTOH8XHk//HJ81apH1ygFxYJtIKGKZUrBISGVK5a3oZ/6vz1jznxMMSMlXENcBXLFVSCZyic4C9cCSXV1RksNhhSwLp+MMMSNIcKvBNTFDqgfmA7EWXcLurLG3YUwTGIQR6aEGNLWzRlx46EVQRhkUojhDtLNIKfiRMi7rsvA6clA35Hdbmpb653PXzty5Mjp8ei8uAyktAyck1ATdptSJ1QmU5odni9r+tKAgb07CiRKadN383UZcBlwGXAZSDoZoH6m+uVQQ1COOARJtWwVMhW4imrqbSi/vwQDJEWvEveE/ddRqXrHmgzSlQUEMCsLQAkw6VLSYw1T8ogKhMrzTWrsX/JInz59atRv2PP9t7Jm9cp1QAd8IQlZVDiFpEhDRX7JGg3XLFvoDURFfderfdWK3+Dlzjvv7NGn/+qVS8V/59UX90VluLRWI0JSMOjKmrAP3uSVdceSDj//k3b/9haNryt9VblKlSqxeEfqnNlJ+ofP+1s3eiNnzpxzN6dhjjMPlitZrtLCOUd1AYn69z+B7gXnpiFRXd9+qUSZCt1ef471R+aiRIkSrVq1ql+//j97U4GDgOhZBtj3etvefwd1fMGEc5SuxXVkmjUvyVG0z+DRM5cc3rYm/e41c5b9s3F/6sIFC6TKViDznlXsii2VSyrWkDVSfdPDL/J6O/FBrLo/d9em1z+7olwZc4eM0DflkC2Tli748+DmDc9PjXrvkessBhoEoOvOGRSPK4Iu13q9jx56dlgX5i9E0rohi4HguSJGOMICwqhIBgGIa6c7kzajQeLmEQdFLod3of0bGwsywhkyj8Fuu4cb1L732ajqoZwEzbq2y0BiZ2Dhqt+7f+ctKvzy54G3Vm9ycYFLE9vjSdnfsXvbp2P7pkmd5rEGz5yUYnyEF/39x5xlv23cvj7fefkbVm+SNk3a+Fhzui4DZCBh4CE9FuSWDrlb6tQyULZEwVSZ87rth04te07LZcBlwGXAZSBBMsA/MyzMpiqmHlbJrQqZM90g9CN3KqrVRgUBgAlqb5X68GkY1kOXal/GdUZdeJCwHmp+q+cloLJc0M/1j7zMgZGdqaPYEIiSHmwoGKTCwKYhBair+1aLW8b0C63EWTpv+tRxI9dfdX3eTGnqtHj7kza3S5Fzl1dbXTz0iweeevHympdfkSet1Pfs3J5h1aSB48ePHTuWPYMkvHX142++/EKrjp9++3G37wf3DT7eZdaiN+5o8/4F56Ym8ooF0ufqenK6vL9i477UTA1Q7OYbr2veqO4PM1dsXv9P1uzZC150ceGcGZU64BXSKFQlBHLtjWrf8u47b6774aDv8xzZfHGFmqXKV2Z9jfCR+VtSsxTrijwliPY+/6k6XcTtUz+z+OvXDu306d0Gews0ePDZ7/u9r1ES8lHLG1c1vOu/e5rfVOGi2RtTwWeCUdtXj/2VDaM8MjvpMgx+7LbqujeIUA2mY9fd7orgZZ3r6yMv4oKKuDrIQzQ0U+NrakgGb0XJSMswSrqgaRD4FBS862RcTMVDm18Q/lV79tmQa0/HkcvAaczALwsmmLc0qZMcDvLjbyP6j+xFhFVL1yxT5OhKQ4s5ARvbdm/9fuq330wcvGZTaL98jK/burblrW0S0EvyNrVs7ZIZi6du3bl5x54d56Q559JCpWpcXidblsDK2OQ9/+PPLmHgIR4LosI/vhc34jIQWwZ4voz//LT9UII8MxmbMzfmMuAy4DLgMuAyEC0DlL5NmzYdNGhQ9D9DlNlawaHqmrPKdc6U9yqtQRyCa0Ao0SMKbA9d8ut5+AKbDBcAUKBt9bxCQ0zYAV08MkqFrzblPbgSJCDAUADJIAzhQgLYmbUh7wvt2rM+yGNFRe1eNS9vnesJ+JY6lQu+/PIbb7whPuclc6e/8MAtrLThtVxH0mb8Z/WqvbuOLtIxsa279vn2M3Vq1/rVZ5q369Tr+8H9YgGJrql/U41mbXJdUJjIiRlkJH/mTNvva33Vbc1XjOrVr1+/iK2gzRGNanVvavp4m2wFCotJojASFZXmlsrFZ20oLiYJ9EAZPyFKuy3m0tW5/MLcH730kHKCmFZd0RU8JyM6azvn6Qv/Mua2fFV03ckzwd/z4GNbVi3+ZexwExgzdDBHqwwZC15Y9L/Dh9f/vdLeoWYyNPKn36OVTUGnuPOwqvB+z3Zv6Mp6d5q/mItQWVUE0UWGSJiv3RWemP8SN8QQkDXJKOHKiRSR0Y0UjA1rwtcUgLxLAOMGLQVVXNtl4DRngKfJhk752pxmz5LN2kmk8d/h/xTJjD9/SSR46N///p38+/hhU7+ZOHdc9Fn/tX5FdObp52zeuTFD2gxZMp57+l3H0SPL0Dp83m7+ijnR5R9v+MyD1z2WKpWH9adYShh4iPRVLh36y51iU+kmHp8M8H0U/5QDEkX/vzw+Zp2uy4DLgMuAy4DLwEllgLoaMiyGgpmqXtAPQ8IUKKdVcsPX+gvDeqL7osAWuiQsCV0tUaGMhxiFZE0ogNrwMY4wTA9aKu2JGcG3GJAEBUBGUAjqUtEzVox6Bm9oOvTyEh2799u0c++1je6d7e/O48FJdz9yySWXPPzwwxEAzdKloYfOzKM18hUoXO7auzQFmNmyZevUrs1dj7ZeuHLt+lXLcuxaunPnzj/3ZC1R4Lys52YvWiBXjStKzt+aFl+ADpxFSnKVotkyNW6zuHXrH+et3bN+2fyFS1Mf3Plf2qyXFz5vS1RIN23atF5a/JVWmrIyBlPXSDiILBvwAZMIPdAtDHzACQbg4SnHvnIOswgo+YPXhKqskiVLVi2aAwRH5CMv6d7v1rP7hyUHdn83xPV/8KzZ8kULgpxgu0LV2k3rV1m6zweD/JA0CwImMG4hQsW1glScTNDiQYwupAkKymFqCMAX3kQXYlKaAjLiwMSsqSCsWwImwvhlUrqHUWRILrxs+JATZ0ixdfvLaycSae1eIhk/U2YbNGhw5ZVXvvRS6EHFMxVG8vA7c9G0dVvW2lyyZU5yqzwMHtp7IPxJZ+HGu3Hov0M8UvfR8G479x4D2Z+bKVujGnemS5tu5foVD1//RLz9xNdA92Hv9x3RI0O6jO88/EGNMtfE11wi6LPI64U+Tx3PcPehnbbu2NL6jleOJ5AS+AkDD7Hx0KA3m6WEfLk5JkYG2H6I1Wdua+rEyK2z6TLgMuAykGwycBoKyHV7juwObA9EVRx8/Ef1NliMSmgSS9UNCgMSIaLkhhilulYRzhlwQcU/Q9ThyKvLkAyqng8q0vaRCM8+AUCq8FGBZN9r+YQkLjArCABJUAPIsANFEpWnwhcfV2BUMcgpQ1lK1Gj7ybiFw7oNHjxYBmM8Z8marWaN6tWqVatct+HPGzPhDvshy1FRfNdaqkgBjivy1ASwuMPfRAk7CMzf6tkDlWCyqBAtTICJUA5zR/X7I1W5fAVy5Stw23U1kSRCJFkppQg1HeSVdnSxIFPyzqzpCgwSByMGuMARExlIYRjUwhBtIsELZBdi6QofjImKorYXUxnGNYSpIjc++XbZK0cP7Dpp0iSPdRxiW+t6dWryvVfOstdO3pKK6euRQJzSlmXiF5NgLC208YiMiK7aNKSl6SAP0fYOH4AjQoxrmlgQwkjqtPgIYdxxyJfFEFxOJY94UZbkTsw8/jZbaifgmd9rvh3ka8LmzZsnMySFnap4s3PBggWZGhlLZrNLwHsgLqa+nfK5iYE+8DSQdZNIw+ChxX8vZCHP2i2r12/9h6fAMqbLlDlD5mqla+bM5i/7PMlwV65bPnb2yG8nfbFh2/qgaoFche6/rvlNVW5LUvsN/bJgIkHuP7jvhY+eHvS/oUUvCK3xDEZ+BttrN60JYkNF8hWrWOKqPOflnbP0t8nzf1Zgn48fcGftZgVzFz6DcZ5Z1wnwqzVh5JfMgQr/zM7EeT/bM5Bq3yb+OTgN//2f7Yly8bsMuAy4DJwwA6q4nn322eS0JJNvEXj+iyeRKdrJQCLVWlaT44IqGihHDUEDqqitSqcBB3yBWlolt1APimq6HJTohg3BpIYHyJALDFK3B93hKKIsx6zUaajCl2UF43n3l34EgxRwoJAwSFfC3jT8rvAsdDEldznzFezQoUPr1q15K/zUuYtWrly+df2azOnT5shToEiRi9LnKV6lapUil1yWL2sqrP0c3t9HlrEDCYmgQZzMCABCpDmqjS8IeYEaYmJEK7C09AkmnCDRRVGYkYxLACamzLsADtzJfjDncKTIKIEF7cuIoL4d0wAAQABJREFUADgsiJBf91do8dS/OUsIOcKgh9f4QEzompauVLLcZ4+uW/ztkKErlq/YsGblgZ2bU/NIRZ7ClxQvWvaykheVvbp+udDGCxjZsC9kH+yG6x5BXC8BkfA1BSE7apNSyx4ckV1W4hFaJ5ngg3UGBklGNyfq6AavQshgGI2iixfdmTRklrzpNsNUsfByJCme2tlQIX6jk9knVTAhfExBgER8lQ4lPwgsONnEa7NNzJiZI81+5gyBm9W4Z66xZ9+upWuXzF32m0KYtnBKw1fqRoRTs+w1nR/rHcE8YXf4r0Nf7v9chFjlklXvqnM/eFMSfAaqbNEKf676g4BBiB7vct+od6YkqSBHzfzOklm/UoM37u/IVuJw7r82auofkx7vcr9GJy/4+a7a95lkSmskADw0ZfbSFo2qpbTEufkmbAbYfqjjoPHB9+wmrH1nzWXAZcBlICVkgIqLafJVPGdhKMlp1lZoMakZM2Ykxnfy+TKnohgGTdD6CxxZWS4oR13OEXW74BhV1Mo5MhT81O1SgamSW5K0KbMFalgFbjJqcDZcwOx7RsJb1VD8E6pBIQwFoRMLBiaHYsamMAIUkZcLzn4M51ds2KJiQ5/pr9BBBTEIFQE0xGzu8C4LMMEy1DWnnppPihwwwncRYiImXfrEpjwoeDjKGALMVGGLgxEOy4nCA24TtCRJoR6SDznzf8CXgKYAj6kpFcLLmKCmsHn90QdYypQuqQevmLWCDGbAC2DvJU0fP/paNHOhhFgATE05ZFJYkCkLADH4woNgakYEYxk2O57H8BVBRe4Uv2WeiWABJk6xIJKMqcAkHrpBjqxxn3PzQ3Qhyag7cZHHEUjktU6J+IwC5+X3l28E+a8vxt2+TslwklYKfnYlxgdXkp58vIM7fOTwqwNaB80kkSfLBo3tP3jcJ9t2bQUKCYYXY7tMsVPZrHrS/GP2GHrw+kcbX3137vPyxegi8ZjjZo9avfnvJjXuyZj+BMAcj2VdVar6gB8/mrVkOsuddu7ZnqQ2ey6Up4hl6eWmbwkbEqdKqatrl683fvYYuuB9JpYCGwkAD4GFO3goBd46CTtlvd6+xb0V3fZDCZtYZ81lwGUghWQghXwVz9Wk0LJrGvxOPmLIZE6hIUQDRcEHNLTaQtBD0CB8EVW0ECWr+WkISaGhettgHcwKOUKXIpx6PqJKh28ogNm3qFS3w0dLoICgDUkSJF2RPCIGRRg0a5ojMgjImuyDLBhH4QEcgDJA6tJAUmteQCIE7kgXAS9j4YU/uIADLEJsuFBXbc4KAMWICLGPBZxKgLYyrAkSXow4CFq4sAhpiEM8hAoZvMWQOOAyZcPP/aGbftvRfZcKFr1k+xEPbRHhWhlQwrFASIYb0mUKihYmq70sG2oQM3zBK3Akr0lhn1HmKGSHtkhoFH4RlhEi1FwIWxfa4/glGzIMyYXsi4MpokKYIV1TmbIYUA9yQMQwLpJMqOP/4AHMGuHwgvwTtlPOZ9TxUqHPLs6J9MF1PL9nO589dxb+9XtwFqn9FR9Bzim3eSKM3XxyZDnvZC3wzNf7X70Vi1bJCy+7oviVlxcpWyx/CR4EO7Wn4c7NlD3ogj2Gzs/uw7dBbhza4B1pUp+TIX3G48kuWfPn+Dk/rtvyD5sc5cmRt9gFl9QqWzdTBu9XfeDYfp2+epsGT2a1vfvoqwyCppb/s6zp27ekOyfdZ22HVb+sFocetQviLya/78Debbu2Zc+SXfaNf7zGKV+j6AavKX/dLVVvHz9nTL2KN0SHug4eOhhdJQVy4g0PbZhN1twr7VPgrZPgU3Yvv0vwlDqDLgMuA8k+A1ZxMdOU81W8XVb7Tp5vqmDq2Y3q1avH85E6Cmmrt1UeU59TxkOq/6m64VByew3/YSjKckp6DjiCOZBXkY8YHC0JUeTI2FIRRiUWrNsR8Gr+wJvRCCN6VFhTVY88lb9ixi9tiK4sI6OQJGAxqMsQMeCLGWlSQQH4EHNETDPCF2KcZVZgBxxIKaKhIex7E/EtwEGGJMipuaPLvBQnilJBWPY99bARmEhyhuATEgaDWA98OLooIRk/KixIVytoFA8CO/5e0L9t2/Ur/pxSq06PLu+Xy5UJLAbhVatWMQqdnzvf9iOZ4HAYtqXYtH8T8yUMeSQkCI7ySZuGmIpZHMshYZgpzYLZaUaSxCwWuE9IDnYIQKS5wNf+SjBl31P30TTNV3wsoMhiJQg7IFa4CJIuRJBDm3iEEMk1MmoADEVIxqVrn1F6iAyVeP56xsVpEpexDy6totIHFzELP0riwZ/m8NZv+afrkPfk9L3mHz7f+0naqVOnjn8YvL6q65AO0/+chqnzzz2ft9HfevUdsbxxDJDiqwmDeJF8kQuK3Vb9zlhWDFUsUfnDJ/unT5s+/kE2v+EJHnqyPbl7Duvy3ZRvHrr+sRsqN4yL/QMH9w/++ZNPRvXRhtZli1WodlmNe+s9HLFd0cR5Y5/u3jwiWjZ4anPnK7dUbbxg5VwN/bpwSoSMdbt99x4J4Rg3Z3Szeg/DjxEYmrts1oAxfSbMHStFnpJ79OZWl19UzuxENE72GkWoR++mTpX61WbvcEQf4q1wvy2eLv555+aMLpByOPGFh978aKSr6lPO7ZKoM+Xld70+HeKeL0vUJDvjLgMuA8kjA1RcTET7udJQ3ZVii65grUWhBZGQU3thkApg4BWKc4iynAIbUIOCmQoZ0sYuNCRAZY4ApBJdDamwIEUkjAALVO8qs+HTUJsFLIIAtLUQQ8gL5qBNYS/7UT4SIS8IaM2OOZUjA0cEZxhYA3CgKSBGG7+coQhrksed8A6LQfEgr/jREgdJIRF0IdoIcEgAa0KpzAsCYpq8p+ab9RTDy4gYxTKSEkNdGZBlqcAkA8A9Mi4mOAiZhGBKlzZ2mC+EOgGEprY7KnfGwy8885T2GBo3eniTe3fc+NKA5mXTovjzjPmeQlRUoSLFNUF1iVCEfdqkNCp8n8ijBBQ5HHwpcmlhGWsKJiTvA4vMQtAP6uaOC6RLbPIYkYBnx8drvDsqHBKjSGqOtHWVaWCQAxWLkC5EYLqBg7e6ZGScM0MUhchbDOsvrfls50F3XF/DM3EiCn5G8fuYQh4iO1FWjhkXGMRKIrh8aunsNrEO5oiK/ZleLQTENKhyW/XLamv0eCtxDhw6AHwD9lEgdyGAgKCpYJsFMq990mbk9GHG3LJzy/dTv+X44PHeet/WkSNHnuv92NadW59u1AbMiAfc2vZ/5scZw6Wya9/O++o1b3btw1/+PKhssfI1y9atWqrmgr/mvfhRSwQAF+KC3Zj3WBq5cuT99IVvOn79lrkGKnpjYNsu3757d937eNorlke3fl04+aX+zzI1s8/uSBzT//zlwyf6pU+XQfw5S2dGx4YYIu3tBrw4cd64bOEVTOTWTAUbf65aYIhPxUsqB4eCbcC19oNfDXJ+XfgLR/eWH/NgV5BPOy7XKEIlnt1pf0zSnYadSwv5mHo8LZ616sf9zYnjjIC93Svt45grJxZ7BqqVLco/EDyLHruYG3UZcBlwGUjJGaDo4mXJ7NAM6V0/q1ev5v04KRYbspuBWos8UFxBZIZyiz0+KL1UpppY7I3iV9S0yhlJimRQgAgYAo5KbgRU81tXHGExrL9glIPiHwFqb0h4gdp0aWilBnzaHDSQB2IQoa42tbppaQhoQCgPMhAukESdmCFwJcAChpDRShCpixm0Bl8IBUyAAIEaGBd84NmK8pKgKchXMBLsE3BQWDEIqlBmkMEgWtgnSNocEByZEj7iscJEzBCjeAwa1zhM8ibjcOhiAWxIs4NDEuRCHATgyJfOy9ftsP2nkZ87ffLozi0PHTpUJN226T//KC95C14oYdSxpjBoMx1ckzTSQhfSGSZizBEtZLw8hNEZCciajNNGWGa5WMSPsAkQttAfKUpFqaONC/HhoKV20IVmrQuBTeYO2IQvufA4mTwOKpZbmMjLuM7IRBAcVubJUcRQsGufUeDX7DntPqCCyYneFrpNlvjg4t9gD94+pc+u6JaTAafDF+20z3G+8/O3uePVNOG3lZ2TOnJ9w47d2975/LXKj5ds9Np1N79c5/oXqrPoJsYMADk93f2RIDbEu+FN0vCLDVvXsQ0NYMrAMX0Z/Xh0LwNo6Hb55l2WNT192wvTui3o+fSnTWreUyBXwRIFS8rOnn0+9GtG49fgfWfvPNTly1eG8zyUWWI1ECuJaj5TgRTt3R9+/NWGo6JmLpr26Af3GTYUnOPMRb/+NHuUyXYb1sna1S+vdd91j9xb76FLC5cSE9xn1rIZJrDp2Lenid93VHc1UC9Z+DKQtSkLJvDwnWnR4Ak1y22QT/u9L1+P4MTxGkVoxbPLC8tkgaVkJQqFph9Pm2epeuRv18lOg//Anr2t2clqOXmXgegZ0PZD/GnkHwtX50TPj+O4DLgMpOQM8MFo+7mSBz4qk/G7fuJzofWFvCyo0OJM3QUnOBTdBX93qNCo6iEKYBXqICYeDOG/LFyFNCW0FvsgozpZkiiqK5yCAh4tYRxYkEx0p3A0hBbqqAhaorw3CICGFhYhgDBnc0EwWt8hO8QAXkDMClXCxEAAGJEM6oIJEBbT8xt+fsprh2OQgLkT4iCbsuBZ9LfssTU7qOAdQhjS1LBADHq+SUO4wwJkMWABRSRFzIKFOcgwQdqKiiEaFoCBNTBJiOaopEkeLxJGEVMKJuSAJ8tSZeGt8xvXrTHOb+N/qFx1Tro0Ubt37RDziksK0cAa6sGUMjs42Cc2pqa2VBBTkMoAySQkwS6yYO6YO4QRRUv82JEuZ0gBo6suprCJPKSFRTQwrikHXWjW5AQAi1FIwXBreYn1CRncIQPJkReGb1wxKDxNRxydkWfI2r6x0InPKFvPCItfOh7zpKHVMUHJxGvLY/zt82F7Ukb4svyk5BE+4beh+vgys+Qz9k8wk0w2DRCcbyYO1nTeeaQL+9SAGqib9px0wWmOmvHD65++aEs/GGJT5Gd7PgZ2ExRTm0VAUxeEkKObqzR65vYXz82c3d5aheLyf5byOvac2UO/Lbv37WRj5m5DvRc+BOmnOaPuuebBICdLRh+bj4rastP/1QqOxbt9cYFLOzzc9albWg8a1++7KV/bZL8Y/+mMP6f2aPlJnsB+1Yv+/uORTk3lE2Do7Yc7Vy1VAwTt1U/bTJzr7XU9Ye5PN1b2XkPAZGcvCd29jzd85qH6j1ukPNj13pdvApCt2fh3w2qNh075iqH5K+fUyVHfZGgsWDlP2znTbtPkNc4gQZ2/bs8GRiPfmaw1XCN+/U67FzEKPXpzy6svqzN90dQPvvEe8vpr/UqSnCXjuf6gd4rjNTL5+DeYBe+bk51GNe6KZelZ/H0lfQvxgof4S8AM3Svtk/5lPlsi5EHFE/69PFvm4uJ0GXAZcBlIkAxEFF1nNTCkfxtiSUssVVlcCrDj/QWh0MKpcKLYSyzV2xTA1NtUyxwqnnm2i5qcehuOSm7KdRANVdQYh0kRbsBN9DkG622MG1igShsvMKnYhSXB5IDgWykupkEhjAqeoKEyXgiC2gobg8QcJDiKmWg9uCFUzniOFINNAXeakSEvwgWwBtAA4YiYZZ8hZSBkPOwFMRnULDgHgySB9viSbYvDpPCrWaALTmQ5xwUe5dr74edH10XzFUeZNChEU1YACCiGK/Kk3fba+2+2vO/A/qOvHAqiRUjuuLAeppis8C84kK67GsTDEAiRZq0YlEY4moLdJJiCY2SQGfEgoyHaiKFr04EPR+44w0dGWJJNED5BRlxoujA1WQSIU9kjzzKCOs8/EoaRnKLCAakr4WDw2OEIUsRnlIYi0I2gfOK19Zseo30+OWPknylm9HiO9/FFhCkQG2LtSdt+z+jqPN3oBW1Ps/+gjyt7ew+lsgv35YSB7wx+zbo02AfaHoP6ee6YYVO/qVWm7s1Vb2do+p9TxswcIeGHb3z8sQYhF/+FgSd/yDN+IPwyMjYn0v5EMK+4uFLO7Lm0jAicJQIesg19/jt8WC4S/Jw/VwFWUT19axuW/7BjN6AGLlasW9b49Rt6thrAyh15tHU6LIQZ0GYIWvDTpU1/8OCBiJBY5iMOSeNxueAo1j5u/SWvHtu6a+vOfTsED/2+cl6d8kfhIR4Be/njZ6X1yI1PyJFSB9C2adsGQCsWE3X+xtvZGgKrYu9qVlrRZoUO2JOeSvvvv//8ce8U92tkKvFsADu+PfhlGSHCZvUeiafBs109XvBQLP/Gne15cfGfkQzwoOLcRau5r5L+6iF9GxZ7mXFGcuicugy4DJztGQhiKDF+G88EgzIJON/Y/6yfEKCJpbxJkCCjF1TRzcYoY4GpZD1hrUX1SxUtXEMu4FCTwxEQANMgDGppFdKMenV74G3idBmitEYGkEIQABzBT8JBDKAJzkUYBL5QNAEUg1AIRvCFCwgXAgsUCWd5FNBAm0NMcdCFQCUEnUjLY0V5KIOmADPojiEvLWEIjAZOsUZ4tCGdzQt8xUCEKHJIHjG54wwHCAMjkLq0hZWgC2lVkdfyibCRxyANDEJYoCtfnGGiiBHmhSPv8PcUV0M2vSn7oNh911fJl//nD995Zd4U703GEVS+fPk6ZS/impIQ+ULAM54lFDDAipJ/dH8o3wSxaUYSxh3E7OCTT4NpaEDIePFk8ibC7YFxulLxhuXRn46lxZPxlxHB0Y5UuvEQlhFNE2syLvuagpiI4VEcC143J0bsQnjuw2QRhhnH/Ax+TEX8ArIF2DGisXZOau1P0v9HMda5xjaoj3dllXymwIWibKj8XK/HlKM65a9lq2PedcUGN1P+mCAmj0fd3u76ehXqVytdy7AhtlJ+8a7X6l5xPW+k4nmrQ/8e5EmoZ3o8igpQTqUSVXOfl/fdL46+eOuj4d1/njO2yAVF129ZN39FaGsL8JSL8hVBBUxEvuxc/uIruz3Zj02IBA+t2bTKhtQ4dPiQGocTDR6SfbYNYu0Px4AxH2kBDs+avTmo7eC23yMwdtZImw4PlzVqd12FSyplypBp0ryfbc2R7fWzfus/snlztUYx7ujE0ioOZk16UR83+0ceqZMK5x7DOrH2h8aFeS96oH7okqVPl1ECqzetAh5ibyN7xq3rU32FDUnghTteuzBvkeL5S9gOSjiK+zWSkZM9c2+MmTViyu8TWLK0Z/9ePO4/sA+ITXaaN3gq+hvNTtbF2S4fL3jobJ+8iz+pZYDth3p9M+WEFcgph21fcPEEwSkbkSL/xFBHsbEFZQYchxPFM59O3WXAZcAywGdLjN+BC9owsVNoRFRup2AhFhXqwJMqBWMxdcpDwQCCpWYwpSQhOBSjLyp5imoqZ6/ADu+1jKQ4qroNjkGYehsCLKC0hlR+01CXwhtcQFCCN6xngvxVIValY5M28jRQxzhaMisjaNGVERrBYh4VCLBAIXmdYwkLwiYswiCMYrKYDcYAX+6wrxgwYhCYaWFK1gRDmDANcYTF0MW4ONIVByY2RZoyHIYgZQMmXU/Yf1hP8ApdDyLxkRrFRgzEz1okCAvSUrTBCEGXZAExjOTMm793337v9R86omub/YFlRIw+9vwrockGnvkSNCOPyEgAO3JHAFwC4TiyryVC5A0ZtHDNqC2YgikLMkiX/MiUgCSMQIxigfnaXWTyWvtjlxULkpEAZ0KKCynn0kKFGPCoBjYVA2c4SJIEUhok9vziOzN9agHF8n8Rv2LJGL4Jzj1h2/ZvaspEhSyZrwx43sp18IirnihtuIbJLFu7GCYvYhcH8KLf85/b8hn/jemZuwzpYPLgIz/P+dHMio8RDpOh8WqzDnqwaOvuozs6w2dxDdiQXgzPi8nAp1gdE1T02keOiBMRLU9p9RvV45Yqt1e/vHakSqx9YLLtu7cFnxqLEAc4uzDPRdpYmk2adu3dkTVTtp7fdwmKEcyU3ycEOZcXKae1VDC37Q6hYGnTpAvKRLTJydVlarPwimVZvy3+tYK//zQ41Cej+0jy3Ue62W7cOc/1v7WIilq6djGSS9cukky9itdHvBiOqbW8tU3Q10ldo6BiHNurN666/93bDa6KrvXeF28sWvXHPXUfLF6gRPTRpMw53mZbpxBzvOAhynj32rJTSLpTOf0Z0F9c/CbUlzD83wOBCvEvEb8Iwonc/0On/8o6jy4DyS8DgptVawnQ4YPrhNPkE+mEMilNwGotTTwuFRdVMaUvVbcWd1AwUx6rKo4oua1CpqGlRobOUEIHCQtYo7xXva0hmBKjAQkXoKGSngBYtKJS3Kp07MspQYICIABH6jKFCwmIKaCBNjYZEtajCUoAd8EYaEfMUUAAKjICtCEkgi4Ba74aQhIS2OG1AoRrFOXIQpK83CEr2AhTAiYkj1+LhyF51wThkxZLmrzhggBIC6PIRw8GbIhRCMtEBcn+B082LFu+/FddXv19mrcrB/T8K2+uzn6FTNHFGu6YL1pSgckU6vuIlRIrp3bnIEB4DKHLEGEr87pVCNUiNIOezUyeGByM6x5QxuDgmniY1EM+BKkAosLIGl1x8vk7CqGOR1xgkDa6CgPjkF0FzNo9iQBt5HVZUddL+uBYhCh6MuEHEj1bYeJTC9J/RAZkn3CZXlg7pf8MflLxMZXC3/L20YgPx8wcGbwnItAWDT1+S6umdR58tndoxUqOrOcVveDioBbV8hfjB4pzccFLWQIzZlbI7LON2+bPWYDthIJoEetfXrjztUqXVpPK1p2bg9Y6P9bbFpXwWivgIUbXbFodXAuTPcv5Utm5Z0dQt+uQDjye9sfK+aNPBh7imaw73ryRtTkE3+nRXgQctGntEgVLWXvv/r0AH5oUy6A+fWHoxz/2sv2bJHZb9Tueb/KyvXU+7rto80I3PZf33ldvftL6K1YGPd/7Sdl8/o6Xg2CKwUOzlky/s3azqFShJwGBZgC87I1pFnawcVLXKKgYl/aefbtix4Zk5IdpQzi4we6/7lFLVFzsJxuZeMFDfEXQolHotyjZZMRN5AxmQPtY2VMA8Y/E/uLGpSo4NXf8P4Qijvh2mhcJ4Yivr8U8NYNOy2XAZcBlwKu0/FpLIJF9tjgMOi73hj75kbS/JnH/E0BVTAFMVU9D5FXX/vNB6qrkph2sq+naUiPagi2Q5BB5xbZfrktLfFXdcIAtUBcxpOodIIDK3FOMtoyIap8IPQEf45Ai1ij+sYaW4SZBprnT6hW0xMEj8hExMIodgR1KhSyjouAVpIyAVRG/osKUBBgyGdrBkBDAsk1ZvhDAoye520MoAIPUVpCMBkkukKchAQEfQmpgIk/bJuiZ8jnCSmSN8IwuK1b431c+qb9q9r4/xt57773/Zc1LGIiZfSQJyVTE52we5YsuUxOTWZA6ApMwbeWQidOALELayBAbMjRwjTVGucRI4lSzY77YJNu2AAoZtAhMLixCtOwqY1NhyKmmgH1UZMoLJYxLMmVNBI4Xkn9F8I4FcVC3CD3WscSnFgz9RySQiLP7v+jYJB3tKVF8xcgnFVlKWFSIPxb683HU39nQ+mRM7x7DPoiIlNeWXVakLO/SKnB+wZc/bi206Nbqd7KWp3LJatpnmte9P/L+3ddXvjl39jy/r5w7/c+pC//63ew8UP9R2svWLBHn+ko3n5f1/Jpl6oKkLF7zZ7o051yU7+KiFxQzeRprNh99zqDV7S8Wy38Ue7ooX1FJzl3+WxAe4uEsdq7hOS8Ow0EOHDowbzm/jidNTFPPbS1Z/eeN/6tR7bKa9SvdfNmFZdKdk+7f//7bd3Dvui1r5q6YNWLadzJdJF+x87LlnDRvrLo1y9a7IGf+tne/8VTD51i+tH7ruvw5C15aqFTmjFmDoRw4tF/dzdv9z9zg2LHteldc321oR/JMPNe1qcYcNc7byu6qfV9Q9vxsodVD2hqp+AWXaJT1TXe9dTNAW6E8Fwblg+2TukZBxbi0ZyyeZuuGKpes+sKd7T749h1tfhRdvft3nWctmfF+i57+SrTo40mRw8dIgoQVL3goQSJwRlwGghlgPRrbDwU5p9ZWeZAYf3FjjMdfSxRaTIQAi4n0/5Cr5WJMl2O6DLgMxCUDPkYU+kKeTzNI/+67ZzdizB4f+/AjtkHRwqu4L62ykpgGdTKFsUpuulTXqpZVP8PREh6cWsmNjB5fgolYsCAXR1qGC9BFni4N5DlT7QsL0INIcES4VpUOhxqeLiiDAqCtkKjhI7ZMCikHfgh5wYJithHFIPQEpkbpGtihucu7zZcucxTeoTAkYGCTJYFRhhQn3pmdupzlFAEaWMM4TOIEJYFoQzJOBhCgDW4iiEQcVCQs+0q72ujSUKKMg7rlHAFL+Ppc5aMqlF8LlhRGgvAuj4rHwiNURW64icRwoaTZyiMEOGwWcscUSIJR0BQW5FRJRky6CGMZU8q20B/NSACiYpNN2Q9eZRRVoWLN8kCQ3N5sYKQI0YIiMCM4qAgsIwmsD+JzSRmTr+hn+4/I4A9BRe5hfMtV8H/UhFrVbsbP3ga1+oAfP1L8bDl0ZYkqvP2q9EVlzg8/rMTQgDF9BTr8vmIO61lur37XkEmfC0Zhwx3bcyeYBIxcU/46OHv2hwD1P1bO4zmvVKlS8YYyjqCwtW31EKuKIuCPMkWukBivDNP7v0wr7/kXCDcBOWIhEnsed/j8NeFZ1S6rZWJxabBYiZ2wWYAj4Sm/T+CIRfHd5t3YG3tP+CX3C1fNlzCPm9mSqOjqtov2qo0neBIV8OvJhs//r+/TGDFsCOTu7Qc7RZjNnT23OKlTp6HBnk1Ae2BDtMHjmrxx47UVbqhyWY2Kl1TOkeW8CN2TukYRuifsZgxvioTkwr8W3Nv+NpsIV3ngi0OWr1s2d9ms/iN7iv/rwl8e7/pA/+e/4FY5ofEkIqB/hOIZTOpT1k8Q96fsPVEV127ccd9rgx9584sDB/+NxRFiN7TsjeSfK8L/vsUi7YZOVwa4Mxs0aMCX7TjkqxgeiY97YRD/GFXOsbcRa4j4RogwgIr4dwqKv3FnwWXAZSAFZoBPFT7H+FShuNJXQ9Ra7rMleCfYxz5pAUQjS+TKPv9P6k8AFTjFs+pnVcUGhVAbewt2wk/WIEN1DQ4CX0TNTBemoAq61NscVt4jhjoC2GGUQziF3FHeQxTqqMOh4aEe/rNCuIADXzLYpItZIJIIwqBwJYyLBG9JHiZGglFhBBWbAsJ0bY7EgDU4HFiAHxSWfawJGaGLNWQM8Apx/CTQZggBDtxhmS5IBKR0aYKyBhMBOBAeSYLanpaPDQHu0JYYWcIO1iQj+4jRENFgFsbRkijkDXlRAPKohOuio44YBBPCPkaCMSsGbyxMyjDhQRYAbblTkCQZg3KBjMQiTOFI11r3gAIwGUYjCA5ZlSmd4XCorWAQgEM+YYqPcQ7ikS8myKH7hywpn+jSQAv6uLO3wRC/bhaJxz0+8asX/PhCEHX9U5SMi4jj58NbV8U/qGQAFBtUiE/10/w/aiyxnfEh9gkybOiWqrcDdjSu2RQAKIgNEeSm7aFf7EWrPbiBBUTdnvykWP7Q+pQYZ3Fnnfv0iFCBXIUl0Gt4V165FaOwMWuVq6c2OzFH7NnM6iHQBEYnzgs9jmpa+c6/QO0WnZs9+sG91VuW1Qu/YN5/bQsTi2PjrQfeByE6oTDT/6ztd8K5CuQsJHngGN7adkJdE1i98S9rH69R/8qbuCg2yn5M/Z/7IvhCeg0BSLEVFG2e+BOnZ8sBIERqA5bxLrk2vZ+s/UzFa5678vGu9/ce3oUnAdk4CYGTukYyGPczW1OBZ0keAMiwIdZ8dWzRi4mwNRLbOY16Z3LTax6QGO9W27LzBOuq4h7A2SJ56quHeJSGSbKX8Nky1bjH2fmzCeOme9to/fzbsuuqHHdjqh8m/TF/CV8yRW3ZvvuHDx6Ju30nGUsGnmtau+lLA/gLelL/0GMQFW0+qmU7/MWNxctpGOJfIotKK4f5r8h9dXYaMu9cuAwk1wzoU4XZCW7mIwWiyzllfrboY18fsOSBD/+TXSsUvFWwBroEqJTf38WJkhggACjHCBBBCzdYocMoQAA4CLW01tfAsQZDKr9hQqrG1cYIBTkCCGMcHEF8wQegAAiLg6JcCG+yglzIgsQ4Y83sU+QLN2E5CUx8IWzQD2aFAoAR0JYWZT9TwDgHHCE7KB515295o1lImFFI8hHoCfYlgwBiGBRpRl5IYZxFAoJRaCuekLQPxyAsLV0CdUkI9tHCDm0FScPjhJYFeDbgc8AnHiZrHIxYimByHbEjGYtQGYApDAuzsgCGQlYh4iEGzmRehFlIF8LuATjwLWz43Dx0MYhlJQenOJKu98MnZLybITwdrxttqZEuqxlBBmLKcLhpyQYWlByGNCqOmBHxEw+zM0KGdUYYQdHSQhfauC+ViZ1sQx9fnPXxxe8aFlLIBxefLbaqkY+phH2I7GQvRNKU5+3ptslxs2sfDr4bKyJg2xB603Yfs+TOz1Vg0P+Gjps9+p8tq3kHGQ9z5T4vT77z8r//1dtatsMqFRl56PpHtRiH585a937i7Qc723ZCQS9rN6/ZsPUfnpAa8OI37C4ERBUcVfvVe9+5/90mlUtVjRi6rdodvCVNTNae2OiLd7XTS9+NE5cGOzf3fW4wMYyf++PUBROY8uYdmw7+exDdzBkyZc107iWFSlUrXfO6ijcZgFWueEV2ntYqKt7a1ueZQRVLXBXdF2+C+2v9ih17trFKa/aSGQhUuCQGseiKre945eChA9MXTeVpu3b3vRt9+Y9U7qh9DxeUSNTlxWQfP//VwLF9+43spYsiPo968WygHg8EUfrhrfEne40K5wl/EEePNRqH/afA0Tp99fbwX4faIMvT2j/UJfiQIE+TsTsVC8E6fqmv9k/9c8+8nF2NU4eHzq55nlS0vy/9R/IHDv0bi+LlxUMI8dzFa2IRc0OJnQH7u3vG/+gSSXCyglDh6GU69mCIKrqgZIWFoSWgQaZruwy4DLgMxJgBK7QYVa0VxKBTwjOtwY99kpCA5da6PUeUc2pjcBNIRbKVygbHCAehltZqC+EOKqdhWnmPBZgGVZgA9TYWBD0ggzyWEaNQ54wFzoY14F0ohuRpKwxZ42wC4sip4C1MiTAYBAKQjBAgHqEAyNusbZomjEFGiZNg1CYYqYCtCMzCuDIgIEMB6IwW6sKVDAcJzc6HRTQ1/EKcQ6iKjwfhlAg1R4wEXRAJRggAeREcAUDMOkbCjmck/CJ58mOTteRLUZIKEmts+YQWuvCRhBQS09HO0OKYFl3dSzTkTnP08sAaH38VlUA6OEbIYFxzlLwCsCCVfDEVALpCr9gzWwQfAcgEFCQcxU+DrFo8kmeOMm7xSH1UxtBvh2fxVMk+voIwd3LFiZgjH85KVQJ+TJ1q7pOu3qYd3i88izjefKBj7K/3erVZ+3YDXkQ4T47wJu1RUbwzi+2EgtP77/B/bw16WZyLC1yqRuWS1WuXrzd+tremhh1nbmpb85Ebnypf/Ep2fd66c+uyfxZN/WPyxHljhUCxzGRk+0mXXxTzZi5li1WY+uHv0dElgtd7zSyYmmWvebD+Y2AQxjnZBsjFvXUf4oiLIo9BtW36RpPXb5TwI52aXnvljQ2uuu2SgpeyadHqTX/PXjpzyu/j2SpbAm891Pmp256fs/S35jc+ERf7rMN6tdk7J5TkfWQNqzbJlf3ohy87Uj90/RN31mo2bs6P85fPmbdiTsQ744CN9h3YdwrX6ITBBAXAs954oCNT2LJjE6hP1szZol9Eyd9d5/6ShUqnPSddzmxHZxE0lWTblMPxjC2+8JD2Eo5nEElKfc2G7QtXriOkjOnTXnfVcZcOIVD2khA8RPvQv/+lPcd7wNJRPDNwUndUsEJI8L+7x8N67C+9ZgroE88pFytW7OKSl4/68ad42nHqLgMuAykwA6q1NHH7Wp5/DpLrHvmJ+rGvNFpJTIUMcabADkIMlMqGlUhAO/4Iu4FjhbcUscCBDETBT1sutBZJbWzSoHSnUEdLwgaFwAdWEC4AXoAjZJCMIGSMsCash4aMa8iAALoIYFmmOJs7RcJ5+OQ5aTbM7/vtX3tSZy1fpVbe0uUi/Mq4LONdBukipmxwJmBhHPDlnQaQhFJKbB4I4keOGF1IZ+YL2KEujlCxITXwosU+llICsJBoKGMoKp9oEQDuJMMZv4oTpsA4TEGchfpJhTMh4Q6D/N+t8FDn0JD3I4CzIAYhxsGtApX17wrcMWumqQCUJfmSd2lpVGdL3dEAwg/WeXYDxBQgpsMRBBY1QcIQAiUwCEm8o2Jd7OPRQkJAHmlE3GlZStSAGX+KjhPx/9UZX/od/3lFWHBbC0UkJMYuD0axrCP/+QVYZhKjgDFvqdo4U4Ys1Pa3VmtizOgNlhGJyQIWW1kD583733856rlxs3+kzdKV9oNfja4rTvYs2Y83JP7xYIUPn+z/5YSBf61bXqJQKTbZOeGMYvdyCqPAYd1bfvxsz8e0TufHGcM5jmcna4Ys11/b4v5rjzd+6vwY959mY+wGVRpxYHf/gX08IcjW4EvXLGY10+017iqYuzD8BLxGx4ueW4KVWccbNT5LsaydohrxhYeSX7KGT16oSTWuVz59utjys3mb/6fYl96+a1+uHOEva8JJOXL4yJqNO1as3QJyVLpovnOzpA+PuJ/xzYC+kLE9Jvi2HIsRgI582BKeGF3GB+sROhsjRqvlQhEeFSRMQsKvgq9bt+5PPzlgKCJVrusy4DJwKhmIKLfY4YLv5IP40akYTUo6zIiP3EQtt/Jl5gtFDzWwkpia2RAEYQdwgCEgamnhHchrkQtDEJiLKDTqPygEE3VUeHIHkn3OKsg9L+EHgsQ0GV/ciwFkwUAEuvLOqJyiRbUvFECYAuW9RpEUKiFOZNnvvwzLsCFUEBi29NDXH742ceinngmfPu/TuVKlSre2+F/fPeURtsg16sUffmZNmIvlkCEBPahA8o4wwARnckK0EdYQQ13YEOqoWEo9R36iaEAm4wE3/sIi+BoSB/v26jcF4HHCS42kIpQEUzgSyQvhQbThc0adcxD60bWTil0ORcuVEuECUpe2vOPR5GUf7EYX19wxTbVRN0cEaQFgBCIqWVO0Sg5nC0CTEocAlAQpYjYWwjuEWQtVwsqGHMWiHvch++CKu8rZIpmcPn4TO+fsCxNHF7xC64SSqzeFbu4yRcsHhcF03mvefeDYfv1G9LCtZ4ICtFk3VLPMNY82aBnBj2OXpUxxXOkTR4OnIFal1NVfvjz87cEv2Sqh6EZY5cQCmdjXakXXSkAO+0axCIsjwuZpuEYRHl03IgOxwR8RotG7vGQqOvNs5wybGHrM57bafNkTG+3ee8CGs2Q8Cv3s3H1g9NQ/R/zyx/gZi02gQJ4co7q2yH5uRuPE2Dh46L/9B/6NBUjasHXX9xMXLFu9edee/QBSF15wPrsj5ct5bozWzlIm9xUASox7D9n3xjY1QBYo+uNaJmCNII5j8A0NQ21MMtiIMYygwEm1LX6C0XInOA4eOqkcOmGXAZeBE2ZANUnsH24nNJIEBRJ8lWhwjsHvEiihKYm1vAUZFdiqliOQFy0jUsmNmOptyct4BJNim/JeoAYC2MQLQIl5ETMCmIBJNY5rFMFNIHlEXViMPAI9RKx58UR9Aiywp67Qwj4GVfZjFtQAO6AMWICOHDny8Yt3z5o+Tbp2nu7RzTc0e+pwo2fW701DDKgoLcgoBi9O/3EtRj1HWbyDR65Y5kMXGc6hIR98UQzYEdNkiBBh+ISHjLp4EQemTZxRrSFSAJwjONiEsCDjtMUhmcQGYQ3iQsD34ve7nGUQpoKUuuzTJgDFrPDsbsEUadTSMNqS8RwECOPiY42DSUFSUZySVQC0EeZO4zKJg0cLgFHcgTkqIbqCnBEWQCZT4iCjWcCkgS8oCCniiGA0I9R1zxjH9h6ywGTcnV0GkloG/t64SiFdXiSymuPxK+CbJjXvGTVjGE85Iblr387zsp5XOE+REoVKVrm0eu44rCtJavONHg+Ld3q1GrRs7ZKRM75btWElL6RPk/qc/LkKXpS3SMWLr7q8aPngoqro6meWk0Ku0ZlNcize4wUPxWL3LB3iVWULlnkbD/FkWZnw1kLHm8um7f5/EL5wxgxpERs7fcnwyX98Oy6Gp43WbNi2dtOOWOChEVMWvjtg7Io1m7FTvFDumhWKPXjzVflzZwt6X7Vu2w0te+3YtS/IfKXn8IdvrdKm2TWxr3UKqpy9be1lSPwxYj3wI8qhhAV34pO36MBQfKw5XZcBlwGXgRNmIOl8AJ4w1DgKnJ4ZqfRV9S4IQOHBp2yGGApSEPgI1tsq1K0IpwFZxa4uHJACaWFcTmEqBmRUqHuaPkmSJkPIeFgGj0eFFyvBlzVDAeQFLVk27EP2I5bVICxMpPOAodGxIQXAecSArivnTbv2uR55M+XFDipBHIEuQAlnRejZDMMoNJiOoTDkR7MTE8tYMyQiFKG/toghmyNaGMeUydMw1Mzj+mQcArDwaHOEkhZe66RFTLqmWBZUR5C6fLJGAi1IOAjTFREnbeKxCMX3HPkYHA1GhewgSReSsOZLF78Qrs2yXWhvwAfFsAChjpbETAamco4jCXiiYbxJHM44RZcGh4iuCPSQjCkJXobDr4fTqHFC0u6Hy0CSz8Df4fdwHW/zINb48JwaR5KfSrwCZAPppxq2jpeJM6ecQq7RmUvwcT07eOiY1PCuevWLFsyVOo23wjwWGjdziUZZGURj4Ijf/tct8m1ZwEyMZs2c/tKL8pYqmjdGa6vXb2/R/ku9BE0CS//eyDF80oKvOjxw4QXnibllx97bW/eLwIY09NGQqRN+W9arbZOLC/n/PsToJlkweQlo8LEyrf05PQXDqeUviAol6gMRpxae03IZcBlwGXAZiCUDVOCqrpHxCmx/tYU4ABngDtrPxcr+YL2teh5FMdESNqSu8AVV9SbAOg4Im0Gi5qduR11kwjLI+g4a/2fvTOCtqsr+fzFwQBxxIkEcQsl5xFnqrzlkmkMOKQ5lpmZmaoMlZiY2vEkqVs5lSak5T1laFlKGKIJDvjigCChqzgKK+Or/u/b3nOduzr0gcQ9w77nr+ezPZq1nPfPaFs+PtfcBX4BPSAojCTNUAozAKWBW2EfAczcoAgFgAQ4yt151eTmAluPHxt3/zNE7ffDDX72wxQ6seuzFXBQ2GKwRT9TQlAFBBF9YZSAcQ/zCMQwC9cAUMhYZflqqvn+nF1Yh5MOdKchhqSZfVuEYEuO5UMSDC5waFaH642WJWX0SDAAZEBYfBjgGpgu0EHYLAv0J72RNSJYOMS0TIVfIlK0x5mLVRyLJF+eziFD5sgCRKIYLjFthxJi6lP4osKqa410+qHjxwcAmxC5/Lf2ZKVeg3VXgsWcfGfnI3TtvtgdoiME988IEBj2X7dkYR4HaXcVzQO2yArxPU5e4Mjw0WxlffbP4B6mmpj6rLj/bQmuTu0ZV3h37+FrpbxlnXXpHWeqgXbfYa6cNt9tkrW5dFyvza8ZTX37zwFN/zdki+cBJDN6eOYs7S8OuvudnJ+/j0hW33gfH8cbrrr71hn27de366ITn7xnzFEzgpM987aLbhh3bAAjRNhv2/cA8W7vHi9zgRLxT5mtl7fA3L8rA0AJ9IaK1ImVerkCuQK5ArsD8VIDP7kZLXO63E0ZQ9OQYRYCG2Rbaltu2X8gAARURiBMZMFEPwAgLCoMdoNsq4QIVxHDti0Vh2QFM7DsWFMCjbwPJFAUIXAAZwSCxD2JAXkQj+ariC3/8zXlPlX5Mc8899zzu22c+99rMZ58af+tVlz3yQOWNs7fffvui73zxC+fesskG6Uc8rAADoR8GVAabENXAvggFU5KCz5VKWgWDYBJPyCOGChRFxhocysVbVGQk6uGq1UaASy9yUCffdIyoij3BEYhBUe8MxNcwDgdyX+AHRVREaM2R0UUZZxEj49xNslx9VJLZAkgSPIp84WuBQWBkMVAF46ZJJJERA53KZIwvFFsViBQYICbFs2cMTI2NN3AYQOWqIiO8pf39d91h4iP3RfAVi/mPXIFFWoEHHh919NBDCeGKP116xbf/sF6f9Rk/Punf3Dds8WbZIo00O88V6BgVmBtyMfcMar7pO3fhjrL6ToHLEO3qq3wIPDRh8suB6WyzUfrL3ab9e0eanBjaqN9Ht9tkzbljQ9PffveAbzVjQ2cc8+l/X/fdR6/97imH7aypO0f9rwN+Ge3Xt4xyvP2m69x+/jHfO3r373xhl98NOXzM7761/878kkYClSZNrcBMSjb2HZyIw0QAQ5A4EV8tBTOCFmHioEJ7FzR06FAONwEM8TMc7fl80yKsVXadK5ArkCvQ3ipAG0wPHx044dkY0znThEsJ+Cg+nBw9uZ08q3TOEhgBwATwhBzuYgqoMMaFujD1RZduWw5egy/bflUQBnpgVfxC+wggGepYQ0C0SAE9VqIpIK2atl9QgFwgg7nhsRnfPfHLt1w+NLQYbLvttpusteqqq68xYOCux//sDwef+INYfeedt68/8/DlmqbjK9wRrQQHEuAoRw6H6qWrCh7hnUSYEqGRoIiKGQXHASUNpCNBP8XrVOERO5CuKRF4HO6CIx871BPUA/vUEIKDOzgQA6YoGqSR65pawScASZwFj2BGjCFTo7A6lYmuBuFj0ynChGc8JI4RKxDPGGJaJkhj0CkqCjPgUp7I9YVMWYBpBKY1pgiXYyAqkiI1LqpRU3Odxp2/zJxxaTomj5dMuQLtpAKjH7/XSPihri8PHfToMw/9beyd/CoZzDVWXrOdBJnDyBXoQBWYf3iIJMeNn9yBUp2XUD+onlrp+pEPqcyNf38kDO62bfqns2Hf3L9vr54yQY4G/+LWnb407Ka/PcLvl4VkzeCCa+55dmr63y/oijMP+9I+2/AbZ10XW+y9/3tfZtwfe/rFeK3sR1/9TPAZrLJij/O+se8TN55+9yUn7LT5OuWljjued/ARkEicCCAGnAhFoKJFghMJDPl1JN4jAxUisAwMddyHMEeeK7CAKhDt5QKyn83OXwX4X2z+f6RVXbp6WmgaabtiO3AggJjapdN1Q7T9NuEIAEOgCwdyNY0KgmmXji74Ak07LuAEBkE/zxQX3pOp4oM+aBsAkiyhjiku1BkbEgGIKYhTsBr2UUdLFAALQW/PmP6TEw547J+znYNOwltskdwVoAkhHXzYF756+k9D66UXpo66/kJyxCCkO+QNA0cQd7yjK6XYREOq51mUwQIpoIgAZMBIagoOA/7bQYY7GeEUd5pliQuOcSKMAEsRgBzuQRhJ6FIVzBKjYdWaqMiUMGJsYO5pcldgfKyK6MGRzIJpyrr6WSgGuMOpYmgRnkVDK1ygkpIqfrOsYq76ZSKmoas8+RID8uw7Dyfh1QgYg8LcjRNhKZxGkFhDJmqOukUO1zX2K4byH7kCi7QCu27R3BbxY2SH/Wi/ky88zojiXbNFGmB2niuwMCrAz9XVy82HgCD1ctNR7PRcrvL3l+f/U/kIUauR8/Nhl1z/D5f4ZzUAGsar9Vz2rxcdf/xBzXsDSHTC/1y7+1cvunPU4y1BIr5z9Itr7gn7R55x5ee++avjf3zt9kedd/7v/yZ/4OYfcxAfReLo0FqrV0Co0GXAt7H79Vl58W4fKTM71Rgghr/cA8p4pIjcgYrAiTjKw3kisJsFVA1RIRxxXAhUCO/5uNACKnU2myuQK5ArsEArEIB+wBy4oyWmo7a7plsGd4Ci5XYaOAt8umt69WjCmZYJa+pGE46uwIFduhgEEIO+YMIJd5gqB8AUdUyBOyCZxsWbaPAFLxgYkgJMISxgnwjt9uE8/Z+3Tz32sOefeLhYr9z2PejQy++4f+bKGzLHckQ+6NCDt/x0epVDuujiS6a98RopAHYYaoItZv90DvWEAxFMAC6EDdMYCCkBEwWYQuRMrbmJI8MlfgTHcuk9ZVe8zMUqBolzLoQ7BaxA7LIxGB5jsuByNbYJTpQ00tEXNgmeADCLPCHhqCYecR/FdIEuu4AYBMdMsYwjg2QJgUCjDFIxx/iSkEcMF/hVAH4MKkJVxA0x1bljgZDk4BfCVLggAIsAhyKTHdTSbOJmyhVYdBUAA/rVN69ec7XiKODsYeyy+R6zM/IsVyBX4MMr0PXDReYgwYsz9foA0hw8LAJ2n9XSR6ahu0aNn/Li671b+wLRrPfeB8Tx80BInnDgwEIj3fjtsFOP3OXzu23xq5tHXfWnB5R57JmpR535u/5rrnbiIQM/vd368cXri6oAU6jf9+jEGDNYbpmlTj96NzmvvVX5VzaOF5Vl8rjVCoATBd93zcBuONfD753V8WvWWPaUE6gQ7qKvCNd5kCuQK5ArkCvQsSpAk2wDzJ2xDb/tOnd/0SnachtsEmQQbTMtOi237+ywxBSkA2QBGcdJq4ByGDjGIH04vvxyDVpYU57WPZCmViuJGAQ6oDpjvXOXFCiHhLuIllXGX/3Ksc8+dn9Foalp3Q02/cl5v9iy/xqsghGgqxgCBnbkKT94+YkxE58aD2fmO28/PeqP3zn2UMoVWeOCeAQ7VGGMEZncSYp6ap+yIB9FZhWiYnJQv3/q+3eP+Pubk8ZPnfnKjUus9O6K62w/8JOrde8WUXkUCC1iQBGDulNAL0AtYhz4igpgHHeos8oYkoMAdjAyY8aMi/7w59teeHrSq29v3/+j72+00VZbbWUWmI0ACFWsB0WYxkMYxMOS+WKccWWnighZ/VL1W0JlU4zRNWC0CMOaMIDQCjJanxBcQ2HHIOHonXQiazhcMeV72+mbStXvJaFCodhKYLgaIqpMuQLtsAKb9dvq2jP+NOyG/7nyrubP6h/32RO7L9niIW6H0eeQcgXaWQXm//RQzS+It7O85jMcfmJsx+LADsjOab+8veWRH74WdPSQq+97ZKIODtljqx03X7vGWd9eK5x57B7jrv72OV/f1x81Q2D8xBeO++E1nz3l0tfffFv5G/9W+We6284/9rSjdgcMKtvhUNIdw47jRJLMt6a/U17N43mvAFAR5KkisCEUwYk47FOX80S+RAYwlLGhed+RLJkrkCuQK9AhKmBHHaEm6GFGau8hmnAuiP5cWMEpd4EPxBAGW6EJBxRAhimoAXCP2JC63OnDtUDzj4wCWkOXizAkcQExgnAXdlTHApS8T0s4AmK4w77ABKtcukPMwE4cdsMjo+5OagWttNJK1wz/dZcVEjYEYYqQgmBiofdyi3/6oCOD+fiouxhjDSCJlHt2fXfp18avs+QbgA4wUdEjkZBOoBXyy0eBMKLTlHX1YBH/Z33Cwbv9/JtH/PaCH11yySW/ueCHV5151Gn7bvbMyOuUJzyQFMty2xPv/vn+8TusmA6A4xSPxEBIoj8Eg+W0KcUxJdXhEAPuJAKwku/NmvWb3/yGvzace/rXLvvFeXdedfEZZ5yx3377bbPDwCVfSn9/MynSgQzbTceU8fAksLnmi+sgPLIUxRHciVUGBkOQVi9qwiA9hMVXvREzgBTttLRHTOUgxhVmEXAHXUWRAMqEcIKiqu/x4UKYDw7B+9RhATtcCGfKFWiHFeDjHCcf8N3Lv3HVhmttQnhH7Hb0lz791XYYZw4pV6D9V2D+Tw+1/9zmL8LD9xww8sH0W2B3j378gFN/PfSkfYF7mL41feZNf3/kgqv/Hj8fttX6fYd8Zc+yl/977/2nnnt5vb6rwOy+5OIH7bbZ53behK8Unff7v/uNoXGPTwEhuvYnX1y860f8lhCfK9pk3Y9yffGzWz86YeqTz/5npRV6rL/2qr1WqgBD2n/n3fccvPTKW2WPefxfVaUa+mIAAEAASURBVACcCHnu/HWz7YCO1v6rALJwrkCuQK5ArkB7rkDAGbbBdtR2+PTM9P8QTEEBumUJDvJlIMCmOs5fsGpPDkK0WvVcDEyAAGwy0IIygQfpKyzbosvEfjrxsXQFFoGpCwyiThYGibs9SlAIYqIArEpoPXTzhZVJ8QeYCAjRStXP9yAA0IBNBpJZd9v9U788+1Q5f/3rX99///3FFluMCHdb4/0jDz3kiXH3sbTOuv1XvvB2sCTGKbxqJFgz34vveqxH09ubbbwFYZMRfAa4cxeemvra975y3MOj/6mX8v2NN9446aSTvv/9Nzb9zFHwtbzKUu8PP+0QflqLfPr373/77bc3NS3uFpgvMVhwdupfUx7j3FP/jbdAnYzwDpPiYAqxJV586ISTjp865dmyU8fPPfv0cYd97sorr9x6663Ngo0QfsIIFtIWFGATWcBPaFR1x7GAcbVYUpGso8IuRQUslIG5BIcI5UdsuIttRQx5iIGUKl86WMSUkHzqGAdpHw7WTIclMuKxLz9mihED2WXKFWhvFdh83QFXfueGGe9Mz+eG2tvW5Hg6UAXm//RQJcmlV+tA2c5LqLtv1/8TW66r5H2PTNzhi+dueOCPdvji+et/7uzv/vyWwIb4BtDvzj685ofJrr/74V2O/fm2R5476tHKXyn40PTndtlkxCUncJII3AezT095+cxL7pj29kxdABu9Vpwn4rNBm/fvDaK084B+NdgQkvGx6meef7nlmSZN5fu8V6Dt2NC8+8qSuQK5ArkCuQIdqAItG2zgA5vkcha03LT39M9lom+XAEoQEEmxCaejhiNeAIeLLh0gANIjU8eCC469o0gACiehKoH+GECEoYz2kcKdIemOePAFMfXOz9VPeCK9IyZdcMEFG2+8MWMEECYSLsEmBkSoOgKzllqlz1r9KmpNTWBDqOD9xSceFBtiCcurvDsFxQRwFKAGTGNA+Onxjw754m6nfnGfP/zs2yhSMeyTqbhDv6Xe/PlJB7aKDYXT73//+6++/CIq6GLwsj89CDbk6vjx46dMmULurOKRALiIhDHUd/qjJx2SXP/y7G+bEU6RJABMvTX53185/IBWsSGNv/32275U7hT0BEUCsNS4wJQeEfBIEasQd+rgnYGK8K0wA7SccjcwOLGnaW32r2UzxRoCsa3JeHHoTFNMIVbDGqH64iEcCsKlgHdWY8qAwHiEIO04cCvjkUvLmXIF2lMFMjbUnnYjx9LxKjD/8JAN9oh7WvlXnY5XhtkjPveUfTb82EeDxzGf+H0xmUfuvfWVZw3ia9Ah42DiC+lnyPgi9QHfvPyIM3739weemvj8q6A/b86YuW7fVXbYtPKzYiMefGrVnsuE7rnVD1EHZy4D3np75c3Z/zY6F+mOuRTgWscMP0edK5ArkCuQK9CxK0CDDZVbYjpqmHLs2OmuYYLawJQfwAfT6NijCQc7YKwWTbhdulAI8gA9nnNRN/p268iULh1f0ZMzxjvQA3AGTIEJhANowD4B485EdKc1mWSB2VceGymT+yc/87k+2+6jPDJEItDAABd/uOz84SftcfpPL3rnnXcMY7llK3+TWWbZ5QQj8D5u3LgwyKD3ct0sphHqGj4Qw603XKPk73//+2lvvRkVwPj06dM///nP+20jZXr17nvqmT+64oorDjroIDnen3/gT9jEBQlOmzib65EvpL+k6ZHtwL6RwPzV8IrrO2/8/ZtvvgmHHCGK/PBjjx93+EEAQGle0EZbbnvhhReecvbPt9z+/1V5Tc8+++yTTz6ZKl9ATmwEATDWnffksTgwJSZFiQRWZFIQ4gloSctwMFKmxCmenOCTS+CSaY+qB3lwiowxMFbMacV4j8pjFk8dMhAybigRUgE8upssWRbj9zGA6TOmkaSfKVcgVyBXIFeggSrQppfL+NBvA5WiOZWVlu9xw0+POv/qEcP/eH/8nDzL/dZY5ZNb9Tvs01ut+dEVm6VLo502/dgFV42QwbtpXKXF5mGvnsvxhWl+48xfLvv1zaP4xtBx+2/fZbEuzULFaNZ7//f0c6/859Vp22+ylj9ntnbvlQivRqzxpn4kqPHyyhnlCuQK5ArkCrTzCtAG294zsD0mYJphLqapya+ecIEDLmAvbQttavTbQgBMUWEJmYTXVDv5iljxzhECyNOW41TjwAGowAcFAANiShuPLwX4fLXycCCNMyAqwxazEGyCDxM7TJVPOgUJxzz99NNVRtMXD/osY2LApniBBonkF9eNuPu357A6Yfyjjz0z5YvfHLLhCrMmT5yg7uJLL4N36Znni/MnxWT1vms//F6f1RavBBkyLIJqPT3mbxWdpqZ/TXhts48tizv8svS7Id96+OHK9xmR2XnvA/c98cdbrt6NFHbeeeftttvuxBNPVHfUqFFHHHEEBSHBv77Y7Lrvmmuv1KtP7IJoGpUkF7zce0+z65FPvrZp//Q6P8KTX333xjMPm/ZW+nSR9OMf//jQQw9Fq1dT09cP/+w3vvGNa66pQEt3/P3e7tv24wGAEGAHHZgFY9FDdzatFVn78CCPOzealAmPFCA2GnW2gAGrkNaQDw5FwDJVAgzTWpIrXgSzwgijFdvtFAFUEvpTHF9ianHcawJIS6VHiBiSTJVjVD/98dn/vO4ifqE1rWXKFcgVyBXIFWhnFRg5svmffOY7tDbBQ8nr0sU/Pcy3//aqyMkgfoPs5EM/ye+XvTNzFu+I9V5l+aWXSu/Pz4W22bgv35n+yW/+4teLWpVcaolu55yU/nXuxIMH3vDXcb6t9qNf3Xn7P/79lQN23GidXj2XX/q5/7zx8BPP/3X041z+/NnBu21x+RmHXnHrfV/7/MBWzWZmrkCuwIKuQNclXp53F4stkc4Stlt6f2bPeYztvZl8hCRTrkCnqAC/R7nZoYNpmCVaYlpr+vbgtKwCvX25Jw95BhAdvkbKTTg4kZABHbgojE04KkAA9PAaRJEGnr4d4IOxhB379uBgQS0FmOI0unqFdYd9tRgYAypjn329YrqpqUuX9G9U6Bp8uKACLz8+KsRG3/qbLbbd6aHJD/ABIJn9N9g0KRa4w1OlV9UOOXB/+PiCiByzxkBUWy39Egdw0kJB6/Sq/KsboMbIp8eOvPOW6krTpw88Yq/jz9ps1S7kxbXyzEk//unPYnX11VfHpvle9lzzW3Kbfmp/PSatAn3DMhfCv3/gpecmN7tedoUVqbngy9jbLp86dWoY51W7ffZJf2GTbvjzCH7mojprev4jvU8oHhVs4gIL2mdKysJG4jhwIIJEjIsptSIwK4wWG4RKQHixywwgJCGDZMAWo1LzI3oKKI+AIeE9QsJjPBXWBA6rUARAGJL2CbVVqsvXG1u1nJl1rED+ikIdi5lN5Qp0oAq0/YxFW+Ghf/zzXwP779qBSvZfhcr3gNbuPa9NlJb5yPTvzz78wfFTbrj7oScmvfTkpP9MnzFz+WWWWmGZpVdbedltN1rz4F03X37ZpRAGgbrup0cdecbvnpz0EtOHn3ju2LOvnlN4y/ZYctdt1uOak0Aj8ceNn7x1BsEaaUcXWC5zwmvmBM10XXJukE3XJV5dYJG2Q8NP1ium92ZW+roag++9M8f/8WwJTmUQqqZ6ebrwKwA2dPHFF9909GBa64BRCEOUAY5NPlPabwAOB9HkswqTIzn05BDyQjNhjZYbyMCDPNrHgirwNY41zarFFMgACiMMaPKDk6bVQyiMuaBo+9O49LJSpCA25HTllZv/O/3nvyft1j9FLsaUbFU/UL32sh849X7hd48qT79+zBFaA1B4a3rza1lL992UdIgKj0SljEeizjr1rLCwzW77d196GVcR+/XQ78USgz/+4Tcj/3TTrp/a5b0lV5w8ecqD99xRXu261jZM0YW6vd/seuetE2IF313AbND1v2x2vf/+yfXaPRL4slbXV84999wQY3DCCSfA6bfptmuu3OPBBx+8//77y6ubbb55wlmmpewAVoRa8OgWKwnTn40XeXFz01GmohrKWB8ERI4qdajucnhMW1l8lxqbbrSPDWPTxHg4RRKKkCgCxhVQF2uk3FSFtFgtW0sPQCGPsPa5o7Jhzw/4osTU6bM9DBWv+Y9cgVyBXIFcgYaoQJvgIdCpUSP+2BB1qHMSfGSa60ONrrHaCred/+UfXPrn3/1xtr9zlBXXX6vXfjtvcvQ+25aZeZwr0CEqMCf4huBbRXDmBN90MuCmQ+xtc5Bz2p058QvNeQKnWgJPLSGnMtKUMabmXcmj+a2AzXlqhgtAgSkk0wF3e35kouVOQqltbj4AYjuNQJlgChMw0CYtNxeAgnyd2tXT0rNkS88YFcmQmML0zEsECQdyykAVLDDggs8V+JSr/2/Tdf5+g4ab+BDPd44bNObFLogRgyCRh2IeX2ONilCLP/r167fNNgmjgQh45nvvO+a++RrL4IWkvlTANBx4gQC8rr1z1E033RRie2y3MTJG+Mtr/vTYI+NiycFbb75x/fXX1zCZ8lWgjbbbJfj8elqMl14mfRcpeS+cCoUAmoy8d9Tou5pdr7jWxiSLGFU64qRh5U8OaYqX78rv34X9Q4495aBNV6DgbDp7hxEJjlspCmYZQV7gE4nVQIB4qAZaAjFhAQEuSINuqCnIYVXoBxl0YcJBjG0K0oL7TmGR9zmB7wOGJAN1U2WKl9q4y0EMwIuYg2PwrE7bcUcgVPi49ilNQplyBXIFcgVyBRqoAm2Ch3bcccf7MjzUtqeh+5KL//iEvU4Z9Mlr/zLuf595ceLzr/AjZR9daTlOLW3x8TX45NAySy/RNg9ZO1dgbhV4esyICeNGTnp0NEJTxo+dm+ic1/putOacF9PK2tVfA5y7GKv9/ssjcuttv/6H2swCNRV4/J+P1XDmb/rkqNY/rzYv1p5+4Il5EWtV5tlHJrbKn3emT2w8ljx162y5A+rCT+++2SnOac57uTqbpE0yRzzskE0foMf2ntUyxz45mny67sB6ELNpp9/WZsBJcKLlRrdX8aNgYATllht3yGufVh95kIhySNint2dJSs0/YFOPJCk0EO70jgAcwQtkIMRmrtf8tPNdoR9d9LvvHjdIg8AKyAsobL3LPqv0uuClqVNcKt93//wxTJP3AonovepK8d/2HaP+vdt+m5mUKSD5yVVn/Ohn3y5bWHzxxZUh2duubz5G3X+bT00Y+49ZM98uC8d4zXXX3/Wkn5u+3pdevvlN2CfH/3vSspuVYRc2dMXFZlw9dDbXPZdZHKfUZ9asWWPuakageKesDGCFUwccdxryreNRhEC7AFMgi8wWMICohi8GEiGSkb6S3NkCdyRUXHJrGKPlMxD7GAKqoB7Ek8AxJUimMXDHWjrRVoBBjNOGFnhZPGllTBN1VAQQw3Krg1BvdTUzcwVyBXIFcgU6bgXaBA+RNu8Bddzk20/kK6/Q4ysHpOYk04gx6VOXII+5FAuuAkJC916X/g0QolX+SLem3b66V1NT5SGcF8xlThDDh0IGc8cFXG17/29q83L/UGxrXozUXWZhVuC/DX6+KxZwDB7L4zkF8F9hhfPy0OrIR7f8oF5yzAVNTReQl1F95pR9kXznjX4cTconkua0Ow3P5x0x2mnRhxjQe3PZdXushmkFRqniAiA4NtgKWCibdq1F6ejVIdTt2ANlgBm4j/bFaAA7jERragUHa3oEJoAJld0RM1rCCuAOvg+FzLbbbstXnJ+d+HRSaGq68IffmfTQP04//fQXuq4uKlENoPvXvvmdwScfr1jce/XqNXCP/XCHZRLBS781e99dXb760mEnHbk/L9PD0Pt7s2YddPiXwp2C06alV6FAHO6bMuuRf/1V5gor9zrrZxct88Gbl1xyye3XXDHznWaQaMWeK+2yzyH99jphr/5LKuyOdF2xt1Puv7pw2Gnb7v/CjKXwyyr0/nuzhp3xpeeerWSq5JMvTCNsXI8ZMyY+pbTdLnvy1aFPHnw8P9b2z7/cpqR3MKm9Dv7ioYcc9HDxLnLUEyRI5EVfCGOT/YIZG5RKVOCA7CarAkZaCBdEC5+7h4+wlupWhfwQ0xo7aF6x0UzdBWQiBpgebjJHfEHEEKhlPAZa074AohzuyhjDW8VrZb2W7pIMZcoVyBXIFcgVaMQKtAkeqvy2/ZgJA7dYpxGLk3NaZBXIX9RbQKUHGPrb8KGcEqIT/uQXdqX9nvemuiakOSnOiV+jvoCmcwKtFpC7DmR20e5LuyqUpSgXBDyIJ0fA6G+/vpNLqAg+L7hxpCifJ2pXO7igg4lWmS+z2HLbTuPXdh1mQD9wlCn35OIC9uRoMYg+nCn2bdHhx1EjQAF6flAGKZAd7aPuuSF0AwUQZUBLNIrmH9ABgiMo0JxI9VAJKjCx5geSCoEu+3/ppJ8NPqHiGACioPW332PXnQassOn6r7/++t3/fuGx0SMeGRWwT8g27bnnnl27dWua1Qw69N9p36bi/SOEXvvP1AMPO+pXF57/bveVefNrxhMjTzvttPIXqTV0zz33HHvssQQz/tmpYfrjA3bqtvjiG6y60vlDvnvIsd98ftLTE5/8341W77Huuuv27t37V48txrGdqLBaW+2y751XVf7Z46UXpl531lFHnX7+2OnJ9UP3jbzsJ6dNnfJs2Hcw5aF7mpqS63JUWw/clSL3/Vj/q3994YwZQ8ePH//MM88826XXamt8bL0+q7jR7qmbIhJEVcVftIxNqg2x14wlBLjYZSEY5VnVZqgwRUsxl4D8WA1kx41O8gXiowXusbO4c4ojLDAOGWEpv2ztqsIIQBjUI0+RT1rI8J/Dz+/JwFCqUgeiAf2360DR5lBzBXIF2kMF2gQPkUCj/rZ9e9ibzhnDP8ZVfii3c6a/4LIuA0NfvviEcm+84JwufMuNmtfCr2Rn88iT48MTUJE4ESiqh4kyQtRJHgmaZLt68qUJt4vm3ZwaCmAiyc/eoivpGRCt2bRjjSkdvthNMj6t8tvngjVwGPgtmLK7MtZD607TjkGaduQhjACUIBMYFrACdnSHR9EB/Ao5YR95dVEHWjrxsL3/dP3wx8beV3b62D/v4DqvzGptfP3Nt+9+1HdX694N+ynyVZrWWm8DMCMgJsXH3Tdy8803777Mch+8927LL/soww/xDr/zgRX6bblx91fCySuTnhTMIn4QqMV7rbdur/U+XnxKmQqIWQhhkDhFYBf23HWDv+2y571/qbjG7MhdN++xzHIz3313Tm+o3XfvyA8mP9Clz5b3PtX80wSvTo7X45q6d+9O/JB7Z3j4hcCGHOCd9JkiEwVHIEXF74UV371mGsgLHHHAGsgPvvuCEZ8fNhoSr0mj1sj9NTwMUhweWkwRUjOyUyiy5DPAXUgxLGskHhLEiaTy8BfvrMEhnd3XbBreWgyZlyuQK5ArkCvQMBVYrO2ZnDO8lX9QarvZbKHTViBjjnXf+rsuH3Ll4EEf6fYawNDXrvp2xlDqXuFssJEqwH8gQEJDH70QbAiQ6JQNj/vLZf8zddL/LL7s/H9rqZHq09i50CfTBtNsQ95puQE+yhx7bJpte3JUuGjI4UhMoWSqegYk4BhwHNAZltBNPXwVIdI+U1EepxjBZjT5MLkQwALAAWPgCVyAOxAkusIlgRwZBhYQDrMJRimOogTGMfY/i33np5duvPHGBv9f3Tkf9NvLfoEKNnFBoXC6/wlnbrhh8WXjqq0Zb71Rgw0NHnLOTp+tfOQIqauG/xYLq6zS/IHlxx95cPRDj2ON+MkR40QOqsI0cBaqKhMBkx324zPXWHc219PeeqMGGzrhe7O5/u1vf0sAy/ZcuRps083XX7Nr71nlDaVW1DD2nVKzChEAKcdSFFxTyCSQpbqnBCnBYQe53GW0yAIjEkwUvadtLQ4fKUkA6DImHkuNrpKoYwRKMgVUF1VCILkoftheO6jzkCDgFC0GaKEbnHg89EjZzQ5hfIVYcpkpVyBXIFcgV6CBKtBWeIgfL2ugauRU2kUF8kNV320AG3ru8T9nYKi+Vc3WOkMFyiAR3ycCJMoIUWfYd9tgG2DHttx0yJAc7rb09uTw6aLTvYpTpEn1t8wc02+zaluuKfjYAUEQBZDJFHcQU2KIJj95LI6E4AgBzgfRpYMRgJtESEy1mf6okoAUZssZoYIwlyjAMsuvcNFVt5511lmLL7lUVa/1Pwfue/hdd93F78HH8i2XD31kzL0YNzDCXn/NVfmu8777pm94taR+/dfnbNExRxx08teOX3bFysekZ7w4AcXnF+sVHBR/8q0vX/fom8RvkYkZSIVpDck0uylNq55zxU2779W6a74cNHT47Xw56LAvHb98z4rrRx5PZ5YP33mTMPv6Ky9//etf/2j3D6gtp2yw7IGsEGAfqadEPOyp3uGwR5BYjzUnbCtDFlgjTVax7EOForoeHGMV8lnShWLxXLnXbJwPCcJwCI8i4NrdjGeSVYwjoAt1cYF9xk5Z8snUoMIwjUH7psOYF/+HDx/+9c8NZKwvBplyBXIFcgVyBRqpAm2Fh/iEcP46dSM9EIs8l1GP1n4aYJGH1KEDuPykvcGG8omhDr2JOfhFW4EySHTa9rtMfLT5540WbWDZe30rwN9njjkm/QgXRJNMQ14m+vky0RvTM9OTc7fBZtWe31acKX04wAFdty03/XZgPazW2C+39KzaugcoAEeziMHEO9gBxsEmoo1HgFWuCMAgERPmCLNag4kwQAO+uF56e7GVBx75q9tHn3zyybxLtUQVJ+q5Si+OAoEHnXjq98659eHhw86evkL/k77/0/U33Qo70g+/fuTDTyRgA9eYJaRHX19i2LBh3znn0s223HqppRLk1Kt33z0OOPzkYdfefdefPam0xbq9b7nhug122gtM6uD99iaYh15Z7JhvnlUxCgDx7NPnnXJk93deIjxsCp2QDhUwR+7/nPBazxdHzRh32633jFFg2z5LXPrLYZdeemm/TbbutkRy3bdv38MPP/zaa6899bI/r9N/Y0ztvmnvm66/bodP7YXAjp/aG/v9+vUbNKj5NNMtt9zC97lX7vYuyJ0bLdbD2CLja9KkSUOv+8dbY25aeeYkczdyBOLBwDK7wB3iLpJFCi2hHzgopg0tHjxV0EJdj7GtCMCJKQMMwiEvjIBAQWI9BAlmhAB3PbKEZJBeQJfYtTLhgsvVsK9HECIzCoNlxTzOFcgVyBXIFViEFRg9enTbvXf54IMP2milT58+w4cckb9O3cYyZnUr0GeP7/FvU53n09QjRowYOnToIceefP65Q48699b6PgZn7tEnvpxSX8vZWq5AJ6zAbUNv5F0zEt/uc8d86qjBbakAPeeQIUMuvvhi8IjBg9tkqi1hZN05VYBOmN6YNpiWuzywQ6bZpqOmJ7d/FpWg63aKTcTs4Ws4NuHYDFIgHGkfpgPEwCM4V4IKTg2mxn5IwtdaGEnoQBEVHOMRJgiB0NVjxEw8kWAYAXfguZVgTn/z9XOO2WPqc1Pk9Fq99ykX/bHHcisITIRHQ0IGDrlQNKjMVJ27q9xvOP3ARx74V/BBlw46+uSBe+438f1VrPnkN9+//V+PPfKvv4z5x1+fe3xcSH73xscH9OkeRcAUcAw1F8ggANIHDoNkkiZTj/+gdc+Tbxy7z/ZvvflGGOQb2Hsec/onBg5cqns6LER9Vuw6872Jo+++++47/nzXc5Mr/5oFmnbzzTdrDctuk/kSgAVBXQEG4RGODwOWVTTCeJZUqSmpmxi6GDRlhdFFANKyVY2a4AhrZbPubxixSqGeDFXtsyRprTLJf7TjCtCg/eLEX2+3wU7tOMYcWq5ArkDdKnDYj/brtvxivIXTxr9YtvXT1CTEl2L4/FCGh+q2t53YkL9q33mwodjqpx78R4zrNcjYUL0qme3kClgBjhFxFSBR+nWkNiJEuartswL2wDbYwApccmyJuTMFUwhsiCxo7AUd5pSRFmjLGQjBIBkdO4PAfbQPEFOGFURqQoUB4WGHSyYdPkQYEkbo/wPWYQxGILIDk+8NQ8pwh4hKCwIHBhkJGjYCoFQSAmAQLzQtf8Yvrjrp87v6USFwovO/tv+QS64d82JPzVakiz+0Kc4Fgyky3CEjIUdSZsy1xLfPff1bh01+5slCtQn7Vww7m4uPTH90jbWmT3uz5sfpFeM+aL3/u+flitmARZLrAo9LRSvey4PDfvndZcrCFGGKuelHl7vs0kuOPPLI+EzSlClTLj79GP5rB/xarMdK77z58itTK3BYOGUwY0baCYxTFixbMdxtViBK2CepEHBMPVNg1e+FM6YC1kd1tQw4KReFSttXPdTDk8lqWECAaboXAkiKE/UqnhOeAYV9SNKDXXoaDcl40s4WkCgyGIGIR0IMmUy5ArkCuQK5Ao1dgba+XNbY1cnZLeQK8LNl+bvUdak575Tlc0N1qWQ2kitQUwEQIv7juve6i/mqV81SnjZGBWiw6Y1rqKYxRkay8abrBg6wnbbNtqlm7NQOXBUbeMfwUydfHPeQUx7DYQpuAn4RCBQGGafmv3jDiACSWPHZZsdMRQo0Je6QhAoIycgx0ipF4uWAkRQ1YKAilklwtd5rfu/nvw87ADpDvnLAG6+9UoYVkOcifixgPMwq45RoQaNMB2uHbbP6Ny66bfOd9gjLDvjI9BP/HjcnbOj444/v0aMHUZEgV8BbuJAj6oEpOPgiJArrFGFf09tuu+34stLaa6+tx7gDfnFMqVVsiLfw9v7CSeRITSyLqeHOKUYsgr60aZyEEUQM7JRkWUAJHchMLopNh89GM4WwwyCmCMBJT1RhWawKDgPy5SmN/dUmWROk5IBaOdAdKjpCJpntkUIKTkUz/5ErkCuQK5Ar0D4qMHbs2LYHUgd46JRTTuHzQ577aHtA2UInr0D+LnXbHwC61injx9LEtt1UtpArkCvQsgL8x9V3ozVBiJ4eM6LlauZ06ArQANMS2+GTCJ0w/bBdOmMuBOi3ab/p0hFLSE3xYRcab5k02AhAaDG2LQ9TyHBFg80AYaAEkR3uojmaYjWhDN0r72QxTZzi0zMRFfIEzFR8QRncYZaQCBJ+GRRI7goARSbyYjfwy7qMJQdWIMriEkx+jZ4PDFVEm5qefPLJ+2++jCk2yUUtxsTgmCUGBBCkTZgKGMPWfbufPvTiE888l08ChWSrg3X6b7jDgV+5/JpbTj311C5duiBDuSADYEAYcspO4YMNAY7gzspzjAgBpnyo6I477jjk2FM4rJQMzZn4WNVpp53297/dvfa2n04lLT79ozt9WWHzhWOC2sMRuwYZAEs8RZweImw4EkUTCVKMO2JciLHpSGqQOw+hHhlHvsiTONa4yJR8iTBILynr4tlDyycNAQauYo0UCIwp20TAkZFFC2t5kCuQK5ArkCvQTirA/ze1MZI6vFzWxgiyeq5AVIDvUn9j8LExzYP5qADYEF0rv1M2H7pZJVcgV2AeK8Dn3od9/idXDh502JDha28xcB61slj7r4ANMK/eCFvYwxM2rTIc2uwvVT8zTENu160KMjTSTf9JKdJOI8892vLUexc/LAVfAazRvSeVwjjCyGCT5h+DXCyBAtDVI6aWIcUUReQBEUAHCFgZUAOIth/CJqsQzTwGIyosuIoL5MvYDVMCgIwf++EODgEob8qYBVR6bpXdl137/tFX/vDGG29Ecdpiy8GEEOYFLiNkagwOuBuDRTBymLqwyKsv3aXLnp9bfsDnmp4aMXns3RMnTpwwYcL06dO7dl921T7rbL3BWsuttdnhn9lhwszlUSSq5uyqJ1yonunrjmRDBk6KvPj2EBEaMHcEuJqauu90yNfP+uZXyOjme8a+OOmpt16cNGvWrNWgPmu9u/xaa2607Zc/M6DvCosjivxq3fgzEQbZRCgqbBkjWR0llSqkmKoEtFQ8CVSVjYMTZbEg8T5gMl0lHgzshH2MQAYAMyKBTz3dYuRDBWHEvMNEjEed4IkQ72kLikcIAaLiAXOQ/ijIB6A6y3/mCuQK5ArkCjROBeoAD/mlmPz5ocZ5KBZdJhxD64QfHqpvvcGGePNlve3Xr6/ZbC1XIFegpgK7fXUvfu3+b8OHZniopjKNMbWlp2G2E6aFtmeOBptpnECxIQePAFtBBbQCEkmxgbd7tyFnSSYCexTwkByOimAwGm/s0NUjYyQ0/Ay0k6wLTBTIEUvoCuvEkgM9EjMdvvAHfJkCE4ENIYMvQAEGuI5QtcMdvpGkkIqwCTUxezQN2mq1j68x7Ac/+MF9z07fbZPVVRFoCHdIQqgYCWNhCGwSf7nIhMSSwBwFadpk4B6fGhgCKOLRCBFD3VCJiuIwVTKYWAgZncYUSQGsCAAtoyrAkcU/9omDDh1wEPIQS4i5xSgi9vK7lTpESKQmyKJ3UtaakB9TtziChIM8WgQfTwKr5V22jMaQ4mhK8mHQqlIQPDK2tgzk4B1KNSxIGWPAbJmS02LT0eXBxj4Ds8AIq+piltxjqWwhj3MFcgVyBXIFGqMCdXi5jELwvZj88/aN8UAswizOvvzO/OGhNtbfj6Hk18raWMasniswLxUAhOWYHi9y5o8QzUu5OpYMDTBke8wYXCA18NVjNTTMNPCgGPTYMFlCxrMqNM9oCevQljNGF7Lnd8xU++ii5Zg7WoAFMaUVhzzEAZPuHbKrZ6qYEeIUsQrKsFYaEx6Xq0mtCKCsAkf8RSSCpXKCrMJhqcYdfDj6wh0y4gXw8fXP15ZfabUKNqQFsuYKv6RA6fQLUxgCSfhMuYiBImAKL9g3AOSpEqVDwKTIF79MMQ6xhArVxmANYa3MxFF5I7CgLwPQFPJogaEQKvEgjwwXwoTEEt4hKwMHiiIgDMeNSwvFj5QRmCoYKceDQciQfBLMEabGCQBCzKnyFjAMUhAupwhTKwQiHktNVJYLAWMjx/KjSGASLnwMMGjKcKwAAnCI9rzrRpQTrGjmP3IFcgVyBXIFFnUFtlh3QF1CqA88xOeHiCZ/fqguW9KZjeQPD7Vx9z061EYjWT1XIFdgHisAQpQ/QjSPteoQYkOGDKFtti23Z/YeZ0bMgoY5iEZaOEYwKPhMIazRUUN01EIe9Nt25lgO3fBoW26Hj2LEIArQ6jRZLxzR8+MULXEEoAEH3gkAI0EwkSQkLtyJy+BdUtg0daquY3RdKgMTwdRdmhZHWkKlYrk4pcJqGYZgCVPGEPLCOkgSFegJsAsCAiWVKIs/kCcRUhDWwU45AETcHZjykceIKkQowWSMETeaKZW0noylcgUqrOIPi4BxjQSYxS6bUWyE+x7xoG0AKEaOWNNjslZ830pfMgGtYMY24ZQpl9khQ15MuUMwGZBshV+822iCAnxqEUOyU3xXm4BJPOxjhHgM0jC4Dz1p0E9+eHZM8yBXIFcgVyBXoMEqUB94yBeC+NmpBqtOTmdhVuCi6/6xMN01nq98dKjx9jRn1P4rwCtmBDlh3Mj2H2qOcO4VABu6+GJ+xLyW6JA59OEBFtZs8sUp6Kuh1F1Xu3Sn3BGACaiBOmTLnYCJGYlpt58WwAhKYBNTreEuZFra1x1ggcFwxyZBal8YRY/IQJqKmA077IuDlFEArNUIpzgLMEXdhCa0SBmD2gRnIc1AGWRGJCwRIYhPWcAYUqxFhRWIGGAqoHemAB+6kxNQjioEQITKIAxTp0YCByJ+YRSnBBMxWFURIu1zRx47DFyVbxF0BwcjXIIv7CA7gkc43FmNeACMiAcKjjF417JjLFAKPcIhpJgGE+NY45LDVHfIM5CSzLT0kAT6Y02SYoElEaQBB5iVAi6OjyFgSOXAwnvFQf6jvVZguw12aq+h5bhyBXIF2mkF6gMPkRyvBfFd4XaaZQ6r3VeAN8uIcfDgwe0+0vYbYD461H73JkfWuBXwANGkR0c3boqdKzP65IAY6IFpiYEA6K5FiAACAtyx94YDMa6ZWjV0pWinBRSi02ZgE44YpoIPxMDYALQQ9pHXXbyRRANvkEiCcRCqdoBIBBSwTF6QRoAYHMBhCfkEFlRRADhgBAon+KAAMhDAgmbRRdgYEAtm0imIGFoSkWCKjERhsBCEBWIgBQQYi1NEwDDhkKz7ggBXxM+AdDCOOvwgVEIGpmUhNUgxV0PFOmAERbaYAe486aOW8tyJXA6mwkWNO5YIqUyW0ccAC6ngRWFDRmY5R5aw7y7r0ZAIj6kFRIAU2C8CjicTjsYju/CCmDGwxBhCUiNsAQPKzmMT7oyBuxwEoFWW+sDdSZNMuQK5ArkCuQKNVYG6wUP+vH1jFSdns1ArkD881JZy56NDbale1s0VaEsFOEDEF4jyj9y3pYbtTZc2mOaZnt/mmfDg0D+DMthUlwOu7fMLECQ6c6EWmnk4XNixx2ZMow60ISIgk2nZIw0/HL17LyMvtOjgF+Ug6eFt8hEWQcA4OAVaxsMdgyAOeudOOrr2jhYcm3/CY8xFSGIKKZQCXlGYcTqkU4WQXMK+WI8eZXInEkpBtKwyDgxCMQxy6YuAwz6KIjuBhtRU25RRhLCpNRzhJU5XIUPK2o9DTMjrBRUuLDDlslzJXEFars4qfxKS26Q7ZMrurJvbqgB3phgnC4JhIAdzTL3DlJJYUVI57AXGy/tuSBEAWSPDnazJDoMsMVY9AsC4IRkDU1QwxWUMEYClrkRT/aMcQJWX/8wVyBXIFcgVaMAK1A0esjb580MN+IwslJR4syx/eKiNleYHy9poIavnCuQKzEcF1tlyh979N+MnzOZDN6u05wq0BFY8TiJWYuNNy81Fg80FMsKYflvIhvablpvW3cbbnt985SAGbBGEYo1HOSGAC+RFASIGsAPECIaLgZa9Ew8qYiV4N0LkDQnsCVIyXLQcIEBSCGOfMUlB5s4AvikDTMAUG0JMSTwaJ1MI3AHvRIIkMVg3pq4miYJMxzEWUFEALS7SgeBDTE057GNNgoMiFUYSGdxBBE+FCdKo4FQUqzFokCIjozsGemQKecdyOUjtoEU1cMdds95TANWPHCGJIpwQcDd1kRxUk1KMKaGSFA+eSwaAfAiQGkzuXAQmjmO+EQC64TEZqpJMZlE3TJkyS/qSQ3ZkIWf06NFVA/nPXIFcgVyBXIH2UoEB/berSyh1g4f4/BCnP/h5+7qElY10qgrkN8vY7r4bbtOWTefNsraoZ91cgVyB+a5A1yVe3fmoI/IBovkuYLtSjJaYqMAvAg+iZ6ZDlsmdDr+Ma9Cu05YDPUBiEAxqTmHQzyOm/bQ6I8mDXwTQk3SLcyj4wr5EDBBaXKiLAhCVSARLtPFcWCvHg00UAyxAAMtGyFiD6Y8q3hEDtEQBcEf8gWugjn2jCgtMDQ+OkAoeXcUgoRIwcWrExFktZ0RIWHbJuwEYD/ZZ1SB3c8QsY/jlc0DIK8YAd1YejnAbTIlgsE+olL3GKVMua4gLZBi76XrEguljH8tcxgNf14gRlRlV/BV/wMQvhH1LAUfvMIXMEPAx8K59cyTUKHJ4TL6qkBOmDABdAm4JXRVRNN9QZF90bUbYh8MYU1rAPoRZmMGxDvmgdypNplyBXIFcgcatQN3gIUqU3y9r3OckZ7YAKzB27Ng2Wp/46PVY6LfNem20k9VzBXIFcgU6eQVoiW3dbbmpBs0zLTpdd0tSkrYcAQAF2/6EqlTfyVKgbFD7cJAJqMIuHfuscpVhHZncgQZs2g0DdZnctaZ3p3EXSEIAdYEDorXnx7s5ssoFOdUL8pDjZL/6IlIgLwokoYKEOWBqSqZwDHc8sqR9lhgwFXyRWRYQhqixn2Io8LWoleeAwp0DZIgEm6bJVNyKVS5ShrAv6ld2Cp94kJeQEU0zX5jGiYyENSCVkIcJh1UsO447ikgKZkX1qKchIYYAgbEEh1rpSL6AI7lA3HVHZRgjbzpRKJ9S1LWAtSRWfF4aDlOIO6mhC4clcpRgYo348ag6fP3CKUNR/3n7Ay6XKsr5j1yBXIFcgVyBBqpAPeEhy5LfL2ugx2MhpcKbZcccc8xCctaIbp68739Ji0/kNmJyOadcgQ5QgfX/Xy/eL8u/X9YBtmoeQqRt5ooGGBBBbIjW3R6bPlxogHYaDnwEaLBtudGlnWZMM89YOMOWGxnkYUK23HAEg2Aqo1896s473bsDdBng2hgC41CdKZYDhkA4QQ+FRwS4iBZQQPuswmEVa6prHI7BE4xOywbhcCFAAGanDBb05ZQ7UwjJmjM1mK0BX2oEtG8MjK0J9imXUAsBY1yUBwEuCRku5EkzuS6+y6NYRaL6B8aTQHFHHYPlmpMXU+OPHBH25FEUWXcGgAVWIZiQWo6ZArLELjAlEQhOCDCw7GmhoOSle8rRB88tcNPhkJpZEySSjAm4bA0bPKimwJglw0YydFHBqXZYZYpHp9QQj7jDAgOY7sLKS3XhMhjjzPdcgVyBXIFcgUaqQD3hofx+WSM9GQstF98s23HHHReax8ZzxK8m5Q8PNd625ow6UAV4v4xo8++XdaAtm1OoV//x7/TD0RKnXrp4v4numgEQAF00KADtPRzaaTiQbbnCTG2eZbbqSASHfttWHDuQY+XRVR13kGPuyHCJRBADdmqAFYRFRlRhijyRR9sPx7DDHRbIyCl3QQrUKQKm0rR4jyligMPFlACQIUKnWIbgQDCVJ8fATcIjZokhJGsEjAF1LsLGOPJaS6YLAm0RrcCI8cAOGesPtMHAIidUpYqnIGlUBoA7AiAePbJK8PjVGnfG5ogFUnazsBzu4GOBkFiVrBiBQfhCWFgHjkWI4Jk6ZhDxoMU0YijnqAusAf1AxmCV0rxE+CJU3cFmgJYbSjysal/dSFmDCCMD6U4mHhnwogAkp+QtD3MFcgVyBXIF2kUFAGTaGEc94SFC4f8zxo2f3MaYsnqnqgBHh8i37Y9ypypaTbJ89KSGk6e5ArkCC7kCfH5oIXvM7upbAf+VotfSXTRLA0wLzRWdMAMgAMAUO2fF4NBg006njrqAA2jmkWQq0ZNDTEEBZKZuv+i97dVZRV4IIMEQ5S/+FK90YVYEAckaJIKO3RMr2kdAd6ECHxXsc0GBAgAcgGiwCke0CzGmKIoaJOnq4ZoyOpMMFmATihBT8SkGZbImcIwQlMEAEIsYWIWJusAKY2JglRiohqQW47CvQWsIM/gVBeGYwkK5yAQg0mHYUTfsi+yEo7BTMyBOSHdkF2MG1Id6RspsFq6JEI7vlJEXHFyYF0acoguTcapJCYMzyAgJg3rUqXw8UqioQFhGBsICTwKSljSqpB1SZqAd72anrpwkU/28EXx8CTaNHDly6ND8GX5Lle+5ArkCuQINWIE6w0M2+fn9sgZ8UhZMSh4dym+WtaW6/pz2Z07Zty1Gsm6uQK5A2yuQgdq217D9WKCjpoXmitZaDmBKTVuemv8CSIqWmyzosW3RQSVsswERaLmjJ0elhlCH4AtkAAqIy8BnYBjgGoFEYJ8xwtpUGAsgBVxMEWjpRQ4q4gvI6857GSbAI2KgDFEEUSqYXDDBPsiIgjCFjNC7HASMMC0XNakZmAIukMQ4MRAAlnWEMNbIBQ5hw4yUtQaTC3XBEVaRIXGnxGCyEX+EHVFhnzEC6OqOjFSPdDCCAGIUX4PYMUFkUGS1zClDP2oZbXIgHtRi6+GjVSYdwcGFS0QFU74u5JQfKuVRoSaxrYQXWroAjwtCGIF4qrXAncippBuhjCnfPuK+0M2DXIFcgVyBXIH2U4HtNtipLsHUGR4ipvz7ZXXZmE5lZPDgwZ0q3/ommz93Ut96Zmu5AvNXAT4/hKJw7fxZyFrtoQI9+g+kGbYrjp4fCAAmBIeum97bRt2GGT4NtqQYYwZJrPhoCyogRAAftNwKYB+VylX9SjE25SBTA3PABwUgDCwYlQ1/BIBH3AGyCAowRUWsR45+ARTgi8UYMFOXvMeXlZlinDvqemEsKlFRLB0tgaOMIEtEBQxB2BYTGV0Qp2JM8Y4wLoAnxLMEm3CEVhKonqlBC4FwxMAaMkBYPtZAcLhLCqAo6Z26GRVTOQTAZfWIlgAgOLimUHAMkimKlNTKK1OzC2iVCftwxFn0xSq518BPMIOjGFMuJLmiAiwRJMEQlY4UjocKO/KJKh4Di481EoFYwgJTzKLOlRIsXpqDCZU9wqG2cCAtc+fDQ2meKVcgVyBXIFegQStQf3gov1/WoI/KAkkrf5S6LmXtu9GadbGTjeQK5ArkCnTaCnD8efjw4bbBoAwOqAYDOmq76HJxYNqfy7Tlpv2267bl1ggcDII72OqHVrJc/Yy0fTscVcowhzGITegLC3gHLNAUd7To5AF3GAusEDDAgUEqpmXuRIJ9iJhl1kAABGMirhqATAPgjgXsYyrOmJidAnrEvr6YRgwgF/DLHlGBSahQYD2YxWPAHPARMB0hDx1hCtI48oRE4kyRQYX4k2Xe0SuuhH1UfzAOMa5IUGs1d2rOFXmhyzTc4cWQzEU+BpFnKa0WAzgRJEzhKv0C9ECOuRN/OSSMQ9SBJRQhq0GOKMJBGGuqK+Dd8lKBKAJ8NgL72FTesmABfhQZFwjoMflrQRjkt1YHDBjQYiUzcgVyBXIFcgUapAL1h4f4CxYHiPL7ZQ3ygCzINHyzbEF66AC26/LRpbW3XLcDpJpDzBVo6Ar4deqGTrHxk4v/QbYDpxmGbLnFQRjDBBmhA7fNBhqIdjq13AWQVMY1EKMJB1ZgNfpwVAQd5Ni3w9EjTpFHS9fGgGWiQjKAleSu+FiPMIQGcYdxdBFmzIUKUwZB4CaaCvssIRb54ktOqOCUlLlUYap9YoCJR/iYZUwYZdQDAX84jBiMEJsaCY/BQd0xAsTApaR308FIuIuU0SIk5JHRHTKRMjFwwQkiVIgia5mxaFfSLQ58wUEeg+XsYDLlche4GxIYDSmnAIqzTviCWE0WqmeskITD5SoCLLnLsekcTeIxMySZ5SJggSW0GCDmFmsNDkQAkOMUJN+fLgAyTMFkjDWoJmX4se9puUCIMAUTSbxgCmIMB+FMuQK5ArkCuQKNXYH6w0PUiwNE5wy/u7ELl7NrewX8KHV+s6yNlcw/ltTGAmb1XIFcgVyBqEB04MAfQAb20nTX9OHiAvT8NOdloqu3wZZJzw8FBwuYipM+LIERBCnmiRJc0H7b5OMLLZpz1LlQIQYuOnxfGdOCPb/AR+AOLBkDA+wjjykTkaM108EdXggAy5EvYgITBoOuEcInBqasMgjSlFNCwjsuogIRg+CC6aCStAosxtg0yB3dyBebyoc70yRmZMopIwkniAg1K4exGTFljDqbaNhMSZMSRQDIwIkcGURIWoNT3gXCQB2OCZo4MmWKACwpS+waAQBIMUAFPiFhijAQ1hRjLvNKFoq3wNTFI3yYkjgUnCi7fN0ppjX4TOGbcrJffepYEn4ieB4GqwQTGQiOgzTJlCuQK5ArkCvQzipw77/vqUtECwQe4t/f+P2yfICoLjvUqEbyR6kbdWdzXrkCnbYC+TXPBtj66MDphOmf6ZBrKIEFxRkKemxWmSImsGJnTs+vDALRltP2Y5kzJjTwrIaApgQFUBcI0CMBgBfAwQs2IQSiY7fDVxJrrnKHz1R5xoJZmMK7SIGoRCgCaoDmGID5GgMqXKBCHgVCQMKmAgyCGIdHo2VKavCJmeKIRCAPB4Ey2IQ1LnxJyFsf7cNH3gQVkE/MRO7YOx4xS45MGWiwLACHC18YNGUMpjgLUEl5XVCrIC2UQ4pdgFm2D9ADwXFPGUcRFDMAtAySVYkBJYKiyIyNigG6SsJRAA5GsCZf4zhlSuKCTTDNCEk4UCQFvyZll2TqIoSTZkEUjQTPu24EM3/jr7KQ/8gVyBXIFcgVaDcV4BWutseyQOAhwsofqG773jS2hXx0qF77y48l9dtmvXpZy3ZyBXIFcgU6cwWit7cIHhQCEeCKtpwlxjTMoh702wIr9OH25NxpsOm3uaLTxrKgBhzFWgpgWegken44GMc7BIgQB0zSvAoiYI0L+wJAGudOhJANP3fgA7Ae7cMX5sB4wBwwmZbPxWATDnEaj3fs6MIpTsseyVGSWcaGDCmgGWPAezkAPCKmLpa5SJlpBEBSyBMDgTEup0yOLJk1xjUiJOcYJgIEEB41y1Q7CMAhBuxDMt0vLbBqRvgK+0g65k6tpDIHptOQJEj3FA42y7sQIbFEACgqSUgsuVpjTTHk3WWew9gymBBTliQskC+PgabS6rTmp0IZ953KYwox7PsfAnFmyhXIFcgVyBVonxUYPf7eugS2oOAhP1CdDxDVZZMaz0g+OlTfPV1v+/XrazBbyxXIFcgV6JwVoPGmGaYhpytmnBCH4uvRtty25dxp70EZnFIoBnTOcZ4FDg02RB/OAFNctOhM/boN9jHLVElWAwKoKBZfNcYLbTzGYQJz4CKa/FBPJlojbJZBB0Tg0ORjSl9khADGzQL7DpDBOMJcEMzASoiHJUlhahV4E2M9MpBIOdAiODUChEEM2NEU7hBwjHAMNMWUVQwaswKMy2CWAcfrWsgQLQEgY8oGZjqsapApxtkLKiBH12ZaBlbg1+y79mHqGi9UA5UgOFjmUgA+A2QMkrF8LOvUCBGrxFAosmVO4WM8tkAvidPCvpIV+RIYRDzIY80HG3cEwBQX1AHL5ZCQgUl2UDx4jKdO/yCxMnWECtTrZZOOkGuOMVcgV6A+FVhQ8JAfqM5fIKrPLjWclXx0qOG2NCeUK5ArkCvQIBWgJRYMMp9ojKMJp8cWBwFQYAzRZtNjJ+inOGhjEy7uwCpMe3ibfO7Y5xAK3XhgK0xjNalMS205XsK7OIvuxBQQIyQ9wmGAR6jc4WOz3PNjDQ7GCRhdPSad4tyQCBEy4TSQCAaALMQQS2qlAKqv2mkweay+P8WgHFIIkDW+hCS0U45EDlkgjySVIWsTd8m7RabmEMIKaAenGIeJLhYAvBiUU1aMjHARioJNcCQsGAB2JAbIYBBTIYMv7LNZMiOpmOKLq2YXUE9Bzv5iXcVmAd/oFEVkJDmRWjCJx3QQUIYls0CXAavuO6vUAQsSTHSRga8FOMRJLk4RY8lHXcv83f6nF1958Kc/UTGR/2jfFajLaybtO8UcXa5ArsBsFdhivQGzzedrsqDgIYLhly/zF4jma1MaXCkfHarjBj89Jn0IIFOuQK5ArkCuQL0qQCdM20xfbUssfGBTTfNM2x9nXuicxR2QlxQrN+HRaUd4mgVqAUqwbxcJko8YKnAQYABTvpZxTQDa5M5FnHT4yBuDS3AcYA0XUJmT5iUKv8AcAl6mjAjuHJORMIGoB0tqpQCqXzUKj4lZvOTFEiS/HAAhpfSr76yFNXLEPh7DOJJERRiCNRaBVWRgOtU+0whAp9YQC1wEXz5nhDqrWIawzFQOYvCxbwDEjy5mmZo4Yzj4VUaPYT9igM92CLclH63tAhaoUjxmFY8FIoYipF98EQarUUCdGkBYhokAF8JkBGG5hlglQR2xFB4Zk74cdEHcWIKjAJbLHjM2lCqVKVcgVyBXoHErsADhIb9dlw8QNe7DM5+Z5aND81m4rJYrkCuQK5ArsCArMGTIkL333jtAAVzRIdMw2x5zp/8HZaDzt52GI+5gQ25oLZtwUQD4WOMCgIATSAcccAqc6o47SARm8cISZM/PwDAUAJKQUPRcjzEYmPiFAhqREz1/2btiGMcmkYAECXPAIU7xL+JhKgfwIrASdPErEqFrOHgkZk2VPQr9KEB2xBA1gYlxCCbeY8qAqLBP0STFkMFCmZAJ+/CtIUyC4UI9zhmxijoZYTll1CPlKP7F1CNg1jyKrCP3qOzUlK2wYJNjZCgLnAhJfuwCirjAnekgJjkFOxOlckoYPjPhGj7qWpCJQZLlsnoy3WWNEwAV0yBj4/GZSdZ/Tow1AABAAElEQVSKA2VmjS4c7OBUeTkphuqrhdrP91yBXIFcgVyB9lOBMU+MrkswCxAe8v2yfICoLvvUMEby0aGG2cqcSK5ArkDLCkwYN7IlM3M6SgX4Z62xY8c+9UDlVGZqm4sm3PjpwAFigBXopbmixxZQsN+mFY8mnLFM7HDRukMYFGphHPbp/wOJQAtJluDQjdOT0+SHOyEAdLXMQFxD4zDp6tHVOFPxi+CUe36ZxANF249TsAkoImdaPneDFvEHpwxzECRTFI0ZU1Sm7NFERCvMN3FagzkiAAeEVwZ3UCFf3El4JCoumFTDGBjLRJh4xG7gQFFh1Y3ErOGYOIOyfYzELihDSFhzX7TDHRlXMYIAlstaeleLOBVG3uITpALaSY9Z9VfqCZj4WcWsG5rsl74ohAqRqM7jhynIKXc4HkcyUzhY5pIUY+yeKoN9H3UGEgNCcrXCyn+0+wrwHdgLbzmv3YeZA8wVyBWoTwUefeahrT++XdttLUB4iOD4Hybu+QBR2/epMSzwqXKODh1zzDGDBw9ujIxyFrkCuQK5ArkCDVMB/lmLXEaOrGB8tMT08PThDGjL44CJ+dpj210nFGBGBXeIJhwxGnWbf8a25QyCg1mbfBGTEEAGd3oEFkli1d9B0w5auIOPomATusqwJIKgI9CZcAe/pudnSVPR9mMTNEHkgnFy3T1hE7ozMO6BiGE/YA5iEAwSi1G4xiPMMhES9rlwJGEBO1yQAeBakhngDlME0rR6pAUO1WCbUFEYRQMIrEdTYZOpNSQGNosxS4yjAkQI32kEgFbsMvJJpQCD4CMjJ0rKEpwyMSXOSJnY8EjRAHFgosgdLZyyREjsSLO7gqMjmLhDAHkG3lnCOMRUwggcnPpgRM3hmBGShuRGMCVlVGJVU04rRvMfHaQCtIsdJNIcZq5ArkCbKuB36HfaIf01po20YOGhOEDkmZE2xprVO3oFMlDY0Xcwx58rkCuQK9DYFeBjrqNHp+PZtMT2w97L2BAcIQn6djtn5GmtywRfYAK8w7YcDvJctOuM6eohTNHkAwTA4cKIdpBBUZwFGaYSbbxaiMW4slT9eXKE9YgFIQYsQ+UU4CCjWb0zTQHMDnMAE0SEWEBA+IYxWqzWZF2DxeCRFLCgrmGg6JS71hSgIFyuspSyrn5TSXAkqc0DedxJXxgU5iAvxhEAuwAWU0m5es4ozkkZj3dTmJNbLMR+KYMLOFy4kyhyOWUE8K5xxoZk2QVxVCynHLuPQR+8iuniD6xBaGHTPUUm7MOBmBIDdnTnKhymKFJnAoYpv4wNyUHAQbKVqeNUQLw7/3hZx9mxHGmuwPxXoF6/ak8ECxYewoEHiPzczPxnnDU7fgWACHnTMB8d6vg7mTPIFcgVmGMFJj06Ty9+27PN0UpeWKQV4P0y/Nf04WAHNMmxcfb83Gmbo8kXBaDfRix6bLp3QBDbcnp+rjl12gltKRp77viCyriAJcGappzS1eOLK+xX+vnqqRPE5BBVnAeBg32iilU4TGEGKqFBVMKdWnpEEb7oVQiUXcB0qkF1sc9Ap+QIQGPFKpwCX4sKMwBsSmWvQmM6UoVxTBFwHBwPNxGAqAf2CYMxFTYApsiYshxkECBfSDsMCBgOCJFReScAgkeGyxpGSApgkAtrngbSvndCgqImusAaHJ2iRdaSnDAOE/t4LHMqWsUpMIxbq9nsV186Q50YyMhyGQ+uJafYR8BHXX7E4KAinf/oOBXIP17WcfYqR5or0KYK8OGhI3Y7ulvPOmA7dTAx91Q8QIRMPkA090I19qqvlZFjfq2ssTc6Z5crkCuQK9ChK+C/aY0YMcIem1yiQ6Z1pzlnymU7zT3aZjtt+eVzOthBka47WnFsMgXo4a669wRqFAd/uKPCpXdX5aSl4iAMusAEuhOkcIwKA1YhhFuliDlW4WjfJWACkJSwL6jBXWCC7ALm0ClTLTgtRxIuyvZhEpvYk5CKfoXS4HBZQzyarwJhnwS5YopBQwrXVi+8mxEVRqzsETEth31igJhysYpBUDCCYUrM2vfZIAAFkJfPRkQADEiQMuouInFrdKEFzCrjE0KQrnLXmncCIAy3AI5l4V7e5ahJMlE8ZtxRlBigHgEkgeqTpgxT0keGCw5RhcFp40fUJFIxmv9o9xXInx9q91uUA8wVqEMFfJO0W88ubbe1wOEhQowDRGAEbY84W+iIFfC1Mo4OdcTgc8y5ArkCuQK5Ap2kAv6b1tChQyPfOHKSevLiBEq5wU79eRWLqTT53dP5i8AFaLMRSGBQ6ZAL7XfARgoEEADEYE8uh7H2maYAelROrBCJFOiAHT5MBqwaKmP5eEQ3pqzKCbhE+6oDQ3gyiGlyWhyEETdhSnZxwiUEyBf7Unh0AJNB2BdLUhEm+QayQ02IKlWgAI9YhWCm6lXBoMQqTvrAVDFNq66LxeaUmSoTGWEf4zAxyFgXEQDyuINp2SMAIBsSDCIdBChLDWGwnDJRoUg6MOUjwIU7rlSx6itdyCBMYHo0gLJxOVQ+yH0PReOhJuUAmGrQAHDBACbEwMcVAWUICQHSl2BSNHPkF/0GDRoUn2yvSOQ/OkIFaMHy54c6wkblGHMF2lSB867/MfoD+tfhu9TYWRjwkH/Z4nxj/vRMm3a+wyr7WhkPQD461GH3MAeeK5ArME8V+Ei31+ZJLgu17wrwfhkHiIiRLtpDLhGvLTp8if6chl8Sd2AMxyMnogA05IIOLMGxG2fMgPZbmEALmIXDFfYFYujbg+AggC4ydvgGAEAAR0VWuTy9gn3GEDEkDKIAs4JDbEIM2oePADkGTCAfU4jhDvt4JzvM6ss7OAjpM3aqfe5MW6aMCwUwLlqhF+/YN0inOLXI2DFfbKJOhOj6CSEFhD9YDfuIYQ0ZAo6MELOGZadlj1hAiwuPEpw41GMMuMY4F0uWhTHEHS0V9cjuYDxCQgbLBEBJVcECMqQQJIcpAwmDaCEW7uDDIQxxIsZYwyN3ZFQ0AL2kGIrTZMqgDvrjUjJVvJ5WjgGPQoTmi4wfsmGQqQNVwF2zdexAYedQcwVyBeajAp/Y8ZPzodVSZWHAQ3j1AFGXpVbKr5i13IPG5sRrZT4DjZ1szi5XIFcgVyBXoKNXIP7fih6bttkWOlpuWnRacQgOLTRTCBkkyxADTTj9PyhADdSCrq17Uite/0EMRZj04YII4REBVlEReUHAUzAIII9H+nnuUAqgerJJdZgs4Z2r7NHgk05BWCYF7qIAZRhCAXWxCRIhfMCY7BIeVPxYG2J65A4n0CjVueMxIAatBazDqgEwcMm7VXVMyk6Nkwh1h4phxCEXpqRMANgvExVAJmKwhnrEhfIEiWWZ3HHBJRMZPXonYAhfEszYBTk1FdYvTNNhSpXYVgiOxrlHyoyb97T6RW3UI4YAcRAzDAyqYgDcy5wIAKZGcAGB/jjgrk1iYIw8dzxiHPmf/PDsiy++OJ/+jtp2uAF795s/X9rhws4B5wrkCsx7BfhvnA8PdV1h3jXmJrmQ4CHR6wEDBvCN6vyK2dw2pOHW4rWy/O9ODbe3OaFcgVyBVirQdYmXW+FmVsepAP9vNXnyZO7RftMk01pHd22PzbRMoAZ01FyQXTe4Q6AAwSz35+ARABlwuGMtcBCEyx4Z2/wjgEERAZgJlSiQo0r/X4BB4YiBBjVu20+QKHIFCsC44q545QqDwUFGxEROIBG6Q8tkk35B8onQ9OExIHLU8atHmEwxFW9dhTuWcFdTZA/+FOYrN5yGfVWAOdDSOzVJ09J7fMiAesCkGsjggrHAB1rukVMKVQ4ARWNDpjnl0jEuAyKYVOGqRyUD60GGkJhqCmF3jSkXAXDBdDU4lhpdBuRLAGX0B46KrFoK7EcALTksqY4YCZojHLNmmuyX9p0IIUNicONvL8qnv1NFOiztuOOOxJ4PEHXYDcyB5wp8SAXizbLu637kQ0TnbXkhwUMEwz/H8WOxYNj5FbN525pGkMq/VtYIu5hzyBXIFVgAFfDX0xeA4WyybhWILh2L9M9c9NJBcuzGudOx01FHU40kQAlTkQIVXeVOcw4HWEEX3svACihD2T4C2OcUDBQeUZeEBoQhZCKDfXAQCN0EHxTwU6AMxIA8zHknDGInmapiPcAK5ewwiHdzRNg4TU0mAI0DnZIgASg2pzCoCWbFXJTUgmPu1tCocIpNPEYMmHUMU0AKjiHp0ZI6lo+FCIkBU2IQWmJKMNTQ7UMrCVRxFqcUR48RgyViFQ75YtAltxh+7ILuyIiB5CCgNJhykMEO+ULuqTZZxZpj76kmnGurRoVWUimqxEBr5SLAUcYlvjqEGP+4m9QydcwKgHQD8HG4IP/CfcfcwBx1rsCHVMCjQ7vsXZ83y3C28OChODySXzH7kE1ulGWwIQ6LkU3+5FCjbGnOI1cgVyBXoDNWgFaZZpuLzpnzLLblaVqcJWGVnrwGBeC1siDFAgWAT7uOKe4SFgJYYcwFDFHxWHq9SBeAMgYgDqJxD4BgTSa+AgKAiUHi8VhN2SMyLCnAXfutcmASoURgEIiJ8FOUJTzCQTiFVDprQ0jIaxx1BqyGR7ASwSY4LDFVElNwqEbUHF0DiCKkaOaBkscC49OyU4wThhwM1uwCVvHFReTkS4QIUGEuBjUBIFbeYiOKCjPVIwMV3X3scLEkBIYRdxCOUcGBLJSKTJGR8BhjJOMxqCwXn3byWYKDwRAIj0SocT1iEDs6/dppldfK8t/iop4ddOALs/knzDro9uWwcwXmUoEFcTBw4cFDJMb/PPECc37FbC573EhLYkP5ffVG2tOcS65ArsDcK/DsIxMXW+KVucvk1Y5VAfp2u2XCZsBRi4B+6MwBF0QNbO+Roc2m5ebICZ22TThAQIIhqsc9YGoKa4zLKAMctDSYhAogAEWbdqY1ASjAPUE/1ZM4BgATy+IsGCQecQSYBsAUaxDTsn0yMiRXDQmBgCFQYZoSrMJbcChLBIBTltLHiaqvXOFCYEUsJgJAUS8BNskB0YglBujOnTQYMEcKoADsAvpRAF+6KyfoJsIxQv2izqUwnMi0PHBfyvuedrkKNoXHSFmD2MQRICNTCO9uBBUId/DRQoB7DWkEpwI9mNIRYqTAY1AOQHWiQoYLXXYNLzqiyAg45s4qNq0h1vgle14r469wGRuq2YKOOPUAET9htiA6yY5YkBxzrkDDVMAvi319/1Pr9WYZlVmo8BD/88T/0+RXzBrmiZxLIn6DPP/FYi4lyku5ArkCuQK5Au2/AmVYhLZc6IdG2racHjtBM9PSxapMO3Bad9rvaPthogsuANmTR+628UyxAPZEDy8+AkeDdvisGgAeY0mBhHEUKFIAH7rzzIugACr6FWJIJgoOU3TnRNhntQxDyNEUYwMgQpEFppTCVe+ERIKW0Xs5APwyLYNN2oev5SRQ4GvBCY/YZ+y07JHqGQzu4EdNsNAqYT9Vr4R2MeWKAMTXALwgmOwChGW8oGUMBpA2YnZ8zZTLyAsq5MtGx74TIcbDMvbZuDCuU7fJqEjQPQ136LLElCtSxpHecUQACBhkir6gcsqeL8OONHX6B/mTQ5VaNMQft9xyC3nQSWaEqCH2MyeRK5Aq4H/OfJS6+zr1+eqQZV2o8BAu418hth746fwrZu5B4919rSz/xaLxdjZnlCuQK/ChFei6ZD499KFF6qgCtNM02Fw05/TbdPXgJjbkpERLXwY+4CMPlCOaYM6cskFRjvcyCoAF2346ed9iC4M4BRQwgPBYBpvs/ONkE+7klGuNRyxziZiwZApIigsgILYiRyZjPTJVUcvcCS8i1CPTlhQwRKSMKccMArYIJpZ1AYcr3BmwjvRSAT5mP2WDQbQkU2asO+6B1CjjXY+MEeDuVKCHlN0UjBgJJXIgxyLojjvJEkCQHnkMwp0litNV8HmKIGyiy5THQARQLwQQ4E7Zry7YGtyVdwELcgJ+Ek5CPmJgzIOHgBxc4DGZ6lGpQL8tPiGgoJd8b4AKDB8+nCzyR4gaYCtzCrkCVABsyK8O1ffoEJYXNjyES/7/hlfM+JD+fROmZ4So8Z5vsSHyit8Gbrwcc0a5ArkCuQK5Ap2nAnygF6LPLzfnQAZgMQF80Ff73pn9NsVhgDwttz0/6hA9ObgA7Tqr8DXovQaG0JrAgaUWdNCOnDLYJEdYwRi4EyQcwAU5esQdZoWfhLfQhYmM2JCm4AA6BMQgs3wnEkLCgsZZYkCCDuIuDEF2UDllxjJN3zvIiAOEDSCpVallMAQszmJZjEQLjhFgmky1QF6wVsbXcIIY8uFF5KV8zMdAyLqcMhUIjmHoURmxHkzBJN9U0uopnhRVga+xKsFhs9jW4BAMAcBhCcImKjU1h0+RWcK7AcgxZZzqUQuVGApALSXY4ivpaEEKp1GmRqmA73CQzfHnfyF/prpRdjXn0UkrIDa04VqbgA0tN6BbfauwCOAhEgDAHjp0KPBB/p37+m7nIrcW2BCvlcXHyBd5VDmAXIFcgVyBhVOBvhutuXAcZS8LswL8gxb/rLVN/z6ARPq180/ARPWLPwIrcCBWuQJloNNGTJxCdZp5OHMnQAGQHcCFAD6wk0wVMIdM7IQMHpkKQ2A5kBc4RgUnen6Ywls1MYAjBL6gQd0hxhTCRYzNSIiBVS5TxougA5J6RAZFsJioSbLSGpGOkbOIwYS8VE+4wLHI2DQYJI0HDnFGykhGDArIqUFeYFKEMmEW+ZqUhZ/0yB3LXBBjmWSnO6bouooAA1LGBQIS6ZSJUlNwLu2wxAD54CDAAwBHPIhVUiBCPZJvTcp4p0SQMXDnMcNahMQSzxUUIaGCAHfJ3S/LVxbyHw1RAd7h4Gg/qYAQ5bfMGmJLcxKdsQJiQ2R+5Xdu4LWybj271LcKiwYeAjjgA9UjR44EJxo0+Dcjxkyob1bZ2iKpQBkbircIF0kk2WmuQK5ArsCiqkDXJV5dVK6z3wVUAf7S4nsZgEQeI8KRLbToAyhAGYagJ69Bf+jAo+dHlz5feXEB73ACCqHtByihhweboOFnGsCKfoVmMBUyNR5ZquEQgGiCAXAX+BARYAnLXCAFxMOlI+9GWOYgHymLNeAuQAcGCXSYMRvYVIOzEEC5CAaAIkwDQIApF37lhEc4yONiLiTwYeSIMQicRS1rqHGKgIBFNk3sQ46D4yD4JBgc4qwhPcK0wkwJqZwyU9S1wCoXJMcngQiJGY5FABviqQgSbIoHI/gx0CBTB4ShBcGm4PMValywymVIYSEPGq8CvMMBQgTl7xA13ubmjDpDBQIb+sWJvwYbquMXqaN6iwYewj3wAd+oBiHimMk5w++OgPKgg1YAjC9+qixjQx10E3PYuQK5Agu5AhxLWcges7v5qwAI0eTJk/kbi8eI7ri0coxIa/TwoAmBAgBbgALQaQfHnh9hUYA5wRCiAJiigQ+4oVUQBGa5+Uc+OKnJ93e7qngQkngkBsQwriJTiDtx6tFcEmd2eEuOq3EPd3A0KKxTTtkisGoASOq0JgBiKwegizLyAocppHEGyWCBDWlKfhl50SPuuBgrxtgKy1Eea/ABYqDmms/+MaO0xq+JlWoeHrXMKoNwx2p4hGmFIyM4ECk7YOwgBOAQiSmHDAZ5zOKhQoaYo8JM9RhFYIoFpljweUNezNEiEMDIkSPOPHrvQYMGjRgxAqZ8TGVq7AqAEPGP9CBES6652GZfXie/aNbY252za5gK8J/qYT/aD2CXd8rAhj6x4ycXBDZEuRYZPIRvf+eeAZ+p3uukSxtm8zphImBDnAIj8fxTZZ1w93PKuQK5ArkCnaQC/OMHx4gEifr06UNTTeKBvNB+BygA38YeTnT4Dmja6cMl+3aBADnIC3w4DRgicAENhnE9RmPPVNQAdWTEg/CiR+5wcBchVcIoHcMxgDIUAgeUASP6SgIF2IQRxkaoQe5CIWE/cVp8XqecLwGgQlRBkXIYZ4CAxmuQF7RYJWU9cic2gA8oisy4ZcowdcQAC9Y8OFFDlhTgrnE5xBwe4SQLVUQJPurKpz+KJ4R7OcfE/DCwCQHKjuWalHGtR5EgwyDlyFEOUwXwRSlQIQAGPLTQ3nvvfexOfYaeNIhVHun8NQDq0KmI/ykDIQLsBiTKL5p1qq3PyXbQCnBoiP9UH33mIX6njHfKwIaW3bqev1ZWLkvX8mQhjz2tzb9a8DetLkutxKtJpx2160KOIburSwXEhvj/mHxuqC71zEZyBXIFcgVyBdpnBfirC8T/2cV3iIQhuK9efDenjALQyQdqQDq06FA07YwRcAoEYA8P0/Y+iRYCTrkLE4RBOBoMeQbABDX2BT60psGyABYwiGuRkbLB4Px/9u4DPqpi7eN46AhI7713hAhEWigWEEREREHFiopYrij6WsB6wWuLqNcWxcIFxQYqVlBKaEoEAtKR3ntT0FDff5hkOGzaJtndnN397SefOHvOnDkz31kkeZh5jmk/aYCnowxqRAXzMn2wHTAHnQJqRB2wfVYF3UIdMI2bt/puG1TZGXlRNdtDndKNzIX2jqYDzhElmZ+dbUcXOivYBk1THg2qsjU0d3GaWATbAdXXyxiashr0GLLyB2ljnX05GzQHbTRKzepyDUEvcwt996ivI7aHtk2PITtjQ6pjWlgzP25wTExCQoKO6Ac2vfTPtPow20YohJWA+YldESL9Fpb0kbijjn7tjGrYrl2TjmHlwGARcLmAFg0pMGQ6qUVD+hPqpz1l1iE3w0PqhP5aSko/dDpCNC8+ngiRnZggKpiVX/o5g2egBtGs0VUEEPCfQP5Ce44nlvVf+7TsBoE0/znEhCEUOdL2eXVy99+nyp2Tx/ZWby/rdEHL4cPNEVNN/4Zv3u76O6lm+XNO/XB6A77K+q3exAhMhaTAR7EI/ZK/xryPSEolM6TvmV/vVd8ZmvEIK6Ru0FQwoRl99wiU6IgqeER21IizS6rg8VYVTIOmjzbWY97aBk0djzuqjkeDJlCScYPODmTaoNrXyzRovqfuodMwSw2aMaZu0JkwKM0GbfRK63q2Hz6140ge7SOLW5n0Q7Jp0zlGDUE9tNFJfdI8PmY1m13w2shh5kJ91x1N9EoN6mWyaNmWbTUKYSig/4nppc+SDRLpt1AFiUShxyGFIQhDRsA9AmbX51uTXtGKIe0mG9xriAJDBUvnO6duXp/novYYdS6Hh9QbIkQeUxJcbxUbWrRys/rMY+yDa+LoLQIIIICAvwWcsSHdy+Otjmgph1nN4dET/c6mI86ggPkl/4NRSb/IOSvH3J/87sXYsdGdOjsjKeZyRRz0j3CqpH/FcV6osiJT5kbmuK61l+sqZYf0qK8AlqJXNryls84e6pIJU2ab8Ja50KTWcoY5nKtsVEe3U7TLhC3svcxbE79wtm8q6IhuZMqKpKjwV9EzAThdZYegU0KzLZirdEmlonnsHfW2f4/OpjXz3dnD1CMydWz8Tm+d9fXWRgZNTX03sRtL7UTWWfVQ2X/S/AzorNn2ZYdg2jQCHh+Dco7J1RSYmua7BRGOXs5TlBGQQFKIyBEkMqFts5hIZ4kT8SFBIJACigopJKQ7Kiqk7ybNkFnTp9iQ/zaUOceY++Eh9UZ/XbGGyDkrQVFWviHlFDexIfMTTFB0m04igIC/BQ7u3D9hxKeV61W+aFD3AoUK+Pt2tI+A2wT0u5Y3XTr9S5lXNdWa+SVflzjTmTsjOB5hDtsB/YilzSPmVz57MNNCTMoupDRrpjlAdebL/50VurIhDHXAXGJDFaZZxV9M6CrNuygXeJrHM7jE46cRezuzPiLN1tak9M2ctZdk0DdnoM3WT7NxHTQxwfT8dbkiR3rZy53zm140R8fTw7HtUEAgSwLmf0f6k2KClfqQKwOuvitOpHbMkiK2nmWJlMoIeCNgVgmpplkopIJCQvpu9pGpEJgVQ7qRfeU5deqsf2SwJwJfMH8Tmx9iLqhTlDxEgZ8C7+9oc1HrEo+fxrxvhJpGQPlN9bFfujfPxQPPrAb3EmfdgrixwwfELH3Ly/pUQyAAAt+/8vXU0T/qRv2euSGqT7sA3NEltxjadHCNZjX/Nf7hI7vaZLy5TJtNBl+btGSA/3+6ZO7ohq8E9LNc6qbSC3OoZpr1TQvZuMq3l6QeCEcQCBMBxYk0UoU101zapl9fW9Y/E9M0JgoeZQOHVEfZQOOS3BKwoZysdiB+5Vx7yYLVSXvP9TLrg1Qw8SD9mbIRWIWEdFz7yPTd31vJdAuPlytWD5k+mX8MMf8/mrf2MHmIPKbKPW81NXqGfYuG1bR0iN9t3DMv9AQBlwhsXb7J9GT3htPP73FJt+gGAgj4XyCDAE2aN89qfdNINq7KxiVpdpiDCISDgHOFoInhOtcqegiYxXG/75rvcTz12zSDTamrZXrE/DqdabU0K6QObKVZzecHsxc+83k3/NSgM/zhp1uoWRtYyeEtbFwmh+2k9zksUDLtR8ObhaLta7Yxq0SP7j3RvumZDb8FyiRfFfh4kIeDi8JDpmf6/5FZA3z60QoRrCHymLBcf6vY0K9LNyo2pJ4QG8r16Qh8B44fO77utz+2rdx6aM+BelENGnVuFvg+cMeMBTRH8RPmbl664YpHri5c7JyMK/vj7I61202zPlydqjZnj5teN6p+i+6t/NFn2kQAAQQQQACB1AImtJorAdYMVhem7qc9kkEky9bJtJDehtAMLjx24GQGZ7MR2vBVFCODXnmcSi/e4VHNT28zjdxlWkEdy0kYLtO1bGZRjx1+/lJJRRvWOV0+kwvPVgu6guvCQxI0EWttWdcSFb0lQuSeT5WJDbVpWkPLuyaNj40oWsk9faMn/hbYuW7HvC9mx3/1y9+Hjph7xf1v6h2x9zZo39jftw6N9meNm554+J+ON15Y8JxCzhEdP3o8X/68efKm/U8Nzppelvdu3jNhxHhVLlu9/EV3XOrlVb6qpuEc2LnftPbPX3/7qtkpb3y3eMqC+d/MU0Sy0NmAvrqFD9vJW2hvBE8u8yEoTSGAAAIIhJ9A9mJS2bsqMLrH9mae1OXY3ozCTLafx5N/1LIH0i4c3Xci7RPhetQjxGMYTKDHlJ3hnpQjoRD08X7C3RgeUu9NgrRevXolRYiKlB92bQvvh0RNPwmYB9gnx4YmTfLTXWjWbQIKBiV8P3/+179sXLIhdd+0eyjXw0NaLKNcyGWqlkvdPfcc2bNx11fPfab+JHz3232fPVKwcEHTN3X+hZ5P7d22N7J7634jbvBJIueTJ5J/sFj968rAh4e2r95m2f/cc8iWc1g4cey4Wjj2z9FtKzfXiqybw9a4HAEEEEAAAQQQCLCAN/uGCpRJyjvju1fWWvMmgJVB37yMbWXQQnqnUkdt0qup4944Z3B5mJ9yaXjIzMqkSZO00ezt2Nh5S9ZPenVwxOEdYT5buTh8xYYUGFIHktYNERvKxZkI4K3/2nfop7d/mP3xDI97VqpXpXWvNnu37jl58mSrKy7wOBvgtwoMvXjlCMWw6kY1GBR7T94CLv1/2qbfNxiZHeu2//zW9z3u723eblq8XrEhlRN++K1qo+qdb73YHM/J95PHk/+l6OjhxJy0k71rtaktexdmfJU+bKbC0SNHM67JWQQQQAABBBBAAIFsCOQwsOLr2FY2RsAlORVw6a9Sdlhmo5n2f1breMedfTuw0czKBKxgHlKmZENKOZTnnLLEhgImn4s3Whu/asnUxbM+mu7Rhzqt6l90e7dcXy7k7JXWqpjNbmviV33+9Mf9RtzoPOue8sYl621nZn40ves9PfOfjmTt37rPHl8ydaFvwkMpq4d2b961e/2OP/f+qe1mf+37M2/evIWKFa7evFbl+lXsTX1eOLDjzHLnoqWK+az9lIdsbly8vnj5kns37Tq488A/f/1zzrnnnHNukebdIl0bGfSZAA0hgAACCCCAAAIIIOBPAbeHhzR2EyEyy4j0lgiRPz8Pnm3bRNR6SNmdN/YZNvJVzxq8DzmBT4f/TwmGPIYV1btt9I0X+TWs4HFHL9+Wr12hQOGC2nOk+up27fPrtnblk9SdW67U2w0L19a9oIH6bLM4q3xgxwEvR51etZPHju9Yu2NF3BJTQYGz5y5/2qOyuJ6b78c/yEcOHrZ3LF62uC1nu3Boz8Edq7ftXL/TtDD5zW/15dHa4YP9Olzf2eMgbxFAAAEEEEAAAQQQQMB7gSAID5nBKEikh8ApF712OT044MJOLet4P0hqZkNAi4ZeGjdNF2pPmdYNjY19qXOPftloh0uCTmDJtMW2z+cUL9Lz/itbXNaqcJHC9mAACol/Jy74el6BgvkzjfWUrlJ2+E8j4z74+ZcvZikasmXl5tYB6F/Wb7Ft9RbnRUumLjLhoaUObY3aWcf78rr5f0x4ZvzBPQfNQqqML6wXVT/jCjk8ezzxzOavEhVPP9Qh6y0qv/WEZz5e/ctKm+U6gzYU8KpYlzT5GQhxCgEEEEAAAQQQQACBzAWCJjykoSgRvV4jIiIGDI9lo1nmc5uDGlo0pKTgQlZgSM18M+71iArn56A9Lg0mgSIlitoog35LP7ds8ezFhg4fOFyoaCGzhSr1+I/+nbhkyqKNSzcc3v9XkeJFSlUqXb9946qNq5ma4x58b/npJTAFixZu3i3tz57iRx8P+7Byg6r3f/rIZQ/01pfSPOsRYKnvpSNKUaTvxcuV8PIBYRr4scRj2riUZmtZPSgKS2quXTx54ZWP9duyfPOuDVlOqabHgeXNl9f5+LP3//W2R/vOHpavWbHW+XVqtqhduWG1crXK+/uxX8cSk3JIm1elBtncxRY/cW7qJWwprUYoHlQ7sm7NyNo1mteqULtiiYol8+RJe97tJQEr1G7l3+hbwAbCjRBAAAEEEEAAAQTCTSCYwkNmbswyopiYmGrdnyBI5PPPq100JNukCJE2lD32CA+w97mzrxqs3bKTr5qy7fR7+vo3b33FvNU2qPfvfatOy3pKOVS/fSNvfgn/c8/BH177Zt7EOWpBv8bXu6DB+d1bR/Y8a03P0X+Ojr7zjbUL/rA3VeG7V79q2K7xNc8M0JKT9QlrzanVv6xIMzykFr55eaLqbFu1ZevKLdVO501PHYpSiGfG+z/99uVck/753DLFL+jT/pK7eqSuaXvy+5SF6r+J2lSsXalBdJOOA7qUrFTaVshGYdWsZR5X/bn30OalGye//o3H8QzeashzPpox9b3JJhKkp3c17ti0400X5S+YX8GsNC+U/xNTn1W8L82zfjqofpqWtfSsRrOa2bvLsb/PLEHyaOGSO7p3u6enl2E+j2t5iwACCPhJQDkQtMhd/4rpp/ZpFgEEEEAAgQAIBF94SCinVxF1MtmIzCIXEhL55LNiFg0pC7VaY0OZT0iDsZE6UQ2GfPLIF898vGX5JtN/xXH0pUUoF956ScvLozLIAaxYjInamAsVXdIiIH1tX7Otx5ArrMbYB971iA2ZUyvnLv9Pz6cGPH/LuaWLmyCIM3OzvVyFXz6ZpQiLCopBVEon0bICVaPvetOOQpV1yc/v/qC4zG1vDk49in1b94x94L1NyzaopnnpKWP6Wvzjgrs+vL9MtXIph7P8X20lM9coXqNojhnaJ4+NUePOtjJYAbR67oqPH/3QDNlcsj5hjb5W/bJi4Jt3XffsTR8/NkbraJp0atb0ohaFixUeeenjqla4aGFvYkPKWPTrhLlzPp6hIJqmTKTyb9418tJ/9XJ2z6OstVrzPp+96peVW5dvKlmxZJurOrS4rHWBQgVOHD1hajbven7qII5yLf305ndr5/9hxlKyQqkyVcv2ebx/xbqVne1f0K/DytnLNq/Y1KBtI8UHtfrpp9gf54yfoTqlKpdJ3azzWlP28kapL+QIAgggkD2BAQMGDBo0yGTMzF4LXIUAAggggEDuCgRleMiQmb+AY2NjFSEyRwgSZfvDZBcNKTaUlIVaD4l75CE2lGXbM9gv1GKcIZ8+vDJumYIpGxYnP3JLC2o+eWLsd69+fdHtl3a4vlPqlUQ/x/7ww38n2bFrqY4NZ0wd/WO7ftFmDY4S5SyfudRUU3SgSZfzChc7Z9/WvUunL1ZsQl8f3Bfb9ppos35nz8bkhMS2WRW0WGb6B1PMkUvu6KGAy6FdB9YtWNP80vNtr/7ad+i/171oFg05r1V51S/Ll0z73WNR0sEd+9++5RVbX3Ec1VRn9F3pbzS0bD8TTcGXFbOTVw91uLbz8aPHzCPhPGJDppPahla0pOdinzXzVsXe8ZqpoO8K39hAkp7XtmRyQssrLjiv6/m2gq3zz+F/nAfTLGur2ruD/mtnWXXUuL5+eueHYmWKt7+uoyV1Xn5g+74xQ961oTQRqYXfpy4a+MZdGqCp2bhTM+clKid899u4h993HtSF+vp02NjrX7ilbI3y9pT2Mw567z77VoVK9ZLjR3bszrMeZe9v5HEhbxFAAIHsCeiHUj1mVz+U6kWQKHuGXIUAAgggkOsCQRwekp3+MtbLPtRMRzq0qEPW6ix9qmxgSCmoFWhTeGjzzHciyrdgQ1mWGEOvsoICjTo305eWqMwcM+33nxPMGBXx+eq5zzYkrO3/7E1aKmIH/uvns21sSFuK+o28SYtZdm/Y+e4d/zUxl+UzlrS7NmnV/axx081VCnM8+NXjNr9P0kawD36aOnqygjKFihTSYiVFiHStnoTlsQRm9rgZJvCkOu2vT2rz2R5P6qrEI4kXXNVeb5Xj+e2Br9lYT/UmNXsOVWaiE9rMtXHJBlVYv3CNMzz0z5F/3rhllK3f+/+ubnttx4hTEdPfn/Lj6f1fSted7cTsCkWZMJPu2+LSlqdOnTLhIb01L3GdOHHSrHLat2WvR3hI+YneGpi8109iN7xwa4MOTRRF+vTxscumJyURV1hN4aGUxpL/axJI6b7K8eTMUuRRTQ8Fe+f2/27/Y6s5LqiK9Sop27cmS6GfL//zqeJuPe7v7XGVAl5almWvsme1TExRtm0prdmZNRXmjo+bMPITU9ZA6rauX7pKmRLlSk6KmaB7vXTVSKUYL1aqmG3No6CVUObIX/v/9Djl8TaHN/JojbcIIIBAVgUUIdIl7DXLqhv1EUAAAQRyXSC4w0OGzxkkYq9Zlj5SHrvJxo24qdOlfVg0lCXDkK+sHDf6+nPvwflfzZv98Qyt9dCQF01eoEDGwLfvNr/PK1rx1fOfG4q6UQ1ue+tuEzlSBmUFPszxUylSdinNlQ9f44wg6JJL7uzRZWBXpZE+59wiRUsUUzYiXbRx0fpGnZqmXB2xZ+Oub16eYN72faK/ySJk4i8KWpnw0KLv59vgxYW3dutxfy+zBObcciVi+ozQtYrR2AZVmPbO5L2bd5sjt71xt7ndqZMnTx5P7ryzclbLNhxWpnKZqk2q63LFg0yUyjR12f29F34/34SHdqxWHqWkOvb15cjxpqzVWP/66KHSVcvqbf5C+U8kJi/SsTWdhQIFkv/ffvLskTrrKEnQWze/YlZpaevcrf+90+7w2rpyc8TpBU9KdRR9Y5dzy5RwXvjjG99Z3haXtrrq8f6Hdh/cunzzyeMnZo6bZlf37FizvXareubC3ybOtbGhrndedtGgS83E7VizLSImaTY1g9NGT+710FXOGznLWiNm3p5M+UQ5z9pyzm9km6KAAAIIZEkgKioqISH5n1JOryJiGVGW/KiMAAIIIJD7AqEQHjKKChKZwtunt5uRtTrjD5cJDKmOyTT04IALCQxlLBbmZxUgUOCm3XWdPnt83KIf50tDKz6mvzf58geTfp/Xqhy7QEbbnZ7r8WT1ZjW1ychuIlMd8xx3LQWyNVOveVE1RQ3KVE3K8tMwuokJDylxjw0PKd/N+Mc+1Fm9onq3VZokU9ZeMDW7d/Me83bBN/GmoP1NNjakI5XrV+n/7xt3rtvR8aYLTQV91z4p7X2zb0ff/YZScRcvX3LT4nV2PVHD9k1shSwVtq3couVX5pJ2/TubQp/h147q9x9TVvZrjWLj4g3m7fa1Z2UjUp5su+1LC6Ze6P1vPZZeq4GWxS2xjA07pNG3Y8eSk1WfOnFWIMzcxXz/6c3vTWxIa3nuGfOAhmyOaxnXyjnLbc2p707p/cjV9u2pUyfjv5xr3vYaelWnWy5WWYuVFFpS8iZtP7Q1l05d3K5/R739a/9fE5791Bw3iaWddWw5bszPXW65+NyyZ4Wi7NnjKSmNMggP+eRG9o4UEEAAgSwJaLmQokKRkZHOIJF2nClsZH9GzVKDVEYAAQQQQCDAAm55GLBPhm2WEWnLt1rTMiI92kxBEJ+0HEqNaDfZ5fe/axM2aU/ZN+883qnv3SwaCqVZzvZYtPRDWXvSu1zPRL/hpYHR13cxFVbOTFphovpzPolzXqIVRtqM5owNXTSwm/aaqc6RA4edNTMoV25YVetlVGHh979p85ep+c2LE0y4RCtxeg87s9+reOlzVUHbmhS8UGHT0g2mfq8H+3ikzml9ZdueQ68s7ohBTP/gJ1PZflfa7IQffrOxIUVPLn/wSns2S4Wf3v7e1m/dp60paw3R1U9eb8q9H7tGhfK1K5i3O1ZvMwXzffLr3zrfKiQkVS3dsrGhms1rte7dxlknuZyyxOZYYlL6JPPSIq9PHvtw6jvJsbDFp8N8OqV1QzY2pLczx0y17evtrHHT7NIqvd26YqvZ2ac1ZSY2pIPm9eWzn6UUk/6rHE9KbJRUmLXMNKhonR46Zuv8/effM8b8bN+q8P0rXzvfnlVOWQbl8Zi2hd/Ef3Dv21pTpsq+udFZd/XXmx1/+atl2kUAgdwSsI8tU4RIfRg3bpx+IlWoSDEjpUHIrV5xXwQQQAABBLwXCKnwkBm2gkSbN28mSJT6Q2ACQwOGj1HyaZ3VAiulGRo24uWIWt3JNJSaKwyPaPXH0xc++mTHh8c++J5y1qQnUK1xDXPKZD5e8nNSBhy99Mv/0InD9XB689Z8V2zliof62vw1igg4z2ZcNrmNFVn49sWJivvM+2KOdreZS254+XbFquzl55ZLCiQptrVz7Q4V7EakbWdHW2x9Z2HBt8lLjYZ88ujlD1ylDjvPaq/cA58/WqJCKedBL8t65JnN2dSy5wXOpEJtru4w9IthI399uV6bhmrNLK1SYV3CGq2QMu3vXr/DpK9WmGzY5H+3uyZpJY7z1aZvh0HvDcmbP5/zoCkXT+nwkYNH7Fk9aOy3SfO+f+1rTbSWCJn4V4O2jWu3TN4Cppqrf1mhCvYSU/j6heTdfHp75GByYKN+26Se29c3L01Q7iHztk6r+qZglnFpl5l5e8mg7vahY4ryfPTQe3YnmqkQ/9UvyrVkyh7fi5dPmmK9Du8/E1mR1WdPf6TsSz/HJoXhfHKj0zfhGwIIIJAdAbN0aNKkSSroQWZaT2R+HFWEqFq1agSJsmPKNQgggAACARQInc1lHmhmJZH+JtZfyVopY3ISqU54Pt3M5J82USHtJkvaStaxPfmnPT4zvN25ZodZ5aHtY8tm/B55acsW3VuVq1E+KYXQ8RN///nP7vU7tVXKrhXS/i+hJaWPOf06v0dr7d66/Z17tV1LmXQS/0osV6t85UbVTJYZU+f4sTMZc7QmSA+oMsfT/N7l1kvmTZyjU798PmvptMVm0Yre9nqwr56t5rykeLnkjVGbf9+gXU6V61ddO3+1Kvxv6LuXberd5bZLPNYQ2Wu12c1EKJR8R0l/9NVhQOetKzftWrNDz+2q3KBKiYrZCQyp/c1LN/7vwdH2RtqaZ8umoOVR9oie3WYSEslfT3ar37aRTtlgR9MLmytd9FVPXNtjyBWbl208uH1/6aplqjSurqtsCx6FEhWSQZQrumKdSubs2t+STDxeWuOzbfVWTZxW+sR9OHXK29+ZClUbV6/fptG09yfrrXJgTxz5ae+H+yoUdepk8m61rSuS4zhKBP7VyE8V2TEXavtezRa1nrv8ab395bNZ7U/nIzenZn8cd23TGooQaYzfvDjRPvhMAcRp708x8zt68Ou3vXVP1cbVzCX2u13fpNRU9uD2VdvMJ9YeMYWc3MijKd4igAAC3gsMHTpUUaG4uDhTiImJUahIP5GaH0f1E6ma0lvvG6QmAggggAACgRQI2fCQQfQIElnZ8AkS2RxDGjuBIfsBoJCmQK3WdU0SH53Vb936hd/+zp+6vuIpVzzcV8cTU56evmXFpsjLWuuIHmBvnmGf+qqTx07Yg3s27E4dBbBnVShXs0Lbq6MVG1LZxoaaXdSi080XOaupXLx8csIas5Smy80Xm/CQTimB0eKfFp53cWSD9o0r1q/sjFXprO289k+Zh8pr5VGN82rry+MWWX1rtjuZq5p2aW6fy55eOw3aNzH5qpdN/92Eh/75K3lLnUJC5iqtbDKn0mvEHrfhobW//aGB6/hvX/6ycu5yFTRxCjapoBRL2kangtJ1l6xQyiQd11u9tGbqltfvVPDu4K4DC76dpyNzxs/Yv3XP9S/cWrRU0j4+vbRm561bXylXs/yKuKX22g7Xddb2PZ3V1jNFEpXBWmGyRh2bmDCTmlo+M2mFkXPR0C2vDmp6UYs6UfX/e8NL+tRpol+/8aUbXhzYpMt5SbdJeRVPCXhJSQEprR3TfH325FhzvmF0UvJyn9wo5Yb8FwEEEMiygNlfZqJCWjekeFCvXr1MhMgZJNIpgkRZxuUCBBBAAAH/C4Tg5rLUaPo72Gw309/HZiWRgiahnZbI7CNT9iWTY0iBoXGjHvrmqwmdbhrBVrLUnxCOGAGFA25/8y6l9ckURNGWe8cONU9MN5mkdcmcT2Zq11Km19oKezftsuX0Cr0e7qtlLPastpspQmHf2kL5msm5e4qVSQpeNOrcrN8zN9izWsqkDVPKBv1w5L0vXTni86c+ip84V4ue9GwyPcvMVvvpreSFM/ZITgrlalW0l+uJbLacXqFBdFIQRy9ldDYZlMpUSwri6KX+L526yJS9/F4qZXOZsm6/edPL/77osU8e/5+5ttvgy0yh+5BetjUb39ERTe6g2HvMwq5r/j2gccekyIteSnv0VOdHTh4/bvffKQ251gfZa5V2+srH+pnKfVIyQy38Ll7PL6t/QfJONAWGbGxIsci73h+i2JAuqdKomj575loFid6/9y2lSTJvzXfFg1TflDWcN28e9USHB7et2qIjCnidd0mkCj65kbkF3xFAAIHsCdjU1PrhUz92KveQ3VNmfxxlr1n2bLkKAQQQQMDfAvmeeuopf9/DJe13PP164IEHjhw5Ejv2y/nLN436aPrficc6nl/HJT30STcUGLr3xQlvfjZzx55Digr17nzekNv7Pfr40zVbXBxRMPmf/X1yIxrxlcCoUaNatWq16+88tc/3TC7jzS3iPhrV7a4z6X69uSSDOlpXot1VjaKbFitZLCIiT74C+VRZj5zPkydP8TLFS1Uu0+ziFj3vv/LC27oVStkXVqFOhTnjZ+qh5vpaHre06YXn6bH0qW+hDNZaS6LM0StnLTOJjbT4KIPtUaaFfPnzNe1ynvLR5DkVEXVlu37PDLB5hZy3KFmp1NxPkvpw+UN9TMcUbmjc8Tzthtu/fZ+z5l/7/lS0RSt0VP/YP8f02K8TR0+sX7hGdTYt2VCwUMGakbU0WOclKivHza71O7av3qoOF0wJUnjU8XhbvFyJxMOJil/0HHLleV2TghcZv0pUKPHLZ7Mlo4xO5apXqNSgiga1eu6KAzsP6MJFPy7QYh/zVHuPdrSURgmtFe0qVam0zexTpnq5GSkptyVgskTpwsjurS+993LTgurXbVVv3W9/2IRQirP0ebTfpff2tPmMtK+w2cWRGsXu07mfJawUSMqXlPDDfGc3FFLsP+LGdo59ZOeWLX7y2Ml1C9coD5GyFDXrGqnFUJtTUobr2tZXtLlp1O2VG57ZRKbPXvUmNZdMW6y7qELpymUje7Ry3kVxpQ2L1unI8cRj+7ftNacUM7rtzbtLpuwB9MmNnDfNUnnKm9/ViqxTv12j44llTiQmR/fSbKFS0Yi4b8bv2LGjT58+NWvWTLMOBxFAIBgFKleuPHHixJYtW+qPtn7wnDFjxrfffmvemuHooH4Q1d/7ChKpcOrUKf4nEIwTTZ8RQACBkBTIo7+WQnJgmQ7K/GOO2QeuJM2qH9Q7zpzZhTSWpH1kg/p36n41Oacz/STkegWlq9Q/MC7dm+figcOy0Zmnu1eLWfpWNi704SVzPp4xMeXJ5Wr24tu7N+rctELtSnps1u51O/74dZUexG4WeuisFozMHDtdv0I7s9LkvDOKkvy155ACHB5Nbfp9/crZy/V9w+/r7boVUyeqd9t+I25UROb5nk/bJTCKUHQZeImWLBUpXezA9v1bl27Sfij7IPkL+rS/5pkBHrfw1dspb3w7+fQKJuWifmLqs4rRbFu5JabvSNu+gjt6TlnlhlXyFyiwZ9Pu9QvWrJi5dPW8labCgOdujeyZtLnPvH566/sf3/gm5V2EchtFD7jQ7P6zB1XQEqp9W/cqg3XZGuXPOTfdZEbaKbbjj+11o+prx5+uUkqjRT8uVKytWOlzy1Qr07BjU49de+YWYq9Qr7JNIq70Rrs37CpcrHDZ6uVsJMvZGZWVDWrFjKWFihXSqqW8Bc7a/qzdZP/p8YSdRAWGWvW6oOudPWxaIttUDm9k28lqYWjTwV1u6apH4/1zsN7RQw0yuDyyXMTTt/fSsgI928g+7SiD+pxCAIEgEtCGMvVWe8pMn/W3vAqp/7DbH0TZaxZEk0tXEUAAgdAWCN/wkJ1X+9ezjihOFFxBorSjQtEdIopVIjBkp9jlBf3gqLXoRepcELzhIQlPe3eysvx4Q/30zOeLlS7uTU2f19m3ZY8SKm9duUWxlfK1KnS+5WKzP0574t4b/KZ5TFjGN+1888WXP3hVxnWyffbkseMTn/1MuZZseEhNrZqz/IP7YtNMwOxxo9tev0u76pwHE777be38P5RXqHm3SOdmN2ed4CorLDX301kKIelZb407N9O6Nlf134aHjuxqczzD1UOEh1w1cXQGAd8KKDW1ElQrrYFp1rzVX/Q2YOS8nf0plCCRk4UyAggggECuCBAeOsNu/4bW0ps2TWt0aFGnU0uX7jszUSF1/czDyAb111uWC52ZzuAphUZ4SN6rZi/76rkvdm3Ykaa9EtY0aNvoksE99GSxNCvk7kEtPvrmhQkmDXaaPancoGqrnhd0vOnC9Ja9pHlVNg4q9qEtXc5td8pyPeGZ8XaVUOo2lUm64w0XeuRyTl2NI/4WIDzkb2HaRyBYBLSASA8vs2sD9ROmlqunFyHSoMyPoCqQsjpYpph+IoAAAiEpcNbS/ZAcofeDMn8l67v+ktaOuwHDk54/avaduSFUpJDQ7EVrf1260YaE1D0lnE4KCelVtFLSd15hKaAFJubpVLk7+gYdmvzfN41WzV6xcvYyJf05tPfQOcXOUX7linWr1GvXUI9Oz93uZXx3bYDq++R13e65bP5X85RjaM+mXSdOnNS6G+2l0mPatVbFGa/JuKkcni1SoqhHC9r2Nei9+7b/sS3h2/hdG5KyKeXLl7d01XJaAKXdXtWb10pzY5dHI7wNjEC9NhntKQtMH7gLAgi4QUDPL7PhIfMTpiJE+gkzzQBQmgfdMAr6gAACCCAQVgKEh9KYbvOXtL6bOJH+OrfP/9KqIl0QsA1oCgnpdi+Nm2ZCQiqbZ5CpwEIhIYTGS/+cGBoD0Sjy5MnbMLqJvoJ0ROeWKdFlYFd3dr5SvcqV7u/tzr7RKwQQQAABp4CWDml/mbaVOSNE8fHx+pEyOjraHnReQhkBBBBAAIFcFyA8lNEU2DiR/oKfNWuW/lI3YRoTLTILi3S9r9YWmWCQWSKkZu0qoTznlB03qv+pIhU6bAaL2wAAQABJREFUdzr9ZCsWCmU0aZxDAAEEEEAAAQRyU0ABIP3bj350dEaCTMxIYaPUaapzs6/cGwEEEEAAgRQBwkMpEhn+V3+762WiRSZUpOrz4uP13BkVTLTINGDyFpmywkamkOZ3hYF0XJvFzFkbDNJbxYOG3nhxRLnmnVrVZ9dYmnocRAABBFwrkHFeatd2m44hgIAPBaKiorRcyNmgfpJUYEjhIee+M2cFyggggAACCOSuAOGhLPufjhR1spfZaJH9IWDe2sOpw0a2vnMnkX50uKBTEy0z1lkiQZaIAgIIIIAAAgggENQC+ulOq86d+8s0HP0MqSeU6bhyV6f5ILOgHjKdRwABBBAIdgHCQzmdQY9oUU6b43oEEEAAAQQQQACBIBfQz4f6F8HUC4XMUnRFiNJLUx3k46b7CCCAAAJBLJA3iPtO1xFAAAEEEEAAAQQQcKWAkg2Z5eQevVOESJEjs7bI4xRvEUAAAQQQyEUBwkO5iM+tEUAAAQRCTeB4YulQGxLjQQCBbAloAZGu0/6y1FcrcqSD5ulmqc9yBAEEEEAAgVwRIDyUK+zcFAEEEEAAAQQQQCDEBcz+stSDVORIaap1XLvPUp/lCAIIIIAAArkiQHgoV9i5KQIIIIAAAggggECIC6S3v0zDVoRIaaq1+0xpqkNcgeEhgAACCASJAOGhIJkouokAAgggEAwCx/8pEwzdpI8IIBAIARMDSu9OJgmRIkRKU51eHY4jgAACCCAQMAHCQwGj5kYIIIAAAggggAAC4SVgHlWW3pj1eHvSVKeHw3EEEEAAgQALEB4KMDi3QwABBBBAAAEEEEAgWYA01XwUEEAAAQRcIkB4yCUTQTfCWiDNB9+GtQiDRyAIBWo0qxmEvabLCCCQywKkqc7lCeD2CCCAAAIpAoSHUiT4LwIIIIAAAjkWOJlI7qEcI9IAAmEmQJrqMJtwhosAAgi4VIDwkEsnhm4hgAACCCCAAAIIhIkAaarDZKIZJgIIIOBmAcJDbp4d+oYAAggggAACCCAQFgI2TTUPMguL+WaQCCCAgPsECA+5b07oEQIIIIBA0AocTyzrfd+1o8T7ytREAIGQFzBpqmNjY+Pi4kJ+sAwQAQQQQMBtAoSH3DYj9CdMBWq16BCmI2fYCCCAAAIIIHBawCQhUjEmJgYSBBBAAAEEAixAeCjA4NwOAQQQQCBkBU4klvZmbDuOeFOLOgggEI4CSkI0aNAgPdK0V69e4Th+xowAAgggkHsChIdyz547I4AAAggggAACCCBwtgBpqs/24B0CCCCAQIAECA8FCJrbIOA/gQbtG/uvcVpGAAHvBY4nlvK+MjURQACB9ARIU52eDMcRQAABBPwnQHjIf7a0jAACCCCAAAIIIIBAdgRIU50dNa5BAAEEEMiBAOGhHOBxKQIIIIAAAggggAACfhAgTbUfUGkSAQQQQCAjAcJDGelwDgEEEEAAAe8FTmblqfbeN0tNBBAITwHSVIfnvDNqBBBAILcECA/lljz3RQABBBBAAAEEEEAgIwHSVGekwzkEEEAAAZ8KEB7yKSeNIYAAAggggAACCCDgOwGlqVZjsbGxI0aM8F2rtIQAAggggICnAOEhTxHeI4AAAggggAACCCDgHoFx48apM4oQxcXFuadX9AQBBBBAIMQECA+F2IQyHAQQQAABBBBAAIGQEiBNdUhNJ4NBAAEE3CpAeMitM0O/EEAAAQQQQAABBBA4LUCaaj4ICCCAAAL+FiA85G9h2kcAAQQQQAABBBBAIKcCpKnOqSDXI4AAAghkKEB4KEMeTiKAAAIIIOCdwMYlG7yrSC0EEEAgmwKkqc4mHJchgAACCHghQHjICySqIOBugVVzlru7g/QOgXARqN2yU7gMlXEigEAuCZCmOpfguS0CCCAQ+gKEh0J/jhkhAggggAACCCCAQGgI2DTVAwYMCI0RMQoEEEAAAZcIEB5yyUTQDQQQQAABBBBAAAEEMhcwaapVr1evXpnXpgYCCCCAAALeCRAe8s6JWgi4WOCPX1e5uHd0DYGwEGCPZ1hMM4NEwDUCpKl2zVTQEQQQQCB0BAgPhc5cMhIEEEAAAQQQQACBMBEgTXWYTDTDRAABBAImQHgoYNTcCAG/CFRtGLlu/mq/NE2jCCDgtYAW8XW5pavX1amIAAII+ECANNU+QKQJBBBAAIEUAcJDKRL8F4GgFeBx2kE7dXQ8pAROHC0VUuNhMAgg4HoB0lS7foroIAIIIBBMAoSHgmm26CsCqQWqN43SQfKepJbhCAKBFJj+wZRaLdoG8o7cCwEEEJAAaar5GCCAAAII+EqA8JCvJGkHgdwRqNMiWjcmO3Xu6HNXBE4LmPhs/XZN8UAAAQQCL0Ca6sCbc0cEEEAgJAUID4XktDKoMBKo3bKTRkv6oTCacobqPgHFZ2s0q3kysYz7ukaPEEAgLASGDh2qccbGxo4YMSIsBswgEUAAAQT8IEB4yA+oNIlAVgSiopJ2h+XkpezUpB/KCSDXIpBzgdqt6h9PLJvzdmgBAQQQyIaAkhCRpjobblyCAAIIIOAUIDzk1KCMQBALkH4oiCePrgezwLcxXyrxUOKftYJ5EPQdAQSCXoA01UE/hQwAAQQQyG0BwkO5PQPcH4EcC3QZkLSkfPLr3+S4JRpAAIFsClx2/9XZvJLLEEAAAR8JkKbaR5A0gwACCISpAOGhMJ14hh1KAko/ZPaXsYAolKaVsQSFgFk61PH6fkcPNQiKDtNJBBAIbQHSVIf2/DI6BBBAwK8ChIf8ykvjCARIgAVEAYLmNgikJXA8sWRahzM5FhkZmUkNTiOAAAJZFyBNddbNuAIBBBBAIEmA8BCfAwRCQcA8v0wJqllAFArTyRiCRMAsHVJnLxk4PEtd3n44S9WpjAACCGRBgDTVWcCiKgIIIICAQ4DwkAODIgLBLKD9ZXq0NhmIgnkO6XswCSgUq4zU6nG7voOCqd/0FQEEwkDAmaY6Li4uDEbMEBFAAAEEfCBAeMgHiDSBgBsEtL9Mq4f0pRUNbugPfUAgtAUUiu1yS1eNMatLh0KbhdEhgIBLBGya6piYGJd0iW4ggAACCLhcgPCQyyeI7iHgrYD2l5lVDFrRwBYzb9Woh0C2BBSErd2qvi5l6VC2/LgIAQQCIUCa6kAocw8EEEAghAQID4XQZDKUsBfQKgb9sqpdZu8M+i8RorD/OADgLwG7QO+PX3azdMhfyrSLAAK+ECBNtS8UaQMBBBAIFwHCQ+Ey04wzTATsL6skIQqTGWeYARZQbGjd/NU9h16pZXrmiYEB7gC3QwABBLwXIE2191bURAABBBAgPMRnAIFQEzC/sp44Vuq1a58PtbExHgRyT0Ar8syfqX+Nf1hBIq3UM08MzL0ecWcEEEAgcwHSVGduRA0EEEAAgdMChIf4ICAQagImCdGWlQlKUz206WC7ESbUxsl4EAiggP4cac+m8g1p3ZDKiX/Wsiv1AtgLboUAAghkR4A01dlR4xoEEEAg/AQID4XfnDPiMBDQL65P/rBZSYj0pfQoRIjCYM4Zoh8F9CdIG8ruiL1XsSEtICI25EdrmkYAAf8IkKbaP660igACCISUAOGhkJpOBoOAU2DgqEnVm0bpiH6bZRmRU4YyAl4KKDCkPzuKDWlDmS5RuUqDbqwb8lKPaggg4CoB0lS7ajroDAIIIOBCAcJDLpwUuoSAzwT0e6wiRHO/iFWLNkh0PLG0z25AQwiEooBJM2QCQ1o0ZJINaXPZDSPGERsKxQlnTAiEhQBpqsNimhkkAgggkAMBwkM5wONSBIJBwGw0UxpdBYnMXrOHW/ab8NSi5dN2ECcKhgmkjwEVMIEhRYJ0VxMYUkEbyrRJU7EhH+aiTkhICOjAuBkCCCAQEUGaaj4FCCCAAAIZCOTP4BynEEAgZAQUJNLXT++NUJDIhIrmfpE0OJULnbu+dmTbxhdWDJnBMhAEsiSgkJDqT379G2Vzr9GsZrd7Lm/QvrEO6ojiRAqqdhkwwoeBoSz1jcoIIICAbwWUhEgNxsbGxsTEKFrk28ZpDQEEEEAgqAUIDwX19NF5BLImYINE+o1XjzbTxWbf2fQPpqjc8fp++QruV6Femwb69ThrTQdV7awumzr+T5msju9kYpYvyeAWxxPLZnDWf6fyF9qT7cbzFtrrzbX5C6dbLX+hfd60kI06Jh70x6+rlFRIl9uokGlKUSETKtIfkxtGEBjKBjCXIICAqwUUIYqPj9caxhEjRphokau7S+cQQAABBAIlQHgoUNLcBwHXCChIZPui9UQqb1oar2jRzI8+NcdNtMiUtZjCFNL8rud8p3ncVwdPHC3lfVPHE0t6X5mawSKgD2fqruYrkBTHzMZLkaDUV5kPuVkopLNaK6TvtV/gH9VTU3EEAQRCREBpqgcMGKA1RBoPEaIQmVSGgQACCORYgPBQjglpAIFgFnCGijSOdQvi0hvN2kWzUp9K/DP1saQjaf5Kn2ZVs4gpzVMc9K2A1sL4tkHTmnk6nj9a9r79Oi2ive1Av7Mqmk+1uZx40Fk0vEEAgZAWMGmqTYQoOjqaXWYhPdsMDgEEEPBWgPCQt1LUQyAcBDJIsJLBqYDJZBC9ClgfAnAjN1AHYJhuuAXUbpgF+oAAArkiYNJUawGRgkTjxo0jQpQrs8BNEUAAAVcJEB5y1XTQGQQQyEiAX+Yz0uEcAggggAACWREgTXVWtKiLAAIIhL4AD7YP/TlmhAgggAACCCCAAAIIpBZQhCgyMlJpqnv16pX6LEcQQAABBMJKgPBQWE03g0UAAQQQQAABBBBA4IyA0lTrjXmQ2ZmjlBBAAAEEwk+A8FD4zTkjRgABBBBAAAEEEEDgtIBJU62i8hDFxaX7hAq0EEAAAQRCXoDwUMhPMQNEAAEEEHCjQFRUlBu7RZ8QQCD8BEyaao1baaqJEIXf/DNiBBBAIFmA8BAfBQRcIVCnZSdX9INOIIAAAggggED4CSgJ0aBBgzTumJiY8Bs9I0YAAQQQSBIgPMTnAAEEEEAAAQQQQACBcBfwJk31iBEjWF4U7h8Uxo8AAqErQHgodOeWkSGAAAIIIIAAAggg4LVApmmqo6OjWV7kNScVEUAAgSATIDwUZBNGdxFAAAEEEEAAAQQQ8IdApmmqVUHPOGMBkT/waRMBBBDIdQHCQ7k+BXQAAQQQQAABBBBAAAFXCGSapjoyMtIVHaUTCCCAAAK+FiA85GtR2kMAAQQQQAABBBBAIGgFMk5TrQ1o7C8L2rml4wgggEBGAoSHMtLhHAIIIIAAAggggAAC4SaQcZpq9peF2+eB8SKAQJgIEB4Kk4lmmO4VUJZH93aOniGAAAIIIIBAWAqkl6Zau8+0v4wFRGH5oWDQCCAQ4gKEh0J8ghkeAggggAACCCCAAAJZFcg0TXVWG6Q+AggggIDLBQgPuXyC6B4CCCCAAAIIIIAAArkgkF6aai0s0v6yXOgQt0QAAQQQ8KcA4SF/6tI2AggggAACCCCAAAJBK5BmmmqFjTQgHm8ftLNKxxFAAIG0BQgPpe3CUQQQQAABBBBAAAEEEEgzTTXph/hgIIAAAqEnQHgo9OaUESGAAAIIIIAAAggg4K2A1gFVq1ZtxIgR6V2QOk21OZJefY4jgAACCASjAOGhYJw1+owAAggggAACCCCAgG8EtFls8+bN8fHxvXr1SnPLWOo01TrC4+19o08rCCCAgGsECA+5ZiroCAIIIIAAAggggAACuSQwadKkqKioAQMGpLmMSPGgQYMGqWuqYEJI2l82a9asXOost0UAAQQQ8L0A4SHfm9IiAggggAACCCCAAAJBJ6A0Q+PGjYuNjU1zGZFJQqRBxcTE6Lv2l2nBUdCNkQ4jgAACCKQnQHgoPRmOI4AAAggggAACCCAQXgJmo1l6y4i0wkiLhrStzKwwYn9ZeH04GC0CCIS6AOGhUJ9hxocAAggggAACCCCAQFYEzPPstYwodcpqk5Rap8zOMrOSKCttUxcBBBBAwKUChIdcOjF0CwEEEEAgtAWio6NDe4CMDgEEglpAESLlq1a+IUWCnNmInGmqNUAtIArqYdJ5BBBAAAErQHjIUlBAAAEEEEAAAQQQQACBMwJpLiOyaapNvTQfdnamCUoIIIAAAkEiQHgoSCaKbiKAAAIIIIAAAgggEHAB5zIiu9dMB5WEyPSF/WUBnxNuiAACCPhFIL9fWqVRBBBAAAEEEEAAAQQQCBUBxYM0FG0000sPLFPuaqWp1gPOtLmM/WWhMsmMAwEEwl2A1UPh/glg/AgggAACCCCAAAIIZCpglxEpHqQgkRISmTTVutCZnCjTdqiAAAIIIOBOAcJD7pwXeoUAAggggAACCCCAgOsEFCQaN26cSVk9YMAAs8VM0SIyELluqugQAgggkEUBNpdlEYzqCCCAAAIIIIAAAgiEsYBSU+ulxy/q2fYKDBkJZSDSwTBWYegIIIBA0Auweijop5ABIIAAAggggAACCCAQYAEFg+x2M91a2YgC3AFuhwACCCDgWwFWD/nWk9YQQAABBBBAAAEEEAgjAQWJ9AqjATNUBBBAIEQFWD0UohPLsBBAAAEEEEAAAQQQQAABBBBAAAHvBAgPeedELQQQQAABBBBAAAEEEEAAAQQQQCBEBQgPhejEMiwEEEAAAQQQQAABBBBAAAEEEEDAOwHCQ945UQsBBBBAAAEEEEAAAQQQQAABBBAIUQHCQyE6sQwLAQQQQAABBBBAAAEEEEAAAQQQ8E6A8JB3TtRCAAEEEEDApwJ6JrRP26MxBBBAAAEEEEAAAQSyL0B4KPt2XIkAAggggAACCCCAAAIIIIAAAgiEgADhoRCYRIaAAAIIIIAAAggggAACCCCAAAIIZF+A8FD27bgSAQQQQAABBBBAAAEEEEAAAQQQCAEBwkMhMIkMAQEEEEAAAQQQQAABBBBAAAEEEMi+AOGh7NtxJQIIIIAAAggggAACCCCAAAIIIBACAoSHQmASGQICCCCAAAIIIIAAAggggAACCCCQfQHCQ9m340oEEEAAAQQQQAABBBBAAAEEEEAgBATyh8AYGAICCCCAAAJBJLB2QVwQ9ZauIoAAAj4UiIvL6H+AnTp18uG9aAoBBBBAIEsChIeyxEVlBBBAAAEEfCAQGRnpg1ZoAgEEXCaQcewj487OmjUr4wo6Gx8fn2md9CokJCSkd4rj/hAI/P/no6Ki/DGQ1G1GR0enPujDI0QJfYhJUwhkSYDwUJa4qIwAAggggAACCCCAQJKACQYprGOiNt7HX5rWam4EC5QMXJ6HwEcr+JQEWCAn0cMsdTW9Gx07cDKDdpauX5zB2fRO2T8sLesnBb8KlMp7f99H85dKql6gTN4CZfKkdyHHEUAgGwKEh7KBxiUIIIAAAghkX2D9otnZv5grEUDABQIKDMXExNh4kPkN9o37PlDX2jXpmO0OFiydz15rfgG2b1XQL8POt6eP8LuxB0nIvj2295T3Yzu2N6MwjW3n+H5bzKRwdN+JTGr47fTcZTPVdvzKueYOv877tcknNfUnTtGiIVc9ooP6U6M/LEXqn/mz47e+0DACoS9AeCj055gRIoAAAgi4TSBgWwDcNnD6g0CwC4wYMUJLJxQY0i+oN3W7Paphu5zEgzw0nL+EH93ncTIiYq1Xv6I7Y0zOJlLHm5xnTTl1BCp1ndRHcmsFR5YiJqm7neYRLwMrqa/1PtRir3VOtz1IwUPA/Pny+FP2yoTnVC3yjjr6rj+GihMdWXuiSJ18BIk89HiLQFYFCA9lVYz6CCCAAAIIIIAAAmEnoMBQbGyshq3AkBYKefy+6h6O9IIOacSbUnfauwhU6us4gkAgBcy6IX3X2iItLFKciCBRIP25VwgLeC5SDeGhMjQE3CzAk4zcPDv0DQHfCmxc+qtvG6Q1BBDwt8DTDyXFhkxgaOyjE10bG/K3A+0j4CoB/UlUkCjhnbXqlYJEWlWkZUSH5p3wx8oyVw2cziDgJwHCQ36CpVkEvBUwT2cgF4m3XtRDIPgFtqxc5O/HvgQ/EiNAwC0CSjPUq1ev0Z/EankCgSG3zAr9QOBsAQWJ9CdUxxQkeuHdkQfjjx1Z7dVmzLOb4R0C4S7A5rJw/wQwfjcI6GEirCZww0TQBwQCIPDzeyN1F57aGwBqboFAzgXshjI37ybL+TBpAYEQEDA7zjSQMZPf1XfzlmxEITCzDCGQAoSHAqnNvRBAAAEEEIjg8dJ8CBAICgGtGzIbyrRoKCg6TCcRQEBRISWMv/vVW0ShsrKt51bqdOYCgWAUYHNZMM4afQ41AT3DSJtNQm1UjAcBBNIS0FJBHluWFgzHEHCdwIABA8yGMtf1jA4hgED6AkpIpOV+WkN0w3/6aJcZeYjSp+IMAp4ChIc8RXiPQOAFTBYSslMHXp47IhB4AWLBgTfnjghkQ0D5hsyzkLJxLZcggEDuCpgI0dL1ixUh+nvNydztDHdHIIgECA8F0WTR1ZAVIDt1yE4tA0PgbAGTeGj48OFnH+YdAgi4S0Aph44dOGlTmbirc/QGAQS8ELARImWqJk21F2BUQSBJgPAQnwMEXCGgXCRzvnjbFV2hEwgg4DcB/TEn8ZDfdGkYAd8ImJRDg3sN8U1ztIIAArkkYHeZ/Txpei51gdsiEGQChIeCbMLobqgKDB06VENjf1mozi/jQkACZukQiYf4MCDgcgGTcki/WLq8n3QPAQQyFdAfZO0SVabqQ/N4zn2mWlRAICLPqVOnYEAAATcItL3kcnVj4Khv3NAZ+oAAAj4XeLp7NbW5efNmn7dMgwgg4CsB8yT7hHfW+qpB2kEAgVwXUAYi9eGHmd/mek/oAAIuF2D1kMsniO6FkUC/QQ8qZy0LiMJoyhlqOAmYpUODBg0Kp0EzVgSCT0BPstdag+DrNz1GAIH0BbRXVGmqf5o4Lf0qnEEAgSQBVg/xOUDALQJbD0f07c0CIrdMB/1AwLcCLB3yrSetIeAPAS0dmjPl17GPTvRH47SJAAK5KMAColzE59ZBJMDqoSCaLLoa4gJVikawgCjE55jhhavA7+NHaugsHQrX+WfcQSOgpUMt60cFTXfpKAIIeC1gFhAp8bzXV1ARgXAUYPVQOM46Y3azwKNj4sYNHzBgxLg6LTu5uZ/0DQEEvBTQtrIja+clJCSQdchLMaohkCsCZB3KFXZuikDABLSAqEDJvJMmTQrYHbkRAkEnwOqhoJsyOhziAvf07dS+750zxr0U4uNkeAiEh4CJDWmsLB0KjwlnlEEsQNahIJ48uo6AFwJaQKR/qmEBkRdUVAlfAcJD4Tv3jNydAtpi9tAjw9Q3k8jWnZ2kVwgg4I2A/hTP+eJt1dTD7IcPH+7NJdRBAIFcEdDSoVy5LzdFAIGACegh97pXTExMwO7IjRAIOgHCQ0E3ZXQ49AVaVoi47/6h+q3yvfsv50FmoT/fjDAUBSoVjVC+IRMb0r9VEhsKxUlmTCEoMOSqR0JwVAwJAQRSBJrWap5S5L8IIJCGAOGhNFA4hECuC/Tv0fnF2LHqhvIQsYwo16eDDiCQJYHIchEJH43YsGSe2VDGtrIs6VEZgVwRiI+P5/fGXJHnpggEUsDsLwvkHbkXAsElQHgouOaL3oaRgCJEX3z1zZU33qkFCESIwmjiGWowC2jRULFNM56+vZd+1dSGMqUyGTduHEuHgnlK6Xu4CGiVH88sC5fJZpxhLGD2l5F+KIw/Agw9EwHCQ5kAcRqBXBRQHqLXRg7T0gNFiJ7uXo0gUS7OBbdGIGMBBYa611JsKO6hQTcoMKSXIkR6VFmnTjyCMGM5ziKQ+wL8rpj7c0APEAiUgNYJkn4oUNjcJ/gEeLB98M0ZPQ5DAfO0XTNwPddMhYsHJqWv5oUAArkuoMBQi/IRH4waoXiQOmMCQ/rOoqFcnxo6gICXAjzS3ksoqiEQAgJzl80cPfM1Hm8fAlPJEPwhwOohf6jSJgI+FtDvmVqGYDKYaCWRXUxE4mofQ9McAl4LKCpkVgwpzVCbhtXMbjJdbQrEhryGpCICuS9gYru53w96gAACARHg8fYBYeYmQSnA6qGgnDY6Hc4C5uG7ymliEcx6olotOtRpyTYWq0IBAb8IKCSk158rZqxZOFu/UupHzMjISLNiSMeHDh3KbjK/uNMoAv4U6NWr13nlW/HYMn8a0zYCLhKIvKOOMgPy97WLpoSuuEYgv2t6QkcQQMArAbMqITo6etasWea3Uy0m0pXme9WGLWo0baO3ihbpuwsDRqx4ynSaXThrmfY5tCsoJDR7ZlyFohHrEmZPWjJPISEzXgWG9DJvCQyF9meA0YW2gP4Un9etVWiPkdEhgIAV4DGFloICAh4ChIc8QHiLQHAI6F887D96KKemQkXqt5YUbVm5SF8qm2iRGYxiRtkblWkqe9dyVfAKZPsDE7xDTq/n6f0RUFRIl2jRkAK1Ktg/jOm1w3EEEEAAAQQQcI+AfnLm7273TAc9cY8Am8vcMxf0BAEfC2T8KBYTUfLxLV3WXBClk1CgwWV4fu+OCaxkehtffVC9vF0G/TE9UTv8QJmBEqcQCEaBatWqvXHfB+aJ18HYf/qMAAJZEnhlwnO/75pPduosoVE5TARYPRQmE80ww1Eg419iMz4bjl6M2ZUC7vmguqcnrpwoOoUAAggggEBwCEQ1bKfwUHD0lV4iEFgBnlwWWG/uhgACCCCAAAIIIOAyAZYOuWxC6A4CCCCAQC4IEB7KBXRuiQACCCCAAAIIIIAAAgggEHgBhYPtUyYCf3fuiICbBQgPuXl26BsCCCCAAAIIIICAHwUyztPnxxvTNAIIIIAAAi4TIDzksgmhOwgggAACCCCAAAIIIIAAAggggEBgBQgPBdabuyGAAAIIIIAAAggggAACCCCAAAIuEyA85LIJoTsIIIAAAggggAACCCCAAAL+FGBjqT91aTtYBQgPBevM0W8EEEAAAQQQQAABBBBAAIEsCcxdNjNL9amMQPgIEB4Kn7lmpAgggAACCCCAAAJnCcyaNeus97xBAAEEEEAgXAUID4XrzDNuBBBAAAEEEEAAAQQQQAABBBBA4LQA4SE+CAgggAACCCCAAAIIIIAAAggggEBYCxAeCuvpZ/AIIIAAAggggAAC5CLhM4AAAggggADhIT4DCCCAAAIIIIAAAggggAACCCCAQFgLEB4K6+ln8AgggAACCCCAAAIIIIBA+AjEr5wbPoNlpAhkSYDwUJa4qIwAAggggAACCCCAAAIIIBDcAjy1MLjnj977R4DwkH9caRUBBBBAAAEEEEDA9QLx8fGu7yMdRAABBBBAIBAChIcCocw9EEAAAQQQQAABBBBAAAEEEEAAAdcKEB5y7dTQMQQQQAABBBBAAAEEEEAAAQQQQCAQAoSHAqHMPRBAAAEEEEAAAQQQQAABBBBAAAHXChAecu3U0DEEEEAAAQQQQAABBBBAAAEEEEAgEAKEhwKhzD0QQAABBBBAAAEEEEAAAQQQQAAB1woQHnLt1NAxBBBAAAEEEEAAAQQQQAABHwtERkby1EIfm9JcSAgQHgqJaWQQCCCAAAIIIIAAAggggAACmQksWB2fWRXOIxCmAoSHwnTiGTYCCCCAAAIIIICAEYhfORcKBBAIH4GoqKjwGSwjRcB7AcJD3ltREwEEEEAAAQQQQCCkBBISEprWah5SQ2IwCCCAAAIIZEuA8FC22LgIAQQQQAABBBBAICQEWtZnHUFITCSDQAABBBDImQDhoZz5cTUCCCCAAAIIIIAAAggggAACCCAQ5AKEh4J8Auk+AggggAACCCCAAAIIIIAAAgggkDMBwkM58+NqBBBAAAEEEEAAAQQQQAABBBBAIMgFCA8F+QTSfQQQQAABBBBAAAEEEEAAAQQQQCBnAoSHcubH1QgggAACCCCAAAIIIIAAAggggECQCxAeCvIJpPsIIIAAAggggAACCCCAAAIIIIBAzgQID+XMj6sRQAABBBBAAAEEEEAAAQSCRGDp+sVB0lO6iUCgBQgPBVqc+yGAAAIIIIAAAggggAACCOSWwAWN2+XWrbkvAm4WIDzk5tmhbwgggAACCCCAAAIIIIAAAggggIDfBQgP+Z2YGyCAAAIIIIAAAggggAACCCCAAAJuFiA85ObZoW8IIIAAAggggAACCCCAAAIIIICA3wUID/mdmBsggAACCCCAAAIIuFlgwep4N3ePviGAAAIIIBAAAcJDAUDmFggggAACCCCAAAIuFYhqSJJal04N3UIAAQQQCKQA4aFAanMvBBBAAAEEEEAAAQQQQAABBBBAwHUChIdcNyV0CAEEEEAAAQQQQAABBBBAAAEEEAikAOGhQGpzLwQQQAABBBBAAAEEAiEw+vvXLx/W5bnxT506dSoQ9+MeCCCAAAJBLpA/yPtP9xFAAAEEEEAAAQQQQOAsgeUbl7zx1Sgd+nT62D7R/epXbXTW6dx+c/Cv/f/7eXS+vPnu6vVAwPqyctOyhDXzdx3YUal0lSuj+xXIVyBgtw7SGx0/cXz/oT3qfNmSFfLkyROko6DbCCDgvQDhIe+tqIkAAggggAACCCCAQBAIzFk6w/YyX17XxUEmz//u/e/fVg/bN+3cvPb5tqv+KOz/a9+kuRO+iPt4y+5Ntv3t+7be1+dh+5aCU2DPwZ2jvnh+wep5O/fvMMcLFzynbpX6Dao1blrrvG6tep5TqIizPmUEEAgZAcJDITOVDAQBBBBAAAEEEEAAgQjtJvty9ucWomSxErbsksKJkydMT+JXzPFTeEgrX2Ytmfb13C/iFk1NPeoNO9alPhj4I3sO7SpcoHCxc4oH/tbp3fGvvw/1//flew/tdVb45+jfS9cv1teEmeOfH//MNZ2vv7bLTRXLVHbWoYwAAiEgQHgoBCaRISCAAAIIIIAAAgggkCzw28pftu/dajlKFC1lyy4p2PDQkcQjPu/SsRPHtKXu3W9fP3TkoLPx4kVK9O10bcECBdfvWHd7j3ucp3Kl/MbXMaO/e1MLc567/ZVOzS/OlT6kvunuA7s9YkMedRQq+t+U0foaP3xSw+pNPM7yFgEEglqA8FBQTx+dRwABBBBAAAEEEEDgLIEJs8fb94o+5M/nuh/4bXho1ablWsizde/mHfu2aRfYOQWLFC1ctEPTzmVLlLdD8L6wfvvanxd+P2HmJ3ZXlLm2arnqt1w66PJ2V7kq39CcpXHqnqItj7w7ZNxjX9apXM/7kfqvZo2KtTq3uLhQwcK92l7VqEbTkkVLHT95fNPODas2L1fY8as5Z1alHTp8VvTNf12iZQQQCJiA6/62CNjIuRECCCCAAAIIIIAAAiEmsGbr6im/fW8HVbSwu9LEHP77zz+2rl60Zr7p4S/LZ1/5xCW2t6ag8MSou2I9Dmb69ttfv3z8/Qc9qrVp3P66i25RvMmFmZVb1Gm1YuMydVgRortfvfmH52a7oZN58+T1wDcxtfU71kxLmGJ5FXZsXrelfUsBAQRCQ4DwUGjMI6NAAAEEEEAAAQQQCHeBk6dOPjnm/5wKLtlZNu7n9z+e+uH+P/cpFOLsXprl5nWzk6x65u9n5Rga2GPwNR2vL1+6Upq38N/BqQt/2LxnU79ON2Sav/n/+j/Rtkn0mMnvmiTQhw4fKFHMXdsAtWho/PQx0xOmeKzGkt6IW18sVKCQ/xhpGQEEckWA8FCusHNTBBBAAAEEEEAAAQR8LKCcO8s3LHE2mjdvPufbnJS1I0zZfEoVK53VRrTnK+azkRlc1bhms5b1os6r3aJulYbaCJa93XDFi5R03kI5hsqUzM4ONa1vypc3f+FC5zhbc5ZXb1kxLWHy9r3blOSoQqmKdSs36NLikiKFi6rO2J/fe/mzZ1XYunvLsOv/7bzKltduWzPg2d4F8xf8aNjX0c266MtstcuX1kz9nXhk/5/7SxYradq3jaRXyPYcpdngS5+PmPX7dI9TNSvWenbgK9p35nGctwggEAIChIdCYBIZAgIIIIAAAggggEC4C+zYu+21iS8ahRcH/feh2HtVzps3b85dlm9c8trE5+et+EVNlSleRk+j79OxfwZPHFOQ4rMZ4/Qg+dqV614VfW0GK4ZaN2zz33vf98k6lEGX3TN32Uybk/utr1/9avYXt/W467I2V3rTfuLRfz6e/uGHP7xjElq3qNuqQ7NON3a93SNdUdzin4e8MciDVDutHr72id7tr1m6fpE59evy2R517NvXv3pRIPqamvDjTV1v1/E0A0OL1iwYM+WdGYt+Nhdql9zgK+4/r1akbcejkNU58rg8zbcmbuVxqlLpKuu2r6lXtWH2ongerfEWAQRcJUB4yFXTQWcQQAABBBBAAAEEEMiygJ7j/sDbd5pATK92V0U3u9A0kd7v8InHEhW+UeyjavnqSjeT3v20QOapDx/+ft7XtoIeazVp7gR9vXJ3rHne1qlTpx6MvWvfoX1D+j6smJE2uA17/4HJ8d+aS/78+9DNXQfd1O32T6ePa1H3/M4tLmnfpPPSDYsfffc+VShdvKw3sRt79wwK5UpV/N8jX7z0+Uh7a4WK/j122KsTXrj+kpu12yuDrVu/Lp81/P2hzid2KTuSvuatmPPfe95TnmZz34Q/fksdG9IpsT895tG4xVNLpKxgkm2aXV2xcamN+LRu0CbNOjqo4Np/Pn7SefbX5XP09cZ9H7Rr0tF5XGVv5sjjEi/fdmzWZe7SmR6VlS5KX298/fIzN7/QKv0heFzFWwQQCAqBdP8yCIre00kEEEAAAQQQQAABBBB4/pOnTZ7jSmWqPNz/yXwpTyvLn9fzH4MP/rX/ufFPtbm7cd+nLr3i8Yt6PBKtRTdpAirkNOSNO5yxIT0b3ta08Yud+7ZPWzhFwZSxU0br7Ac/vm0DNHr76hcvaFnTkKse+eX1pW8N+V+/zjdULVetYbXGpp3Df/9lG8x5Qc87e+62Vz994tuurS+zrWk1kFYSdX6glYiO/HPYHrcFPZBr8Cs329iQc4y/rfz1p4U/2Jqvf/2yLUef1+XmS++4settjWo0MQcV91mwJt5W2L1/hy3bwugf3jBlXd64RjNF1mYvnaHNd7aCCtqhZm2dx1V+8dNnPI54OUceV3n59prON7w8+C2F9to27qBVY86rFHq7Peb6d757TdFA53HKCCAQ1AKEh4J6+ug8AggggAACCCCAQLgLKILzRdzHRuG5O1515qkpkL+gU+eH+G8ufSRaKYrsQWUdHvrWXfats6BFQHbxyBXt+saNWhD3ykItYDF1dOHabX+oXDYlxc9ffx9SYubXv4xxNqLyTwlnIizmVLFzipnC3kO7PSrn/G39qo2ev/21b0fG9b/wRm37sg1+Mu1/N/ynj4JZ9ogKKzctu+PlAeaIAkOv3/e+xjjj5fmdWlxkDs5Y9JMpaLALVydHf+6+8oHX7hl9X5+H7+/76MfDJn007CttRlO1Lbs2XdnhGlP/9/UJpmC/L12/WHE08/bhfk+poEjQva8NHPzKjTbI8t2vX5nsRaba4CvuGz980pC+j5i3G3asF7Ipm+9ezpHzEu/LepJal8iuCu29OWTMzy/FT42J1+z37XSdbUFxt0dHD7FvKSCAQLALEB4K9hmk/wgggAACCCCAAALhK6C1J8Pee8CMX3EEk57mn6NHzJG8efNYmk9njH1s9BCzAc0cVB5oe3b6oilD3rzj6zmfmyPzVsye8tt3pnx7z7ufuvn54kWTcj+fOHHcXhIRkdR4YsrDyJSc6MG37zFnW9a/oFtUT1OOW3TWM8V00Cb0OXHSX2tPqpSrqlVUCvT8+9aXmtZqbnqipDnXPHOZ0vSYt/pu1+lodYwCPe2bdNLBggUKHT2aaOuYgpb5mILQtF3OeVbrgD74v08VPvvymZ96RyeHh5asX+ysoy1gj38w1By5o+c96p7Khk6Btt37d+qtFhON+iIps7VeilV9M3LGHZf9q2H1JkpR1LnFxeb4iRMnTEHfvZ8je0lOCqXPLaOtbUq5PfvVRQ9c85gJvelDosfS5aRZrkUAAfcIeC43dU/P6AkCCCCAAAIIIIAAAghkIKCEyg++nbz256LzuymOoGddKcHN7GUzzFXaHnX10z26tureoWmX5z5+yhzUL/aPXvfUJS176OHr2m917PhR7YR64M3BOqtQzgUN25cvXfGFT848eOvdb9+YnvBz7cp1duzd/vu65EUxiqfUqlRbl+z7c59p1n4/v37U6/e+pxUxZpfZlt0b7SlTOHbymCmc9Ft4yLSvtEE921yprzFT3n3li+d0UHvNRowbpkiQyj8v+N4OR5vL+j59aasGFxQpXGTm4uk2iGZz/ezYt820eUWHvmlmdFL4TF8atXh1+dSFk7Xuxlyi729+/bLW/qigJ3/d2j15ygqlLG7avHtjhdKVlNvI7nF77V+jtQvPXv5I/6dqVqxdr0pDm0FJN/J+jmw7PikUPefcGy4eqIfNDR51kzz1WLq2jaPrVK7nk8ZpBAEEclGA8FAu4nNrBBBAAAEEEEAAAQSyL/DEmIe0IsZcr3hE23ua2riGbXTN1lU6qAexmyMKXrz30HgteDFvT+9EK/rqxOdtfcVHpidMts2a42pEX7aOCk/e9LzJab3vr73O41pco9iQeTC8Hkym+JRWxzgrJJVPnTJHPHqrdT3v/fBm73ZXR593oeclGb5XmOzAX/sVYUmvlgJnNSvUMomllaTpzyMHzy1S4q1JrzrrqzOzl8xwHjmvduQV7a82R/b/lRwFK5DvrP16zvoqy6Rj8wu1pkbZqeev+tUkb1Yc6sMf3zE1X7jjdZuNu2zxcubgH1tXqeYfW1eat11b9/B4MJyGpr1s5qz5nqU5cl7oq7I+Qi/d+YbZmvftrxM9uueru9AOAggEUoDNZYHU5l4IIIAAAggggAACCPhG4N3v/jvlt++dbXlEW8ypu3vf//kT3+88kByjKXVu6TqV6zuvUmrqT6YlZyOqX62RlsBMWZDc7NBrhr1811u1K9V11tf6l7fvHxPdrIs5uO/QHufZUXfFalGSOdKoelNT2LJ7s7NOyWJlzNtDhw86j7828Xll5xn50ePOg5mWtSer/4ielz7Sod+/e27dsyW9+g2rJeeQVoUj/xzZsGOdCYFpGdR3z850ptQxLVwV3f+dB8bZp857n0XbPNBNjbz42Qgt5lq9ZcVDsfeaNh/q/7geCW/K+m7DQwtWz0s6mCd5J+DmXRsV8LLV0ixkaY7SbCHnB1s3bGs+GzZDU87bpAUEEMhFAVYP5SI+t0YAAQQQQAABBBBAIDsCH06JffPrVzyu1GPLmtVuoWdpVS1T7fEP/s9Ei/pEX6u1PG0adzB5pvXMqTtiru/R5oryJSssWb9o3oq5yzecycVza/ekLWZrtqw2Lfe44AplnOnc/BJFUlZtWVEwX/5alerXqXxWtGjLnjOhn/uvfrRulTOxp1qV6ph2Fq2d79wqpc1Zyq2jfUn6UhzEPDk+8Vji4rWLPEbkzVsN0+zbWr15Rc/HOnVo1rn7BVc0q9m8YP6Cx0+c+Pvoke17tyxat+C7X74yrSmiUbpE2ZmLfzZvO7foWrlsFaXU+deVD2r50o5926uUrdaoehNtoXLePfFYcrxmz4FdzuOpy11b9nj9y5fkrP5c+nAHjdHU0dPKrrvwZmf9MiWSVw8pa7WO16vcwJzV+qbrRl6hQFv1CjWd9Z3lLM2R80JvykrCXbp4WW1ny7iynkm352BScvEd+1ItEMv4Ss4igIArBQgPuXJa6BQCCCCAAAIIIIAAAukIvDLhuTGT3zUnlXIoqmG7CqUqKgFzmZTNSjo1ZspoE3RYsi5B61mujr5u4szxJoyihDs2547zDmrk4vMv1ZHD/yQ/b37Z+sXa56UnWCmzTHrJZezqIa0q8gh/NK/d0rQfv2KuEgA571WxTGUTN1Hk6IJGHfSA9ufHP2XiWR1S1iU562dQ1mIlZcJOXoATEaENYh57xDyufWHQ68qNfTjlIffLN/5uKmi7mXriUdm+tVm0N+5KSiGUwUvBr3uvfEhZwFXHxoYUuXt24MseV5VPeehb3rz5dEo5mxTaU2xIZcXjtBiqW6vL2jXr1LpBm1LFSntcm6U58rg247daVzXwpWtVR5+HS6Mu79rqsjqV6pkQnvNCbZ0b/v5QM8AmNc9znnJ/ee4vc93fSXqIQOAFCA8F3pw7IoAAAggggAACCCCQTQHlCbKxod7tr378xmdNDiCP5nYf2GmOrNy8TOEhLSB6/d4P9WwyjxRCzquuvehms5eqarkaJmHQ29++1qZJtH3QmLOyLevZ5yaxjjIxe+Rs1uohxYwUk4pb7PnwskplKmtxjRq5c9RNbRq3X7Rmod0Zd0u3O23jXhZG3hoz7P2hNkKU3lV1qzR4+ubnTZyratnkp7YpHKOntnVp0TW9qzyOb961weNI6rfdoy5ftGb+ZzPGmVPKx/TuAx8VO6e4R00FpEwea+34M6feum/M4FdvMhEigXw99wt96ZR2wDWo3uS82i2a1YpsVqu5LszSHHncN+O3ew4mL4/SZ0CfNPNh02ovLSbSQI6fPLZtz1YtyLJZtNXaXb2SYmHB9YqKigquDtNbBAIgQHgoAMjcAgEEEEAAAQQQQAABHwgcOnzAJjm+qdvtzmdjebRuE0LvPpC0/UcvPUx93GNfTl3447a9m/UMMm3mKl+6QqXSVWI+e9aEZrRKxdS8rcdgE2rRvrP/i73n2YGjbDohU8F8V6Kfnfu2aYfUmEe/UHYhm3PHWefJG5+75YV+bZq0dx5U+aoO/e0D739dPseeffS6p81D3+0RbwrK3Dz6wY/Vh2mLJs9dOkND1qano8eP6tqihYucW6S4Yisdmna+tPXlNoAVWa+1Mk+bVVR6apvSDCmTTup7KXmQVtMcPLxfq7S05UoVWjVIo1rqC/+v/xNHjyXOWzlXu+2evvmF1Mt/zCX9L7xBE6qemLd6MNkHD3029ufR733/to2X6ZRiMdobaLYHKqL0zchpWZ2jGhVqpe5kmke0FEjRKGf0R9W0Sii9RWcjbo3Ruqc0m+IgAggEl0Ae5XILrh7TWwQQQAABBBBAAAEEfCJQrVq1N+774K1Jr4x9dKJPGvR3I2u3/dH3qUu1lGPErS9l/Hivr+Z89vSYR9Wfu64YcvtlyamRU3fvxMkTre5Mzhb021urbPRk6NuDlSXa1Few4I6e/zq/XlSVslX3Hdq3ZtvKuctmxS3+2USgtG3q+//MTN2yPaIIS5rRpTtevl7PNbPVOre4eGD3u7RFzh7xd0FJo/s909PepVtUz15tr2pQrZGSFm3evWnhH7/NXjJt3opfTIWRt43auX9bwh/zn7hhZNkSFexVOS9s2rmhXMnyHkSH//5zasLk39cmLF6XkHrB16QR06qVr+HDOfIYhaZs/up5G3auW7Fx6YqkfEw7nLEqU1lbzy6M7KbsVIGcMo9+Zu9t5B11bus/KF+JiOHDh2evBa5CIFQFCA+F6swyLgQQQAABBBBAAIFMBIIuPKTxKH1ylTJVtcwkk7FFROjhVnsP7u7ToV/qxDH22q27t/Qc1klvtYBlzCNJ+5jMSwGCxz94cOrCySkH0v2v0uV8PGxSuqfTP6HlS5/OGLth+9qG1ZsoyY43I0q/sWye0VPbhr51V+rYR+rmXrvn3Yzjcakv8dWRfxL/1g5BpQb/Y8sqzcvVna5rUbeVGg/AHNkhnDx18s/DBw8eTsomXrRwsUqlK2u7oj0bXAXCQ8E1X/Q2kAJsLgukNvdCAAEEEEAAAQQQQCBHAo1rNPPyej1CK9Oam3cnJ1puXud8Z2UtZnlx0Btjf37vve/etPmVnRVU1rqhzs0vHtzrPo/jXr4tVKDQjZfc5mVlP1Vr16Tjp49/++zHw+0qodQ3at2wzfUX3ZJbsSH1R4EYxYNMSMjZvQDMkb2d8lspfpcrITzbBwoIIOBvAcJD/hamfQQQQAABBBBAAAEEXCqwaddG0zOlPfbooh5YpvBNv843/BD/tXY5qeaffx8qfW7pGhVqN6zeuF2j6PKlK3lcEoxv9fD4t+8ft2br6u/jv9q4c70eSJ8vb/4q5arVqli7df2259U53264c+HowmSOXChPlxAISQHCQyE5rQwKAQQQQAABBBBAAIHMBTalPIfrvFrJ2ZE9rtEan97tr9GXx/EQe6sE0v+68v+CdFBhMkdBOjt0G4EgEsgbRH2lqwgggAACCCCAAAIIIJBtAeUtiv32Va2UsS2s37FWZSWfDo2lQHZcFBBAAAEEsirA6qGsilEfAQQQQAABBBBAAIHgE5i/6tfbY65Xvz/88d0PH/6sQbXGKq/atEzfm6baWRZ8w6PHCCCAAAI5E2D1UM78uBoBBBBAAAEEEEAAgWAQiF8113RTD+q6I2bA0vWLpydM2Xtorw5WL1czGEZAHxFAAAEE/CjA6iE/4tI0AggggAACCCCAAAIuEejasue7375hOqOHkd3wnz62Y8q8Y8sUEEAAAQTCU4DVQ+E574waAQQQQAABBBBAILwEFAN6/6FPalaslXrYF5/fPfVBjiCAAAIIhJUA4aGwmm4GiwACCCCAAAIIIBC+ApH1Wn/+5I83XDLQSTD4ivuKFC7qPEIZgdAWWLAqPrQHyOgQyJ4A4aHsuXEVAggggAACCCCAAALBJ5A/X/4Hrn7svQfHN63VXL2/qdvtt/W4J/iGQY8RyK6A+eRn92quQyCUBcg9FMqzy9gQQAABBBBAAAEEEEgtcH79qLGPTjzyz2HWDaXG4QgCCCAQngKsHgrPeWfUCCCAAAIIIIAAAuEuQGwo3D8BjB8BBBBwCBAecmBQRAABBBBAAAEEEAgzgfiVyY97D7NxM1wEEEAAAQTOEiA8dBYHbxBAAAEEEEAAAQTCRyAyMjJ8BstIEUDACCQkJECBAAKpBQgPpTbhCAIIIIAAAggggEC4CCxYzTOMwmWuGScCViA6OtqWKSCAgBEgPMQnAQEEEEAAAQQQQCCsBVrWjwrr8TN4BBBAAAEEIiIID/EpQAABBBBAAAEEEEAAAQQQQAABBMJagPBQWE8/g0cAAQQQQAABBBBAAAEEEEAAAQQID/EZQAABBBBAAAEEEEAAAQQQQAABBMJagPBQWE8/g0cAAQQQQAABBBBAAAEEEEAAAQQID/EZQAABBBBAAAEEEEAAAQQQQAABBMJagPBQWE8/g0cAAQQQQAABBBBAAAEEEEAAAQQID/EZQAABBBBAAAEEEEAAAQQQQAABBMJagPBQWE8/g0cAAQQQQAABBBBAAAEEwkegZf2o8BksI0UgSwKEh7LERWUEEEAAAQQQQAABBBBAAAEEEEAg1AQID4XajDIeBBBAAAEEEEAAAe8Flq5f7H1laiKAAAIIIBCqAoSHQnVmGRcCCCCAAAIIIIBAJgJRUWwzyYSI0wgggAACYSJAeChMJpphIoAAAggggAACCCCAAAIIIIAAAmkLEB5K24WjCCCAAAIIIIAAAggggAACCCCAQJgIEB4Kk4lmmAgggAACCCCAAAJpC0Q1bJf2CY4igAACCCAQNgKEh8JmqhkoAggggAACCCCAAAIIIIAAAgggkJYA4aG0VDiGAAIIIIAAAggggAACCCAQcgKsFgy5KWVAPhMgPOQzShpCAAEEEEAAAQQQQAABBBBAAAEEglGA8FAwzhp9RgABBBBAAAEEEEAAAQQQyKZAp06dsnkllyEQugKEh0J3bhkZAggggAACCCCAQIYC0dHRGZ7nJAIIIIAAAuEiQHgoXGaacSKAAAIIIIAAAggggAACCCCAAAJpChAeSpOFgwgggAACCCCAAALhItCuScdwGSrjRAABBBBAIB0BwkPpwHAYAQQQQAABBBBAAAEEEEAAAQQQCA8BwkPhMc+MEiqinBkAAAxBSURBVAEEEEAAAQQQQAABBBBAAAEEEEhHgPBQOjAcRgABBBBAAAEEEEAAAQQQCC0BNpOG1nwyGl8KEB7ypSZtIYAAAggggAACCCCAAAIIIIAAAkEnQHjo/9u7nxApyzgO4LUieMuTN2kjkKAolo1NlN296a0yD3mwRMgkOmRsRJkHD0JdBDvEskRY5FHDlqA/N10SWZRFUUhBDCSCoHPSwe0nLw5i+8zuzswuO8/vA1HbPjOz7/fzm/cwX573nb4bmQMmQIAAAQIECBAgQIAAAQIECPRSQD3US02vRYAAAQIECBAgQIAAAQIECBDoOwH1UN+NzAETIECAAAECBAj0RmB8fLw3L+RVCBAgQIBAnwuoh/p8gA6fAAECBAgQIECAAAECBAgQINCdgHqoOz/PJkCAAAECBAgQ6HOBC9fP93kCh0+AwFIFnO9LlfK4fALqoXwzl5gAAQIECBAgQOCBwNDQ0IMf/ZcAgfoFZn+7UH9ICQl0JKAe6ojNkwgQIECAAAECBGoR8HGxlknKQYAAAQKdC6iHOrfzTAIECBAgQIAAgQoELt+crSCFCAQIECBAoBsB9VA3ep5LgAABAgQIECDQ3wIjIyP9HcDREyCwHAF18HK0PDaXgHoo17ylJUCAAAECBAgQIECAAAECBAg8IqAeegTE/xIgQIAAAQIECCQSGB0dvXb7SqLAohLILeB8zz1/6dsJqIfa6VgjQIAAAQIECBDIIOC7rjNMWUYCzZnu+wq9EwgsKKAeWpDFLwkQIECAAAECBFIIjI+PR87J6RMp0gpJILdA8zWF7jiW+10gfVFAPVSksUCAAAECBAgQIJBBILYSuN4kw6BlJOC+1N4DBNoIqIfa4FgiQIAAAQIECBCoX6DZSuD6svonLWF6gSiCXVmW/l0AoCigHirSWCBAgAABAgQIEMggEHenjpiuL8swaxkzC0QFHN3Q3Nxcc8pnppCdwIIC6qEFWfySAAECBAgQIEAgi0Bz+6EsaeUkkFUgbjzUbBV0ymd9C8i9iIB6aBEgywQIECBAgAABAtULNLcfcn1Z9YMWMLPANz9/+esvF11clvk9IHt7AfVQex+rBAgQIECAAAEC9Qs0ewpcX1b/pCXMKnDizGf7dh7YvmOrry3L+haQe3EB9dDiRh5BgAABAgQIECBQt0DrXiQ2ENU9aOnSCsTWoQ2DA1NTU0eOHEmLIDiB9gLqofY+VgkQIECAAAECBOoXiHuRxCUnw1tGbCCqf9gS5hOIrUNN6IMHD+ZLLzGBpQqoh5Yq5XEECBAgQIAAAQIVC0xMTFy+ORsBW58kKw4rGoFsAlEMxdahbKnlJbAsAfXQsrg8mAABAgQIECBAoE6B2EC0fuPA1pe2xkUodSaUikBKgSh846S++/u9SO/KspRvAaGXKqAeWqqUxxEgQIAAAQIECNQtcH8D0Y3Zt/YctIGo7kFLl0oguqE4qa/+dcmVZanmLmwHAuqhDtA8hQABAgQIECBAoEKB2EAUqdY98Vh8ktQQVThgkfIJvPHpa/GFZXFSxxeW2TqUb/4SL0/g8fn5+eU9w6MJECBAgAABAgQI1CuwefPmU6dOHT9+/PlNLx7a/VG9QSUjULlAq+SNDUR37typPK14BLoWsHuoa0IvQIAAAQIECBAgUJFA0w2dOfl93Km69fGyonyiEEghECdvnMKHP/wkuqE4qVNkFpJAdwLqoe78PJsAAQIECBAgQKAugbjELK5D2b3/lR/P/+Aqs7pmK00WgaYb+vbj7/Z8sCtuOdRcN5olvJwEOhVQD3Uq53kECBAgQIAAAQKVCsQ9SqIhOnbs2PT09IbBgbh9iW1ElY5arNoELlw/Hyds7Bt65+VDQ28/7ZZDtQ1YnpUUUA+tpK7XJkCAAAECBAgQ6E+B5i62cR+i0dHR7Tvuf9u9hqg/J+moEwnESfru5/uHt4zEP/FDXFPmdtSJxi9q1wLqoa4JvQABAgQIECBAgECNAvHBMi5L2bt3b4SL+9rGNqLYjKAkqnHUMvW9QGvT0BfvnYytQ3FZaHRDrinr+7kKsLoC644ePbq6f9FfI0CAAAECBAgQINAfAmNjY8PDw6dPn75161Yc8dmzZy9cnTlw9M27//5zb/7e5k1P9kcMR0mgXoEohg5/9f6lGxdjx9Cff//x9U9Tu15/dXJycnBwsN7QkhFYEQFfbL8irF6UAAECBAgQIECgJoG4D1HEmZqaiv1EcbnZzMxM/By/2bfzwMgz27Y9O1ZTWFkIrHGBqITiCCenT1y7feW5p16IYih2DK3fOBB3GorT06ahNT4+h7dmBdRDa3Y0DowAAQIECBAgQGBtCZw7d64phqIkah1ZqyeK30RVFP/WFrVw/ECgVwIPV0LNa0YxFPVQ/Dw0NDQxMaEV6hW110kroB5KO3rBCRAgQIAAAQIEOhRoNhPNzs7Ozc3FS8Sn0+aHh1+u2dTw8G86+zm2RXT2xN4+q/kc3tvX9GrxJukvhNins2oH3HrnL/jei5Ou2SsUx6MYWrWh+EN1C6iH6p6vdAQIECBAgAABAisu0OwqWtafiWppWY9faw/+fx221o6wh8fTqxJnwZqjh8e5pl4q6pvOjidKnzZPdO1YGxxLBLoUUA91CejpBAgQIECAAAECBAjUIBA1Xw0xlpzBppslU3kggRQC6qEUYxaSAAECBAgQIECAAAECBAgQIFASGCgt+D0BAgQIECBAgAABAgQIECBAgEAGAfVQhinLSIAAAQIECBAgQIAAAQIECBAoCqiHijQWCBAgQIAAAQIECBAgQIAAAQIZBNRDGaYsIwECBAgQIECAAAECBAgQIECgKKAeKtJYIECAAAECBAgQIECAAAECBAhkEFAPZZiyjAQIECBAgAABAgQIECBAgACBooB6qEhjgQABAgQIECBAgAABAgQIECCQQUA9lGHKMhIgQIAAAQIECBAgQIAAAQIEigLqoSKNBQIECBAgQIAAAQIECBAgQIBABgH1UIYpy0iAAAECBAgQIECAAAECBAgQKAqoh4o0FggQIECAAAECBAgQIECAAAECGQTUQxmmLCMBAgQIECBAgAABAgQIECBAoCigHirSWCBAgAABAgQIECBAgAABAgQIZBBQD2WYsowECBAgQIAAAQIECBAgQIAAgaKAeqhIY4EAAQIECBAgQIAAAQIECBAgkEFAPZRhyjISIECAAAECBAgQIECAAAECBIoC6qEijQUCBAgQIECAAAECBAgQIECAQAYB9VCGKctIgAABAgQIECBAgAABAgQIECgKqIeKNBYIECBAgAABAgQIECBAgAABAhkE1EMZpiwjAQIECBAgQIAAAQIECBAgQKAooB4q0lggQIAAAQIECBAgQIAAAQIECGQQUA9lmLKMBAgQIECAAAECBAgQIECAAIGigHqoSGOBAAECBAgQIECAAAECBAgQIJBBQD2UYcoyEiBAgAABAgQIECBAgAABAgSKAuqhIo0FAgQIECBAgAABAgQIECBAgEAGAfVQhinLSIAAAQIECBAgQIAAAQIECBAoCqiHijQWCBAgQIAAAQIECBAgQIAAAQIZBNRDGaYsIwECBAgQIECAAAECBAgQIECgKKAeKtJYIECAAAECBAgQIECAAAECBAhkEFAPZZiyjAQIECBAgAABAgQIECBAgACBooB6qEhjgQABAgQIECBAgAABAgQIECCQQUA9lGHKMhIgQIAAAQIECBAgQIAAAQIEigLqoSKNBQIECBAgQIAAAQIECBAgQIBABgH1UIYpy0iAAAECBAgQIECAAAECBAgQKAqoh4o0FggQIECAAAECBAgQIECAAAECGQTUQxmmLCMBAgQIECBAgAABAgQIECBAoCigHirSWCBAgAABAgQIECBAgAABAgQIZBBQD2WYsowECBAgQIAAAQIECBAgQIAAgaKAeqhIY4EAAQIECBAgQIAAAQIECBAgkEFAPZRhyjISIECAAAECBAgQIECAAAECBIoC6qEijQUCBAgQIECAAAECBAgQIECAQAYB9VCGKctIgAABAgQIECBAgAABAgQIECgKqIeKNBYIECBAgAABAgQIECBAgAABAhkE1EMZpiwjAQIECBAgQIAAAQIECBAgQKAooB4q0lggQIAAAQIECBAgQIAAAQIECGQQUA9lmLKMBAgQIECAAAECBAgQIECAAIGiwH/qGNDAm7YjvAAAAABJRU5ErkJggg=="}}, "cell_type": "markdown", "id": "b1c91c26-ca4c-42ab-abff-12fdc5af7137", "metadata": {}, "source": ["# llama-agents\n", "`llama-agents` makes it possible to run agents as microservices. That makes it possible to scale services up and down.\n", "\n", "# llama-index\n", "LlamaIndex  is a data framework for your LLM application. It provides tools like: \n", "* Data connectors ingest your existing data from their native source and format.\n", "* Data indexes structure your data in intermediate representations that are easy and performant for LLMs to consume.\n", "* Engines provide natural language access to your data.\n", "* Agents are LLM-powered knowledge workers augmented by tools, from simple helper functions to API integrations and more.\n", "\n", "\n", "\n", "\n", "![image.png](attachment:7bd73318-7929-4675-8998-c2e9ef091906.png)"]}, {"cell_type": "markdown", "id": "d54a180a-0611-447f-a5f1-838e33be4362", "metadata": {}, "source": ["# Mistral AI\n", "\n", "Mistral AI is a research lab building LLMs and Embeddings Models, they recently released new versions of their models, Mistral Nemo and Mistral Large which have shown to be particularly good in RAG and function calling. Because of that, we are going to use them in this notebook "]}, {"cell_type": "markdown", "id": "53a14628-0480-49cf-97f2-752b5ef41116", "metadata": {}, "source": ["----"]}, {"cell_type": "markdown", "id": "2b284d30-d30c-4c06-8ec7-f6b627ea3e0e", "metadata": {}, "source": ["# Install Dependencies"]}, {"cell_type": "code", "execution_count": null, "id": "46743c26-b37b-494b-b3ef-42468062f35d", "metadata": {"scrolled": true}, "outputs": [], "source": ["! pip install llama-agents pymil<PERSON>s openai python-dotenv"]}, {"cell_type": "code", "execution_count": null, "id": "449cd302-5f4c-482d-aa33-19944095e319", "metadata": {"scrolled": true}, "outputs": [], "source": ["! pip install llama-index-vector-stores-milvus llama-index-readers-file llama-index-llms-ollama llama-index-llms-mistralai llama-index-embeddings-mistralai"]}, {"cell_type": "code", "execution_count": 25, "id": "f7992d79-5901-4a2e-82e6-2baac53c9821", "metadata": {}, "outputs": [], "source": ["# NOTE: This is ONLY necessary in jupyter notebook.\n", "# Details: <PERSON><PERSON><PERSON> runs an event-loop behind the scenes.\n", "#          This results in nested event-loops when we start an event-loop to make async queries.\n", "#          This is normally not allowed, we use nest_asyncio to allow it for convenience.\n", "import nest_asyncio\n", "\n", "nest_asyncio.apply()"]}, {"cell_type": "markdown", "id": "39cd28f1-2176-4112-93ab-4881c11af022", "metadata": {}, "source": ["### Get your API Key for Mistral on https://console.mistral.ai/api-keys/"]}, {"cell_type": "code", "execution_count": 2, "id": "e9592e84-2a1c-4cb4-a498-53232b259386", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["\"\"\"\n", "load_dotenv reads key-value pairs from a .env file and can set them as environment variables.\n", "This is useful to avoid leaking your API key for example :D\n", "\"\"\"\n", "\n", "from dotenv import load_dotenv\n", "import os\n", "\n", "load_dotenv()"]}, {"cell_type": "markdown", "id": "41f2c4b3-8055-4597-9dba-9f8f9281f38c", "metadata": {}, "source": ["## Download data"]}, {"cell_type": "code", "execution_count": null, "id": "b789c284-eed3-4cb6-bcbf-637defb28349", "metadata": {"scrolled": true}, "outputs": [], "source": ["!mkdir -p 'data/10k/'\n", "!wget 'https://raw.githubusercontent.com/run-llama/llama_index/main/docs/docs/examples/data/10k/uber_2021.pdf' -O 'data/10k/uber_2021.pdf'\n", "!wget 'https://raw.githubusercontent.com/run-llama/llama_index/main/docs/docs/examples/data/10k/lyft_2021.pdf' -O 'data/10k/lyft_2021.pdf'"]}, {"cell_type": "markdown", "id": "92b8fdb3-63dd-4a21-9d57-66a4ba277b5f", "metadata": {}, "source": ["# Prepare Embedding Model\n", "\n", "We define the Embedding Model that will be used in this notebook. We use `mistral-embed`, it is an Embedding model developed by Mistral, it has been trained with Retrievals in mind, which makes it a very good one for our Agentic RAG system.\n", "\n", "https://docs.mistral.ai/capabilities/embeddings/"]}, {"cell_type": "code", "execution_count": 3, "id": "908113a7-724c-4dd4-a50f-8b21ff237de7", "metadata": {}, "outputs": [], "source": ["from llama_index.core import Settings\n", "from llama_index.embeddings.mistralai import MistralAIEmbedding\n", "\n", "# Define the default Embedding model used in this Notebook.\n", "# We are using Mistral Models, so we are also using Mistral Embeddings\n", "\n", "Settings.embed_model = MistralAIEmbedding(model_name=\"mistral-embed\")"]}, {"cell_type": "markdown", "id": "730ffa30-d135-41ac-bd7b-92f8300dc592", "metadata": {}, "source": ["# Define the LLM Model \n", "\n", "Llama Index uses LLMs to respond to prompts and queries, and is responsible for writing natural language responses.\n", "We define Mistral Nemo as the default one. Nemo offers a large context window of up to 128k tokens. Its reasoning, world knowledge, and coding accuracy are state-of-the-art in its size category."]}, {"cell_type": "code", "execution_count": 4, "id": "4808bc3e-ca49-4857-aecc-705fdc981006", "metadata": {}, "outputs": [], "source": ["from llama_index.llms.ollama import Ollama\n", "\n", "Settings.llm = Ollama(\"mistral-nemo\")"]}, {"cell_type": "markdown", "id": "4a531b0f-4103-4a92-8739-666f6f7f5f4f", "metadata": {}, "source": ["# Instanciate Milvus and Load Data\n", "\n", "[Milvus](https://milvus.io/) is a popular open-source vector database that powers AI applications with highly performant and scalable vector similarity search.\n", "\n", "- Setting the uri as a local file, e.g.`./milvus.db`, is the most convenient method, as it automatically utilizes [Milvus Lite](https://milvus.io/docs/milvus_lite.md) to store all data in this file.\n", "- If you have large scale of data, say more than a million vectors, you can set up a more performant Milvus server on [Docker or Kubernetes](https://milvus.io/docs/quickstart.md). In this setup, please use the server uri, e.g.`http://localhost:19530`, as your uri.\n", "- If you want to use [Zilliz Cloud](https://zilliz.com/cloud), the fully managed cloud service for Milvus, adjust the uri and token, which correspond to the [Public Endpoint and API key](https://docs.zilliz.com/docs/on-zilliz-cloud-console#cluster-details) in Zilliz Cloud."]}, {"cell_type": "code", "execution_count": 5, "id": "34ed43fb-906a-4316-ab7e-b76977e6a9ba", "metadata": {}, "outputs": [], "source": ["from llama_index.vector_stores.milvus import MilvusVectorStore\n", "from llama_index.core import (\n", "    SimpleDirectoryReader,\n", "    VectorStoreIndex,\n", "    StorageContext,\n", "    load_index_from_storage,\n", ")\n", "from llama_index.core.tools import QueryEngineTool, ToolMetadata\n", "\n", "input_files = [\"./data/10k/lyft_2021.pdf\", \"./data/10k/uber_2021.pdf\"]\n", "\n", "# Create a single Milvus vector store\n", "vector_store = MilvusVectorStore(\n", "    uri=\"./milvus_demo.db\", dim=1024, overwrite=False, collection_name=\"companies_docs\"\n", ")\n", "\n", "# Create a storage context with the Milvus vector store\n", "storage_context = StorageContext.from_defaults(vector_store=vector_store)\n", "\n", "# Load data\n", "docs = SimpleDirectoryReader(input_files=input_files).load_data()\n", "\n", "# Build index\n", "index = VectorStoreIndex.from_documents(docs, storage_context=storage_context)\n", "\n", "# Define the query engine\n", "company_engine = index.as_query_engine(similarity_top_k=3)"]}, {"cell_type": "markdown", "id": "a22eaa7d-f60a-4092-bc9a-a6e742d2ef89", "metadata": {}, "source": ["# Define Tools \n", "\n", "One of the key steps in building an effective agent is defining the tools it can use to perform its tasks. These tools are essentially functions or services that the agent can call upon to retrieve information or perform actions.\n", "\n", "Below, we'll define two tools that our agent can use to query financial information about Lyft and Uber from the year 2021. These tools will be integrated into our agent, allowing it to respond to natural language queries with precise and relevant information.\n", "\n", "If you look at the graph we have at the top, this is what an \"Agent Service\" is. "]}, {"cell_type": "code", "execution_count": 6, "id": "bde8f9fb-b762-431e-a153-f14db8501a57", "metadata": {}, "outputs": [], "source": ["# Define the different tools that can be used by our Agent.\n", "query_engine_tools = [\n", "    QueryEngineTool(\n", "        query_engine=company_engine,\n", "        metadata=ToolMetadata(\n", "            name=\"lyft_10k\",\n", "            description=(\n", "                \"Provides information about Lyft financials for year 2021. \"\n", "                \"Use a detailed plain text question as input to the tool.\"\n", "                \"Do not attempt to interpret or summarize the data.\"\n", "            ),\n", "        ),\n", "    ),\n", "    QueryEngineTool(\n", "        query_engine=company_engine,\n", "        metadata=ToolMetadata(\n", "            name=\"uber_10k\",\n", "            description=(\n", "                \"Provides information about Uber financials for year 2021. \"\n", "                \"Use a detailed plain text question as input to the tool.\"\n", "                \"Do not attempt to interpret or summarize the data.\"\n", "            ),\n", "        ),\n", "    ),\n", "]"]}, {"cell_type": "code", "execution_count": 7, "id": "f73d84fd-4b56-4770-b3c8-989b0a4b2911", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Response without metadata filtering:\n", "The revenue for Lyft in 2021 was $3.84 billion.\n", "\n", "Uber's total revenue for the year ended December 31, 2021 was $17,455 million.\n"]}], "source": ["from llama_index.llms.ollama import Ollama\n", "from llama_index.llms.mistralai import MistralAI\n", "\n", "# Set up the agent\n", "llm = Ollama(model=\"mistral-nemo\")\n", "\n", "response = llm.predict_and_call(\n", "    query_engine_tools,\n", "    user_msg=\"Could you please provide a comparison between Lyft and Uber's total revenues in 2021?\",\n", "    allow_parallel_tool_calls=True,\n", ")\n", "\n", "# Example usage without metadata filtering\n", "print(\"Response without metadata filtering:\")\n", "print(response)"]}, {"cell_type": "markdown", "id": "6b99771c-b5a6-4a25-939d-2a6dc7e63e4e", "metadata": {}, "source": ["# Metadata Filtering\n", "\n", "**Milvus** supports [Metadata filtering](https://zilliz.com/blog/json-metadata-filtering-in-milvus), a technique that allows you to refine and narrow down the search results based on specific attributes or tags associated with your data. This is particularly useful in scenarios where you have a lot of data and need to retrieve only the relevant subset of data that matches certain criteria.\n", "\n", "## Use Cases for Metadata Filtering\n", "* **Precision in Search Results**: By applying metadata filters, you can ensure that the search results are highly relevant to the user's query. For example, if you have a collection of financial documents, you can filter them based on the company name, year, or any other relevant metadata.\n", "\n", "* **Efficiency**: Metadata filtering helps in reducing the amount of data that needs to be processed, making the search operations more efficient. This is especially beneficial when dealing with large datasets.\n", "\n", "* **Customization**: Different users or applications may have different requirements. Metadata filtering allows you to customize the search results to meet specific needs, such as retrieving documents from a particular year or company.\n", "\n", "### Example usage\n", "In the code block below, metadata filtering is used to create a filtered query engine that retrieves documents based on a specific metadata key-value pair: `file_name`: `lyft_2021.pdf`\n", "\n", "\n", "The `QueryEngineTool` defined below is more generic than the one defined above, in the one above, we had a tool per company (Uber and Lyft), in this one, it is more generic. We only know we have financial documents about companies but that's it. \n", "By adding a Metadata Filtering, we can then filter on only getting data from a specific document. "]}, {"cell_type": "code", "execution_count": 16, "id": "4e60dce7-66f7-425a-871b-c05aba76f426", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["filters: filters=[MetadataFilter(key='file_name', value='lyft_2021.pdf', operator=<FilterOperator.EQ: '=='>)] condition=<FilterCondition.AND: 'and'>\n"]}], "source": ["from llama_index.core.vector_stores import ExactMatchFilter, MetadataFilters\n", "\n", "# Example usage with metadata filtering\n", "filters = MetadataFilters(\n", "    filters=[ExactMatchFilter(key=\"file_name\", value=\"lyft_2021.pdf\")]\n", ")\n", "\n", "print(f\"filters: {filters}\")\n", "filtered_query_engine = index.as_query_engine(filters=filters)\n", "\n", "# Define query engine tools with the filtered query engine\n", "query_engine_tools = [\n", "    QueryEngineTool(\n", "        query_engine=filtered_query_engine,\n", "        metadata=ToolMetadata(\n", "            name=\"company_docs\",\n", "            description=(\n", "                \"Provides information about various companies' financials for year 2021. \"\n", "                \"Use a detailed plain text question as input to the tool.\"\n", "                \"Use this tool to retrieve specific data points about a company. \"\n", "                \"Do not attempt to interpret or summarize the data.\"\n", "            ),\n", "        ),\n", "    ),\n", "]"]}, {"cell_type": "markdown", "id": "*************-48ba-bf6d-17c2e090d62f", "metadata": {}, "source": ["# Function Calling\n", "Mistral Nemo and Large support native function calling. There's a seamless integration with LlamaIndex tools, through the `predict_and_call` function on the llm. \n", "This allows the user to attach any tools and let the LLM decide which tools to call (if any).\n", "\n", "You can learn more about Agents on the llama-index website: https://docs.llamaindex.ai/en/latest/module_guides/deploying/agents/"]}, {"cell_type": "code", "execution_count": 17, "id": "781065ff-8d14-424a-830c-4b0277ad190c", "metadata": {}, "outputs": [], "source": ["# Set up the LLM we will use for Function Calling\n", "\n", "llm = Ollama(model=\"mistral-nemo\")"]}, {"cell_type": "markdown", "id": "1b151f43-5f64-4b80-b028-e1ccad85abd5", "metadata": {}, "source": ["# Interact with the Agent\n", "\n", "Now we can the Metadata Filtering in action:\n", "1. In the first one, the Agent shouldn't be able to find anything to the user's query as it's about Uber and we filter on Documents only about Lyft.\n", "2. In the second one, the Agent should be able to find information about Lyft as we will only search through documents that are about Lyft. "]}, {"cell_type": "code", "execution_count": 18, "id": "4df10783-8919-4111-b8b0-719e3904a8af", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I'm unable to provide information about <PERSON><PERSON>'s employee count as it's outside the given Lyft context.\n"]}], "source": ["response = llm.predict_and_call(\n", "    query_engine_tools,\n", "    user_msg=\"How many employees does Uber have?\",\n", "    allow_parallel_tool_calls=True,\n", ")\n", "print(response)"]}, {"cell_type": "code", "execution_count": 19, "id": "293a639b-88bf-46ba-b4ce-6cdc8b14907c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Investing in Lyft carries significant risks. These include general economic factors like impacts from pandemics or crises, operational factors such as competition, pricing changes, and driver/ride growth unpredictability, insurance coverage issues, autonomous vehicle technology uncertainties, reputational concerns, potential security breaches, reliance on third-party services, and challenges in expanding platform offerings. Lyft's business operations are subject to numerous other risks not explicitly mentioned here, which could also harm its financial condition and prospects.\n"]}], "source": ["response = llm.predict_and_call(\n", "    query_engine_tools,\n", "    user_msg=\"What are the risk factors for Lyft?\",\n", "    allow_parallel_tool_calls=True,\n", ")\n", "\n", "print(response)"]}, {"cell_type": "markdown", "id": "4c735aed-c3fd-49a7-9fa0-c1bc2f84737a", "metadata": {}, "source": ["----"]}, {"cell_type": "markdown", "id": "5522e9e5-8432-47d3-aaa6-b6f9f31ab324", "metadata": {}, "source": ["# Example of Confusion Without Metadata Filtering\n", "```\n", "> Question: What are the risk factors for <PERSON><PERSON>?\n", "\n", "> Response without metadata filtering:\n", "Based on the provided context, which pertains to Lyft's Risk Factors section in their Annual Report, some of the potential risk factors applicable to a company like Uber might include:\n", "\n", "- General economic factors such as the impact of global pandemics or other crises on ride-sharing demand.\n", "- Operational factors like competition in ride-hailing services, unpredictability in results of operations, and uncertainty about market growth for ridesharing and related services.\n", "- Risks related to attracting and retaining qualified drivers and riders.\n", "```\n", "\n", "In this example, the system incorrectly provides information about Lyft instead of Uber, leading to a misleading response. It starts by saying that it doens't have the information but then just goes on and on.\n", "\n", "# Using an Agent to Extract Metadata Filters\n", "\n", "To address this issue, we can use an agent to automatically extract metadata filters from the user's question and apply them during the question answering process. This ensures that the system retrieves the correct and relevant information.\n", "\n", "## Code Example\n", "Below is a code example that demonstrates how to create a filtered query engine using an agent to extract metadata filters from the user's question:\n", "\n", "### Explanation\n", "* **Prompt Template**: The PromptTemplate class is used to define a template for extracting metadata filters from the user's question. The template instructs the language model to consider company names, years, and other relevant attributes.\n", "\n", "* **LLM**: Mistral Nemo is used to generate the metadata filters based on the user's question. The model is prompted with the question and the template to extract the relevant filters.\n", "\n", "* **Metadata Filters**: The response from the LLM is parsed to create a `MetadataFilters` object. If no specific filters are mentioned, an empty `MetadataFilters` object is returned.\n", "\n", "* **Filtered Query Engine**: The `index.as_query_engine(filters=metadata_filters)` method creates a query engine that applies the extracted metadata filters to the index. This ensures that only the documents matching the filter criteria are retrieved."]}, {"cell_type": "code", "execution_count": 12, "id": "375ba5f6-6745-4133-b726-875393a1f1e9", "metadata": {}, "outputs": [], "source": ["from llama_index.core.prompts.base import PromptTemplate\n", "\n", "\n", "# Function to create a filtered query engine\n", "def create_query_engine(question):\n", "    # Extract metadata filters from question using a language model\n", "    prompt_template = PromptTemplate(\n", "        \"Given the following question, extract relevant metadata filters.\\n\"\n", "        \"Consider company names, years, and any other relevant attributes.\\n\"\n", "        \"Don't write any other text, just the MetadataFilters object\"\n", "        \"Format it by creating a MetadataFilters like shown in the following\\n\"\n", "        \"MetadataFilters(filters=[ExactMatchFilter(key='file_name', value='lyft_2021.pdf')])\\n\"\n", "        \"If no specific filters are mentioned, returns an empty MetadataFilters()\\n\"\n", "        \"Question: {question}\\n\"\n", "        \"Metadata Filters:\\n\"\n", "    )\n", "\n", "    prompt = prompt_template.format(question=question)\n", "    llm = Ollama(model=\"mistral-nemo\")\n", "    response = llm.complete(prompt)\n", "\n", "    metadata_filters_str = response.text.strip()\n", "    if metadata_filters_str:\n", "        metadata_filters = eval(metadata_filters_str)\n", "        print(f\"eval: {metadata_filters}\")\n", "        return index.as_query_engine(filters=metadata_filters)\n", "    return index.as_query_engine()"]}, {"cell_type": "code", "execution_count": 13, "id": "f0f68f41-5f90-4a52-a7d1-cde34d1264f7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["eval: filters=[MetadataFilter(key='file_name', value='uber_2021.pdf', operator=<FilterOperator.EQ: '=='>)] condition=<FilterCondition.AND: 'and'>\n"]}], "source": ["response = create_query_engine(\n", "    \"What is Uber revenue? This should be in the file_name: uber_2021.pdf\"\n", ")"]}, {"cell_type": "code", "execution_count": 14, "id": "44b55363-82d8-4e36-b258-519a26e42918", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["eval: filters=[MetadataFilter(key='file_name', value='uber_2021.pdf', operator=<FilterOperator.EQ: '=='>)] condition=<FilterCondition.AND: 'and'>\n", "Response with metadata filtering:\n", "Uber's total revenue for the year ended December 31, 2021, is $17.455 billion.\n"]}], "source": ["## Example usage with metadata filtering\n", "question = \"What is Uber revenue? This should be in the file_name: uber_2021.pdf\"\n", "filtered_query_engine = create_query_engine(question)\n", "\n", "# Define query engine tools with the filtered query engine\n", "query_engine_tools = [\n", "    QueryEngineTool(\n", "        query_engine=filtered_query_engine,\n", "        metadata=ToolMetadata(\n", "            name=\"company_docs_filtering\",\n", "            description=(\n", "                \"Provides information about various companies' financials for year 2021. \"\n", "                \"Use a detailed plain text question as input to the tool.\"\n", "            ),\n", "        ),\n", "    ),\n", "]\n", "# Set up the agent with the updated query engine tools\n", "response = llm.predict_and_call(\n", "    query_engine_tools,\n", "    user_msg=question,\n", "    allow_parallel_tool_calls=True,\n", ")\n", "\n", "print(\"Response with metadata filtering:\")\n", "print(response)"]}, {"cell_type": "markdown", "id": "1659042e-4644-408f-ae23-62ba0599bd50", "metadata": {}, "source": ["# Orchestrating the different services with Mistral Large\n", "\n", "Mistral Large is the flagship model of Mistral with very good reasoning, knowledge, and coding capabilities. It's ideal for complex tasks that require large reasoning capabilities or are highly specialized. It has advanced function calling capabilities, which is exactly what we need to orchestrate our different agents.\n", "\n", "## Why do we need a smarter Model? \n", "The question being answered below is particularly challenging because it requires the orchestration of multiple services and agents to provide a coherent and accurate response. This involves coordinating various tools and agents to retrieve and process information from different sources, such as financial data from different companies.\n", "\n", "### What's so difficult about that? \n", "* Complexity: The question involves multiple agents and services, each with its own functionality and data sources. Coordinating these agents to work together seamlessly is a complex task.\n", "\n", "* Data Integration: The question requires integrating data from different sources, which can be challenging due to variations in data formats, structures, and metadata.\n", "\n", "* Contextual Understanding: The question may require understanding the context and relationships between different pieces of information, which is a cognitively demanding task.\n", "\n", "## Why would <PERSON><PERSON><PERSON> Large help in this case?\n", "Mistral Large is well-suited for this task due to its advanced reasoning and function calling capabilities. Here’s how it helps:\n", "\n", "\n", "* Advanced Reasoning: Mistral Large can handle complex reasoning tasks, making it ideal for orchestrating multiple agents and services. It can understand the relationships between different pieces of information and make informed decisions.\n", "\n", "* Function Calling Capabilities: Mistral Large has advanced function calling capabilities, which are essential for coordinating the actions of different agents. This allows for seamless integration and orchestration of various services.\n", "\n", "* Specialized Knowledge: Mistral Large is designed for highly specialized tasks, making it well-suited for handling complex queries that require deep domain knowledge.\n", "\n", "\n", "For all those reasons, I decided that using Mistral Large instead of Mistral Nemo was better suited here. "]}, {"cell_type": "code", "execution_count": 20, "id": "3be692f0-dab4-4627-bc98-c3ca6d5c82a8", "metadata": {}, "outputs": [], "source": ["from llama_agents import (\n", "    AgentService,\n", "    ToolService,\n", "    LocalLauncher,\n", "    MetaServiceTool,\n", "    ControlPlaneServer,\n", "    SimpleMessageQueue,\n", "    AgentOrchestrator,\n", ")\n", "\n", "from llama_index.core.agent import FunctionCallingAgentWorker\n", "from llama_index.llms.mistralai import MistralAI\n", "\n", "# create our multi-agent framework components\n", "message_queue = SimpleMessageQueue()\n", "control_plane = ControlPlaneServer(\n", "    message_queue=message_queue,\n", "    orchestrator=AgentOrchestrator(llm=MistralAI(\"mistral-large-latest\")),\n", ")\n", "\n", "# define Tool Service\n", "tool_service = ToolService(\n", "    message_queue=message_queue,\n", "    tools=query_engine_tools,\n", "    running=True,\n", "    step_interval=0.5,\n", ")\n", "\n", "# define meta-tools here\n", "meta_tools = [\n", "    await MetaServiceTool.from_tool_service(\n", "        t.metadata.name,\n", "        message_queue=message_queue,\n", "        tool_service=tool_service,\n", "    )\n", "    for t in query_engine_tools\n", "]\n", "\n", "# define Agent and agent service\n", "worker1 = FunctionCallingAgentWorker.from_tools(\n", "    meta_tools, llm=MistralAI(\"mistral-large-latest\")\n", ")\n", "\n", "agent1 = worker1.as_agent()\n", "agent_server_1 = AgentService(\n", "    agent=agent1,\n", "    message_queue=message_queue,\n", "    description=\"Used to answer questions over differnet companies for their Financial results\",\n", "    service_name=\"Companies_analyst_agent\",\n", ")"]}, {"cell_type": "code", "execution_count": 21, "id": "3c636aac-d2dc-406d-969b-acd26ad3874e", "metadata": {}, "outputs": [], "source": ["import logging\n", "\n", "# change logging level to enable or disable more verbose logging\n", "logging.getLogger(\"llama_agents\").setLevel(logging.INFO)"]}, {"cell_type": "code", "execution_count": 22, "id": "1f46a40d-f084-4a40-a64a-8592e30a176d", "metadata": {}, "outputs": [], "source": ["## Define Launcher\n", "launcher = LocalLauncher(\n", "    [agent_server_1, tool_service],\n", "    control_plane,\n", "    message_queue,\n", ")"]}, {"cell_type": "code", "execution_count": 23, "id": "56271d58-4657-4b6b-9e50-3ca6b25f4502", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:llama_agents.message_queues.simple - Consumer AgentService-27cde4ed-5163-4005-90fc-13c158eda7e3: Companies_analyst_agent has been registered.\n", "INFO:llama_agents.message_queues.simple - Consumer ToolService-b73c500a-5fbe-4f57-95c7-db74e173bd1b: default_tool_service has been registered.\n", "INFO:llama_agents.message_queues.simple - Consumer 62465ab8-32ff-436e-95fa-74e828745150: human has been registered.\n", "INFO:llama_agents.message_queues.simple - Consumer ControlPlaneServer-f4c27d43-5474-43ca-93ca-a9aeed4534d7: control_plane has been registered.\n", "INFO:llama_agents.services.agent - Companies_analyst_agent launch_local\n", "INFO:llama_agents.message_queues.base - Publishing message to 'control_plane' with action 'ActionTypes.NEW_TASK'\n", "INFO:llama_agents.message_queues.simple - Launching message queue locally\n", "INFO:llama_agents.services.agent - Processing initiated.\n", "INFO:llama_agents.services.tool - Processing initiated.\n", "INFO:llama_agents.message_queues.base - Publishing message to 'Companies_analyst_agent' with action 'ActionTypes.NEW_TASK'\n", "INFO:llama_agents.message_queues.simple - Successfully published message 'control_plane' to consumer.\n", "INFO:llama_agents.services.agent - Created new task: 0720da2f-1751-4766-a814-ba720bc8a467\n", "INFO:llama_agents.message_queues.simple - Successfully published message 'Companies_analyst_agent' to consumer.\n", "INFO:llama_agents.message_queues.simple - Consumer MetaServiceTool-5671c175-7b03-4bc8-b60d-bd7101d0fc41: MetaServiceTool-5671c175-7b03-4bc8-b60d-bd7101d0fc41 has been registered.\n", "INFO:llama_agents.message_queues.base - Publishing message to 'default_tool_service' with action 'ActionTypes.NEW_TOOL_CALL'\n", "INFO:llama_agents.message_queues.simple - Successfully published message 'default_tool_service' to consumer.\n", "INFO:llama_agents.services.tool - Processing tool call id f4c270a4-bc47-4bbf-92fe-e2cc80757943 with company_docs\n", "INFO:llama_agents.message_queues.base - Publishing message to 'control_plane' with action 'ActionTypes.COMPLETED_TASK'\n", "INFO:llama_agents.message_queues.base - Publishing message to 'MetaServiceTool-5671c175-7b03-4bc8-b60d-bd7101d0fc41' with action 'ActionTypes.COMPLETED_TOOL_CALL'\n", "INFO:llama_agents.message_queues.base - Publishing message to 'Companies_analyst_agent' with action 'ActionTypes.NEW_TASK'\n", "INFO:llama_agents.message_queues.simple - Successfully published message 'control_plane' to consumer.\n", "INFO:llama_agents.message_queues.simple - Successfully published message 'MetaServiceTool-5671c175-7b03-4bc8-b60d-bd7101d0fc41' to consumer.\n", "INFO:llama_agents.services.agent - Created new task: 0720da2f-1751-4766-a814-ba720bc8a467\n", "INFO:llama_agents.message_queues.simple - Successfully published message 'Companies_analyst_agent' to consumer.\n", "INFO:llama_agents.message_queues.base - Publishing message to 'default_tool_service' with action 'ActionTypes.NEW_TOOL_CALL'\n", "INFO:llama_agents.message_queues.simple - Successfully published message 'default_tool_service' to consumer.\n", "INFO:llama_agents.services.tool - Processing tool call id f888f9a8-e716-4505-bfe2-577452e9b6e6 with company_docs\n", "INFO:llama_agents.message_queues.base - Publishing message to 'MetaServiceTool-5671c175-7b03-4bc8-b60d-bd7101d0fc41' with action 'ActionTypes.COMPLETED_TOOL_CALL'\n", "INFO:llama_agents.message_queues.simple - Successfully published message 'MetaServiceTool-5671c175-7b03-4bc8-b60d-bd7101d0fc41' to consumer.\n", "INFO:llama_agents.message_queues.base - Publishing message to 'control_plane' with action 'ActionTypes.COMPLETED_TASK'\n", "INFO:llama_agents.message_queues.base - Publishing message to 'human' with action 'ActionTypes.COMPLETED_TASK'\n", "INFO:llama_agents.message_queues.simple - Successfully published message 'control_plane' to consumer.\n", "INFO:llama_agents.message_queues.simple - Successfully published message 'human' to consumer.\n"]}], "source": ["query_str = \"What are the risk factors for Uber?\"\n", "result = launcher.launch_single(query_str)"]}, {"cell_type": "code", "execution_count": 24, "id": "1f81794e-e413-40e2-a8ad-299c55fd1cac", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{\"name\": \"finalize\", \"arguments\": {\"input\": \"Uber faces several risk factors, including general economic impacts such as pandemics or downturns, operational challenges like competition, market growth uncertainty, attracting and retaining drivers and riders, insurance adequacy, autonomous vehicle technology development, maintaining its reputation and brand, and managing growth. Additionally, reliance on third-party providers for various services can introduce further risks to its operations.\"}}]\n"]}], "source": ["print(result)"]}, {"cell_type": "markdown", "id": "58644911-e164-418b-a2cc-42a1ba51d182", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# Conclusion\n", "\n", "In this notebook, you have seen how you can use llama-agents to perform different actions by calling appropriate tools. By using Mistral Large in combination with Mistral Nemo, we demonstrated how to effectively orchestrate intelligent, resource-efficient systems by leveraging the strengths of different LLMs. We saw that the Agent could pick the collection containing the data requested by the user. \n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}