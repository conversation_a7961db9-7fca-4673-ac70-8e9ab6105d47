{"cells": [{"cell_type": "markdown", "metadata": {"id": "SQMBlxsBv1cH"}, "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/integration/rag_with_milvus_and_camel.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/integration/rag_with_milvus_and_camel.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>\n", "</a>"]}, {"cell_type": "markdown", "metadata": {"id": "AwnfLQvMvtXo"}, "source": ["# Retrieval-Augmented Generation (RAG) with Milvus and CAMEL\n", "\n", "This guide demonstrates how to build a Retrieval-Augmented Generation (RAG) system using CAMEL and Milvus.\n", "\n", "The RAG system combines a retrieval system with a generative model to generate new text based on a given prompt. The system first retrieves relevant documents from a corpus using Milvus, and then uses a generative model to generate new text based on the retrieved documents.\n", "\n", "[CAMEL](https://www.camel-ai.org/) is a multi-agent framework. [Milvus](https://milvus.io/) is the world's most advanced open-source vector database, built to power embedding similarity search and AI applications.\n", "\n", "In this notebook, we show the usage of CAMEL Retrieve Module in both customized way and auto way. We will also show how to combine `AutoRetriever` with `ChatAgent`, and further combine `AutoRetriever` with `RolePlaying` by using `Function Calling`.\n", "\n", "4 main parts included:\n", "- Customized RAG\n", "- Auto RAG\n", "- Single Agent with Auto RAG\n", "- Role-playing with Auto RAG"]}, {"cell_type": "markdown", "metadata": {"id": "LUpFBEB9vrIz"}, "source": ["## Load Data\n", "Let's first load the CAMEL paper from https://arxiv.org/pdf/2303.17760.pdf. This will be our local example data.\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "HTKnWg9Xv_y4", "outputId": "3929f357-4edf-4f69-ef83-c22e8a9b447d", "vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["!pip install -U \"camel-ai[all]\" pymilvus"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> If you are using Google Colab, to enable dependencies just installed, you may need to **restart the runtime** (click on the \"Runtime\" menu at the top of the screen, and select \"Restart session\" from the dropdown menu)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "HCqghaGiwkdv"}, "outputs": [], "source": ["import os\n", "import requests\n", "\n", "os.makedirs(\"local_data\", exist_ok=True)\n", "\n", "url = \"https://arxiv.org/pdf/2303.17760.pdf\"\n", "response = requests.get(url)\n", "with open(\"local_data/camel paper.pdf\", \"wb\") as file:\n", "    file.write(response.content)"]}, {"cell_type": "markdown", "metadata": {"id": "HCqghaGiwkdv"}, "source": ["## 1. Customized RAG\n", "In this section we will set our customized RAG pipeline, we will take `VectorRetriever` as an example. We will set `OpenAIEmbedding` as the embeddding model and `MilvusStorage` as the storage for it.\n", "\n", "To set OpenAI embedding, we need to set the `OPENAI_API_KEY` in below."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "RxG99FkYwm_p"}, "outputs": [], "source": ["os.environ[\"OPENAI_API_KEY\"] = \"Your Key\""]}, {"cell_type": "markdown", "metadata": {"id": "NACsmaa0wple"}, "source": ["Import and set the embedding instance:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "NCT5ygT-wrku"}, "outputs": [], "source": ["from camel.embeddings import OpenAIEmbedding\n", "\n", "embedding_instance = OpenAIEmbedding()"]}, {"cell_type": "markdown", "metadata": {"id": "q4nVmsm0w5lH"}, "source": ["Import and set the vector storage instance:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "IQ30ih1_w6p3"}, "outputs": [], "source": ["from camel.storages import MilvusStorage\n", "\n", "storage_instance = MilvusStorage(\n", "    vector_dim=embedding_instance.get_output_dim(),\n", "    url_and_api_key=(\n", "        \"./milvus_demo.db\",  # Your Milvus connection URI\n", "        \"\",  # Your Milvus token\n", "    ),\n", "    collection_name=\"camel_paper\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> For the `url_and_api_key`:\n", "> - Using a local file, e.g.`./milvus.db`, as the Milvus connection URI is the most convenient method, as it automatically utilizes [Milvus Lite](https://milvus.io/docs/milvus_lite.md) to store all data in this file.\n", "> - If you have large scale of data, you can set up a more performant Milvus server on [docker or kubernetes](https://milvus.io/docs/quickstart.md). In this setup, please use the server uri, e.g.`http://localhost:19530`, as your url.\n", "> - If you want to use [Zilliz Cloud](https://zilliz.com/cloud), the fully managed cloud service for Milvus, adjust the connection uri and token, which correspond to the [Public Endpoint and Api key](https://docs.zilliz.com/docs/on-zilliz-cloud-console#free-cluster-details) in Zilliz Cloud."]}, {"cell_type": "markdown", "metadata": {"id": "W9KccfRDw9F4"}, "source": ["Import and set the retriever instance:\n", "\n", "By default, the `similarity_threshold` is set to 0.75. You can change it."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "d8S6VjAQHnai"}, "outputs": [], "source": ["from camel.retrievers import VectorRetriever\n", "\n", "vector_retriever = VectorRetriever(\n", "    embedding_model=embedding_instance, storage=storage_instance\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "PdezllM6Hp83"}, "source": ["We use integrated `Unstructured Module` to split the content into small chunks, the content will be splited automacitlly with its `chunk_by_title` function, the max character for each chunk is 500 characters, which is a suitable length for `OpenAIEmbedding`. All the text in the chunks will be embed and stored to the vector storage instance, it will take some time, please wait."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "QTFvD3_SHv3m", "outputId": "3293ed3b-b871-4f63-c44c-5945dc4e9cdd"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["[nltk_data] Downloading package punkt to /root/nltk_data...\n", "[nltk_data]   Unzipping tokenizers/punkt.zip.\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /root/nltk_data...\n", "[nltk_data]   Unzipping taggers/averaged_perceptron_tagger.zip.\n"]}], "source": ["vector_retriever.process(content_input_path=\"local_data/camel paper.pdf\")"]}, {"cell_type": "markdown", "metadata": {"id": "0nhIo1wUHyvl"}, "source": ["\n", "Now we can retrieve information from the vector storage by giving a query. By default it will give you back the text content from top 1 chunk with highest Cosine similarity score, and the similarity score should be higher than 0.75 to ensure the retrieved content is relevant to the query. You can also change the `top_k` value.\n", "\n", "The returned string list includes:\n", "- similarity score\n", "- content path\n", "- metadata\n", "- text"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ionD9pnqH13Z", "outputId": "4263bec8-889d-4332-b3ef-e42e841ba19e"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[{'similarity score': '0.8321675658226013', 'content path': 'local_data/camel paper.pdf', 'metadata': {'last_modified': '2024-04-19T14:40:00', 'filetype': 'application/pdf', 'page_number': 45}, 'text': 'CAMEL Data and Code License The intended purpose and licensing of CAMEL is solely for research use. The source code is licensed under Apache 2.0. The datasets are licensed under CC BY NC 4.0, which permits only non-commercial usage. It is advised that any models trained using the dataset should not be utilized for anything other than research purposes.\\n\\n45'}]\n"]}], "source": ["retrieved_info = vector_retriever.query(query=\"What is CAMEL?\", top_k=1)\n", "print(retrieved_info)"]}, {"cell_type": "markdown", "metadata": {"id": "48G3USMzH6Hd"}, "source": ["Let's try an irrelevant query:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "UNh3tjCOH7d0", "outputId": "853450d2-457e-4c36-b2a9-fce5d026138b"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[{'text': 'No suitable information retrieved from local_data/camel paper.pdf                 with similarity_threshold = 0.75.'}]\n"]}], "source": ["retrieved_info_irrelevant = vector_retriever.query(\n", "    query=\"Compared with dumpling and rice, which should I take for dinner?\", top_k=1\n", ")\n", "\n", "print(retrieved_info_irrelevant)"]}, {"cell_type": "markdown", "metadata": {"id": "mHdEsIzSH_AD"}, "source": ["## 2. Auto RAG\n", "In this section we will run the `AutoRetriever` with default settings. It uses `OpenAIEmbedding` as default embedding model and `Milvus` as default vector storage.\n", "\n", "What you need to do is:\n", "- Set content input paths, which can be local paths or remote urls\n", "- Set remote url and api key for Milvus\n", "- Give a query\n", "\n", "The Auto RAG pipeline would create collections for given content input paths, the collection name will be set automaticlly based on the content input path name, if the collection exists, it will do the retrieve directly."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "r2vvUDmJICm0", "outputId": "2a2d35d5-b9e5-4c9a-d68f-36fb818db59a"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Original Query:\n", "{What is CAMEL-AI}\n", "Retrieved Context:\n", "{'similarity score': '0.8252888321876526', 'content path': 'local_data/camel paper.pdf', 'metadata': {'last_modified': '2024-04-19T14:40:00', 'filetype': 'application/pdf', 'page_number': 7}, 'text': ' Section 3.2, to simulate assistant-user cooperation. For our analysis, we set our attention on AI Society setting. We also gathered conversational data, named CAMEL AI Society and CAMEL Code datasets and problem-solution pairs data named CAMEL Math and CAMEL Science and analyzed and evaluated their quality. Moreover, we will discuss potential extensions of our framework and highlight both the risks and opportunities that future AI society might present.'}\n", "{'similarity score': '0.8378663659095764', 'content path': 'https://www.camel-ai.org/', 'metadata': {'filetype': 'text/html', 'languages': ['eng'], 'page_number': 1, 'url': 'https://www.camel-ai.org/', 'link_urls': ['#h.3f4tphhd9pn8', 'https://join.slack.com/t/camel-ai/shared_invite/zt-2g7xc41gy-_7rcrNNAArIP6sLQqldkqQ', 'https://discord.gg/CNcNpquyDc'], 'link_texts': [None, None, None], 'emphasized_text_contents': ['Mission', 'CAMEL-AI.org', 'is an open-source community dedicated to the study of autonomous and communicative agents. We believe that studying these agents on a large scale offers valuable insights into their behaviors, capabilities, and potential risks. To facilitate research in this field, we provide, implement, and support various types of agents, tasks, prompts, models, datasets, and simulated environments.', 'Join us via', 'Slack', 'Discord', 'or'], 'emphasized_text_tags': ['span', 'span', 'span', 'span', 'span', 'span', 'span']}, 'text': 'Mission\\n\\nCAMEL-AI.org is an open-source community dedicated to the study of autonomous and communicative agents. We believe that studying these agents on a large scale offers valuable insights into their behaviors, capabilities, and potential risks. To facilitate research in this field, we provide, implement, and support various types of agents, tasks, prompts, models, datasets, and simulated environments.\\n\\nJoin us via\\n\\nSlack\\n\\nDiscord\\n\\nor'}\n"]}], "source": ["from camel.retrievers import AutoRetriever\n", "from camel.types import StorageType\n", "\n", "auto_retriever = AutoRetriever(\n", "    url_and_api_key=(\n", "        \"./milvus_demo.db\",  # Your Milvus connection URI\n", "        \"\",  # Your Milvus token\n", "    ),\n", "    storage_type=StorageType.MILVUS,\n", "    embedding_model=embedding_instance,\n", ")\n", "\n", "retrieved_info = auto_retriever.run_vector_retriever(\n", "    query=\"What is CAMEL-AI\",\n", "    content_input_paths=[\n", "        \"local_data/camel paper.pdf\",  # example local path\n", "        \"https://www.camel-ai.org/\",  # example remote url\n", "    ],\n", "    top_k=1,\n", "    return_detailed_info=True,\n", ")\n", "\n", "print(retrieved_info)"]}, {"cell_type": "markdown", "metadata": {"id": "hMgCWKG4IQYF"}, "source": ["## 3. Single Agent with Auto RAG\n", "In this section we will show how to combine the `AutoRetriever` with one `ChatAgent`.\n", "\n", "Let's set an agent function, in this function we can get the response by providing a query to this agent."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "YTE_mUwGIok-", "outputId": "039663e8-f6a6-4816-c6a8-ab4ea11a1327"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["CAMEL-AI is an open-source community dedicated to the study of autonomous and communicative agents. It provides, implements, and supports various types of agents, tasks, prompts, models, datasets, and simulated environments to facilitate research in this field.\n"]}], "source": ["from camel.agents import ChatAgent\n", "from camel.messages import BaseMessage\n", "from camel.types import RoleType\n", "from camel.retrievers import AutoRetriever\n", "from camel.types import StorageType\n", "\n", "\n", "def single_agent(query: str) -> str:\n", "    # Set agent role\n", "    assistant_sys_msg = BaseMessage(\n", "        role_name=\"Assistant\",\n", "        role_type=RoleType.ASSISTANT,\n", "        meta_dict=None,\n", "        content=\"\"\"You are a helpful assistant to answer question,\n", "         I will give you the Original Query and Retrieved Context,\n", "        answer the Original Query based on the Retrieved Context,\n", "        if you can't answer the question just say I don't know.\"\"\",\n", "    )\n", "\n", "    # Add auto retriever\n", "    auto_retriever = AutoRetriever(\n", "        url_and_api_key=(\n", "            \"./milvus_demo.db\",  # Your Milvus connection URI\n", "            \"\",  # Your Milvus token\n", "        ),\n", "        storage_type=StorageType.MILVUS,\n", "        embedding_model=embedding_instance,\n", "    )\n", "\n", "    retrieved_info = auto_retriever.run_vector_retriever(\n", "        query=query,\n", "        content_input_paths=[\n", "            \"local_data/camel paper.pdf\",  # example local path\n", "            \"https://www.camel-ai.org/\",  # example remote url\n", "        ],\n", "        # vector_storage_local_path=\"storage_default_run\",\n", "        top_k=1,\n", "        return_detailed_info=True,\n", "    )\n", "\n", "    # Pass the retrieved infomation to agent\n", "    user_msg = BaseMessage.make_user_message(role_name=\"User\", content=retrieved_info)\n", "    agent = ChatAgent(assistant_sys_msg)\n", "\n", "    # Get response\n", "    assistant_response = agent.step(user_msg)\n", "    return assistant_response.msg.content\n", "\n", "\n", "print(single_agent(\"What is CAMEL-AI\"))"]}, {"cell_type": "markdown", "metadata": {"id": "Cvh6e3KpI0rf"}, "source": ["## 4. Role-playing with Auto RAG\n", "In this section we will show how to combine the `RETRIEVAL_FUNCS` with `RolePlaying` by applying `Function Calling`.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "z3tX4EJVI1hb"}, "outputs": [], "source": ["from typing import List\n", "from colorama import Fore\n", "\n", "from camel.agents.chat_agent import FunctionCallingRecord\n", "from camel.configs import ChatGPTConfig\n", "from camel.functions import (\n", "    MATH_FUNCS,\n", "    RETRIEVAL_FUNCS,\n", ")\n", "from camel.societies import RolePlaying\n", "from camel.types import ModelType\n", "from camel.utils import print_text_animated\n", "\n", "\n", "def role_playing_with_rag(\n", "    task_prompt, model_type=ModelType.GPT_4O, chat_turn_limit=10\n", ") -> None:\n", "    task_prompt = task_prompt\n", "\n", "    user_model_config = ChatGPTConfig(temperature=0.0)\n", "\n", "    function_list = [\n", "        *MATH_FUNCS,\n", "        *RETRIEVAL_FUNCS,\n", "    ]\n", "    assistant_model_config = ChatGPTConfig(\n", "        tools=function_list,\n", "        temperature=0.0,\n", "    )\n", "\n", "    role_play_session = RolePlaying(\n", "        assistant_role_name=\"Searcher\",\n", "        user_role_name=\"Professor\",\n", "        assistant_agent_kwargs=dict(\n", "            model_type=model_type,\n", "            model_config=assistant_model_config,\n", "            tools=function_list,\n", "        ),\n", "        user_agent_kwargs=dict(\n", "            model_type=model_type,\n", "            model_config=user_model_config,\n", "        ),\n", "        task_prompt=task_prompt,\n", "        with_task_specify=False,\n", "    )\n", "\n", "    print(\n", "        Fore.GREEN\n", "        + f\"AI Assistant sys message:\\n{role_play_session.assistant_sys_msg}\\n\"\n", "    )\n", "    print(Fore.BLUE + f\"AI User sys message:\\n{role_play_session.user_sys_msg}\\n\")\n", "\n", "    print(Fore.YELLOW + f\"Original task prompt:\\n{task_prompt}\\n\")\n", "    print(\n", "        Fore.CYAN\n", "        + f\"Specified task prompt:\\n{role_play_session.specified_task_prompt}\\n\"\n", "    )\n", "    print(Fore.RED + f\"Final task prompt:\\n{role_play_session.task_prompt}\\n\")\n", "\n", "    n = 0\n", "    input_msg = role_play_session.init_chat()\n", "    while n < chat_turn_limit:\n", "        n += 1\n", "        assistant_response, user_response = role_play_session.step(input_msg)\n", "\n", "        if assistant_response.terminated:\n", "            print(\n", "                Fore.GREEN\n", "                + (\n", "                    \"AI Assistant terminated. Reason: \"\n", "                    f\"{assistant_response.info['termination_reasons']}.\"\n", "                )\n", "            )\n", "            break\n", "        if user_response.terminated:\n", "            print(\n", "                Fore.GREEN\n", "                + (\n", "                    \"AI User terminated. \"\n", "                    f\"Reason: {user_response.info['termination_reasons']}.\"\n", "                )\n", "            )\n", "            break\n", "\n", "        # Print output from the user\n", "        print_text_animated(Fore.BLUE + f\"AI User:\\n\\n{user_response.msg.content}\\n\")\n", "\n", "        # Print output from the assistant, including any function\n", "        # execution information\n", "        print_text_animated(Fore.GREEN + \"AI Assistant:\")\n", "        tool_calls: List[FunctionCallingRecord] = assistant_response.info[\"tool_calls\"]\n", "        for func_record in tool_calls:\n", "            print_text_animated(f\"{func_record}\")\n", "        print_text_animated(f\"{assistant_response.msg.content}\\n\")\n", "\n", "        if \"CAMEL_TASK_DONE\" in user_response.msg.content:\n", "            break\n", "\n", "        input_msg = assistant_response.msg"]}, {"cell_type": "markdown", "metadata": {"id": "w4-0lswwI5Gb"}, "source": ["Run the role-playing with defined retriever function:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "5agjiXHeI64q", "outputId": "f469c2c3-d18d-4fb2-d34a-708b1b2a3de8"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[32mAI Assistant sys message:\n", "BaseMessage(role_name='Searcher', role_type=<RoleType.ASSISTANT: 'assistant'>, meta_dict={'task': \"What is the main termination reasons for AI Society dataset, how many number of messages did camel decided to limit, what's the value plus 100?\", 'assistant_role': 'Searcher', 'user_role': 'Professor'}, content=\"===== RULES OF ASSISTANT =====\\nNever forget you are a Searcher and I am a Professor. Never flip roles! Never instruct me!\\nWe share a common interest in collaborating to successfully complete a task.\\nYou must help me to complete the task.\\nHere is the task: What is the main termination reasons for AI Society dataset, how many number of messages did camel decided to limit, what's the value plus 100?. Never forget our task!\\nI must instruct you based on your expertise and my needs to complete the task.\\n\\nI must give you one instruction at a time.\\nYou must write a specific solution that appropriately solves the requested instruction and explain your solutions.\\nYou must decline my instruction honestly if you cannot perform the instruction due to physical, moral, legal reasons or your capability and explain the reasons.\\nUnless I say the task is completed, you should always start with:\\n\\nSolution: <YOUR_SOLUTION>\\n\\n<YOUR_SOLUTION> should be very specific, include detailed explanations and provide preferable detailed implementations and examples and lists for task-solving.\\nAlways end <YOUR_SOLUTION> with: Next request.\")\n", "\n", "\u001b[34mAI User sys message:\n", "BaseMessage(role_name='Professor', role_type=<RoleType.USER: 'user'>, meta_dict={'task': \"What is the main termination reasons for AI Society dataset, how many number of messages did camel decided to limit, what's the value plus 100?\", 'assistant_role': 'Searcher', 'user_role': 'Professor'}, content='===== RULES OF USER =====\\nNever forget you are a Professor and I am a Searcher. Never flip roles! You will always instruct me.\\nWe share a common interest in collaborating to successfully complete a task.\\nI must help you to complete the task.\\nHere is the task: What is the main termination reasons for AI Society dataset, how many number of messages did camel decided to limit, what\\'s the value plus 100?. Never forget our task!\\nYou must instruct me based on my expertise and your needs to solve the task ONLY in the following two ways:\\n\\n1. Instruct with a necessary input:\\nInstruction: <YOUR_INSTRUCTION>\\nInput: <YOUR_INPUT>\\n\\n2. Instruct without any input:\\nInstruction: <YOUR_INSTRUCTION>\\nInput: None\\n\\nThe \"Instruction\" describes a task or question. The paired \"Input\" provides further context or information for the requested \"Instruction\".\\n\\nYou must give me one instruction at a time.\\nI must write a response that appropriately solves the requested instruction.\\nI must decline your instruction honestly if I cannot perform the instruction due to physical, moral, legal reasons or my capability and explain the reasons.\\nYou should instruct me not ask me questions.\\nNow you must start to instruct me using the two ways described above.\\nDo not add anything else other than your instruction and the optional corresponding input!\\nKeep giving me instructions and necessary inputs until you think the task is completed.\\nWhen the task is completed, you must only reply with a single word <CAMEL_TASK_DONE>.\\nNever say <CAMEL_TASK_DONE> unless my responses have solved your task.')\n", "\n", "\u001b[33mOriginal task prompt:\n", "What is the main termination reasons for AI Society dataset, how many number of messages did camel decided to limit, what's the value plus 100?\n", "\n", "\u001b[36mSpecified task prompt:\n", "None\n", "\n", "\u001b[31mFinal task prompt:\n", "What is the main termination reasons for AI Society dataset, how many number of messages did camel decided to limit, what's the value plus 100?\n", "\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\u001b[34mAI User:\n", "\n", "Instruction: Provide a summary of the main termination reasons in the AI Society dataset.\n", "Input: None\n", "\n", "\n", "\u001b[32mAI Assistant:\n", "\n", "Function Execution: local_retriever\n", "\tArgs: {'query': 'main termination reasons for AI Society dataset'}\n", "\tResult: Original Query:\n", "{main termination reasons for AI Society dataset}\n", "Retrieved Context:\n", "Next we examine the conversation termination reasons for both AI Society and Code datasets. As can be seen in Figure 8, the main termination reasons for AI Society dataset is Assistant Instruct whereas for Code it is Token Limit. The latter is expected as the since responses that contain code tend to be long. It is also interesting to note that in both datasets, the termination due to Maximum Number of Messages is low indicating that the limit of 40 maximum messages is reasonable. Our decision t\n", "\n", "Solution: The main termination reason for the AI Society dataset is \"Assistant Instruct.\" This indicates that conversations in the AI Society dataset typically end when the assistant is instructed to terminate the conversation.\n", "\n", "Next request.\n", "\n", "\n", "\u001b[34mAI User:\n", "\n", "Instruction: Identify the number of messages that the camel decided to limit in the context provided.\n", "Input: None\n", "\n", "\n", "\u001b[32mAI Assistant:\n", "\n", "Solution: The context provided from the local retriever indicates that the camel decided to limit the number of messages to a maximum of 40 messages. This is mentioned in the retrieved context where it states that the termination due to Maximum Number of Messages is low, indicating that the limit of 40 maximum messages is reasonable.\n", "\n", "Next request.\n", "\n", "\n", "\u001b[34mAI User:\n", "\n", "Instruction: Calculate the value of the message limit plus 100.\n", "Input: None\n", "\n", "\n", "\u001b[32mAI Assistant:\n", "\n", "Function Execution: add\n", "\tArgs: {'a': 40, 'b': 100}\n", "\tResult: 140\n", "\n", "Solution: The value of the message limit plus 100 is 140.\n", "\n", "Next request.\n", "\n", "\n", "\u001b[34mAI User:\n", "\n", "CAMEL_TASK_DONE\n", "\n", "\n", "\u001b[32mAI Assistant:\n", "\n", "Solution: Understood, the task is completed.\n", "\n", "Next request.\n", "\n", "\n"]}], "source": ["role_playing_with_rag(\n", "    task_prompt=\"\"\"What is the main termination reasons for AI Society\n", "                   dataset, how many number of messages did camel decided to\n", "                   limit, what's the value plus 100? You should refer to the\n", "                   content in path camel/local_data/camel paper.pdf\"\"\"\n", ")"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}