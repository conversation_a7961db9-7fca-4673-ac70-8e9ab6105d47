{"cells": [{"cell_type": "markdown", "id": "7f6f2842", "metadata": {}, "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/integration/build_RAG_with_milvus_and_gemini.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/integration/build_RAG_with_milvus_and_gemini.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>\n"]}, {"cell_type": "markdown", "id": "79f27c49-0cf1-4f54-8036-a8b3d03f2b62", "metadata": {}, "source": ["# Build RAG with Milvus and Gemini\n", "\n", "The [Gemini API](https://ai.google.dev/gemini-api/docs) and [Google AI Studio](https://ai.google.dev/aistudio) help you start working with Google's latest models and turn your ideas into applications that scale. Gemini provides access to powerful language models like `Gemini-2.0-Flash`, `Gemini-2.0-Pro`, and other versions for tasks such as text generation, document processing, vision, audio analysis, and more. The API allows you to input long context with millions of tokens, fine-tune models for specific tasks, generate structured outputs like JSON, and leverage capabilities like semantic retrieval and code execution.\n", "\n", "In this tutorial, we will show you how to build a RAG (Retrieval-Augmented Generation) pipeline with Milvus and Gemini. We will use the Gemini model to generate responses based on a given query, augmented with relevant information retrieved from Milvus.\n", "\n", "## Preparation\n", "### Dependencies and Environment\n", "\n", "First, install the required packages:"]}, {"cell_type": "code", "execution_count": 1, "id": "c835b677-41b2-4b71-beda-a6bb60122767", "metadata": {}, "outputs": [], "source": ["! pip install --upgrade pymilvus google-genai requests tqdm"]}, {"cell_type": "markdown", "id": "032f7d28-ca9a-44c5-a13f-f530537159bd", "metadata": {}, "source": ["> If you are using Google Colab, to enable dependencies just installed, you may need to **restart the runtime** (click on the \"Runtime\" menu at the top of the screen, and select \"Restart session\" from the dropdown menu).\n", "\n", "You should first log in to the Google AI Studio platform and prepare the [api key](https://aistudio.google.com/apikey) `GEMINI_API_KEY` as an environment variable."]}, {"cell_type": "code", "execution_count": 2, "id": "9cac2d7e-d49f-4751-b08b-295c23e440fb", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"GEMINI_API_KEY\"] = \"***********\""]}, {"cell_type": "markdown", "id": "ee8fd0e7-287d-4e82-9ec0-1b77d085191b", "metadata": {}, "source": ["### Prepare the data\n", "\n", "We use the FAQ pages from the [Milvus Documentation 2.4.x](https://github.com/milvus-io/milvus-docs/releases/download/v2.4.6-preview/milvus_docs_2.4.x_en.zip) as the private knowledge in our RAG, which is a good data source for a simple RAG pipeline.\n", "\n", "Download the zip file and extract documents to the folder `milvus_docs`."]}, {"cell_type": "code", "execution_count": null, "id": "f80cf254-c56c-4c33-871d-0e2bbfe7d624", "metadata": {}, "outputs": [], "source": ["! wget https://github.com/milvus-io/milvus-docs/releases/download/v2.4.6-preview/milvus_docs_2.4.x_en.zip\n", "! unzip -q milvus_docs_2.4.x_en.zip -d milvus_docs"]}, {"cell_type": "markdown", "id": "d464f026-f79b-46d2-b4f4-e9dcb5fee8e5", "metadata": {}, "source": ["We load all markdown files from the folder `milvus_docs/en/faq`. For each document, we just simply use \"# \" to separate the content in the file, which can roughly separate the content of each main part of the markdown file."]}, {"cell_type": "code", "execution_count": 4, "id": "3e72ceaa-1309-4fc7-b3a6-0335e3e9f8f0", "metadata": {}, "outputs": [], "source": ["from glob import glob\n", "\n", "text_lines = []\n", "\n", "for file_path in glob(\"milvus_docs/en/faq/*.md\", recursive=True):\n", "    with open(file_path, \"r\") as file:\n", "        file_text = file.read()\n", "\n", "    text_lines += file_text.split(\"# \")"]}, {"cell_type": "markdown", "id": "df3786a7-862e-4485-a309-1107c27aa711", "metadata": {}, "source": ["### Prepare the LLM and Embedding Model\n", "\n", "We use the `gemini-2.0-flash` as LLM, and the `text-embedding-004` as embedding model.\n", "\n", "Let's try to generate a test response from the LLM:"]}, {"cell_type": "code", "execution_count": 5, "id": "e0990772-6c37-458a-9c0d-e88c50801d85", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I am a large language model, trained by Google.\n", "\n"]}], "source": ["from google import genai\n", "\n", "client = genai.Client(api_key=os.environ[\"GEMINI_API_KEY\"])\n", "\n", "response = client.models.generate_content(\n", "    model=\"gemini-2.0-flash\", contents=\"who are you\"\n", ")\n", "print(response.text)"]}, {"cell_type": "markdown", "id": "a13aa9d2-a1e9-49a2-a330-31091bb5ac09", "metadata": {}, "source": ["Generate a test embedding and print its dimension and first few elements."]}, {"cell_type": "code", "execution_count": 6, "id": "24dafffd-1ab1-4e52-bb11-00c9d704d70d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["768\n", "[0.013588584, -0.004361838, -0.08481652, -0.039724775, 0.04723794, -0.0051557426, 0.026071774, 0.045514572, -0.016867816, 0.039378334]\n"]}], "source": ["test_embeddings = client.models.embed_content(\n", "    model=\"text-embedding-004\", contents=[\"This is a test1\", \"This is a test2\"]\n", ")\n", "\n", "embedding_dim = len(test_embeddings.embeddings[0].values)\n", "print(embedding_dim)\n", "print(test_embeddings.embeddings[0].values[:10])"]}, {"cell_type": "markdown", "id": "fb6dc738-f37d-4c0b-b326-436d9c70154f", "metadata": {}, "source": ["## Load data into Milvus\n", "\n", "### Create the Collection\n", "\n", "Let's initialize the Milvus client and set up our collection:"]}, {"cell_type": "code", "execution_count": 7, "id": "7a4f6354-aa9d-4930-a1ee-ebaef2c17dce", "metadata": {}, "outputs": [], "source": ["from pymilvus import MilvusClient\n", "\n", "milvus_client = MilvusClient(uri=\"./milvus_demo.db\")\n", "\n", "collection_name = \"my_rag_collection\""]}, {"cell_type": "markdown", "id": "59c27c17-0aac-49b8-98f5-aaf8d4bd3544", "metadata": {}, "source": ["> As for the argument of `MilvusClient`:\n", "> - Setting the `uri` as a local file, e.g.`./milvus.db`, is the most convenient method, as it automatically utilizes [Milvus Lite](https://milvus.io/docs/milvus_lite.md) to store all data in this file.\n", "> - If you have large scale of data, you can set up a more performant Milvus server on [docker or kubernetes](https://milvus.io/docs/quickstart.md). In this setup, please use the server uri, e.g.`http://localhost:19530`, as your `uri`.\n", "> - If you want to use [Zilliz Cloud](https://zilliz.com/cloud), the fully managed cloud service for Milvus, adjust the `uri` and `token`, which correspond to the [Public Endpoint and Api key](https://docs.zilliz.com/docs/on-zilliz-cloud-console#free-cluster-details) in Zilliz Cloud.\n", "\n", "Check if the collection already exists and drop it if it does."]}, {"cell_type": "code", "execution_count": 8, "id": "c9ea6319-d7c6-4705-912a-ac168a149f70", "metadata": {}, "outputs": [], "source": ["if milvus_client.has_collection(collection_name):\n", "    milvus_client.drop_collection(collection_name)"]}, {"cell_type": "markdown", "id": "a3397488-a41a-4c8a-9db4-17d5432a2719", "metadata": {}, "source": ["Create a new collection with specified parameters. \n", "\n", "If we don't specify any field information, Milvus will automatically create a default `id` field for primary key, and a `vector` field to store the vector data. A reserved JSON field is used to store non-schema-defined fields and their values."]}, {"cell_type": "code", "execution_count": 9, "id": "d431e92e-018e-4539-9b28-e3554183b94c", "metadata": {}, "outputs": [], "source": ["milvus_client.create_collection(\n", "    collection_name=collection_name,\n", "    dimension=embedding_dim,\n", "    metric_type=\"IP\",  # Inner product distance\n", "    consistency_level=\"Strong\",  # Strong consistency level\n", ")"]}, {"cell_type": "markdown", "id": "20f6b39e-bb0e-4090-a8c7-65db6f967f9e", "metadata": {}, "source": ["### Insert data\n", "Iterate through the text lines, create embeddings, and then insert the data into Milvus.\n", "\n", "Here is a new field `text`, which is a non-defined field in the collection schema. It will be automatically added to the reserved JSON dynamic field, which can be treated as a normal field at a high level."]}, {"cell_type": "code", "execution_count": 10, "id": "b76907d9-f1da-443c-9c14-e8306195acae", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Creating embeddings: 100%|██████████| 72/72 [00:00<00:00, 337796.30it/s]\n"]}, {"data": {"text/plain": ["{'insert_count': 72, 'ids': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71], 'cost': 0}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["from tqdm import tqdm\n", "\n", "data = []\n", "\n", "doc = client.models.embed_content(model=\"text-embedding-004\", contents=text_lines)\n", "\n", "for i, line in enumerate(tqdm(text_lines, desc=\"Creating embeddings\")):\n", "    data.append({\"id\": i, \"vector\": doc.embeddings[i].values, \"text\": line})\n", "\n", "milvus_client.insert(collection_name=collection_name, data=data)"]}, {"cell_type": "markdown", "id": "81087b1d-0d57-42dc-a720-863e0eeeab45", "metadata": {}, "source": ["## Build RAG\n", "\n", "### Retrieve data for a query\n", "\n", "Let's specify a frequent question about <PERSON><PERSON><PERSON><PERSON>."]}, {"cell_type": "code", "execution_count": 11, "id": "84ada50c-35c1-443d-9a92-0421622528b6", "metadata": {}, "outputs": [], "source": ["question = \"How is data stored in milvus?\""]}, {"cell_type": "markdown", "id": "70cb504b-1c19-43d2-b79e-721ad9cc263f", "metadata": {}, "source": ["Search for the question in the collection and retrieve the semantic top-3 matches."]}, {"cell_type": "code", "execution_count": 12, "id": "199b8400-2494-4863-b521-1e9a2113592c", "metadata": {}, "outputs": [], "source": ["quest_embed = client.models.embed_content(model=\"text-embedding-004\", contents=question)\n", "\n", "search_res = milvus_client.search(\n", "    collection_name=collection_name,\n", "    data=[quest_embed.embeddings[0].values],\n", "    limit=3,  # Return top 3 results\n", "    search_params={\"metric_type\": \"IP\", \"params\": {}},  # Inner product distance\n", "    output_fields=[\"text\"],  # Return the text field\n", ")"]}, {"cell_type": "markdown", "id": "be20f047-5b6d-409e-bbfe-1e817a4e5303", "metadata": {}, "source": ["Let's take a look at the search results of the query"]}, {"cell_type": "code", "execution_count": 13, "id": "b9a15cb9-04f9-4e33-a1f7-777dfe5f85ce", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "    [\n", "        \" Where does Milvus store data?\\n\\nMilvus deals with two types of data, inserted data and metadata. \\n\\nInserted data, including vector data, scalar data, and collection-specific schema, are stored in persistent storage as incremental log. Milvus supports multiple object storage backends, including [MinIO](https://min.io/), [AWS S3](https://aws.amazon.com/s3/?nc1=h_ls), [Google Cloud Storage](https://cloud.google.com/storage?hl=en#object-storage-for-companies-of-all-sizes) (GCS), [Azure Blob Storage](https://azure.microsoft.com/en-us/products/storage/blobs), [Alibaba Cloud OSS](https://www.alibabacloud.com/product/object-storage-service), and [Tencent Cloud Object Storage](https://www.tencentcloud.com/products/cos) (COS).\\n\\nMetadata are generated within Milvus. Each Milvus module has its own metadata that are stored in etcd.\\n\\n###\",\n", "        0.8048275113105774\n", "    ],\n", "    [\n", "        \"Does the query perform in memory? What are incremental data and historical data?\\n\\nYes. When a query request comes, <PERSON><PERSON><PERSON>s searches both incremental data and historical data by loading them into memory. Incremental data are in the growing segments, which are buffered in memory before they reach the threshold to be persisted in storage engine, while historical data are from the sealed segments that are stored in the object storage. Incremental data and historical data together constitute the whole dataset to search.\\n\\n###\",\n", "        0.7574886679649353\n", "    ],\n", "    [\n", "        \"What is the maximum dataset size <PERSON><PERSON><PERSON><PERSON> can handle?\\n\\n  \\nTheoretically, the maximum dataset size <PERSON><PERSON>vu<PERSON> can handle is determined by the hardware it is run on, specifically system memory and storage:\\n\\n- Milvus loads all specified collections and partitions into memory before running queries. Therefore, memory size determines the maximum amount of data <PERSON><PERSON><PERSON><PERSON> can query.\\n- When new entities and and collection-related schema (currently only MinIO is supported for data persistence) are added to Milvus, system storage determines the maximum allowable size of inserted data.\\n\\n###\",\n", "        0.7453608512878418\n", "    ]\n", "]\n"]}], "source": ["import json\n", "\n", "retrieved_lines_with_distances = [\n", "    (res[\"entity\"][\"text\"], res[\"distance\"]) for res in search_res[0]\n", "]\n", "print(json.dumps(retrieved_lines_with_distances, indent=4))"]}, {"cell_type": "markdown", "id": "d8d4b8fe-ca84-4d80-9f66-db06430e2742", "metadata": {}, "source": ["### Use LLM to get a RAG response\n", "\n", "Convert the retrieved documents into a string format."]}, {"cell_type": "code", "execution_count": 14, "id": "68e2c968-8fee-4b65-85f4-4ac0e56891c4", "metadata": {}, "outputs": [], "source": ["context = \"\\n\".join(\n", "    [line_with_distance[0] for line_with_distance in retrieved_lines_with_distances]\n", ")"]}, {"cell_type": "markdown", "id": "dced7734-240e-4fd6-be3b-1eb202d3d85f", "metadata": {}, "source": ["Define system and user prompts for the Lanage Model. This prompt is assembled with the retrieved documents from Milvus."]}, {"cell_type": "code", "execution_count": 15, "id": "062b4407-d13e-4e3a-a8fd-7e21f2435b77", "metadata": {}, "outputs": [], "source": ["from google.genai import types\n", "\n", "SYSTEM_PROMPT = \"\"\"\n", "Human: You are an AI assistant. You are able to find answers to the questions from the contextual passage snippets provided.\n", "\"\"\"\n", "USER_PROMPT = f\"\"\"\n", "Use the following pieces of information enclosed in <context> tags to provide an answer to the question enclosed in <question> tags.\n", "<context>\n", "{context}\n", "</context>\n", "<question>\n", "{question}\n", "</question>\n", "\"\"\""]}, {"cell_type": "markdown", "id": "f61becfe-8e52-409f-bee4-47b216048a67", "metadata": {}, "source": ["Use the Gemini to generate a response based on the prompts."]}, {"cell_type": "code", "execution_count": 16, "id": "81ea72c4-db69-4551-a02b-84a9342064b5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Milvus stores two types of data: inserted data and metadata. Inserted data, which includes vector data, scalar data, and collection-specific schema, are stored in persistent storage as incremental logs. Milvus supports multiple object storage backends. Metadata is generated within Milvus, and each Milvus module has its own metadata that is stored in etcd.\n", "\n"]}], "source": ["response = client.models.generate_content(\n", "    model=\"gemini-2.0-flash\",\n", "    config=types.GenerateContentConfig(system_instruction=SYSTEM_PROMPT),\n", "    contents=USER_PROMPT,\n", ")\n", "print(response.text)"]}, {"cell_type": "markdown", "id": "63fc1a4a-0d40-4d40-a422-18429e5344cd", "metadata": {}, "source": ["Great! We have successfully built a RAG pipeline with Milvus and Gemini."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}