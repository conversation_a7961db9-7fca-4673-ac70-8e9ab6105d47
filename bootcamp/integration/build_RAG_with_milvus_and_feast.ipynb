{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/integration/build_RAG_with_milvus_and_feast.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/tutorials/integration/build_RAG_with_milvus_and_feast.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Build RAG with Mil<PERSON><PERSON> and Feast\n", "\n", "In this tutorial, we will build a Retrieval-Augmented Generation (RAG) pipeline using [Feast](https://github.com/feast-dev/feast) and [Milvus](https://milvus.io/). Feast is an open-source feature store that streamlines feature management for machine learning, enabling efficient storage and retrieval of structured data for both training and real-time inference. Milvus is a high-performance vector database designed for fast similarity search, making it ideal for retrieving relevant documents in RAG workflows. \n", "\n", "Essentially, we'll use Feast to inject documents and structured data (i.e., features) into the context of an LLM (Large Language Model) to power a RAG Application (Retrieval Augmented Generation) with Milvus as the online vector database.\n", "\n", "\n", "# Why [Feast](https://github.com/feast-dev/feast)? \n", "\n", "Feast solves several common issues in this flow:\n", "1. **Online retrieval:** At inference time, LLMs often need access to data that isn't readily \n", "   available and needs to be precomputed from other data sources.\n", "   * Feast manages deployment to a variety of online stores (e.g. Milvus, DynamoDB, Redis, Google Cloud Datastore) and \n", "     ensures necessary features are consistently _available_ and _freshly computed_ at inference time.\n", "2. **Vector Search:** Feast has built support for vector similarity search that is easily configured declaritively so users can focus on their application. Milvus provides powerful and efficient vector similarity search capabilities.\n", "3. **Richer structured data:** Along with vector search, users can query standard structured fields to inject into the LLM context for better user experiences.\n", "4. **Feature/Context and versioning:** Different teams within an organization are often unable to reuse \n", "   data across projects and services, resulting in duplicate application logic. Models have data dependencies that need \n", "   to be versioned, for example when running A/B tests on model/prompt versions.\n", "   * Feast enables discovery of and collaboration on previously used documents, features, and enables versioning of sets of \n", "     data.\n", "\n", "We will:\n", "1. Deploy a local feature store with a **Parquet file offline store** and **Milvus online store**.\n", "2. Write/materialize the data (i.e., feature values) from the offline store (a parquet file) into the online store (Milvus).\n", "3. Serve the features using the Feast SDK with Milvus's vector search capabilities\n", "4. Inject the document into the LLM's context to answer questions\n", "\n", "\n", "> This tutorial is based on the official Milvus integration guide from the [Feast Repository](https://github.com/feast-dev/feast/blob/master/examples/rag/milvus-quickstart.ipynb). While we strive to keep this tutorial up-to-date, if you encounter any discrepancies, please refer to the official guide and feel free to open an issue in our repository for any necessary updates."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Preparation\n", "\n", "### Dependencies "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["! pip install 'feast[milvus]' openai -U -q"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> If you are using Google Colab, to enable dependencies just installed, you may need to **restart the runtime** (click on the \"Runtime\" menu at the top of the screen, and select \"Restart session\" from the dropdown menu)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["We will use OpenAI as our LLM provider. You can login to its official website and prepare the [OPENAI_API_KEY](https://platform.openai.com/api-keys) as an environment variable."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "from openai import OpenAI\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-**************\"\n", "\n", "llm_client = OpenAI(\n", "    api_key=os.environ.get(\"OPENAI_API_KEY\"),\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prepare the Data\n", "\n", "We will use the data from the following folder as our example:  \n", "[Feast RAG Feature Repo](https://github.com/feast-dev/feast/tree/master/examples/rag/feature_repo)  \n", "\n", "After downloading the data, you will find the following files:  \n", "\n", "```bash\n", "feature_repo/\n", "│── data/                  # Contains pre-processed Wikipedia city data in Parquet format\n", "│── example_repo.py        # Defines feature views and entities for the city data\n", "│── feature_store.yaml     # Configures Milvus and feature store settings\n", "│── test_workflow.py       # Example workflow for Feast operations\n", "```\n", "\n", "\n", "### Key Configuration Files\n", "\n", "#### 1. feature_store.yaml\n", "This file configures the feature store infrastructure:\n", "```yaml\n", "project: rag\n", "provider: local\n", "registry: data/registry.db\n", "\n", "online_store:\n", "  type: milvus            # Uses Milvus for vector storage\n", "  path: data/online_store.db\n", "  vector_enabled: true    # Enables vector similarity search\n", "  embedding_dim: 384      # Dimension of our embeddings\n", "  index_type: \"FLAT\"      # Vector index type\n", "  metric_type: \"COSINE\"   # Similarity metric\n", "\n", "offline_store:\n", "  type: file              # Uses file-based offline storage\n", "```\n", "\n", "This configuration establishes:\n", "- Milvus as the online store for fast vector retrieval\n", "- File-based offline storage for historical data processing\n", "- Vector search capabilities with COSINE similarity\n", "\n", "#### 2. example_repo.py\n", "Contains the feature definitions for our city data, including:\n", "- Entity definitions for cities\n", "- Feature views for city information and embeddings\n", "- Schema specifications for the vector database\n", "\n", "#### 3. Data Directory\n", "Contains our pre-processed Wikipedia city data with:\n", "- City descriptions and summaries\n", "- Pre-computed embeddings (384-dimensional vectors)\n", "- Associated metadata like city names and states\n", "\n", "These files work together to create a feature store that combines Milvus's vector search capabilities with Feast's feature management, enabling efficient retrieval of relevant city information for our RAG application.\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Inspect the Data\n", "\n", "The raw feature data we have in this demo is stored in a local parquet file. The dataset Wikipedia summaries of diferent cities. Let's inspect the data first."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["embedding length = 384\n"]}], "source": ["import pandas as pd\n", "\n", "df = pd.read_parquet(\n", "    \"/path/to/feature_repo/data/city_wikipedia_summaries_with_embeddings.parquet\"\n", ")\n", "df[\"vector\"] = df[\"vector\"].apply(lambda x: x.tolist())\n", "embedding_length = len(df[\"vector\"][0])\n", "print(f\"embedding length = {embedding_length}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>item_id</th>\n", "      <th>event_timestamp</th>\n", "      <th>state</th>\n", "      <th>wiki_summary</th>\n", "      <th>sentence_chunks</th>\n", "      <th>vector</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2025-01-09 13:36:59.280589</td>\n", "      <td>New York, New York</td>\n", "      <td>New York, often called New York City or simply...</td>\n", "      <td>New York, often called New York City or simply...</td>\n", "      <td>[0.1465730518102646, -0.07317650318145752, 0.0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2025-01-09 13:36:59.280589</td>\n", "      <td>New York, New York</td>\n", "      <td>New York, often called New York City or simply...</td>\n", "      <td>The city comprises five boroughs, each of whic...</td>\n", "      <td>[0.05218901485204697, -0.08449874818325043, 0....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2025-01-09 13:36:59.280589</td>\n", "      <td>New York, New York</td>\n", "      <td>New York, often called New York City or simply...</td>\n", "      <td>New York is a global center of finance and com...</td>\n", "      <td>[0.06769222766160965, -0.07371102273464203, -0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>2025-01-09 13:36:59.280589</td>\n", "      <td>New York, New York</td>\n", "      <td>New York, often called New York City or simply...</td>\n", "      <td>New York City is the epicenter of the world's ...</td>\n", "      <td>[0.12095861881971359, -0.04279915615916252, 0....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>2025-01-09 13:36:59.280589</td>\n", "      <td>New York, New York</td>\n", "      <td>New York, often called New York City or simply...</td>\n", "      <td>With an estimated population in 2022 of 8,335,...</td>\n", "      <td>[0.17943550646305084, -0.09458263963460922, 0....</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  item_id            event_timestamp               state  \\\n", "0   0        0 2025-01-09 13:36:59.280589  New York, New York   \n", "1   1        1 2025-01-09 13:36:59.280589  New York, New York   \n", "2   2        2 2025-01-09 13:36:59.280589  New York, New York   \n", "3   3        3 2025-01-09 13:36:59.280589  New York, New York   \n", "4   4        4 2025-01-09 13:36:59.280589  New York, New York   \n", "\n", "                                        wiki_summary  \\\n", "0  New York, often called New York City or simply...   \n", "1  New York, often called New York City or simply...   \n", "2  New York, often called New York City or simply...   \n", "3  New York, often called New York City or simply...   \n", "4  New York, often called New York City or simply...   \n", "\n", "                                     sentence_chunks  \\\n", "0  New York, often called New York City or simply...   \n", "1  The city comprises five boroughs, each of whic...   \n", "2  New York is a global center of finance and com...   \n", "3  New York City is the epicenter of the world's ...   \n", "4  With an estimated population in 2022 of 8,335,...   \n", "\n", "                                              vector  \n", "0  [0.1465730518102646, -0.07317650318145752, 0.0...  \n", "1  [0.05218901485204697, -0.08449874818325043, 0....  \n", "2  [0.06769222766160965, -0.07371102273464203, -0...  \n", "3  [0.12095861881971359, -0.04279915615916252, 0....  \n", "4  [0.17943550646305084, -0.09458263963460922, 0....  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display\n", "\n", "display(df.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Register Feature Definitions and Deploy the Feature Store\n", "\n", "After downloading the `feature_repo`, we need to run `feast apply` to register the feature views and entities defined in `example_repo.py`, and sets up **Milvus** as the online store tables. \n", "\n", "Make sure you have nagivated to the `feature_repo` directory before running the command. \n", "\n", "```bash\n", "feast apply\n", "```\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Features into <PERSON><PERSON><PERSON><PERSON>\n", "\n", "Now we load the features into Milvus. This step involves serializing feature values from the offline store and writing them into Milvus."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "from feast import FeatureStore\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "store = FeatureStore(repo_path=\"/path/to/feature_repo\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Connecting to Milvus in local mode using /Users/<USER>/Desktop/feature_repo/data/online_store.db\n"]}], "source": ["store.write_to_online_store(feature_view_name=\"city_embeddings\", df=df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that now there are `online_store.db` and `registry.db`, which store the materialized features and schema information, respectively. We can take a look at the `online_store.db` file."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>item_id_pk</th>\n", "      <th>created_ts</th>\n", "      <th>event_ts</th>\n", "      <th>item_id</th>\n", "      <th>sentence_chunks</th>\n", "      <th>state</th>\n", "      <th>vector</th>\n", "      <th>wiki_summary</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0100000002000000070000006974656d5f696404000000...</td>\n", "      <td>0</td>\n", "      <td>1736447819280589</td>\n", "      <td>0</td>\n", "      <td>New York, often called New York City or simply...</td>\n", "      <td>New York, New York</td>\n", "      <td>0.146573</td>\n", "      <td>New York, often called New York City or simply...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0100000002000000070000006974656d5f696404000000...</td>\n", "      <td>0</td>\n", "      <td>1736447819280589</td>\n", "      <td>0</td>\n", "      <td>New York, often called New York City or simply...</td>\n", "      <td>New York, New York</td>\n", "      <td>-0.073177</td>\n", "      <td>New York, often called New York City or simply...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0100000002000000070000006974656d5f696404000000...</td>\n", "      <td>0</td>\n", "      <td>1736447819280589</td>\n", "      <td>0</td>\n", "      <td>New York, often called New York City or simply...</td>\n", "      <td>New York, New York</td>\n", "      <td>0.052114</td>\n", "      <td>New York, often called New York City or simply...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0100000002000000070000006974656d5f696404000000...</td>\n", "      <td>0</td>\n", "      <td>1736447819280589</td>\n", "      <td>0</td>\n", "      <td>New York, often called New York City or simply...</td>\n", "      <td>New York, New York</td>\n", "      <td>0.033187</td>\n", "      <td>New York, often called New York City or simply...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0100000002000000070000006974656d5f696404000000...</td>\n", "      <td>0</td>\n", "      <td>1736447819280589</td>\n", "      <td>0</td>\n", "      <td>New York, often called New York City or simply...</td>\n", "      <td>New York, New York</td>\n", "      <td>0.012013</td>\n", "      <td>New York, often called New York City or simply...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                          item_id_pk  created_ts  \\\n", "0  0100000002000000070000006974656d5f696404000000...           0   \n", "1  0100000002000000070000006974656d5f696404000000...           0   \n", "2  0100000002000000070000006974656d5f696404000000...           0   \n", "3  0100000002000000070000006974656d5f696404000000...           0   \n", "4  0100000002000000070000006974656d5f696404000000...           0   \n", "\n", "           event_ts item_id  \\\n", "0  1736447819280589       0   \n", "1  1736447819280589       0   \n", "2  1736447819280589       0   \n", "3  1736447819280589       0   \n", "4  1736447819280589       0   \n", "\n", "                                     sentence_chunks               state  \\\n", "0  New York, often called New York City or simply...  New York, New York   \n", "1  New York, often called New York City or simply...  New York, New York   \n", "2  New York, often called New York City or simply...  New York, New York   \n", "3  New York, often called New York City or simply...  New York, New York   \n", "4  New York, often called New York City or simply...  New York, New York   \n", "\n", "     vector                                       wiki_summary  \n", "0  0.146573  New York, often called New York City or simply...  \n", "1 -0.073177  New York, often called New York City or simply...  \n", "2  0.052114  New York, often called New York City or simply...  \n", "3  0.033187  New York, often called New York City or simply...  \n", "4  0.012013  New York, often called New York City or simply...  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["pymilvus_client = store._provider._online_store._connect(store.config)\n", "COLLECTION_NAME = pymilvus_client.list_collections()[0]\n", "\n", "milvus_query_result = pymilvus_client.query(\n", "    collection_name=COLLECTION_NAME,\n", "    filter=\"item_id == '0'\",\n", ")\n", "pd.DataFrame(milvus_query_result[0]).head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Build RAG\n", "\n", "### 1. Embedding a Query Using PyTorch and Sentence Transformers\n", "\n", "During inference (e.g., during when a user submits a chat message) we need to embed the input text. This can be thought of as a feature transformation of the input data. In this example, we'll do this with a small Sentence Transformer from Hugging Face."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn.functional as F\n", "from feast import FeatureStore\n", "from pymilvus import MilvusClient, DataType, FieldSchema\n", "from transformers import AutoTokenizer, AutoModel\n", "from example_repo import city_embeddings_feature_view, item\n", "\n", "TOKENIZER = \"sentence-transformers/all-MiniLM-L6-v2\"\n", "MODEL = \"sentence-transformers/all-MiniLM-L6-v2\"\n", "\n", "\n", "def mean_pooling(model_output, attention_mask):\n", "    token_embeddings = model_output[\n", "        0\n", "    ]  # First element of model_output contains all token embeddings\n", "    input_mask_expanded = (\n", "        attention_mask.unsqueeze(-1).expand(token_embeddings.size()).float()\n", "    )\n", "    return torch.sum(token_embeddings * input_mask_expanded, 1) / torch.clamp(\n", "        input_mask_expanded.sum(1), min=1e-9\n", "    )\n", "\n", "\n", "def run_model(sentences, tokenizer, model):\n", "    encoded_input = tokenizer(\n", "        sentences, padding=True, truncation=True, return_tensors=\"pt\"\n", "    )\n", "    # Compute token embeddings\n", "    with torch.no_grad():\n", "        model_output = model(**encoded_input)\n", "\n", "    sentence_embeddings = mean_pooling(model_output, encoded_input[\"attention_mask\"])\n", "    sentence_embeddings = F.normalize(sentence_embeddings, p=2, dim=1)\n", "    return sentence_embeddings"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. Fetching Real-time Vectors and Data for Online Inference\n", "\n", "Once the query has been transformed into an embedding, the next step is to retrieve relevant documents from the vector store. At inference time, we leverage vector similarity search to find the most relevant document embeddings stored in the online feature store, using `retrieve_online_documents_v2()`. These feature vectors can then be fed into the context of the LLM."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["question = \"Which city has the largest population in New York?\"\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(TOKENIZER)\n", "model = AutoModel.from_pretrained(MODEL)\n", "query_embedding = run_model(question, tokenizer, model)\n", "query = query_embedding.detach().cpu().numpy().tolist()[0]"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>vector</th>\n", "      <th>item_id</th>\n", "      <th>state</th>\n", "      <th>sentence_chunks</th>\n", "      <th>wiki_summary</th>\n", "      <th>distance</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>[0.15548758208751678, -0.08017724752426147, -0...</td>\n", "      <td>0</td>\n", "      <td>New York, New York</td>\n", "      <td>New York, often called New York City or simply...</td>\n", "      <td>New York, often called New York City or simply...</td>\n", "      <td>0.743023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>[0.15548758208751678, -0.08017724752426147, -0...</td>\n", "      <td>6</td>\n", "      <td>New York, New York</td>\n", "      <td>New York is the geographical and demographic c...</td>\n", "      <td>New York, often called New York City or simply...</td>\n", "      <td>0.739733</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>[0.15548758208751678, -0.08017724752426147, -0...</td>\n", "      <td>7</td>\n", "      <td>New York, New York</td>\n", "      <td>With more than 20.1 million people in its metr...</td>\n", "      <td>New York, often called New York City or simply...</td>\n", "      <td>0.728218</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                              vector item_id  \\\n", "0  [0.15548758208751678, -0.08017724752426147, -0...       0   \n", "1  [0.15548758208751678, -0.08017724752426147, -0...       6   \n", "2  [0.15548758208751678, -0.08017724752426147, -0...       7   \n", "\n", "                state                                    sentence_chunks  \\\n", "0  New York, New York  New York, often called New York City or simply...   \n", "1  New York, New York  New York is the geographical and demographic c...   \n", "2  New York, New York  With more than 20.1 million people in its metr...   \n", "\n", "                                        wiki_summary  distance  \n", "0  New York, often called New York City or simply...  0.743023  \n", "1  New York, often called New York City or simply...  0.739733  \n", "2  New York, often called New York City or simply...  0.728218  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display\n", "\n", "# Retrieve top k documents\n", "context_data = store.retrieve_online_documents_v2(\n", "    features=[\n", "        \"city_embeddings:vector\",\n", "        \"city_embeddings:item_id\",\n", "        \"city_embeddings:state\",\n", "        \"city_embeddings:sentence_chunks\",\n", "        \"city_embeddings:wiki_summary\",\n", "    ],\n", "    query=query,\n", "    top_k=3,\n", "    distance_metric=\"COSINE\",\n", ").to_df()\n", "display(context_data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. Formatting Retrieved Documents for RAG Context\n", "\n", "After retrieving relevant documents, we need to format the data into a structured context that can be efficiently used in downstream applications. This step ensures that the extracted information is clean, organized, and ready for integration into the RAG pipeline."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["****START DOCUMENT 0****\n", "City & State = {New York, New York}\n", "Summary = {New York, often called New York City or simply NYC, is the most populous city in the United States, located at the southern tip of New York State on one of the world's largest natural harbors. The city comprises five boroughs, each of which is coextensive with a respective county. New York is a global center of finance and commerce, culture and technology, entertainment and media, academics and scientific output, and the arts and fashion, and, as home to the headquarters of the United Nations, is an important center for international diplomacy. New York City is the epicenter of the world's principal metropolitan economy.\n", "With an estimated population in 2022 of 8,335,897 distributed over 300.46 square miles (778.2 km2), the city is the most densely populated major city in the United States. New York has more than double the population of Los Angeles, the nation's second-most populous city. New York is the geographical and demographic center of both the Northeast megalopolis and the New York metropolitan area, the largest metropolitan area in the U.S. by both population and urban area. With more than 20.1 million people in its metropolitan statistical area and 23.5 million in its combined statistical area as of 2020, New York City is one of the world's most populous megacities. The city and its metropolitan area are the premier gateway for legal immigration to the United States. As many as 800 languages are spoken in New York, making it the most linguistically diverse city in the world. In 2021, the city was home to nearly 3.1 million residents born outside the U.S., the largest foreign-born population of any city in the world.\n", "New York City traces its origins to Fort Amsterdam and a trading post founded on the southern tip of Manhattan Island by Dutch colonists in approximately 1624. The settlement was named New Amsterdam (Dutch: Nieuw Amsterdam) in 1626 and was chartered as a city in 1653. The city came under English control in 1664 and was temporarily renamed New York after King <PERSON> granted the lands to his brother, the <PERSON> of York. before being permanently renamed New York in November 1674. New York City was the capital of the United States from 1785 until 1790. The modern city was formed by the 1898 consolidation of its five boroughs: Manhattan, Brooklyn, Queens, The Bronx, and Staten Island, and has been the largest U.S. city ever since.\n", "Anchored by Wall Street in the Financial District of Lower Manhattan, New York City has been called both the world's premier financial and fintech center and the most economically powerful city in the world. As of 2022, the New York metropolitan area is the largest metropolitan economy in the world with a gross metropolitan product of over US$2.16 trillion. If the New York metropolitan area were its own country, it would have the tenth-largest economy in the world. The city is home to the world's two largest stock exchanges by market capitalization of their listed companies: the New York Stock Exchange and Nasdaq. New York City is an established safe haven for global investors. As of 2023, New York City is the most expensive city in the world for expatriates to live. New York City is home to the highest number of billionaires, individuals of ultra-high net worth (greater than US$30 million), and millionaires of any city in the world.}\n", "****END DOCUMENT 0****\n"]}], "source": ["def format_documents(context_df):\n", "    output_context = \"\"\n", "    unique_documents = context_df.drop_duplicates().apply(\n", "        lambda x: \"City & State = {\"\n", "        + x[\"state\"]\n", "        + \"}\\nSummary = {\"\n", "        + x[\"wiki_summary\"].strip()\n", "        + \"}\",\n", "        axis=1,\n", "    )\n", "    for i, document_text in enumerate(unique_documents):\n", "        output_context += f\"****START DOCUMENT {i}****\\n{document_text.strip()}\\n****END DOCUMENT {i}****\"\n", "    return output_context\n", "\n", "\n", "RAG_CONTEXT = format_documents(context_data[[\"state\", \"wiki_summary\"]])\n", "print(RAG_CONTEXT)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4. Generating Responses Using Retrieved Context\n", "\n", "Now that we have formatted the retrieved documents, we can integrate them into a structured prompt for response generation. This step ensures that the assistant only relies on retrieved information and avoids hallucinating responses."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["FULL_PROMPT = f\"\"\"\n", "You are an assistant for answering questions about states. You will be provided documentation from Wikipedia. Provide a conversational answer.\n", "If you don't know the answer, just say \"I do not know.\" Don't make up an answer.\n", "\n", "Here are document(s) you should use when answer the users question:\n", "{RAG_CONTEXT}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The city with the largest population in New York is New York City itself, often referred to as NYC. It is the most populous city in the United States, with an estimated population of about 8.3 million in 2022.\n"]}], "source": ["response = llm_client.chat.completions.create(\n", "    model=\"gpt-4o-mini\",\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": FULL_PROMPT},\n", "        {\"role\": \"user\", \"content\": question},\n", "    ],\n", ")\n", "\n", "print(\"\\n\".join([c.message.content for c in response.choices]))"]}], "metadata": {"kernelspec": {"display_name": "feast", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}