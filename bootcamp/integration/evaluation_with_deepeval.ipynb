{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/integration/evaluation_with_deepeval.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/integration/evaluation_with_deepeval.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>\n", "</a>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Evaluation with DeepEval\n", "\n", "This guide demonstrates how to use [DeepEval](https://docs.confident-ai.com/) to evaluate a Retrieval-Augmented Generation (RAG) pipeline built upon [Milvus](https://milvus.io/).\n", "\n", "The RAG system combines a retrieval system with a generative model to generate new text based on a given prompt. The system first retrieves relevant documents from a corpus using Milvus, and then uses a generative model to generate new text based on the retrieved documents.\n", "\n", "DeepEval is a framework that helps you evaluate your RAG pipelines. There are existing tools and frameworks that help you build these pipelines but evaluating it and quantifying your pipeline performance can be hard. This is where DeepEval comes in."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prerequisites\n", "\n", "Before running this notebook, make sure you have the following dependencies installed:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["! pip install --upgrade pymilvus openai requests tqdm pandas deepeval"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> If you are using Google Colab, to enable dependencies just installed, you may need to **restart the runtime** (click on the \"Runtime\" menu at the top of the screen, and select \"Restart session\" from the dropdown menu)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["We will use OpenAI as the LLM in this example. You should prepare the [api key](https://platform.openai.com/docs/quickstart) `OPENAI_API_KEY` as an environment variable."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-*****************\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define the RAG pipeline\n", "\n", "We will define the RAG class that use Milvus as the vector store, and OpenAI as the LLM.\n", "The class contains the `load` method, which loads the text data into Milvus, the `retrieve` method, which retrieves the most similar text data to the given question, and the `answer` method, which answers the given question with the retrieved knowledge."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from typing import List\n", "from tqdm import tqdm\n", "from openai import OpenAI\n", "from pymilvus import MilvusClient\n", "\n", "\n", "class RAG:\n", "    \"\"\"\n", "    RAG(Retrieval-Augmented Generation) class built upon OpenAI and Milvus.\n", "    \"\"\"\n", "\n", "    def __init__(self, openai_client: OpenAI, milvus_client: MilvusClient):\n", "        self._prepare_openai(openai_client)\n", "        self._prepare_milvus(milvus_client)\n", "\n", "    def _emb_text(self, text: str) -> List[float]:\n", "        return (\n", "            self.openai_client.embeddings.create(input=text, model=self.embedding_model)\n", "            .data[0]\n", "            .embedding\n", "        )\n", "\n", "    def _prepare_openai(\n", "        self,\n", "        openai_client: OpenAI,\n", "        embedding_model: str = \"text-embedding-3-small\",\n", "        llm_model: str = \"gpt-4o-mini\",\n", "    ):\n", "        self.openai_client = openai_client\n", "        self.embedding_model = embedding_model\n", "        self.llm_model = llm_model\n", "        self.SYSTEM_PROMPT = \"\"\"\n", "            Human: You are an AI assistant. You are able to find answers to the questions from the contextual passage snippets provided.\n", "        \"\"\"\n", "        self.USER_PROMPT = \"\"\"\n", "            Use the following pieces of information enclosed in <context> tags to provide an answer to the question enclosed in <question> tags.\n", "            <context>\n", "            {context}\n", "            </context>\n", "            <question>\n", "            {question}\n", "            </question>\n", "        \"\"\"\n", "\n", "    def _prepare_milvus(\n", "        self, milvus_client: MilvusClient, collection_name: str = \"rag_collection\"\n", "    ):\n", "        self.milvus_client = milvus_client\n", "        self.collection_name = collection_name\n", "        if self.milvus_client.has_collection(self.collection_name):\n", "            self.milvus_client.drop_collection(self.collection_name)\n", "        embedding_dim = len(self._emb_text(\"demo\"))\n", "        self.milvus_client.create_collection(\n", "            collection_name=self.collection_name,\n", "            dimension=embedding_dim,\n", "            metric_type=\"IP\",\n", "            consistency_level=\"Strong\",\n", "        )\n", "\n", "    def load(self, texts: List[str]):\n", "        \"\"\"\n", "        Load the text data into Milvus.\n", "        \"\"\"\n", "        data = []\n", "        for i, line in enumerate(tqdm(texts, desc=\"Creating embeddings\")):\n", "            data.append({\"id\": i, \"vector\": self._emb_text(line), \"text\": line})\n", "        self.milvus_client.insert(collection_name=self.collection_name, data=data)\n", "\n", "    def retrieve(self, question: str, top_k: int = 3) -> List[str]:\n", "        \"\"\"\n", "        Retrieve the most similar text data to the given question.\n", "        \"\"\"\n", "        search_res = self.milvus_client.search(\n", "            collection_name=self.collection_name,\n", "            data=[self._emb_text(question)],\n", "            limit=top_k,\n", "            search_params={\"metric_type\": \"IP\", \"params\": {}},  # inner product distance\n", "            output_fields=[\"text\"],  # Return the text field\n", "        )\n", "        retrieved_texts = [res[\"entity\"][\"text\"] for res in search_res[0]]\n", "        return retrieved_texts[:top_k]\n", "\n", "    def answer(\n", "        self,\n", "        question: str,\n", "        retrieval_top_k: int = 3,\n", "        return_retrieved_text: bool = False,\n", "    ):\n", "        \"\"\"\n", "        Answer the given question with the retrieved knowledge.\n", "        \"\"\"\n", "        retrieved_texts = self.retrieve(question, top_k=retrieval_top_k)\n", "        user_prompt = self.USER_PROMPT.format(\n", "            context=\"\\n\".join(retrieved_texts), question=question\n", "        )\n", "        response = self.openai_client.chat.completions.create(\n", "            model=self.llm_model,\n", "            messages=[\n", "                {\"role\": \"system\", \"content\": self.SYSTEM_PROMPT},\n", "                {\"role\": \"user\", \"content\": user_prompt},\n", "            ],\n", "        )\n", "        if not return_retrieved_text:\n", "            return response.choices[0].message.content\n", "        else:\n", "            return response.choices[0].message.content, retrieved_texts"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's initialize the RAG class with OpenAI and Milvus clients."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["openai_client = OpenAI()\n", "milvus_client = MilvusClient(uri=\"./milvus_demo.db\")\n", "\n", "my_rag = RAG(openai_client=openai_client, milvus_client=milvus_client)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> As for the argument of `MilvusClient`:\n", "> - Setting the `uri` as a local file, e.g.`./milvus.db`, is the most convenient method, as it automatically utilizes [Milvus Lite](https://milvus.io/docs/milvus_lite.md) to store all data in this file.\n", "> - If you have large scale of data, you can set up a more performant Milvus server on [docker or kubernetes](https://milvus.io/docs/quickstart.md). In this setup, please use the server uri, e.g.`http://localhost:19530`, as your `uri`.\n", "> - If you want to use [Zilliz Cloud](https://zilliz.com/cloud), the fully managed cloud service for Milvus, adjust the `uri` and `token`, which correspond to the [Public Endpoint and Api key](https://docs.zilliz.com/docs/on-zilliz-cloud-console#free-cluster-details) in Zilliz Cloud."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run the RAG pipeline and get results\n", "\n", "We use the [Milvus development guide](https://github.com/milvus-io/milvus/blob/master/DEVELOPMENT.md) to be as the private knowledge in our RAG, which is a good data source for a simple RAG pipeline.\n", "\n", "Download it and load it into the rag pipeline."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Creating embeddings: 100%|██████████| 47/47 [00:20<00:00,  2.26it/s]\n"]}], "source": ["import urllib.request\n", "import os\n", "\n", "url = \"https://raw.githubusercontent.com/milvus-io/milvus/master/DEVELOPMENT.md\"\n", "file_path = \"./Milvus_DEVELOPMENT.md\"\n", "\n", "if not os.path.exists(file_path):\n", "    urllib.request.urlretrieve(url, file_path)\n", "with open(file_path, \"r\") as file:\n", "    file_text = file.read()\n", "\n", "text_lines = file_text.split(\"# \")\n", "my_rag.load(text_lines)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's define a query question about the content of the development guide documentation. And then use the `answer` method to get the answer and the retrieved context texts."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["('The hardware requirements specification to build and run Milvus from source code is as follows:\\n\\n- 8GB of RAM\\n- 50GB of free disk space',\n", " ['Hardware Requirements\\n\\nThe following specification (either physical or virtual machine resources) is recommended for Milvus to build and run from source code.\\n\\n```\\n- 8GB of RAM\\n- 50GB of free disk space\\n```\\n\\n##',\n", "  'Building Milvus on a local OS/shell environment\\n\\nThe details below outline the hardware and software requirements for building on Linux and MacOS.\\n\\n##',\n", "  \"Software Requirements\\n\\nAll Linux distributions are available for Milvus development. However a majority of our contributor worked with Ubuntu or CentOS systems, with a small portion of Mac (both x86_64 and Apple Silicon) contributors. If you would like <PERSON>l<PERSON>s to build and run on other distributions, you are more than welcome to file an issue and contribute!\\n\\nHere's a list of verified OS types where <PERSON><PERSON><PERSON><PERSON> can successfully build and run:\\n\\n- Debian/Ubuntu\\n- Amazon Linux\\n- MacOS (x86_64)\\n- MacOS (Apple Silicon)\\n\\n##\"])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["question = \"what is the hardware requirements specification if I want to build Milvus and run from source code?\"\n", "my_rag.answer(question, return_retrieved_text=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now let's prepare some questions with its corresponding ground truth answers. We get answers and contexts from our RAG pipeline."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/envs/zilliz/lib/python3.9/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "Answering questions: 100%|██████████| 3/3 [00:03<00:00,  1.06s/it]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>question</th>\n", "      <th>contexts</th>\n", "      <th>answer</th>\n", "      <th>ground_truth</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>what is the hardware requirements specificatio...</td>\n", "      <td>[Hardware Requirements\\n\\nThe following specif...</td>\n", "      <td>The hardware requirements specification to bui...</td>\n", "      <td>If you want to build Milvus and run from sourc...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>What is the programming language used to write...</td>\n", "      <td>[CM<PERSON> &amp; <PERSON>\\n\\nThe algorithm library of Mil...</td>\n", "      <td>The programming language used to write Knowher...</td>\n", "      <td>The programming language used to write Knowher...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>What should be ensured before running code cov...</td>\n", "      <td>[Code coverage\\n\\nBefore submitting your pull ...</td>\n", "      <td>Before running code coverage, it should be ens...</td>\n", "      <td>Before running code coverage, you should make ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                            question  \\\n", "0  what is the hardware requirements specificatio...   \n", "1  What is the programming language used to write...   \n", "2  What should be ensured before running code cov...   \n", "\n", "                                            contexts  \\\n", "0  [Hardware Requirements\\n\\nThe following specif...   \n", "1  [CMake & Conan\\n\\nThe algorithm library of Mil...   \n", "2  [Code coverage\\n\\nBefore submitting your pull ...   \n", "\n", "                                              answer  \\\n", "0  The hardware requirements specification to bui...   \n", "1  The programming language used to write Knowher...   \n", "2  Before running code coverage, it should be ens...   \n", "\n", "                                        ground_truth  \n", "0  If you want to build Milvus and run from sourc...  \n", "1  The programming language used to write Knowher...  \n", "2  Before running code coverage, you should make ...  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["from datasets import Dataset\n", "import pandas as pd\n", "\n", "question_list = [\n", "    \"what is the hardware requirements specification if I want to build Milvus and run from source code?\",\n", "    \"What is the programming language used to write Knowhere?\",\n", "    \"What should be ensured before running code coverage?\",\n", "]\n", "ground_truth_list = [\n", "    \"If you want to build Milvus and run from source code, the recommended hardware requirements specification is:\\n\\n- 8GB of RAM\\n- 50GB of free disk space.\",\n", "    \"The programming language used to write Knowhere is C++.\",\n", "    \"Before running code coverage, you should make sure that your code changes are covered by unit tests.\",\n", "]\n", "contexts_list = []\n", "answer_list = []\n", "for question in tqdm(question_list, desc=\"Answering questions\"):\n", "    answer, contexts = my_rag.answer(question, return_retrieved_text=True)\n", "    contexts_list.append(contexts)\n", "    answer_list.append(answer)\n", "\n", "df = pd.DataFrame(\n", "    {\n", "        \"question\": question_list,\n", "        \"contexts\": contexts_list,\n", "        \"answer\": answer_list,\n", "        \"ground_truth\": ground_truth_list,\n", "    }\n", ")\n", "rag_results = Dataset.from_pandas(df)\n", "df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluating Retriever\n", "\n", "When evaluating a retriever in large language model (LLM) systems, it's crucial to assess the following:\n", "\n", "1. **Ranking Relevance**: How effectively the retriever prioritizes relevant information over irrelevant data.\n", "   \n", "2. **Contextual Retrieval**: The ability to capture and retrieve contextually relevant information based on the input.\n", "\n", "3. **Balance**: How well the retriever manages text chunk size and retrieval scope to minimize irrelevancies.\n", "\n", "Together, these factors provide a comprehensive understanding of how the retriever prioritizes, captures, and presents the most useful information."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/envs/zilliz/lib/python3.9/site-packages/deepeval/__init__.py:49: UserWarning: You are using deepeval version 1.1.6, however version 1.2.2 is available. You should consider upgrading via the \"pip install --upgrade deepeval\" command.\n", "  warnings.warn(\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">✨ You're running DeepEval's latest <span style=\"color: #6a00ff; text-decoration-color: #6a00ff\">Contextual Precision Metric</span>! <span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">(</span><span style=\"color: #374151; text-decoration-color: #374151\">using gpt-4o, </span><span style=\"color: #374151; text-decoration-color: #374151\">strict</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">False</span><span style=\"color: #374151; text-decoration-color: #374151\">, </span><span style=\"color: #374151; text-decoration-color: #374151\">async_mode</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">True</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">)</span><span style=\"color: #374151; text-decoration-color: #374151\">...</span>\n", "</pre>\n"], "text/plain": ["✨ You're running DeepEval's latest \u001b[38;2;106;0;255mContextual Precision Metric\u001b[0m! \u001b[1;38;2;55;65;81m(\u001b[0m\u001b[38;2;55;65;81musing gpt-4o, \u001b[0m\u001b[38;2;55;65;81mstrict\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mFalse\u001b[0m\u001b[38;2;55;65;81m, \u001b[0m\u001b[38;2;55;65;81masync_mode\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mTrue\u001b[0m\u001b[1;38;2;55;65;81m)\u001b[0m\u001b[38;2;55;65;81m...\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">✨ You're running DeepEval's latest <span style=\"color: #6a00ff; text-decoration-color: #6a00ff\">Contextual Recall Metric</span>! <span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">(</span><span style=\"color: #374151; text-decoration-color: #374151\">using gpt-4o, </span><span style=\"color: #374151; text-decoration-color: #374151\">strict</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">False</span><span style=\"color: #374151; text-decoration-color: #374151\">, </span><span style=\"color: #374151; text-decoration-color: #374151\">async_mode</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">True</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">)</span><span style=\"color: #374151; text-decoration-color: #374151\">...</span>\n", "</pre>\n"], "text/plain": ["✨ You're running DeepEval's latest \u001b[38;2;106;0;255mContextual Recall Metric\u001b[0m! \u001b[1;38;2;55;65;81m(\u001b[0m\u001b[38;2;55;65;81musing gpt-4o, \u001b[0m\u001b[38;2;55;65;81mstrict\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mFalse\u001b[0m\u001b[38;2;55;65;81m, \u001b[0m\u001b[38;2;55;65;81masync_mode\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mTrue\u001b[0m\u001b[1;38;2;55;65;81m)\u001b[0m\u001b[38;2;55;65;81m...\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">✨ You're running DeepEval's latest <span style=\"color: #6a00ff; text-decoration-color: #6a00ff\">Contextual Relevancy Metric</span>! <span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">(</span><span style=\"color: #374151; text-decoration-color: #374151\">using gpt-4o, </span><span style=\"color: #374151; text-decoration-color: #374151\">strict</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">False</span><span style=\"color: #374151; text-decoration-color: #374151\">, </span><span style=\"color: #374151; text-decoration-color: #374151\">async_mode</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">True</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">)</span><span style=\"color: #374151; text-decoration-color: #374151\">...</span>\n", "</pre>\n"], "text/plain": ["✨ You're running DeepEval's latest \u001b[38;2;106;0;255mContextual Relevancy Metric\u001b[0m! \u001b[1;38;2;55;65;81m(\u001b[0m\u001b[38;2;55;65;81musing gpt-4o, \u001b[0m\u001b[38;2;55;65;81mstrict\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mFalse\u001b[0m\u001b[38;2;55;65;81m, \u001b[0m\u001b[38;2;55;65;81masync_mode\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mTrue\u001b[0m\u001b[1;38;2;55;65;81m)\u001b[0m\u001b[38;2;55;65;81m...\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Event loop is already running. Applying nest_asyncio patch to allow async execution...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Evaluating 3 test case(s) in parallel: |██████████|100% (3/3) [Time Taken: 00:11,  3.91s/test case]\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #05f58d; text-decoration-color: #05f58d\">✓</span> Tests finished 🎉! Run <span style=\"color: #008000; text-decoration-color: #008000\">'deepeval login'</span> to view evaluation results on Confident AI. \n", "‼️  NOTE: You can also run evaluations on ALL of deepeval's metrics directly on Confident AI instead.\n", "</pre>\n"], "text/plain": ["\u001b[38;2;5;245;141m✓\u001b[0m Tests finished 🎉! Run \u001b[32m'deepeval login'\u001b[0m to view evaluation results on Confident AI. \n", "‼️  NOTE: You can also run evaluations on ALL of deepeval's metrics directly on Confident AI instead.\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from deepeval.metrics import (\n", "    ContextualPrecisionMetric,\n", "    ContextualRecallMetric,\n", "    ContextualRelevancyMetric,\n", ")\n", "from deepeval.test_case import LLMTestCase\n", "from deepeval import evaluate\n", "\n", "contextual_precision = ContextualPrecisionMetric()\n", "contextual_recall = ContextualRecallMetric()\n", "contextual_relevancy = ContextualRelevancyMetric()\n", "\n", "test_cases = []\n", "\n", "for index, row in df.iterrows():\n", "    test_case = LLMTestCase(\n", "        input=row[\"question\"],\n", "        actual_output=row[\"answer\"],\n", "        expected_output=row[\"ground_truth\"],\n", "        retrieval_context=row[\"contexts\"],\n", "    )\n", "    test_cases.append(test_case)\n", "\n", "# test_cases\n", "result = evaluate(\n", "    test_cases=test_cases,\n", "    metrics=[contextual_precision, contextual_recall, contextual_relevancy],\n", "    print_results=False,  # Change to True to see detailed metric results\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluating Generation\n", "\n", "To assess the quality of generated outputs in large language models (LLMs), it's important to focus on two key aspects:\n", "\n", "1. **Relevance**: Evaluate whether the prompt effectively guides the LLM to generate helpful and contextually appropriate responses.\n", "   \n", "2. **Faithfulness**: Measure the accuracy of the output, ensuring the model produces information that is factually correct and free from hallucinations or contradictions. The generated content should align with the factual information provided in the retrieval context.\n", "\n", "These factors together ensure that the outputs are both relevant and reliable."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">✨ You're running DeepEval's latest <span style=\"color: #6a00ff; text-decoration-color: #6a00ff\">Answer Relevancy Metric</span>! <span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">(</span><span style=\"color: #374151; text-decoration-color: #374151\">using gpt-4o, </span><span style=\"color: #374151; text-decoration-color: #374151\">strict</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">False</span><span style=\"color: #374151; text-decoration-color: #374151\">, </span><span style=\"color: #374151; text-decoration-color: #374151\">async_mode</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">True</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">)</span><span style=\"color: #374151; text-decoration-color: #374151\">...</span>\n", "</pre>\n"], "text/plain": ["✨ You're running DeepEval's latest \u001b[38;2;106;0;255mAnswer Relevancy Metric\u001b[0m! \u001b[1;38;2;55;65;81m(\u001b[0m\u001b[38;2;55;65;81musing gpt-4o, \u001b[0m\u001b[38;2;55;65;81mstrict\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mFalse\u001b[0m\u001b[38;2;55;65;81m, \u001b[0m\u001b[38;2;55;65;81masync_mode\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mTrue\u001b[0m\u001b[1;38;2;55;65;81m)\u001b[0m\u001b[38;2;55;65;81m...\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">✨ You're running DeepEval's latest <span style=\"color: #6a00ff; text-decoration-color: #6a00ff\">Faithfulness Metric</span>! <span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">(</span><span style=\"color: #374151; text-decoration-color: #374151\">using gpt-4o, </span><span style=\"color: #374151; text-decoration-color: #374151\">strict</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">False</span><span style=\"color: #374151; text-decoration-color: #374151\">, </span><span style=\"color: #374151; text-decoration-color: #374151\">async_mode</span><span style=\"color: #374151; text-decoration-color: #374151\">=</span><span style=\"color: #374151; text-decoration-color: #374151; font-style: italic\">True</span><span style=\"color: #374151; text-decoration-color: #374151; font-weight: bold\">)</span><span style=\"color: #374151; text-decoration-color: #374151\">...</span>\n", "</pre>\n"], "text/plain": ["✨ You're running DeepEval's latest \u001b[38;2;106;0;255m<PERSON>ait<PERSON><PERSON> Metric\u001b[0m! \u001b[1;38;2;55;65;81m(\u001b[0m\u001b[38;2;55;65;81musing gpt-4o, \u001b[0m\u001b[38;2;55;65;81mstrict\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mFalse\u001b[0m\u001b[38;2;55;65;81m, \u001b[0m\u001b[38;2;55;65;81masync_mode\u001b[0m\u001b[38;2;55;65;81m=\u001b[0m\u001b[3;38;2;55;65;81mTrue\u001b[0m\u001b[1;38;2;55;65;81m)\u001b[0m\u001b[38;2;55;65;81m...\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Event loop is already running. Applying nest_asyncio patch to allow async execution...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Evaluating 3 test case(s) in parallel: |██████████|100% (3/3) [Time Taken: 00:11,  3.97s/test case]\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #05f58d; text-decoration-color: #05f58d\">✓</span> Tests finished 🎉! Run <span style=\"color: #008000; text-decoration-color: #008000\">'deepeval login'</span> to view evaluation results on Confident AI. \n", "‼️  NOTE: You can also run evaluations on ALL of deepeval's metrics directly on Confident AI instead.\n", "</pre>\n"], "text/plain": ["\u001b[38;2;5;245;141m✓\u001b[0m Tests finished 🎉! Run \u001b[32m'deepeval login'\u001b[0m to view evaluation results on Confident AI. \n", "‼️  NOTE: You can also run evaluations on ALL of deepeval's metrics directly on Confident AI instead.\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from deepeval.metrics import AnswerRelevancyMetric, FaithfulnessMetric\n", "from deepeval.test_case import LLMTestCase\n", "from deepeval import evaluate\n", "\n", "answer_relevancy = AnswerRelevancyMetric()\n", "faithfulness = FaithfulnessMetric()\n", "\n", "test_cases = []\n", "\n", "for index, row in df.iterrows():\n", "    test_case = LLMTestCase(\n", "        input=row[\"question\"],\n", "        actual_output=row[\"answer\"],\n", "        expected_output=row[\"ground_truth\"],\n", "        retrieval_context=row[\"contexts\"],\n", "    )\n", "    test_cases.append(test_case)\n", "\n", "# test_cases\n", "result = evaluate(\n", "    test_cases=test_cases,\n", "    metrics=[answer_relevancy, faithfulness],\n", "    print_results=False,  # Change to True to see detailed metric results\n", ")"]}], "metadata": {"kernelspec": {"display_name": "zilliz", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}