{"cells": [{"cell_type": "markdown", "metadata": {"collapsed": false}, "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/integration/evaluation_with_ragas.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/integration/evaluation_with_ragas.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>\n", "</a>\n"]}, {"cell_type": "markdown", "metadata": {"collapsed": false}, "source": ["# Evaluation with Ragas\n", "\n", "This guide demonstrates how to use Ragas to evaluate a Retrieval-Augmented Generation (RAG) pipeline built upon [Milvus](https://milvus.io/).\n", "\n", "The RAG system combines a retrieval system with a generative model to generate new text based on a given prompt. The system first retrieves relevant documents from a corpus using Milvus, and then uses a generative model to generate new text based on the retrieved documents.\n", "\n", "[Ragas](https://docs.ragas.io/en/latest/index.html#) is a framework that helps you evaluate your RAG pipelines. There are existing tools and frameworks that help you build these pipelines but evaluating it and quantifying your pipeline performance can be hard. This is where Ragas (RAG Assessment) comes in.\n", "\n", "\n", "\n", "\n", "## Prerequisites\n", "\n", "Before running this notebook, make sure you have the following dependencies installed:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["! pip install --upgrade pymilvus openai requests tqdm pandas ragas"]}, {"cell_type": "markdown", "metadata": {"collapsed": false}, "source": ["> If you are using Google Colab, to enable dependencies just installed, you may need to **restart the runtime** (click on the \"Runtime\" menu at the top of the screen, and select \"Restart session\" from the dropdown menu)."]}, {"cell_type": "markdown", "metadata": {"collapsed": false}, "source": ["We will use OpenAI as the LLM in this example. You should prepare the [api key](https://platform.openai.com/docs/quickstart) `OPENAI_API_KEY` as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-***********\""]}, {"cell_type": "markdown", "metadata": {"collapsed": false}, "source": ["## Define the RAG pipeline\n", "\n", "We will define the RAG class that use Milvus as the vector store, and OpenAI as the LLM.\n", "The class contains the `load` method, which loads the text data into Milvus, the `retrieve` method, which retrieves the most similar text data to the given question, and the `answer` method, which answers the given question with the retrieved knowledge."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["from typing import List\n", "from tqdm import tqdm\n", "from openai import OpenAI\n", "from pymilvus import MilvusClient\n", "\n", "\n", "class RAG:\n", "    \"\"\"\n", "    RAG (Retrieval-Augmented Generation) class built upon OpenAI and Milvus.\n", "    \"\"\"\n", "\n", "    def __init__(self, openai_client: OpenAI, milvus_client: MilvusClient):\n", "        self._prepare_openai(openai_client)\n", "        self._prepare_milvus(milvus_client)\n", "\n", "    def _emb_text(self, text: str) -> List[float]:\n", "        return (\n", "            self.openai_client.embeddings.create(input=text, model=self.embedding_model)\n", "            .data[0]\n", "            .embedding\n", "        )\n", "\n", "    def _prepare_openai(\n", "        self,\n", "        openai_client: OpenAI,\n", "        embedding_model: str = \"text-embedding-3-small\",\n", "        llm_model: str = \"gpt-3.5-turbo\",\n", "    ):\n", "        self.openai_client = openai_client\n", "        self.embedding_model = embedding_model\n", "        self.llm_model = llm_model\n", "        self.SYSTEM_PROMPT = \"\"\"\n", "Human: You are an AI assistant. You are able to find answers to the questions from the contextual passage snippets provided.\n", "\"\"\"\n", "        self.USER_PROMPT = \"\"\"\n", "Use the following pieces of information enclosed in <context> tags to provide an answer to the question enclosed in <question> tags.\n", "<context>\n", "{context}\n", "</context>\n", "<question>\n", "{question}\n", "</question>\n", "\"\"\"\n", "\n", "    def _prepare_milvus(\n", "        self, milvus_client: MilvusClient, collection_name: str = \"rag_collection\"\n", "    ):\n", "        self.milvus_client = milvus_client\n", "        self.collection_name = collection_name\n", "        if self.milvus_client.has_collection(self.collection_name):\n", "            self.milvus_client.drop_collection(self.collection_name)\n", "        embedding_dim = len(self._emb_text(\"foo\"))\n", "        self.milvus_client.create_collection(\n", "            collection_name=self.collection_name,\n", "            dimension=embedding_dim,\n", "            metric_type=\"IP\",  # Inner product distance\n", "            consistency_level=\"Strong\",  # Strong consistency level\n", "        )\n", "\n", "    def load(self, texts: List[str]):\n", "        \"\"\"\n", "        Load the text data into Milvus.\n", "        \"\"\"\n", "        data = []\n", "        for i, line in enumerate(tqdm(texts, desc=\"Creating embeddings\")):\n", "            data.append({\"id\": i, \"vector\": self._emb_text(line), \"text\": line})\n", "\n", "        self.milvus_client.insert(collection_name=self.collection_name, data=data)\n", "\n", "    def retrieve(self, question: str, top_k: int = 3) -> List[str]:\n", "        \"\"\"\n", "        Retrieve the most similar text data to the given question.\n", "        \"\"\"\n", "        search_res = self.milvus_client.search(\n", "            collection_name=self.collection_name,\n", "            data=[self._emb_text(question)],\n", "            limit=top_k,\n", "            search_params={\"metric_type\": \"IP\", \"params\": {}},  # Inner product distance\n", "            output_fields=[\"text\"],  # Return the text field\n", "        )\n", "        retrieved_texts = [res[\"entity\"][\"text\"] for res in search_res[0]]\n", "        return retrieved_texts[:top_k]\n", "\n", "    def answer(\n", "        self,\n", "        question: str,\n", "        retrieval_top_k: int = 3,\n", "        return_retrieved_text: bool = False,\n", "    ):\n", "        \"\"\"\n", "        Answer the given question with the retrieved knowledge.\n", "        \"\"\"\n", "        retrieved_texts = self.retrieve(question, top_k=retrieval_top_k)\n", "        user_prompt = self.USER_PROMPT.format(\n", "            context=\"\\n\".join(retrieved_texts), question=question\n", "        )\n", "        response = self.openai_client.chat.completions.create(\n", "            model=self.llm_model,\n", "            messages=[\n", "                {\"role\": \"system\", \"content\": self.SYSTEM_PROMPT},\n", "                {\"role\": \"user\", \"content\": user_prompt},\n", "            ],\n", "        )\n", "        if not return_retrieved_text:\n", "            return response.choices[0].message.content\n", "        else:\n", "            return response.choices[0].message.content, retrieved_texts"]}, {"cell_type": "markdown", "metadata": {"collapsed": false}, "source": ["Let's initialize the RAG class with OpenAI and Milvus clients."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["openai_client = OpenAI()\n", "milvus_client = MilvusClient(uri=\"./milvus_demo.db\")\n", "\n", "my_rag = RAG(openai_client=openai_client, milvus_client=milvus_client)"]}, {"cell_type": "markdown", "metadata": {"collapsed": false}, "source": ["> As for the argument of `MilvusClient`:\n", "> - Setting the `uri` as a local file, e.g.`./milvus.db`, is the most convenient method, as it automatically utilizes [Milvus Lite](https://milvus.io/docs/milvus_lite.md) to store all data in this file.\n", "> - If you have large scale of data, you can set up a more performant Milvus server on [docker or kubernetes](https://milvus.io/docs/quickstart.md). In this setup, please use the server uri, e.g.`http://localhost:19530`, as your `uri`.\n", "> - If you want to use [Zilliz Cloud](https://zilliz.com/cloud), the fully managed cloud service for Milvus, adjust the `uri` and `token`, which correspond to the [Public Endpoint and Api key](https://docs.zilliz.com/docs/on-zilliz-cloud-console#free-cluster-details) in Zilliz Cloud."]}, {"cell_type": "markdown", "metadata": {"collapsed": false}, "source": ["## Run the RAG pipeline and get results\n", "\n", "We use the [Milvus development guide](https://github.com/milvus-io/milvus/blob/master/DEVELOPMENT.md) to be as the private knowledge in our RAG, which is a good data source for a simple RAG pipeline.\n", "\n", "Download it and load it into the rag pipeline."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Creating embeddings: 100%|██████████| 27/27 [00:20<00:00,  1.34it/s]\n"]}], "source": ["import os\n", "import urllib.request\n", "\n", "url = \"https://raw.githubusercontent.com/milvus-io/milvus/master/DEVELOPMENT.md\"\n", "file_path = \"./Milvus_DEVELOPMENT.md\"\n", "\n", "if not os.path.exists(file_path):\n", "    urllib.request.urlretrieve(url, file_path)\n", "with open(file_path, \"r\") as file:\n", "    file_text = file.read()\n", "\n", "# We simply use \"# \" to separate the content in the file, which can roughly separate the content of each main part of the markdown file.\n", "text_lines = file_text.split(\"# \")\n", "my_rag.load(text_lines)  # Load the text data into RAG pipeline"]}, {"cell_type": "markdown", "metadata": {"collapsed": false}, "source": ["Let's define a query question about the content of the development guide documentation. And then use the `answer` method to get the answer and the retrieved context texts."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["('The hardware requirements specification for building Milvus and running it from source code is as follows:\\n\\n- 8GB of RAM\\n- 50GB of free disk space',\n", " ['Hardware Requirements\\n\\nThe following specification (either physical or virtual machine resources) is recommended for Milvus to build and run from source code.\\n\\n```yaml\\n- 8GB of RAM\\n- 50GB of free disk space\\n```\\n\\n##',\n", "  'Building Milvus on a local OS/shell environment\\n\\nThe details below outline the hardware and software requirements for building on Linux and MacOS.\\n\\n##',\n", "  \"Software Requirements\\n\\nAll Linux distributions are available for Milvus development. However a majority of our contributor worked with Ubuntu or CentOS systems, with a small portion of Mac (both x86_64 and Apple Silicon) contributors. If you would like <PERSON>l<PERSON>s to build and run on other distributions, you are more than welcome to file an issue and contribute!\\n\\nHere's a list of verified OS types where <PERSON><PERSON><PERSON><PERSON> can successfully build and run:\\n\\n- Debian/Ubuntu\\n- Amazon Linux\\n- MacOS (x86_64)\\n- MacOS (Apple Silicon)\\n\\n##\"])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["question = \"what is the hardware requirements specification if I want to build Milvus and run from source code?\"\n", "my_rag.answer(question, return_retrieved_text=True)"]}, {"cell_type": "markdown", "metadata": {"collapsed": false}, "source": ["Now let's prepare some questions with its corresponding ground truth answers. We get answers and contexts from our RAG pipeline."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Answering questions: 100%|██████████| 3/3 [00:04<00:00,  1.37s/it]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_input</th>\n", "      <th>retrieved_contexts</th>\n", "      <th>response</th>\n", "      <th>reference</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>what is the hardware requirements specificatio...</td>\n", "      <td>[Hardware Requirements\\n\\nThe following specif...</td>\n", "      <td>The hardware requirements specification to bui...</td>\n", "      <td>If you want to build Milvus and run from sourc...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>What is the programming language used to write...</td>\n", "      <td>[CM<PERSON> &amp; <PERSON>\\n\\nThe algorithm library of Mil...</td>\n", "      <td>The programming language used to write Knowher...</td>\n", "      <td>The programming language used to write Knowher...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>What should be ensured before running code cov...</td>\n", "      <td>[Code coverage\\n\\nBefore submitting your pull ...</td>\n", "      <td>Before running code coverage, it should be ens...</td>\n", "      <td>Before running code coverage, you should make ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                          user_input  \\\n", "0  what is the hardware requirements specificatio...   \n", "1  What is the programming language used to write...   \n", "2  What should be ensured before running code cov...   \n", "\n", "                                  retrieved_contexts  \\\n", "0  [Hardware Requirements\\n\\nThe following specif...   \n", "1  [CMake & Conan\\n\\nThe algorithm library of Mil...   \n", "2  [Code coverage\\n\\nBefore submitting your pull ...   \n", "\n", "                                            response  \\\n", "0  The hardware requirements specification to bui...   \n", "1  The programming language used to write Knowher...   \n", "2  Before running code coverage, it should be ens...   \n", "\n", "                                           reference  \n", "0  If you want to build Milvus and run from sourc...  \n", "1  The programming language used to write Knowher...  \n", "2  Before running code coverage, you should make ...  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["from ragas import EvaluationDataset\n", "from datasets import Dataset\n", "import pandas as pd\n", "\n", "user_input_list = [\n", "    \"what is the hardware requirements specification if I want to build Milvus and run from source code?\",\n", "    \"What is the programming language used to write Knowhere?\",\n", "    \"What should be ensured before running code coverage?\",\n", "]\n", "reference_list = [\n", "    \"If you want to build Milvus and run from source code, the recommended hardware requirements specification is:\\n\\n- 8GB of RAM\\n- 50GB of free disk space.\",\n", "    \"The programming language used to write Knowhere is C++.\",\n", "    \"Before running code coverage, you should make sure that your code changes are covered by unit tests.\",\n", "]\n", "retrieved_contexts_list = []\n", "response_list = []\n", "\n", "for user_input in tqdm(user_input_list, desc=\"Answering questions\"):\n", "    response, retrieved_context = my_rag.answer(user_input, return_retrieved_text=True)\n", "    retrieved_contexts_list.append(retrieved_context)\n", "    response_list.append(response)\n", "\n", "df = pd.DataFrame(\n", "    {\n", "        \"user_input\": user_input_list,\n", "        \"retrieved_contexts\": retrieved_contexts_list,\n", "        \"response\": response_list,\n", "        \"reference\": reference_list,\n", "    }\n", ")\n", "rag_results = EvaluationDataset.from_pandas(df)\n", "df"]}, {"cell_type": "markdown", "metadata": {"collapsed": false}, "source": ["## Evaluation with <PERSON><PERSON>\n", "\n", "We use Ragas to evaluate the performance of our RAG pipeline results.\n", "\n", "Ragas provides a set of metrics that is easy to use. We take `Answer relevancy`, `Faithfulness`, `Context recall`, and `Context precision` as the metrics to evaluate our RAG pipeline. For more information about the metrics, please refer to the [Ragas Metrics](https://docs.ragas.io/en/latest/concepts/metrics/index.html)."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Evaluating: 100%|██████████| 12/12 [00:10<00:00,  1.11it/s]\n"]}, {"data": {"text/plain": ["{'answer_relevancy': 0.9894, 'faithfulness': 1.0000, 'context_recall': 1.0000, 'context_precision': 1.0000}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from ragas import evaluate\n", "from ragas.metrics import AnswerRelevancy, Faithfulness, ContextRecall, ContextPrecision\n", "\n", "from ragas.llms import LangchainLLMWrapper\n", "from langchain_openai import ChatOpenAI\n", "\n", "llm = ChatOpenAI(model=\"gpt-4o-mini\")\n", "evaluator_llm = LangchainLLMWrapper(llm)\n", "\n", "results = evaluate(\n", "    dataset=rag_results,\n", "    metrics=[\n", "        AnswerRelevancy(llm=evaluator_llm),\n", "        Faithfulness(llm=evaluator_llm),\n", "        ContextRecall(llm=evaluator_llm),\n", "        ContextPrecision(llm=evaluator_llm),\n", "    ],\n", ")\n", "results"]}], "metadata": {"kernelspec": {"display_name": "datadog", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}