{"cells": [{"cell_type": "markdown", "id": "ffb811ee", "metadata": {}, "source": ["<a href=\"https://colab.research.google.com/drive/1GqAhNZ6_Fm3PN_wX69MiBe7Pc6g2PjtK#scrollTo=cf2bee4f-c0b2-49e1-8a9c-3688c2d5fb55\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>\n"]}, {"cell_type": "markdown", "id": "jj0Nx5D_7RVX", "metadata": {"id": "jj0Nx5D_7RVX"}, "source": ["# Improve retrieval quality of your LLM Application with AIMon and Milvus\n", "\n", "## Overview\n", "\n", "In this tutorial, we'll help you build a retrieval-augmented generation (RAG) chatbot that answers questions on a [meeting bank dataset](https://meetingbank.github.io/).\n", "\n", "In this tutorial you will learn to:\n", "\n", "*   Build an LLM application that answers a user's query related to the meeting bank dataset.\n", "*   Define and measure the quality of your LLM application.\n", "*   Improve the quality of your application using 2 incremental approaches: a vector DB using hybrid search and a state-of-the-art re-ranker.\n", "\n", "\n", "## Tech Stack\n", "\n", "#### *Vector Database*\n", "\n", "For this application, we will use [Milvus](https://milvus.io/) to manage and search large-scale unstructured data, such as text, images, and videos.\n", "\n", "#### *LLM Framework*\n", "LlamaIndex is an open-source data orchestration framework that simplifies building large language model (LLM) applications by facilitating the integration of private data with LLMs, enabling context-augmented generative AI applications through a Retrieval-Augmented Generation (RAG) pipeline. We will use LlamaIndex for this tutorial since it offers a good amount of flexibility and better lower level API abstractions.\n", "\n", "#### *LLM Output Quality Evaluation*\n", "[AIMon](https://www.aimon.ai) offers proprietary Judge models for Hallucination, Context Quality issues, Instruction Adherence of LLMs, Retrieval Quality and other LLM reliability tasks. We will use AIMon to judge the quality of the LLM application.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "b3eb7e8d-801d-4edd-9eb5-c39667812058", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "b3eb7e8d-801d-4edd-9eb5-c39667812058", "outputId": "3d65363b-a070-443f-c13d-0a440b65cdd1"}, "outputs": [], "source": ["!pip3 install -U gdown requests aimon llama-index-core llama-index-vector-stores-milvus pymilvus>=2.4.2 llama-index-postprocessor-aimon-rerank llama-index-embeddings-openai llama-index-llms-openai datasets fuzzywuzzy --quiet"]}, {"cell_type": "markdown", "id": "jX43bH9WR1YE", "metadata": {"id": "jX43bH9WR1YE"}, "source": ["# Pre-requisites\n", "\n", "1. Signup for an [AIMon account here](https://docs.aimon.ai/quickstart).\n", "\n", "  Add this secret to the Colab Secrets (the \"key\" symbol on the left panel)\n", "  > If you are in another non-google colab environment, please replace the google colab-related code yourself\n", "\n", "\n", "  * AIMON_API_KEY\n", "\n", "2. Signup for an [OpenAI account here](https://platform.openai.com/docs/overview) and add the following key in Colab secrets:\n", "  * OPENAI_API_KEY\n"]}, {"cell_type": "markdown", "id": "zfoCKZIToJe3", "metadata": {"id": "zfoCKZIToJe3"}, "source": ["### Required API keys"]}, {"cell_type": "code", "execution_count": null, "id": "YxiRLADLR0oG", "metadata": {"id": "YxiRLADLR0oG"}, "outputs": [], "source": ["import os\n", "\n", "# Check if the secrets are accessible\n", "from google.colab import userdata\n", "\n", "# Get this from the AIMon UI\n", "aimon_key = userdata.get(\"AIMON_API_KEY\")\n", "\n", "openai_key = userdata.get(\"OPENAI_API_KEY\")\n", "\n", "# Set OpenAI key as an environment variable as well\n", "os.environ[\"OPENAI_API_KEY\"] = openai_key"]}, {"cell_type": "markdown", "id": "WPKNyx7IGBt0", "metadata": {"id": "WPKNyx7IGBt0"}, "source": ["## Utility Functions\n", "\n", "This section contains utility functions that we will use throughout the notebook."]}, {"cell_type": "code", "execution_count": null, "id": "yVKx88Klo_UU", "metadata": {"id": "yVKx88Klo_UU"}, "outputs": [], "source": ["from openai import OpenAI\n", "\n", "oai_client = OpenAI(api_key=openai_key)\n", "\n", "\n", "def query_openai_with_context(query, context_documents, model=\"gpt-4o-mini\"):\n", "    \"\"\"\n", "    Sends a query along with context documents to the OpenAI API and returns the parsed response.\n", "\n", "    :param api_key: OpenAI API key\n", "    :param query: The user's query as a string\n", "    :param context_documents: A list of strings representing context documents\n", "    :param model: The OpenAI model to use (default is 'o3-mini')\n", "    :return: Response text from the OpenAI API\n", "    \"\"\"\n", "\n", "    # Combine context documents into a single string\n", "    context_text = \"\\n\\n\".join(context_documents)\n", "\n", "    # Construct the messages payload\n", "    messages = [\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": \"You are an AI assistant that provides accurate and helpful answers.\",\n", "        },\n", "        {\"role\": \"user\", \"content\": f\"Context:\\n{context_text}\\n\\nQuestion:\\n{query}\"},\n", "    ]\n", "\n", "    # Call OpenAI API\n", "    completion = oai_client.chat.completions.create(model=model, messages=messages)\n", "\n", "    # Extract and return the response text\n", "    return completion.choices[0].message.content"]}, {"cell_type": "markdown", "id": "cf2bee4f-c0b2-49e1-8a9c-3688c2d5fb55", "metadata": {"id": "cf2bee4f-c0b2-49e1-8a9c-3688c2d5fb55"}, "source": ["# Dataset\n", "\n", "We will use the [MeetingBank](https://meetingbank.github.io/) dataset which is a benchmark dataset created from the city councils of 6 major U.S. cities to supplement existing datasets. It contains 1,366 meetings with over 3,579 hours of video, as well as transcripts, PDF documents of meeting minutes, agenda, and other metadata.\n", "\n", "For this exercise, we have created a smaller dataset. It can be found [here](https://drive.google.com/drive/folders/1v3vJahKtadi_r-8VJAsDd2eaiSRenmsa?usp=drive_link)."]}, {"cell_type": "code", "execution_count": null, "id": "Eg2zlLPrWhcX", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Eg2zlLPrWhcX", "outputId": "1add7c04-c9b6-4049-8e43-3e55f2950896"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Folder '/content/meetingbank_train_split.hf' does not exist.\n"]}], "source": ["# Delete the dataset folder if it already exists\n", "\n", "import shutil\n", "\n", "folder_path = \"/content/meetingbank_train_split.hf\"\n", "\n", "if os.path.exists(folder_path):\n", "    try:\n", "        shutil.rmtree(folder_path)\n", "        print(f\"Folder '{folder_path}' and its contents deleted successfully.\")\n", "    except Exception as e:\n", "        print(f\"Error deleting folder '{folder_path}': {e}\")\n", "else:\n", "    print(f\"Folder '{folder_path}' does not exist.\")"]}, {"cell_type": "code", "execution_count": null, "id": "e256ffcd-9b95-4811-9612-3f5ca2589769", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "e256ffcd-9b95-4811-9612-3f5ca2589769", "outputId": "d94f2f51-d5c0-4110-da53-1f368c8461b3"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading...\n", "From: https://drive.google.com/uc?id=1bs4kwwiD30DUeCjuqEdOeixCuI-3i9F5\n", "To: /content/meetingbank_train_split.tar.gz\n", "100% 1.87M/1.87M [00:00<00:00, 104MB/s]\n", "Downloading...\n", "From: https://drive.google.com/uc?id=1fkxaS8eltjfkzws5BRXpVXnxl2Qxwy5F\n", "To: /content/score_metrics_relevant_examples_2.csv\n", "100% 163k/163k [00:00<00:00, 69.6MB/s]\n"]}], "source": ["# Download the dataset locally\n", "!gdown https://drive.google.com/uc?id=1bs4kwwiD30DUeCjuqEdOeixCuI-3i9F5\n", "!gdown https://drive.google.com/uc?id=1fkxaS8eltjfkzws5BRXpVXnxl2Qxwy5F"]}, {"cell_type": "code", "execution_count": null, "id": "q4OAledQRQ83", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "q4OAledQRQ83", "outputId": "8f6ec20a-94f7-4244-aebd-3c86ac86626e"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracted to: /content/\n"]}], "source": ["import tarfile\n", "from datasets import load_from_disk\n", "\n", "\n", "tar_file_path = \"/content/meetingbank_train_split.tar.gz\"\n", "extract_path = \"/content/\"\n", "\n", "# Extract the file\n", "with tarfile.open(tar_file_path, \"r:gz\") as tar:\n", "    tar.extractall(path=extract_path)\n", "\n", "print(f\"Extracted to: {extract_path}\")\n", "\n", "train_split = load_from_disk(extract_path + \"meetingbank_train_split.hf\")"]}, {"cell_type": "code", "execution_count": null, "id": "8cba49bb-a0ae-434c-a6af-d899f9e0b1d6", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8cba49bb-a0ae-434c-a6af-d899f9e0b1d6", "outputId": "f6877bae-14e8-49c7-97dc-eb5b7bc5b7df"}, "outputs": [{"data": {"text/plain": ["258"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["len(train_split)"]}, {"cell_type": "code", "execution_count": null, "id": "4ff3f046-8032-4fd2-acd7-b680700e3f91", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "4ff3f046-8032-4fd2-acd7-b680700e3f91", "outputId": "9a284a23-de02-4f9b-c257-08c5796a23a5"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total number of tokens in train split: 996944\n"]}], "source": ["# Total number of token across the entire set of transcripts\n", "# This is approximately 15M tokens in size\n", "total_tokens = sum(len(example[\"transcript\"].split()) for example in train_split)\n", "print(f\"Total number of tokens in train split: {total_tokens}\")"]}, {"cell_type": "code", "execution_count": null, "id": "c967f0b6-188f-46b5-9699-b96f27f81a35", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "c967f0b6-188f-46b5-9699-b96f27f81a35", "outputId": "9f835aa4-46ae-44a4-d713-f927e9da2bff"}, "outputs": [{"data": {"text/plain": ["3137"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# number of words ~= # of tokens\n", "len(train_split[1][\"transcript\"].split())"]}, {"cell_type": "code", "execution_count": null, "id": "45920b4b-9e79-4c7d-a178-ae205dc6d305", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 70}, "id": "45920b4b-9e79-4c7d-a178-ae205dc6d305", "outputId": "09f295cd-7fb4-41ad-9d59-93f124ba21d0"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["\"An assessment has called out council bill 161 for an amendment. Madam Secretary, will you please put 161 on the screen? <PERSON><PERSON> <PERSON>, will you make a motion to take 161 out of order? Want to remind everyone this motion is not debatable. Thank you, Mr. President. I move to take Council Bill 161 series of 2017. Out of order. All right. It's been moved the second it. Madam Secretary, roll call. SUSSMAN All right, Black. CLARK All right. Espinosa. Flynn. Gilmore. Herndon. Cashman can eat. LOPEZ \""]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# Show the first 500 characters of the transcript\n", "train_split[1][\"transcript\"][:500]"]}, {"cell_type": "code", "execution_count": null, "id": "fbe28657-b951-41c6-a935-1c41f5dada80", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fbe28657-b951-41c6-a935-1c41f5dada80", "outputId": "5a275f7c-c3d5-4bb9-d7fd-6e8e36694fbd"}, "outputs": [{"data": {"text/plain": ["3864.124031007752"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Average number of tokens per transcript\n", "import statistics\n", "\n", "statistics.mean(len(example[\"transcript\"].split()) for example in train_split)"]}, {"cell_type": "markdown", "id": "2cd22ac4-f133-477f-8fde-fa70ff5d4b6b", "metadata": {"id": "2cd22ac4-f133-477f-8fde-fa70ff5d4b6b"}, "source": ["### Analysis\n", "\n", "We have 258 transcripts with a total of about 1M tokens across all these transcripts. We have an average of 3864 number of tokens per transcript."]}, {"cell_type": "markdown", "id": "75094e49-2c5c-427e-984c-a610c258728e", "metadata": {"id": "75094e49-2c5c-427e-984c-a610c258728e"}, "source": ["| Metric | Value |\n", "|------------------|------------------|\n", "| Number of transcripts | 258 |\n", "| Total # tokens in the transcripts | 1M |\n", "| Avg. # tokens per transcript | 3864 |\n"]}, {"cell_type": "markdown", "id": "1352aa57-1258-4fa5-81b7-48af8cf74db5", "metadata": {"id": "1352aa57-1258-4fa5-81b7-48af8cf74db5"}, "source": ["### Queries\n", "\n", "Below are the 12 queries that we will run on the transcript above"]}, {"cell_type": "code", "execution_count": null, "id": "f492aca7-0402-490d-9291-1c85609e3816", "metadata": {"id": "f492aca7-0402-490d-9291-1c85609e3816"}, "outputs": [], "source": ["import pandas as pd\n", "\n", "queries_df = pd.read_csv(\"/content/score_metrics_relevant_examples_2.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "78216775-79df-4389-96df-20ba401396b7", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "78216775-79df-4389-96df-20ba401396b7", "outputId": "228ecd32-6281-4947-dfac-b64c195e22e4"}, "outputs": [{"data": {"text/plain": ["12"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["len(queries_df[\"Query\"])"]}, {"cell_type": "code", "execution_count": null, "id": "NmNkImyHpbMi", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "NmNkImyHpbMi", "outputId": "113e6342-c590-4a2a-eeee-02b1f67aa934"}, "outputs": [{"data": {"text/plain": ["['What was the key decision in the meeting?',\n", " 'What are the next steps for the team?',\n", " 'Summarize the meeting in 10 words.',\n", " 'What were the main points of discussion?',\n", " 'What decision was made regarding the project?',\n", " 'What were the outcomes of the meeting?',\n", " 'What was discussed in the meeting?',\n", " 'What examples were discussed for project inspiration?',\n", " 'What considerations were made for the project timeline?',\n", " 'Who is responsible for completing the tasks?',\n", " 'What were the decisions made in the meeting?',\n", " 'What did the team decide about the project timeline?']"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["queries_df[\"Query\"].to_list()"]}, {"cell_type": "markdown", "id": "e4a377d6-7a7c-4824-96c3-a5d39f1dd32a", "metadata": {"id": "e4a377d6-7a7c-4824-96c3-a5d39f1dd32a"}, "source": ["# Metric Definition\n", "\n", "This quality score metric will help us understand how good the LLM responses are for the set of queries above. To measure quality of our application, we will run a set of queries and aggregate the quality scores across all these queries.\n", "\n", "LLM Application Quality Score is a combination of 3 individual quality metrics from AIMon:\n", "\n", "1. **Hallucination Score** (hall_score): checks if the generated text is grounded in the provided context. A score closer to 1.0 means that there is a strong indication of hallucination and a score closer to 0.0 means a lower indication of hallucination. Hence, we will use (1.0-hall_score) here when computing the final quality score.\n", "2. **Instruction Adherence Score** (ia_score): checks if all explicit instructions provided have been followed by the LLM. The higher the ia_score the better the adherence to instructions. The lower the score, the poorer the adherence to instructions.\n", "3. **Retrieval Relevance Score** (rr_score): checks if the retrieved documents are relevant to the query. A score closer to 100.0 means perfect relevance of document to query and a score closer to 0.0 means poor relevance of document to query.\n", "\n", "`quality_score = 0.35 * (1.0 - hall_score) + 0.35 * ia_score + 0.3 * rr_score`"]}, {"cell_type": "code", "execution_count": null, "id": "17841793-90a5-4cdf-a7bc-2d43bf581813", "metadata": {"id": "17841793-90a5-4cdf-a7bc-2d43bf581813"}, "outputs": [], "source": ["# We will check the LLM response against these instructions\n", "instructions_to_evaluate = \"\"\"\n", "1. Ensure that the response answers all parts of the query completely.\n", "2. Ensure that the length of the response is under 50 words.\n", "3. The response must not contain any abusive language or toxic content.\n", "4. The response must be in a friendly tone.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "SXkF_SvdprIK", "metadata": {"id": "SXkF_SvdprIK"}, "outputs": [], "source": ["def compute_quality_score(aimon_response):\n", "    retrieval_rel_scores = aimon_response.detect_response.retrieval_relevance[0][\n", "        \"relevance_scores\"\n", "    ]\n", "    avg_retrieval_relevance_score = (\n", "        statistics.mean(retrieval_rel_scores) if len(retrieval_rel_scores) > 0 else 0.0\n", "    )\n", "    hall_score = aimon_response.detect_response.hallucination[\"score\"]\n", "    ia_score = aimon_response.detect_response.instruction_adherence[\"score\"]\n", "    return (\n", "        0.35 * (1.0 - hall_score)\n", "        + 0.35 * ia_score\n", "        + 0.3 * (avg_retrieval_relevance_score / 100)\n", "    ) * 100.0"]}, {"cell_type": "markdown", "id": "yJr3joQ1n9Gw", "metadata": {"id": "yJr3joQ1n9Gw"}, "source": ["# Setup AIMon\n", "\n", "As mentioned previously, AIMon will be used to judge the quality of the LLM application. [Documentation can be found here](https://docs.aimon.ai/)."]}, {"cell_type": "code", "execution_count": null, "id": "0vhJ2rzfn7pQ", "metadata": {"id": "0vhJ2rzfn7pQ"}, "outputs": [], "source": ["from aimon import Detect\n", "\n", "aimon_config = {\n", "    \"hallucination\": {\"detector_name\": \"default\"},\n", "    \"instruction_adherence\": {\"detector_name\": \"default\"},\n", "    \"retrieval_relevance\": {\"detector_name\": \"default\"},\n", "}\n", "task_definition = \"\"\"\n", "Your task is to grade the relevance of context document against a specified user query.\n", "The domain here is a meeting transcripts.\n", "\"\"\"\n", "\n", "values_returned = [\n", "    \"context\",\n", "    \"user_query\",\n", "    \"instructions\",\n", "    \"generated_text\",\n", "    \"task_definition\",\n", "]\n", "\n", "detect = Detect(\n", "    values_returned=values_returned,\n", "    api_key=userdata.get(\"AIMON_API_KEY\"),\n", "    config=aimon_config,\n", "    publish=True,  # This publishes results to the AIMon UI\n", "    application_name=\"meeting_bot_app\",\n", "    model_name=\"OpenAI-gpt-4o-mini\",\n", ")"]}, {"cell_type": "markdown", "id": "793868ad-d52e-4910-ba5f-83384683f261", "metadata": {"id": "793868ad-d52e-4910-ba5f-83384683f261"}, "source": ["#  1. Simple, brute-force approach  \n", "\n", "In this first simple approach, we will use Levenshtein Distance to match a document with a given query. The top 3 documents with the best match will be sent to the LLM as context for answering.\n", "\n", "**NOTE: This cell will take about 3 mins to execute**\n", "\n", "Enjoy your favorite beverage while you wait :)"]}, {"cell_type": "code", "execution_count": null, "id": "aba6efe9-4b36-4643-ab3c-b7221c125b9e", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "aba6efe9-4b36-4643-ab3c-b7221c125b9e", "outputId": "bc72b599-ec34-40b2-985d-57adc491c010"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.11/dist-packages/fuzzywuzzy/fuzz.py:11: UserWarning: Using slow pure-python SequenceMatcher. Install python-Levenshtein to remove this warning\n", "  warnings.warn('Using slow pure-python SequenceMatcher. Install python-Levenshtein to remove this warning')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Avg. Retrieval relevance score across chunks: 14.276227385821016 for query: What was the key decision in the meeting?\n", "Avg. Retrieval relevance score across chunks: 13.863050225148754 for query: What are the next steps for the team?\n", "Avg. Retrieval relevance score across chunks: 9.684561480011666 for query: Summarize the meeting in 10 words.\n", "Avg. Retrieval relevance score across chunks: 15.117995085759617 for query: What were the main points of discussion?\n", "Avg. Retrieval relevance score across chunks: 15.017772942191954 for query: What decision was made regarding the project?\n", "Avg. Retrieval relevance score across chunks: 14.351198844659052 for query: What were the outcomes of the meeting?\n", "Avg. Retrieval relevance score across chunks: 17.26337936069342 for query: What was discussed in the meeting?\n", "Avg. Retrieval relevance score across chunks: 14.45748737410213 for query: What examples were discussed for project inspiration?\n", "Avg. Retrieval relevance score across chunks: 14.69838895812785 for query: What considerations were made for the project timeline?\n", "Avg. Retrieval relevance score across chunks: 11.528360411352168 for query: Who is responsible for completing the tasks?\n", "Avg. Retrieval relevance score across chunks: 16.55915192723114 for query: What were the decisions made in the meeting?\n", "Avg. Retrieval relevance score across chunks: 14.995106827925042 for query: What did the team decide about the project timeline?\n", "Time taken: 169.34546852111816 seconds\n"]}], "source": ["from fuzzywuzzy import process\n", "import time\n", "\n", "# List of documents\n", "documents = [t[\"transcript\"] for t in train_split]\n", "\n", "\n", "@detect\n", "def get_fuzzy_match_response(query, docs):\n", "    response = query_openai_with_context(query, docs)\n", "    return docs, query, instructions_to_evaluate, response, task_definition\n", "\n", "\n", "st = time.time()\n", "quality_scores_bf = []\n", "avg_retrieval_rel_scores_bf = []\n", "responses = {}\n", "for user_query in queries_df[\"Query\"].to_list():\n", "    best_match = process.extractBests(user_query, documents)\n", "    matched_docs = [b[0][:2000] for b in best_match]\n", "    _, _, _, llm_res, _, aimon_response = get_fuzzy_match_response(\n", "        user_query, matched_docs[:1]\n", "    )\n", "    # These show the average retrieval relevance scores per query.\n", "    retrieval_rel_scores = aimon_response.detect_response.retrieval_relevance[0][\n", "        \"relevance_scores\"\n", "    ]\n", "    avg_retrieval_rel_score_per_query = (\n", "        statistics.mean(retrieval_rel_scores) if len(retrieval_rel_scores) > 0 else 0.0\n", "    )\n", "    avg_retrieval_rel_scores_bf.append(avg_retrieval_rel_score_per_query)\n", "    print(\n", "        \"Avg. Retrieval relevance score across chunks: {} for query: {}\".format(\n", "            avg_retrieval_rel_score_per_query, user_query\n", "        )\n", "    )\n", "    quality_scores_bf.append(compute_quality_score(aimon_response))\n", "    responses[user_query] = llm_res\n", "print(\"Time taken: {} seconds\".format(time.time() - st))"]}, {"cell_type": "code", "execution_count": null, "id": "SpoYFhAvl7-y", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "SpoYFhAvl7-y", "outputId": "351bcfd5-0ed4-4cac-fb80-64f4b5613129"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Average Quality score for brute force approach: 51.750446187242254\n"]}], "source": ["# This is the average quality score.\n", "avg_quality_score_bf = statistics.mean(quality_scores_bf)\n", "print(\"Average Quality score for brute force approach: {}\".format(avg_quality_score_bf))"]}, {"cell_type": "code", "execution_count": null, "id": "U8-OFNGgvc-Q", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "U8-OFNGgvc-Q", "outputId": "fa885920-810b-4df8-d791-5d79fd59b8a9"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Average retrieval relevance score for brute force approach: 14.31772340191865\n"]}], "source": ["# This is the average retrieval relevance score.\n", "avg_retrieval_rel_score_bf = statistics.mean(avg_retrieval_rel_scores_bf)\n", "print(\n", "    \"Average retrieval relevance score for brute force approach: {}\".format(\n", "        avg_retrieval_rel_score_bf\n", "    )\n", ")"]}, {"cell_type": "markdown", "id": "WsmpZskWQ6Yo", "metadata": {"id": "WsmpZskWQ6Yo"}, "source": ["This is a **baseline** LLM app quality score. You can also see the individual metrics like hallucination scores etc. computed by AIMon on the [AIMon UI](https://www.app.aimon.ai/llmapps?source=sidebar&stage=production)"]}, {"cell_type": "markdown", "id": "0f414543-2667-4eac-a02f-912b8fa85a24", "metadata": {"id": "0f414543-2667-4eac-a02f-912b8fa85a24"}, "source": ["# 2. Use a VectorDB (Milvus) for document retrieval\n", "\n", "Now, we will improve the quality score by adding in a vector DB. This will also help improve query latency compared to the previous approach.\n", "\n", "There are two main components we need to be aware of: Ingestion and RAG based Q&A. The ingestion pipeline processes the transcripts from the Meeting Bank dataset and stores it in the Milvus Vector database. The RAG Q&A pipeline processes a user query by first retrieving the relevant documents from the vector store. These documents will then be used as grounding documents for the LLM to generate its response. We leverage AIMon to calculate the quality score and continuously monitor the application for [hallucination](https://docs.aimon.ai/detectors/hallucination), , [instruction adherence](https://docs.aimon.ai/detectors/instruction_adherence),  [context relevance](https://docs.aimon.ai/checker-models/context_relevance). These are the same 3 metrics we used to define the `quality` score above."]}, {"cell_type": "markdown", "id": "65hl5QnJnd1U", "metadata": {"id": "65hl5QnJnd1U"}, "source": ["\n", "![image.png](data:image/png;base64,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************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************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****************************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)"]}, {"cell_type": "markdown", "id": "M-osS6oae5bb", "metadata": {"id": "M-osS6oae5bb"}, "source": ["Below are some utility functions to pre-process and compute embeddings for documents."]}, {"cell_type": "code", "execution_count": null, "id": "4c59639a-6b79-49ef-b128-93eaae3244bb", "metadata": {"id": "4c59639a-6b79-49ef-b128-93eaae3244bb"}, "outputs": [], "source": ["import json\n", "import requests\n", "import pandas as pd\n", "from llama_index.core import Document\n", "\n", "\n", "## Function to preprocess text.\n", "def preprocess_text(text):\n", "    text = \" \".join(text.split())\n", "    return text\n", "\n", "\n", "## Function to process all URLs and create LlamaIndex Documents.\n", "def extract_and_create_documents(transcripts):\n", "\n", "    documents = []\n", "\n", "    for indx, t in enumerate(transcripts):\n", "        try:\n", "            clean_text = preprocess_text(t)\n", "            doc = Document(text=clean_text, metadata={\"index\": indx})\n", "            documents.append(doc)\n", "        except Exception as e:\n", "            print(f\"Failed to process transcript number {indx}: {str(e)}\")\n", "\n", "    return documents\n", "\n", "\n", "documents = extract_and_create_documents(train_split[\"transcript\"])"]}, {"cell_type": "markdown", "id": "xXioXrNQezcV", "metadata": {"id": "xXioXrNQezcV"}, "source": ["Setup an Open AI based embedding computation model."]}, {"cell_type": "code", "execution_count": null, "id": "IBSSYBXpA4OS", "metadata": {"id": "IBSSYBXpA4OS"}, "outputs": [], "source": ["from llama_index.embeddings.openai import OpenAIEmbedding\n", "\n", "embedding_model = OpenAIEmbedding(\n", "    model=\"text-embedding-3-small\", embed_batch_size=100, max_retries=3\n", ")"]}, {"cell_type": "markdown", "id": "2-Yuv7vapbiY", "metadata": {"id": "2-Yuv7vapbiY"}, "source": ["In this cell, we compute the embeddings for the `documents` and index them into the MilvusVectorStore."]}, {"cell_type": "code", "execution_count": null, "id": "1mGWXjgOlYu2", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "1mGWXjgOlYu2", "outputId": "6ee7fa8d-5581-42b4-8129-90432ed52754"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-04-10 20:40:51,855 [DEBUG][_create_connection]: Created new connection using: 24fee991f1f64fadb3461a7d99fcd4dd (async_milvus_client.py:600)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Execution time: 38.74 seconds\n"]}], "source": ["from llama_index.core import VectorStoreIndex, StorageContext\n", "from llama_index.vector_stores.milvus import MilvusVectorStore\n", "\n", "vector_store = MilvusVectorStore(\n", "    uri=\"./aimon_embeddings.db\",\n", "    collection_name=\"meetingbanks\",\n", "    dim=1536,\n", "    overwrite=True,\n", ")\n", "storage_context = StorageContext.from_defaults(vector_store=vector_store)\n", "\n", "index = VectorStoreIndex.from_documents(\n", "    documents=documents, storage_context=storage_context\n", ")"]}, {"cell_type": "markdown", "id": "aoKmtNvUp9a8", "metadata": {"id": "aoKmtNvUp9a8"}, "source": ["Now that the VectorDB index has been setup, we will leverage it to answer user queries. In the cells below, we will create a retriever, setup the LLM and build a LLamaIndex Query Engine that interfaces with the retriever and the LLM to answer a user's questions."]}, {"cell_type": "code", "execution_count": null, "id": "RSIKUYE4ljub", "metadata": {"id": "RSIKUYE4ljub"}, "outputs": [], "source": ["from llama_index.core.retrievers import VectorIndexRetriever\n", "from llama_index.core.query_engine import RetrieverQueryEngine\n", "\n", "retriever = VectorIndexRetriever(index=index, similarity_top_k=5)\n", "\n", "# The system prompt that will be used for the LLM\n", "system_prompt = \"\"\"\n", "                Please be professional and polite.\n", "                Answer the user's question in a single line.\n", "                \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "T3k2ICSynj99", "metadata": {"id": "T3k2ICSynj99"}, "outputs": [], "source": ["## OpenAI's LLM, we will use GPT-4o-mini here since it is a fast and cheap LLM\n", "from llama_index.llms.openai import OpenAI\n", "\n", "llm = OpenAI(model=\"gpt-4o-mini\", temperature=0.1, system_prompt=system_prompt)"]}, {"cell_type": "code", "execution_count": null, "id": "CSLh6PHinqIQ", "metadata": {"id": "CSLh6PHinqIQ"}, "outputs": [], "source": ["from llama_index.core.query_engine import RetrieverQueryEngine\n", "\n", "query_engine = RetrieverQueryEngine.from_args(retriever, llm)"]}, {"cell_type": "markdown", "id": "DjUepKC5qgnR", "metadata": {"id": "DjUepKC5qgnR"}, "source": ["At this point, the query engine, retriever and LLM has been setup. Next, we setup AIMon to help us measure quality scores. We use the same `@detect` decorator that was created in the previous cells above. The only additional code in `ask_and_validate` here is to help AIMon interface with LLamaIndex's retrieved document \"nodes\".  "]}, {"cell_type": "code", "execution_count": null, "id": "GGey7dRQnz1i", "metadata": {"id": "GGey7dRQnz1i"}, "outputs": [], "source": ["import logging\n", "\n", "\n", "@detect\n", "def ask_and_validate(user_query, user_instructions, query_engine=query_engine):\n", "\n", "    response = query_engine.query(user_query)\n", "\n", "    ## Nested function to retrieve context and relevance scores from the LLM response.\n", "    def get_source_docs(chat_response):\n", "        contexts = []\n", "        relevance_scores = []\n", "        if hasattr(chat_response, \"source_nodes\"):\n", "            for node in chat_response.source_nodes:\n", "                if (\n", "                    hasattr(node, \"node\")\n", "                    and hasattr(node.node, \"text\")\n", "                    and hasattr(node, \"score\")\n", "                    and node.score is not None\n", "                ):\n", "                    contexts.append(node.node.text)\n", "                    relevance_scores.append(node.score)\n", "                elif (\n", "                    hasattr(node, \"text\")\n", "                    and hasattr(node, \"score\")\n", "                    and node.score is not None\n", "                ):\n", "                    contexts.append(node.text)\n", "                    relevance_scores.append(node.score)\n", "                else:\n", "                    logging.info(\"Node does not have required attributes.\")\n", "        else:\n", "            logging.info(\"No source_nodes attribute found in the chat response.\")\n", "        return contexts, relevance_scores\n", "\n", "    context, relevance_scores = get_source_docs(response)\n", "    return context, user_query, user_instructions, response.response, task_definition"]}, {"cell_type": "code", "execution_count": null, "id": "9O7-9<PERSON><PERSON><PERSON><PERSON><PERSON>", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "9O7-9<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputId": "37b4f8dd-5eec-4f3e-b5c5-28feed67bf51"}, "outputs": [{"data": {"text/plain": ["([\"I know in in New Mexico on some of the reservations, there are people actually doing filming, too, now of some of the elders to make sure that that history is documented and passed on, because it isn't translated in many of the history books you get in your public education system. So I again, just am happy to support this and again commend <PERSON><PERSON> <PERSON> for his efforts in our Indian commission and the work that you all have done with our entire entire community. Thank you, Mr. President. Thank you, Councilwoman. Council<PERSON>, I see you back up. Yeah. You know, I wanted to really emphasize the 10th, Monday, the 10th and proclamation that will be here in the quarters we'd love for. And I wanted to make sure the community because we do have community folks, I want to make sure that we come back on the 10th because we would like to give not only the proclamation, but a copy of the bill over. Right. And and ceremoniously and also just for the community. I know this Saturday I didn't mention this, but the Saturday is going to be, in addition to all the events, a rally at the Capitol at 1130. I mean, 130, 130. You mean marches coming from all over the city and they're going to be here. Good celebration of all directions, all nations. And that that's really when you when you look at the what it really means is all directions all nations for went. Thank you. Thank you, <PERSON><PERSON>. <PERSON>am Secretary. <PERSON><PERSON>. Hi. New Ortega I Black High Clerk by <PERSON><PERSON><PERSON><PERSON>. <PERSON><PERSON> <PERSON>. Hi <PERSON> I <PERSON> in Cashman. I can eat. Mr. President. I please close voting in US results. 12 eyes. 12 eyes conceal. 801 passes. Thank you. Thank you. Thank you. You don't get many claps for votes anymore. Thank you. All right. We are moving to the <PERSON> votes. All other bills for introductions are now ordered published. Councilman, clerk, will you please put the resolutions for adoption and the bills for final considerations? Consideration for final passage on the floor. Thank you, Mr. President. I move that resolutions be adopted and bills on final consideration be placed upon final consideration, and do pass in a block for the following items. 539 811 816. 812. 813. 814. 820. 821. 822. 800. 815. Eight 1724. 761 797. All right. It has been moved. And second, it councilmembers. Please remember that this is a consent or block vote and you will need to vote I or otherwise. This is your last chance to call out an item for a separate vote. Madam Secretary, roll call. Black. I Clark. II. Espinosa, i Flynn, i Gilmore. I Herndon I Cashman. I can eat. I knew. I. Ortega, I. Mr. President. I. Please close the voting. Announce a results. 11 Eyes. 11 Eyes. The resolution resolutions have been adopted and bills have been placed on final consideration and do pass. Tonight there will be a required public hearing for Council Bill 430 Changes on a classification of four Geneva Court and Martin Luther King Jr Boulevard.\",\n", "  \"Thank you, gentlemen. <PERSON>, can you please place Council Bill 376 on the floor for a vote? Thank you, <PERSON>am President. I move that council bill three 76/3 of 2015 be placed upon final, final consideration and do pass. Thank you. It's been moved and seconded. Comments by members of council. Councilwoman <PERSON>. Thank you, <PERSON>am President. This is an ordinance that lends money to a developer for relocation costs of a project that's very important along Morrison Road. I approve of the project. I even approve of doing this relocation cost. But I am not willing to do is to lend more money to this specific developer. In a previous deal we had not only a financial deal with the developer, but there were two subsequent amendments to that deal, both of which were to the benefit of the developer, not the taxpayer. And so I am very picky about who I lend money to, and I'm going to say no today. Okay. Thank you, <PERSON><PERSON>. Thank you, Madam President. I do have something to say about this council, bill. Yes? This is Saint Charles Holding Company as the developer of the site. Here's the problem. The problem is this site has been blighted for decades. And in this site, it's not like it's been empty. There have been folks who are living in these conditions that have been substandard and Denver and it's just not right. And we've talked about it for eight years. We looked at opportunities to what can we do to help improve the living conditions here for folks. And there was a lot of unanswered questions and a lot of people who how we had the ability to do it but are afraid to take the risk, afraid to do, afraid to come forward and basically not participate at all. That was true up into the point where Saint Charles Town Company and I think Charles Holding Company here said, you know, we'll do it. Will help will help not only improve the conditions here at this site by acquiring it, but will help trigger the Federal Relocation Act with the city. The city said, we will do this with you. There are folks who are living there who, because of this development, will be able to finally live anywhere else, be able to get benefits for it, relocation costs. And when all these units are built at 60%, am I going to be able to have first refusal, meaning they get the first choice to come back and this is how it should happen. And we can't rely just on VHA or some of the nonprofit folks who are already up. You know, they have their hands full. They don't have enough resources. They're begging for money. They're all fighting over the same pot of money, the same federal pot of money. It should we should actually be working with folks who are in the for profit development side that are willing to do this. And they've done it before on Alameda and Sheridan with those altos down. I mean, it's a very good project, filled a huge need in this city for affordable housing. That's what this does. And now affordable housing in the kind where you know that nobody takes care of and it's forgotten about. And when you complain, you either get kicked out or you just deal with it. Right. But this is the kind of housing these are the kind of units, units that will be maintained that are high quality standard of living, exactly what folks are needing and deserving in this neighborhood. And these are the folks that are willing to do the work. They've been doing the work with the community. It takes partnership from the city. This will help finalize those costs, help those folks find a place to live that way. They're not on the street while this develops or when they come back. I guarantee everybody is going to be standing there wanting to cut that ribbon. So that's what this is all about. And I urge my colleagues to support it. Thank you. Thank you, Councilman Lopez. Madam Secretary. Roll call. Fats? No. Lemon Lopez Monteiro. Hi, Nevitt. Hi, Ortega. Hi, Rob. Shepherd Sussman. Hi, Brooks. Hi, Brown. Hi, Sussman. There's no opportunity for me to click on I. Okay, I'll do it. Madam President. I voted. I call to him. He says, When were you able to vote? No, there's no. I voted for her. Okay. And what was the vote? Yes.\",\n", "  \"This year we may talk trash once in a while, but a manager got an AHA. You run a very good shop with a great manager at his helm. So thank you. Thank you, <PERSON><PERSON>, Councilwoman <PERSON><PERSON>. And thank you, <PERSON>am President. I also want to take the opportunity to extend my appreciation. I wish I knew all 1100 employees. But here's what I do know that public works does everything from Keep Denver Beautiful this far as graffiti, graffiti removal all the way to major projects. I'm very mindful of the role that Public Works played in the redevelopment of Denver Union Station and the work that you're doing currently regarding I-70 and the National Western Stock Show. And we couldn't we couldn't do wouldn't be responsive if we didn't have the help of solid waste. Also, permitting and enforcement have worked with a lot of people, their street maintenance. And then of course, <PERSON>, I have an inbox of a lot of emails that you and I have, so I'm going to have to start deleting some of those. I also want to extend my thank you to host <PERSON> for all of the work that you do and that and for your steadfast, steadfast leadership. And also to <PERSON>, who you're there when <PERSON> is away from the helm. And I really appreciate that. So congratulations again. Let me see if I got these names right. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON>. So I hope I got everybody's name. Thank you. Thank you, <PERSON><PERSON>. All right. Looks like we're ready for the vote. <PERSON>, <PERSON>. <PERSON>, I. But I. <PERSON>, I can eat lemon. <PERSON> All right. <PERSON><PERSON> I love it. <PERSON>, <PERSON>tega. Hi. <PERSON>am President. I am close to voting out the results. 11 eyes. 11 eyes. The proclamation is adopted, Councilman <PERSON> or somebody would like to call up to the podium. Yes, <PERSON>am President, I expected my colleagues to support this, but I hope no one out there was sort of insulted with some of the comments. I would like to call up the interesting, sexy, cool and strong executive director of the <PERSON> <PERSON> Department, which is an interesting, sexy, cool and strong department. <PERSON> one. Councilwoman <PERSON>. <PERSON>. I'm single network secretary, director of Public Works and I want to thank on behalf of Public Works this proclamation. It is truly an honor to be part of this organization and work with these 1100 people that I would say are fully committed not only to the council priority by the mayor's priorities, but also the stakeholders priorities, and be able to mix all these priorities together and come up with public works priority, which is to create a smart city, meaning a sustainable city, a city that provides mobility in a safe way and attractive city resiliency, and also a process to be the most transparent process that we can deliver. Somebody says, I was reading this book the other day. Somebody says that the public space is the the visible face of society. And I do believe that. I think that that's how we judge cities when we go around the world and come back. I like to talk a little bit, spend a little bit of time talking about the ten employees that we're honoring tonight, because I think it's very important to mention exactly what these employees have been working and being part of. Jason Rediker from Capital Management recently designed two very critical storm sewer projects. One of them have been in neighborhood. And the other one is at first and university, which is under construction in your district. Chloe Thompson from Finance Administration. She is one of our first black built from the academy. She has worked very hard to improve and develop new models for for the financial track in streamlining contracts, contracting and putting in place a more efficient procurement process. Adrian Goldman from Fleet Management. Adrian was very close with our fleet technicians downloading software that helps to better diagnose vehicles and speed up the repair process. Lewis Gardner From Right of Way Enforcement in permitting, Lewis is a very diligent vehicle investigator who goes above and beyond to assist not only the public but also the the in the agency. He volunteers to maintain city's vehicle inventory and has taken the lead role a role a role in making sure that the motors workshop is free of hazardous materials and and mark problems. Jeremy Hammer from right of way services. He's our lead on floodplain issues. He's responsible for very complex flood floodplains and drainage issues.\",\n", "  \"So. So I think that this is a fitting combination to have these tonight. And I will be happily voting in support of this. Thank you. Thank you, Councilwoman. Councilman <PERSON><PERSON><PERSON><PERSON>, I saw you click in. I did, but I. I'll reserve my comments. Okay, great. We have. Let's see. No other comments. Councilman <PERSON><PERSON><PERSON>. Sorry, is shown on my screen. I'm not sure why it's not on yours. Thank you. I just wanted to make a few brief comments as well and thank <PERSON><PERSON> for his efforts in working with the community to bring this forward. And I know this is something that has been in the works for a very long time. So thank you for your efforts. I just. Think that it's important also to mention the role that our Native American community played in. You heard me talk about DIA earlier. When we were moving forward with the construction of the highway, one of the things that happened was we worked with some of the tribal leaders to do a ground blessing on the site. As you all know, that used to be part of the old Sand Creek Massacre corridor. And I thought it was extremely important for Denver to do that. And the interesting thing about the event was the media wanted to know when and where it was going to take place. And I worked with <PERSON> at the time to ensure that that happened. I didn't attend it. We made sure the media didn't know when and where it was because it was, you know, a very traditional sacred event that needed to take place and to, you know, pray for the lives of of the souls who were lost in that massacre. The other thing that <PERSON><PERSON> talked about was the the history of where Denver started. It started with our Native American community right at the at the core of the Confluence Park. The city acknowledges that to the degree of seeing a number of the the parks, I mean, not the parks, but the roads down in the lower downtown and platte valley area named after some of the tribal leaders. We want to wynkoop a little raven. I remember when the naming of little raven was being proposed. Our public works department was recommending that that be called 29th Street. And I just you know, I was the councilperson of the district at the time. And I said, how do we make these decisions about what streets, what we're naming our streets? And I said, What other names did you look at? And they mentioned Little Raven. And this was when they were bringing through the committee process to do the official naming. And I said, I want it named Little Raven. And so when when that official, you know, name was put up on the street, we actually had some of the tribal leaders from the Cheyenne tribe there, and they actually were given a street sign that they were able to take and put up on display in their community. So just being part of so many of the things that have happened in this city is exciting. I worked at the state capitol when the Commission in Indian Affairs was created in George Brown's office. The lieutenant governors, it's been part of that office. I worked there and had the benefit of going to a peace treaty signing ceremony between the U.S. and the Comanches, who had been at war with each other for for 100 years. And a lot of these things, as Councilman Lopez said, are not written in our history books. You know, you in and one of the things that's occurring and those of you who have not taken the time to talk to your elders and record some of the history so that you pass it on to, you know, our children is is so important. I know in in New Mexico on some of the reservations, there are people actually doing filming, too, now of some of the elders to make sure that that history is documented and passed on, because it isn't translated in many of the history books you get in your public education system. So I again, just am happy to support this and again commend Councilman Lopez for his efforts in our Indian commission and the work that you all have done with our entire entire community. Thank you, Mr. President. Thank you, Councilwoman. Councilman Lopez, I see you back up. Yeah. You know, I wanted to really emphasize the 10th, Monday, the 10th and proclamation that will be here in the quarters we'd love for.\",\n", "  \"President. I call <PERSON><PERSON> Speaker, close voting. Announce the results. 3913 eyes. Constable 898 has been amended. <PERSON><PERSON> <PERSON>, please. We need a motion to pass as amended now. Mr. President, I move that council bill 898 series of 2016 be moved and be passed on final, final consideration as amended. Okay. It has been moved in second. It comes from members of council. It comes from our take as this from the prior. It was just hasn't gone away. All right. Madam Secretary, roll call. Can each I. LOPEZ All right. New ORTEGA High Assessment by Black<PERSON> Clark by Espinosa. FLYNN Hi. <PERSON> I <PERSON>. I <PERSON>man. Hi, Mr. President. I Please close the voting and ask for results. 3913 Eyes Council Bill 898 has passed as amended. Okay, just want to make sure looking down the road, make sure there are no other items that need to be called out. We're ready for the block votes. All other bills for introduction are order published. We are now ready. So council members, please remember that this is a consent block vote and you will need to vote. Otherwise this is your last chance to call out an item for a separate votes. <PERSON><PERSON><PERSON>, will you please put the resolutions for adoption and the bills for final consideration for final passage on the floor? We put them both at the same time. Yeah. The read through. That's what we did last week. Yeah. And it's easy if you do it from the screen. All right. I motion to approve the consent agenda. So the motion would be. No. No, do I. Do I run through all those resolutions and bills? Yep. Just all of them at once. Yep. All right. Back in my day, we brought it on. Oh, I'm just kidding. All right, Mr. President. Okay. I move that. Our series of 2016, the following resolutions 1000 982 998, 1000 to 8, 79, 33, nine, 34, nine, 92 and 93, 96, 99, 1003. And the following bills for consideration to series at 2016 979 nine 8947 nine 5959 961 974, nine, 75 and 85 831 972 973. And 1978 be released upon. Of do pass in block. Okay. Madam Secretary, I think he got all of them. Yes. Would you concur? Okay, great. Rook for. Black Eye Clerk. By. Vanessa Flynn I. Gilmore, i. Herndon, i. Catherine Kennedy I. Lopez I knew Ortega i susman i. Mr. President. I 3939 resolutions have been adopted and bills have been placed upon final consideration and do pass tonight. Council is scheduled to sit as the quasi Judicial Board of Equalization to consider reduction of total cost assessments for the one local maintenance district.\"],\n", " 'Councilman <PERSON>',\n", " '\\n1. Ensure that the response answers all parts of the query completely.\\n2. Ensure that the length of the response is under 50 words.\\n3. The response must not contain any abusive language or toxic content.\\n4. The response must be in a friendly tone.\\n',\n", " '<PERSON><PERSON> has been actively involved in community efforts, particularly regarding the documentation of Native American history and supporting housing development projects.',\n", " '\\nYour task is to grade the relevance of context document against a specified user query.\\nThe domain here is a meeting transcripts.\\n',\n", " DetectResult(\n", "   status=200,\n", "   detect_response=avg_context_doc_length: 18190.0\n", " hallucination: {\n", "     \"is_hallucinated\": \"False\",\n", "     \"score\": 0.0696,\n", "     \"sentences\": [\n", "         {\n", "             \"score\": 0.0696,\n", "             \"text\": \"<PERSON><PERSON> has been actively involved in community efforts, particularly\n", " regarding the documentation of Native American history and supporting housing development projects.\"\n", "         }\n", "     ]\n", " }\n", " instruction_adherence: {\n", "     \"results\": [\n", "         {\n", "             \"adherence\": true,\n", "             \"detailed_explanation\": \"The response addresses components related to <PERSON><PERSON>'s\n", " community involvement and specific areas such as the documentation of Native American history and\n", " housing projects, thus answering the query completely.\",\n", "             \"instruction\": \"Ensure that the response answers all parts of the query completely.\"\n", "         },\n", "         {\n", "             \"adherence\": true,\n", "             \"detailed_explanation\": \"The response contains 23 words, which is under the specified\n", " limit of 50 words.\",\n", "             \"instruction\": \"Ensure that the length of the response is under 50 words.\"\n", "         },\n", "         {\n", "             \"adherence\": true,\n", "             \"detailed_explanation\": \"The response uses neutral and positive language without any\n", " abusive or toxic content.\",\n", "             \"instruction\": \"The response must not contain any abusive language or toxic content.\"\n", "         },\n", "         {\n", "             \"adherence\": true,\n", "             \"detailed_explanation\": \"The tone of the response is friendly and informative,\n", " highlighting <PERSON><PERSON> <PERSON>'s positive contributions to the community.\",\n", "             \"instruction\": \"The response must be in a friendly tone.\"\n", "         }\n", "     ],\n", "     \"score\": 1.0\n", " }\n", " retrieval_relevance: [\n", "     {\n", "         \"explanations\": [\n", "             \"Document 1 discusses <PERSON><PERSON>'s efforts in the Indian commission and his\n", " involvement in community events, directly referencing his name and contributions. However, the\n", " document is lengthy and contains a lot of extraneous information about unrelated topics, which\n", " dilutes the focus on <PERSON><PERSON> and makes it less relevant to a query specifically seeking\n", " information about him.\",\n", "             \"2. In Document 2, Council<PERSON> <PERSON> is mentioned in relation to a council bill and his\n", " comments on a development project, which shows his active role in council discussions. The document,\n", " however, focuses more on the specific project and other council members' opinions rather than\n", " providing substantial information about Councilman <PERSON> himself, leading to a lower relevance\n", " score.\",\n", "             \"3. Document 3 acknowledges <PERSON><PERSON> in the context of public works and city\n", " management, which shows that he is recognized for his contributions. However, the document primarily\n", " discusses public works and does not delve deeply into <PERSON><PERSON>'s specific actions or\n", " achievements, making it less relevant to the query.\",\n", "             \"4. In Document 4, <PERSON><PERSON> is commended for his efforts in the community and\n", " for working with the Native American community, indicating his involvement in significant local\n", " issues. Yet, the document is more focused on the broader context of community history and events,\n", " which detracts from a focused discussion on Councilman <PERSON> himself.\",\n", "             \"5. Document 5 mentions <PERSON><PERSON> in the context of voting on a council bill and\n", " procedural matters, showcasing his active participation in council decisions. However, it lacks\n", " detailed insights into his specific contributions or perspectives regarding the bills, making it\n", " less informative for someone looking for in-depth information about <PERSON><PERSON>.\"\n", "         ],\n", "         \"query\": \"<PERSON><PERSON>\",\n", "         \"relevance_scores\": [\n", "             35.66559540012122,\n", "             37.18941956657886,\n", "             33.50108754888339,\n", "             33.29029488991324,\n", "             38.80187100744479\n", "         ]\n", "     }\n", " ],\n", "   publish_response=[]\n", " ))"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["# Quick check to ensure everything is working with the vector DB\n", "ask_and_validate(\"<PERSON><PERSON>\", instructions_to_evaluate)"]}, {"cell_type": "markdown", "id": "BKUgTqgDrjay", "metadata": {"id": "BKUgTqgDrjay"}, "source": ["Lets run through all the queries through the LlamaIndex query engine in the `queries_df` and compute the overall quality score using AIMon.\n", "\n", "**NOTE: This will take about 2 mins**"]}, {"cell_type": "code", "execution_count": null, "id": "ekekJe8DpHE2", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ekekJe8DpHE2", "outputId": "b9ad80a4-9e45-441c-c17d-11e4b98b6bff"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Avg. Retrieval relevance score across chunks: 19.932596854170086 for query: What was the key decision in the meeting?\n", "Avg. Retrieval relevance score across chunks: 19.332469976717874 for query: What are the next steps for the team?\n", "Avg. Retrieval relevance score across chunks: 13.695729082342893 for query: Summarize the meeting in 10 words.\n", "Avg. Retrieval relevance score across chunks: 20.276701279455835 for query: What were the main points of discussion?\n", "Avg. Retrieval relevance score across chunks: 19.642715112968148 for query: What decision was made regarding the project?\n", "Avg. Retrieval relevance score across chunks: 17.880496811886246 for query: What were the outcomes of the meeting?\n", "Avg. Retrieval relevance score across chunks: 23.53911458826815 for query: What was discussed in the meeting?\n", "Avg. Retrieval relevance score across chunks: 17.665638657211105 for query: What examples were discussed for project inspiration?\n", "Avg. Retrieval relevance score across chunks: 18.13388221868742 for query: What considerations were made for the project timeline?\n", "Avg. Retrieval relevance score across chunks: 18.955595009379778 for query: Who is responsible for completing the tasks?\n", "Avg. Retrieval relevance score across chunks: 22.840146597476263 for query: What were the decisions made in the meeting?\n", "Avg. Retrieval relevance score across chunks: 19.665652140639054 for query: What did the team decide about the project timeline?\n", "Time elapsed: 125.75674271583557 seconds\n"]}], "source": ["import time\n", "\n", "quality_scores_vdb = []\n", "avg_retrieval_rel_scores_vdb = []\n", "responses_adb = {}\n", "ast = time.time()\n", "for user_query in queries_df[\"Query\"].to_list():\n", "    _, _, _, llm_res, _, aimon_response = ask_and_validate(\n", "        user_query, instructions_to_evaluate\n", "    )\n", "    # These show the average retrieval relevance scores per query. Compare this to the previous brute force method.\n", "    retrieval_rel_scores = aimon_response.detect_response.retrieval_relevance[0][\n", "        \"relevance_scores\"\n", "    ]\n", "    avg_retrieval_rel_score_per_query = (\n", "        statistics.mean(retrieval_rel_scores) if len(retrieval_rel_scores) > 0 else 0.0\n", "    )\n", "    avg_retrieval_rel_scores_vdb.append(avg_retrieval_rel_score_per_query)\n", "    print(\n", "        \"Avg. Retrieval relevance score across chunks: {} for query: {}\".format(\n", "            avg_retrieval_rel_score_per_query, user_query\n", "        )\n", "    )\n", "    quality_scores_vdb.append(compute_quality_score(aimon_response))\n", "    responses_adb[user_query] = llm_res\n", "print(\"Time elapsed: {} seconds\".format(time.time() - ast))"]}, {"cell_type": "code", "execution_count": null, "id": "Rdd02nduGapC", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Rdd02nduGapC", "outputId": "bac36465-54bd-4a1e-9ea6-a37caee9d223"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Average Quality score for vector DB approach: 67.1800392915634\n"]}], "source": ["# This is the average quality score.\n", "avg_quality_score_vdb = statistics.mean(quality_scores_vdb)\n", "print(\"Average Quality score for vector DB approach: {}\".format(avg_quality_score_vdb))"]}, {"cell_type": "code", "execution_count": null, "id": "Cn5ccA04wT50", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Cn5ccA04wT50", "outputId": "56f6eadf-3a15-4767-e857-56e3983ed4ca"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Average retrieval relevance score for vector DB approach: 19.296728194100236\n"]}], "source": ["# This is the average retrieval relevance score.\n", "avg_retrieval_rel_score_vdb = statistics.mean(avg_retrieval_rel_scores_vdb)\n", "print(\n", "    \"Average retrieval relevance score for vector DB approach: {}\".format(\n", "        avg_retrieval_rel_score_vdb\n", "    )\n", ")"]}, {"cell_type": "markdown", "id": "bJsBmRiNZ9eF", "metadata": {"id": "bJsBmRiNZ9eF"}, "source": ["## 🎉 Quality Score improved!\n", "\n", "Notice that the overall quality score across all queries improved after using a RAG based QA system.\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "jedmpEMlsZxM", "metadata": {"id": "jedmpEMlsZxM"}, "source": ["# 3. Add Re-ranking to your retrieval\n", "\n", "Now, we will add in AIMon's [domain adaptable re-ranker](https://docs.aimon.ai/retrieval#domain-adaptable-re-ranking) using AIMon's LlamaIndex [postprocessor re-rank integration](https://docs.llamaindex.ai/en/latest/examples/node_postprocessor/AIMonRerank/).\n", "\n", "As shown in the figure below, reranking helps bubble up the most relevant documents to the top by using a more advanced Query-Document matching function. The unique feature of AIMon's re-ranker is the ability to customize it per domain. Similar to how you would prompt engineer an LLM, you can customize reranking performance per domain using the `task_definition` field. This state-of-the-art reranker runs at ultra low sub second latency (for a ~2k context) and its performance ranks in the top 5 of the MTEB reranking leaderboard."]}, {"cell_type": "markdown", "id": "Pvq8Mcfxuvji", "metadata": {"id": "Pvq8Mcfxuvji"}, "source": ["<img src=\"https://raw.githubusercontent.com/devvratbhardwaj/images/refs/heads/main/AIMon_Reranker.svg\" alt=\"Diagram depicting working of AIMon reranker\"/>"]}, {"cell_type": "code", "execution_count": null, "id": "Lxi0MWUTHt1r", "metadata": {"id": "Lxi0MWUTHt1r"}, "outputs": [], "source": ["# Setup AIMon's reranker\n", "\n", "from llama_index.postprocessor.aimon_rerank import AIMonRerank\n", "\n", "# This is a simple task_definition, you can polish and customize it for your use cases as needed\n", "task_definition = \"\"\"\n", "Your task is to match documents for a specific query.\n", "The documents are transcripts of meetings of city councils of 6 major U.S. cities.\n", "\"\"\"\n", "\n", "aimon_rerank = AIMonRerank(\n", "    top_n=2,\n", "    api_key=userdata.get(\"AIMON_API_KEY\"),\n", "    task_definition=task_definition,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "u4Oym5u2w1KD", "metadata": {"id": "u4Oym5u2w1KD"}, "outputs": [], "source": ["# Setup a new query engine but now with a reranker added as a post processor after retrieval\n", "\n", "query_engine_with_reranking = RetrieverQueryEngine.from_args(\n", "    retriever, llm, node_postprocessors=[aimon_rerank]\n", ")"]}, {"cell_type": "markdown", "id": "UyfWxjZbycKR", "metadata": {"id": "UyfWxjZbycKR"}, "source": ["Let's run through the queries again and recompute the overall quality score to see if there is an improvement.\n", "\n", "✨ **AIMon's re-ranking should not add additional latency overhead since it actually reduces the amount of context documents that need to be sent to the LLM for generating a response making the operation efficient in terms of network I/O and LLM token processing cost (money and time).**\n", "\n", "**NOTE: This step will take 2 mins**"]}, {"cell_type": "code", "execution_count": null, "id": "rpG6s6FfxO9z", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "rpG6s6FfxO9z", "outputId": "e8cdc350-05f2-4661-87b7-7458218a1c89"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Avg. Retrieval relevance score across chunks: 36.436465411440366 for query: What was the key decision in the meeting?\n", "Avg. Retrieval relevance score across chunks: 38.804003013309085 for query: What are the next steps for the team?\n", "Avg. Retrieval relevance score across chunks: 45.29209086832342 for query: Summarize the meeting in 10 words.\n", "Avg. Retrieval relevance score across chunks: 36.979413900164815 for query: What were the main points of discussion?\n", "Avg. Retrieval relevance score across chunks: 41.149971422535714 for query: What decision was made regarding the project?\n", "Avg. Retrieval relevance score across chunks: 36.57368907582921 for query: What were the outcomes of the meeting?\n", "Avg. Retrieval relevance score across chunks: 42.34540670899989 for query: What was discussed in the meeting?\n", "Avg. Retrieval relevance score across chunks: 33.857591391574715 for query: What examples were discussed for project inspiration?\n", "Avg. Retrieval relevance score across chunks: 38.419397677952816 for query: What considerations were made for the project timeline?\n", "Avg. Retrieval relevance score across chunks: 42.91262631898647 for query: Who is responsible for completing the tasks?\n", "Avg. Retrieval relevance score across chunks: 41.417109763746396 for query: What were the decisions made in the meeting?\n", "Avg. Retrieval relevance score across chunks: 43.34866213159572 for query: What did the team decide about the project timeline?\n", "Time elapsed: 97.93312644958496 seconds\n"]}], "source": ["import time\n", "\n", "qual_scores_rr = []\n", "avg_retrieval_rel_scores_rr = []\n", "responses_adb_rr = {}\n", "ast_rr = time.time()\n", "for user_query in queries_df[\"Query\"].to_list():\n", "    _, _, _, llm_res, _, aimon_response = ask_and_validate(\n", "        user_query, instructions_to_evaluate, query_engine=query_engine_with_reranking\n", "    )\n", "    # These show the average retrieval relevance scores per query. Compare this to the previous method without the re-ranker\n", "    retrieval_rel_scores = aimon_response.detect_response.retrieval_relevance[0][\n", "        \"relevance_scores\"\n", "    ]\n", "    avg_retrieval_rel_score_per_query = (\n", "        statistics.mean(retrieval_rel_scores) if len(retrieval_rel_scores) > 0 else 0.0\n", "    )\n", "    avg_retrieval_rel_scores_rr.append(avg_retrieval_rel_score_per_query)\n", "    print(\n", "        \"Avg. Retrieval relevance score across chunks: {} for query: {}\".format(\n", "            avg_retrieval_rel_score_per_query, user_query\n", "        )\n", "    )\n", "    qual_scores_rr.append(compute_quality_score(aimon_response))\n", "    responses_adb_rr[user_query] = llm_res\n", "print(\"Time elapsed: {} seconds\".format(time.time() - ast_rr))"]}, {"cell_type": "markdown", "id": "5rLU0pdB7v9p", "metadata": {"id": "5rLU0pdB7v9p"}, "source": ["Notice the difference in average document relevance scores when using the reranker v/s when not using the reranker v/s using a naive, brute-force approach."]}, {"cell_type": "code", "execution_count": null, "id": "vB3yYaZpxdAg", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "vB3yYaZpxdAg", "outputId": "a367873f-21bc-4883-ea1d-0762a0fa30b6"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Average Quality score for AIMon Re-ranking approach: 74.62174819211145\n"]}], "source": ["# This is the average quality score.\n", "avg_quality_score_rr = statistics.mean(qual_scores_rr)\n", "print(\n", "    \"Average Quality score for AIMon Re-ranking approach: {}\".format(\n", "        avg_quality_score_rr\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "HJgP_cprw_1D", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "HJgP_cprw_1D", "outputId": "40a535b1-067a-4147-d7c2-ac0b99b36ee5"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Average retrieval relevance score for AIMon Re-ranking approach: 39.794702307038214\n"]}], "source": ["# This is the average retrieval relevance score.\n", "avg_retrieval_rel_score_rr = statistics.mean(avg_retrieval_rel_scores_rr)\n", "print(\n", "    \"Average retrieval relevance score for AIMon Re-ranking approach: {}\".format(\n", "        avg_retrieval_rel_score_rr\n", "    )\n", ")"]}, {"cell_type": "markdown", "id": "-rqgP4Wd7mo2", "metadata": {"id": "-rqgP4Wd7mo2"}, "source": ["## 🎉 Again, Quality Score improved!\n", "\n", "Notice that the overall quality score across all queries improved after using AIMon's reranker.\n", "\n", "In sum, as shown in the figure below, we demonstrated the following:\n", "\n", "* Computing a quality score using a weighted combination of 3 different quality metrics: hallucination score, instruction adherence score and retrieval relevance score.\n", "* Established a quality baseline using a brute force string matching approach to match documents to a query and pass that to an LLM.\n", "* Improved the baseline quality using a Vector DB (here, we used Milvus)\n", "* Further improved the quality score using AIMon's low-latency, domain adaptable re-ranker.\n", "* We also showed how retrieval relevance improves significantly by adding in AIMon's re-ranker.\n", "\n", "We encourage you to experiment with the different components shown in this notebook to further **increase the quality score**. One idea is to add your own definitions of quality using the `instructions` field in the instruction_adherence detector above. Another idea is to add another one of [AIMon's checker models](https://docs.aimon.ai/category/checker-models) as part of the quality metric calculation."]}, {"cell_type": "code", "execution_count": null, "id": "0QYkElmJuBkz", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 143}, "id": "0QYkElmJuBkz", "outputId": "13cdca8e-4f0a-4aab-a450-bdc8fc83a22e"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"df_scores\",\n  \"rows\": 3,\n  \"fields\": [\n    {\n      \"column\": \"Approach\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 3,\n        \"samples\": [\n          \"Brute-Force\",\n          \"VectorDB\",\n          \"AIMon-Rerank\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Quality Score\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 11.66581780333284,\n        \"min\": 51.750446187242254,\n        \"max\": 74.62174819211145,\n        \"num_unique_values\": 3,\n        \"samples\": [\n          51.750446187242254,\n          67.1800392915634,\n          74.62174819211145\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Retrieval Relevance Score\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 13.50329518788624,\n        \"min\": 14.31772340191865,\n        \"max\": 39.794702307038214,\n        \"num_unique_values\": 3,\n        \"samples\": [\n          14.31772340191865,\n          19.296728194100236,\n          39.794702307038214\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Increase in Quality Score (%)\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 22.54244873778257,\n        \"min\": 0.0,\n        \"max\": 44.19537161499398,\n        \"num_unique_values\": 3,\n        \"samples\": [\n          0.0,\n          29.81538178143268,\n          44.19537161499398\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Increase in Retrieval Relevance Score (%)\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 94.31174781653296,\n        \"min\": 0.0,\n        \"max\": 177.94015284375112,\n        \"num_unique_values\": 3,\n        \"samples\": [\n          0.0,\n          34.77511509626156,\n          177.94015284375112\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe", "variable_name": "df_scores"}, "text/html": ["\n", "  <div id=\"df-c43e3124-8331-40e6-97e4-b2d026a0ed70\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Approach</th>\n", "      <th>Quality Score</th>\n", "      <th>Retrieval Relevance Score</th>\n", "      <th>Increase in Quality Score (%)</th>\n", "      <th>Increase in Retrieval Relevance Score (%)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Brute-Force</td>\n", "      <td>51.750446</td>\n", "      <td>14.317723</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>VectorDB</td>\n", "      <td>67.180039</td>\n", "      <td>19.296728</td>\n", "      <td>29.815382</td>\n", "      <td>34.775115</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON><PERSON><PERSON>-<PERSON><PERSON></td>\n", "      <td>74.621748</td>\n", "      <td>39.794702</td>\n", "      <td>44.195372</td>\n", "      <td>177.940153</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-c43e3124-8331-40e6-97e4-b2d026a0ed70')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-c43e3124-8331-40e6-97e4-b2d026a0ed70 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-c43e3124-8331-40e6-97e4-b2d026a0ed70');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-3b8c700e-50cd-4b5f-8b23-64725b4af575\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-3b8c700e-50cd-4b5f-8b23-64725b4af575')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-3b8c700e-50cd-4b5f-8b23-64725b4af575 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "  <div id=\"id_94166e57-57c1-4624-bf67-e4b68303403f\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('df_scores')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_94166e57-57c1-4624-bf67-e4b68303403f button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('df_scores');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "text/plain": ["       Approach  Quality Score  Retrieval Relevance Score  \\\n", "0   Brute-Force      51.750446                  14.317723   \n", "1      VectorDB      67.180039                  19.296728   \n", "2  AIMon-<PERSON><PERSON>      74.621748                  39.794702   \n", "\n", "   Increase in Quality Score (%)  Increase in Retrieval Relevance Score (%)  \n", "0                       0.000000                                   0.000000  \n", "1                      29.815382                                  34.775115  \n", "2                      44.195372                                 177.940153  "]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "df_scores = pd.DataFrame(\n", "    {\n", "        \"Approach\": [\"Brute-Force\", \"VectorDB\", \"AIMon-Rerank\"],\n", "        \"Quality Score\": [\n", "            avg_quality_score_bf,\n", "            avg_quality_score_vdb,\n", "            avg_quality_score_rr,\n", "        ],\n", "        \"Retrieval Relevance Score\": [\n", "            avg_retrieval_rel_score_bf,\n", "            avg_retrieval_rel_score_vdb,\n", "            avg_retrieval_rel_score_rr,\n", "        ],\n", "    }\n", ")\n", "\n", "# % increase of quality scores relative to Brute-Force\n", "df_scores[\"Increase in Quality Score (%)\"] = (\n", "    (df_scores[\"Quality Score\"] - avg_quality_score_bf) / avg_quality_score_bf\n", ") * 100\n", "df_scores.loc[0, \"Increase in Quality Score (%)\"] = 0\n", "\n", "# % increase of retrieval relative scores relative to Brute-Force\n", "df_scores[\"Increase in Retrieval Relevance Score (%)\"] = (\n", "    (df_scores[\"Retrieval Relevance Score\"] - avg_retrieval_rel_score_bf)\n", "    / avg_retrieval_rel_score_bf\n", ") * 100\n", "df_scores.loc[0, \"Increase in Retrieval Relevance Score (%)\"] = 0\n", "\n", "df_scores"]}, {"cell_type": "markdown", "id": "dUZXuk5C_NMg", "metadata": {"id": "dUZXuk5C_NMg"}, "source": ["The above table summarizes our results. Your actual numbers will vary depending on various factors such as variations in quality of LLM responses, performance of the nearest neighbor search in the VectorDB etc.\n", "\n", "In conclusion, as shown by the figure below, we evaluated quality score, RAG relevance and instruction following capabilities of your LLM application. We used AIMon's re-ranker to improve the overall quality of the application and the average relevance of documents retrieved from your RAG."]}], "metadata": {"colab": {"provenance": []}, "environment": {"kernel": "conda-base-py", "name": "workbench-notebooks.m128", "type": "gcloud", "uri": "us-docker.pkg.dev/deeplearning-platform-release/gcr.io/workbench-notebooks:m128"}, "kernelspec": {"display_name": "py310", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}