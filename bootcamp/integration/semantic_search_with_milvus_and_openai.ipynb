{"cells": [{"metadata": {}, "cell_type": "markdown", "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/integration/semantic_search_with_milvus_and_openai.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/integration/semantic_search_with_milvus_and_openai.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>\n", "</a>"], "id": "a898e9a1eac83f65"}, {"cell_type": "markdown", "source": ["# Semantic Search with Milvus and OpenAI"], "metadata": {"collapsed": false}, "id": "878df9d74fca7d55"}, {"cell_type": "markdown", "source": ["This guide showcases how [OpenAI's Embedding API](https://platform.openai.com/docs/guides/embeddings) can be used with Milvus vector database to conduct semantic search on text."], "metadata": {"collapsed": false}, "id": "43973bc5cd0a1331"}, {"cell_type": "markdown", "source": ["## Getting started\n", "Before you start, make sure you have the OpenAI API key ready, or you get one from the [OpenAI website](https://openai.com/index/openai-api/).\n", "\n", "The data used in this example are book titles. You can download the dataset [here](https://www.kaggle.com/datasets/jealousleopard/goodreadsbooks) and put it in the same directory where you run the following code.\n", "\n", "First, install the package for Milvus and OpenAI:"], "metadata": {"collapsed": false}, "id": "584c8866814c37e5"}, {"cell_type": "code", "source": ["!pip install --upgrade openai pymilvus"], "metadata": {"collapsed": false}, "id": "39a874f29e61f293", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "markdown", "source": ["> If you are using Google Colab, to enable dependencies just installed, you may need to **restart the runtime** (click on the \"Runtime\" menu at the top of the screen, and select \"Restart session\" from the dropdown menu)."], "id": "78a3a6d4452080fe"}, {"metadata": {}, "cell_type": "markdown", "source": ["With this, we're ready to generate embeddings and use vector database to conduct semantic search."], "id": "998c2d7b2ef17647"}, {"metadata": {}, "cell_type": "markdown", "source": ["## Searching book titles with OpenAI & Milvus\n", "\n", "In the following example, we load book title data from the downloaded CSV file, use OpenAI embedding model to generate vector representations, and store them in Milvus vector database for semantic search."], "id": "a0e354812462821f"}, {"metadata": {}, "cell_type": "code", "source": ["from openai import OpenAI\n", "from pymilvus import MilvusClient\n", "\n", "MODEL_NAME = \"text-embedding-3-small\"  # Which model to use, please check https://platform.openai.com/docs/guides/embeddings for available models\n", "DIMENSION = 1536  # Dimension of vector embedding\n", "\n", "# Connect to OpenAI with API Key.\n", "openai_client = OpenAI(api_key=\"<YOUR_OPENAI_API_KEY>\")\n", "\n", "docs = [\n", "    \"Artificial intelligence was founded as an academic discipline in 1956.\",\n", "    \"<PERSON> was the first person to conduct substantial research in AI.\",\n", "    \"Born in Maida Vale, London, <PERSON><PERSON> was raised in southern England.\",\n", "]\n", "\n", "vectors = [\n", "    vec.embedding\n", "    for vec in openai_client.embeddings.create(input=docs, model=MODEL_NAME).data\n", "]\n", "\n", "# Prepare data to be stored in Milvus vector database.\n", "# We can store the id, vector representation, raw text and labels such as \"subject\" in this case in Milvus.\n", "data = [\n", "    {\"id\": i, \"vector\": vectors[i], \"text\": docs[i], \"subject\": \"history\"}\n", "    for i in range(len(docs))\n", "]\n", "\n", "\n", "# Connect to Milvus, all data is stored in a local file named \"milvus_openai_demo.db\"\n", "# in current directory. You can also connect to a remote Milvus server following this\n", "# instruction: https://milvus.io/docs/install_standalone-docker.md.\n", "milvus_client = MilvusClient(uri=\"milvus_openai_demo.db\")\n", "COLLECTION_NAME = \"demo_collection\"  # Milvus collection name\n", "# Create a collection to store the vectors and text.\n", "if milvus_client.has_collection(collection_name=COLLECTION_NAME):\n", "    milvus_client.drop_collection(collection_name=COLLECTION_NAME)\n", "milvus_client.create_collection(collection_name=COLLECTION_NAME, dimension=DIMENSION)\n", "\n", "# Insert all data into Milvus vector database.\n", "res = milvus_client.insert(collection_name=\"demo_collection\", data=data)\n", "\n", "print(res[\"insert_count\"])"], "id": "7b4d741a70f1cc7e", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": ["> As for the argument of `MilvusClient`:\n", "> - Setting the `uri` as a local file, e.g.`./milvus.db`, is the most convenient method, as it automatically utilizes [Milvus Lite](https://milvus.io/docs/milvus_lite.md) to store all data in this file.\n", "> - If you have large scale of data, you can set up a more performant Milvus server on [docker or kubernetes](https://milvus.io/docs/quickstart.md). In this setup, please use the server uri, e.g.`http://localhost:19530`, as your `uri`.\n", "> - If you want to use [Zilliz Cloud](https://zilliz.com/cloud), the fully managed cloud service for Milvus, adjust the `uri` and `token`, which correspond to the [Public Endpoint and Api key](https://docs.zilliz.com/docs/on-zilliz-cloud-console#free-cluster-details) in Zilliz Cloud."], "metadata": {"collapsed": false}}, {"metadata": {}, "cell_type": "markdown", "source": ["With all data in Milvus vector database, we can now perform semantic search by generating vector embedding for the query and conduct vector search."], "id": "180883400043b828"}, {"metadata": {}, "cell_type": "code", "source": ["queries = [\"When was artificial intelligence founded?\"]\n", "\n", "query_vectors = [\n", "    vec.embedding\n", "    for vec in openai_client.embeddings.create(input=queries, model=MODEL_NAME).data\n", "]\n", "\n", "res = milvus_client.search(\n", "    collection_name=COLLECTION_NAME,  # target collection\n", "    data=query_vectors,  # query vectors\n", "    limit=2,  # number of returned entities\n", "    output_fields=[\"text\", \"subject\"],  # specifies fields to be returned\n", ")\n", "\n", "for q in queries:\n", "    print(\"Query:\", q)\n", "    for result in res:\n", "        print(result)\n", "    print(\"\\n\")"], "id": "aac3c33b53fd2053", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "markdown", "source": "You should see the following as the output:", "id": "8edc33d0f3648a87"}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-29T08:34:16.660435Z", "start_time": "2024-05-29T08:33:35.597838Z"}}, "cell_type": "code", "source": ["[\n", "    {\n", "        \"id\": 0,\n", "        \"distance\": -0.772376537322998,\n", "        \"entity\": {\n", "            \"text\": \"Artificial intelligence was founded as an academic discipline in 1956.\",\n", "            \"subject\": \"history\",\n", "        },\n", "    },\n", "    {\n", "        \"id\": 1,\n", "        \"distance\": -0.58596271276474,\n", "        \"entity\": {\n", "            \"text\": \"<PERSON> was the first person to conduct substantial research in AI.\",\n", "            \"subject\": \"history\",\n", "        },\n", "    },\n", "]"], "id": "8eaf8ee7a73e3c56", "outputs": [], "execution_count": 15}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}