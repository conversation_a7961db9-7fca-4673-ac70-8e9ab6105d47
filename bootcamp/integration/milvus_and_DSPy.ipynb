{"cells": [{"metadata": {}, "cell_type": "markdown", "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/integration/milvus_and_DSPy.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/integration/milvus_and_DSPy.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>\n", "</a>"], "id": "cc9c54a108f3efae"}, {"cell_type": "markdown", "source": ["# DSPy"], "metadata": {"collapsed": false}, "id": "bc01e334dd3737d7"}, {"cell_type": "markdown", "source": ["## What is DSPy\n", "DSPy, introduced by the Stanford NLP Group, stands as a groundbreaking programmatic framework designed to optimize prompts and weights within language models, particularly valuable in scenarios where large language models (LLMs) are integrated across multiple stages of a pipeline. Unlike conventional prompting engineering techniques reliant on manual crafting and tweaking, DSPy adopts a learning-based approach. By assimilating query-answer examples, DSPy generates optimized prompts dynamically, tailored to specific tasks. This innovative methodology enables the seamless reassembly of entire pipelines, eliminating the need for continuous manual prompt adjustments. DSPy's Pythonic syntax offers various composable and declarative modules, simplifying the instruction of LLMs. "], "metadata": {"collapsed": false}, "id": "e4b458b822516ee1"}, {"cell_type": "markdown", "source": ["## Benefits of using DSPy\n", "- Programming Approach: DSPy provides a systematic programming approach for LM pipeline development by abstracting pipelines as text transformation graphs instead of just prompting the LLMs. Its declarative modules enable structured design and optimization, replacing the trial-and-error method of traditional prompt templates.\n", "- Performance Improvement: DSPy demonstrates significant performance gains over existing methods. Through case studies, it outperforms standard prompting and expert-created demonstrations, showcasing its versatility and effectiveness even when compiled to smaller LM models.\n", "-  Modularized Abstraction: DSPy effectively abstracts intricate aspects of LM pipeline development, such as decomposition, fine-tuning, and model selection. With DSPy, a concise program can seamlessly translate into instructions for various models, such as GPT-4, Llama2-13b, or T5-base, streamlining development and enhancing performance."], "metadata": {"collapsed": false}, "id": "ae494766be2e5081"}, {"cell_type": "markdown", "source": ["## <PERSON><PERSON><PERSON>\n", "There are numerous components that contribute to constructing an LLM pipeline. Here, we'll describe some key components to provide a high-level understanding of how DSPy operates.\n", "\n", "<img src=\"../pics/DSPy_milvus.png\" alt=\"DSPy Milvus\" width=\"700\">\n", "\n", "Signature: Signatures in DSPy serve as declarative specifications, outlining the input/output behavior of modules, guiding the language model in task execution.\n", "Module: DSPy modules serve as fundamental components for programs leveraging language models (LMs). They abstract various prompting techniques, such as chain of thought or ReAct, and are adaptable to handle any DSPy Signature. With learnable parameters and the ability to process inputs and produce outputs, these modules can be combined to form larger programs, drawing inspiration from NN modules in PyTorch but tailored for LM applications.\n", "Optimizer: Optimizers in DSPy fine-tune the parameters of DSPy programs, such as prompts and LLM weights, to maximize specified metrics like accuracy, enhancing program efficiency."], "metadata": {"collapsed": false}, "id": "14291bf6c82872a5"}, {"cell_type": "markdown", "source": ["## Why <PERSON><PERSON><PERSON><PERSON> in DSPy\n", "DSPy is a powerful programming framework that boosts RAG applications. Such application needs to retrieve useful information to enhance answer quality, which needs vector database. Milvus is a well-known open-source vector database to improve performance and scalability. With MilvusRM, a retriever module in DSPy, integrating Milvus becomes seamless. Now, developers can easily define and optimize RAG programs using DSPy, taking advantage of Milvus' strong vector search capabilities. This collaboration makes RAG applications more efficient and scalable, combining DSPy's programming capabilities with Milvus' search features."], "metadata": {"collapsed": false}, "id": "cf1b6e2753f1684d"}, {"cell_type": "markdown", "source": ["### Examples\n", "Now, let's walk through a quick example to demonstrate how to leverage Milvus in DSPy for optimizing a RAG application."], "metadata": {"collapsed": false}, "id": "4cea96880e8b5ab3"}, {"cell_type": "markdown", "source": ["### Prerequisites\n", "Before building the RAG app, install the DSPy and PyMilvus."], "metadata": {"collapsed": false}, "id": "172560fa9d3cb854"}, {"cell_type": "code", "source": ["!pip install \"dspy-ai[milvus]\"\n", "!pip install -<PERSON> pymilvus"], "metadata": {"collapsed": false}, "id": "fb7d545e86de6d4", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "markdown", "source": ["> If you are using Google Colab, to enable dependencies just installed, you may need to **restart the runtime** (click on the \"Runtime\" menu at the top of the screen, and select \"Restart session\" from the dropdown menu)."], "id": "bbf27b3225a33dae"}, {"cell_type": "markdown", "source": ["### Loading the dataset\n", "In this example, we use the HotPotQA, a collection of complex question-answer pairs, as our training dataset. We can load them through the HotPotQA class."], "metadata": {"collapsed": false}, "id": "8dfe8d690ede7c56"}, {"cell_type": "code", "source": ["from dspy.datasets import HotPotQA\n", "\n", "# Load the dataset.\n", "dataset = HotPotQA(\n", "    train_seed=1, train_size=20, eval_seed=2023, dev_size=50, test_size=0\n", ")\n", "\n", "# Tell DSPy that the 'question' field is the input. Any other fields are labels and/or metadata.\n", "trainset = [x.with_inputs(\"question\") for x in dataset.train]\n", "devset = [x.with_inputs(\"question\") for x in dataset.dev]"], "metadata": {"collapsed": false}, "id": "ec2d0d28519193db", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": ["### Ingest data into the Milvus vector database\n", "Ingest the context information into the <PERSON>lvus collection for vector retrieval. This collection should have an `embedding` field and a `text` field. We use OpenAI's `text-embedding-3-small` model as the default query embedding function in this case."], "metadata": {"collapsed": false}, "id": "beb9b3971b5dd4e0"}, {"cell_type": "code", "source": ["import requests\n", "import os\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"<YOUR_OPENAI_API_KEY>\"\n", "MILVUS_URI = \"example.db\"\n", "MILVUS_TOKEN = \"\"\n", "\n", "from pymilvus import MilvusClient, DataType, Collection\n", "from dspy.retrieve.milvus_rm import openai_embedding_function\n", "\n", "client = MilvusClient(uri=MILVUS_URI, token=MILVUS_TOKEN)\n", "\n", "if \"dspy_example\" not in client.list_collections():\n", "    client.create_collection(\n", "        collection_name=\"dspy_example\",\n", "        overwrite=True,\n", "        dimension=1536,\n", "        primary_field_name=\"id\",\n", "        vector_field_name=\"embedding\",\n", "        id_type=\"int\",\n", "        metric_type=\"IP\",\n", "        max_length=65535,\n", "        enable_dynamic=True,\n", "    )\n", "text = requests.get(\n", "    \"https://raw.githubusercontent.com/wxywb/dspy_dataset_sample/master/sample_data.txt\"\n", ").text\n", "\n", "for idx, passage in enumerate(text.split(\"\\n\")):\n", "    if len(passage) == 0:\n", "        continue\n", "    client.insert(\n", "        collection_name=\"dspy_example\",\n", "        data=[\n", "            {\n", "                \"id\": idx,\n", "                \"embedding\": openai_embedding_function(passage)[0],\n", "                \"text\": passage,\n", "            }\n", "        ],\n", "    )"], "metadata": {"collapsed": false}, "id": "b5de2cdb94b92e2c", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": ["### Define MilvusRM.\n", "Now, you need to define the MilvusRM."], "metadata": {"collapsed": false}, "id": "3646933bed18538b"}, {"cell_type": "code", "source": ["from dspy.retrieve.milvus_rm import MilvusRM\n", "import dspy\n", "\n", "retriever_model = MilvusRM(\n", "    collection_name=\"dspy_example\",\n", "    uri=MILVUS_URI,\n", "    token=MILVUS_TOKEN,  # ignore this if no token is required for Milvus connection\n", "    embedding_function=openai_embedding_function,\n", ")\n", "turbo = dspy.OpenAI(model=\"gpt-3.5-turbo\")\n", "dspy.settings.configure(lm=turbo)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-05-29T09:26:19.572894Z", "start_time": "2024-05-29T09:26:19.561042Z"}}, "id": "62dc7924976df10c", "outputs": [], "execution_count": 3}, {"cell_type": "markdown", "source": ["### Building signatures\n", "Now that we have loaded the data, let's start defining the signatures for the sub-tasks of our pipeline. We can identify our simple input `question` and output `answer`, but since we are building a RAG pipeline, we’ll retrieve contextual information from Milvus. So let's define our signature as `context, question --> answer`."], "metadata": {"collapsed": false}, "id": "71c60d241a0445c8"}, {"cell_type": "code", "source": ["class GenerateAnswer(dspy.Signature):\n", "    \"\"\"Answer questions with short factoid answers.\"\"\"\n", "\n", "    context = dspy.InputField(desc=\"may contain relevant facts\")\n", "    question = dspy.Input<PERSON>ield()\n", "    answer = dspy.OutputField(desc=\"often between 1 and 5 words\")"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-05-29T09:26:22.375666Z", "start_time": "2024-05-29T09:26:22.370294Z"}}, "id": "7ebd3adadf3b8896", "outputs": [], "execution_count": 4}, {"cell_type": "markdown", "source": ["We include short descriptions for the `context` and `answer` fields to define clearer guidelines on what the model will receive and should generate."], "metadata": {"collapsed": false}, "id": "ad66da62d2a35a4b"}, {"cell_type": "markdown", "source": ["### Building the pipeline\n", "Now, let's define the RAG pipeline."], "metadata": {"collapsed": false}, "id": "ab958537470784d8"}, {"cell_type": "code", "source": ["class RAG(dspy.Module):\n", "    def __init__(self, rm):\n", "        super().__init__()\n", "        self.retrieve = rm\n", "\n", "        # This signature indicates the task imposed on the COT module.\n", "        self.generate_answer = dspy.ChainOfThought(GenerateAnswer)\n", "\n", "    def forward(self, question):\n", "        # Use milvus_rm to retrieve context for the question.\n", "        context = self.retrieve(question).passages\n", "        # COT module takes \"context, query\" and output \"answer\".\n", "        prediction = self.generate_answer(context=context, question=question)\n", "        return dspy.Prediction(\n", "            context=[item.long_text for item in context], answer=prediction.answer\n", "        )"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-05-29T09:26:25.330239Z", "start_time": "2024-05-29T09:26:25.325210Z"}}, "id": "26e72ff7002e0f6c", "outputs": [], "execution_count": 5}, {"cell_type": "markdown", "source": ["### Executing the pipeline and getting the results\n", "Now, we’ve built this RAG pipeline. Let's try it out and get results."], "metadata": {"collapsed": false}, "id": "a5cc2c4e503f1629"}, {"cell_type": "code", "source": ["rag = RAG(retriever_model)\n", "print(rag(\"who write At My Window\").answer)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-05-29T09:26:29.733416Z", "start_time": "2024-05-29T09:26:27.214214Z"}}, "id": "9752d14dc8f59d81", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Townes Van <PERSON>t\n"]}], "execution_count": 6}, {"cell_type": "markdown", "source": ["We can evaluate the quantitative results on the dataset."], "metadata": {"collapsed": false}, "id": "ebf0d31f9ca09800"}, {"cell_type": "code", "source": ["from dspy.evaluate.evaluate import Evaluate\n", "from dspy.datasets import HotPotQA\n", "\n", "evaluate_on_hotpotqa = Evaluate(\n", "    devset=devset, num_threads=1, display_progress=False, display_table=5\n", ")\n", "\n", "metric = dspy.evaluate.answer_exact_match\n", "score = evaluate_on_hotpotqa(rag, metric=metric)\n", "print(\"rag:\", score)"], "metadata": {"collapsed": false}, "id": "8b4d6d8b92a741f7", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": ["### Optimizing the pipeline\n", "After defining this program, the next step is compilation. This process updates the parameters within each module to enhance performance. The compilation process depends on three critical factors:\n", "- Training Set: We'll utilize the 20 question-answer examples from our training dataset for this demonstration.\n", "- Validation Metric: We will establish a simple `validate_context_and_answer` metric. This metric verifies the accuracy of the predicted answer and ensures that the retrieved context includes the answer.\n", "- Specific Optimizer (Teleprompter): DSPy's compiler incorporates multiple teleprompters designed to optimize your programs effectively."], "metadata": {"collapsed": false}, "id": "5b52be516ff3a778"}, {"cell_type": "code", "source": ["from dspy.teleprompt import BootstrapFewShot\n", "\n", "# Validation logic: check that the predicted answer is correct.# Also check that the retrieved context does contain that answer.\n", "\n", "\n", "def validate_context_and_answer(example, pred, trace=None):\n", "    answer_EM = dspy.evaluate.answer_exact_match(example, pred)\n", "    answer_PM = dspy.evaluate.answer_passage_match(example, pred)\n", "    return answer_EM and answer_PM\n", "\n", "\n", "# Set up a basic teleprompter, which will compile our RAG program.\n", "teleprompter = BootstrapFewShot(metric=validate_context_and_answer)\n", "\n", "# Compile!\n", "compiled_rag = teleprompter.compile(rag, trainset=trainset)\n", "\n", "# Now compiled_rag is optimized and ready to answer your new question!\n", "# Now, let’s evaluate the compiled RAG program.\n", "score = evaluate_on_hotpotqa(compiled_rag, metric=metric)\n", "print(score)\n", "print(\"compile_rag:\", score)"], "metadata": {"collapsed": false}, "id": "99dbdfb317d7b23a", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": ["The Ragas score has increased from its previous value of 50.0 to 52.0, indicating an enhancement in answer quality."], "metadata": {"collapsed": false}, "id": "45ff14c8d941c78f"}, {"cell_type": "markdown", "source": ["## Summary\n", "DSPy marks a leap in language model interactions through its programmable interface, which facilitates algorithmic and automated optimization of model prompts and weights. By leveraging DSPy for RAG implementation, adaptability to varying language models or datasets becomes a breeze, drastically reducing the need for tedious manual interventions."], "metadata": {"collapsed": false}, "id": "94080faec499a03b"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}