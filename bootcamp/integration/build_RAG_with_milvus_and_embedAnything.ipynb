{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/integration/rag_with_milvus_and_embedAnything.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/integration/rag_with_milvus_and_embedAnything.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Building RAG with Milvus and EmbedAnything\n", "\n", "[EmbedAnything](https://github.com/StarlightSearch/EmbedAnything) is a blazing-fast, lightweight embedding pipeline built in Rust that supports text, PDFs, images, audio, and more.\n", "\n", "In this tutorial, we’ll demonstrate how to build a Retrieval-Augmented Generation (RAG) pipeline using EmbedAnything together with [Milvus](https://milvus.io). Rather than tightly coupling with any specific database, EmbedAnything uses a pluggable **adapter** system — adapters serve as wrappers that define how embeddings are formatted, indexed, and stored in the target vector store.\n", "\n", "By pairing EmbedAnything with a Milvus adapter, you can generate embeddings from diverse file types and efficiently store them in Milvus in just a few lines of code.\n", "\n", "> ⚠️ Note: While the adapter in EmbedAnything handles insertion into Milvus, it does not support search out of the box. To build a full RAG pipeline, you’ll also need to instantiate a MilvusClient separately and implement the retrieval logic (e.g., similarity search over vectors) as part of your application."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Preparation\n", "### Dependencies and Environment"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["! pip install -qU pymilvus openai embed_anything"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> If you are using Google Colab, to enable dependencies just installed, you may need to **restart the runtime** (click on the \"Runtime\" menu at the top of the screen, and select \"Restart session\" from the dropdown menu)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON> the Repository and Load Adapter\n", "\n", "Next, we’ll clone the [EmbedAnything](https://github.com/StarlightSearch/EmbedAnything) repo and add the `examples/adapters` directory to the Python path. This is where we store the custom Milvus adapter implementation, which allows EmbedAnything to communicate with Milvus for vector insertion."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ EmbedAnything cloned and adapter path added.\n"]}], "source": ["import sys\n", "\n", "# Clone the EmbedAnything repository if not already cloned\n", "![ -d \"EmbedAnything\" ] || git clone https://github.com/StarlightSearch/EmbedAnything.git\n", "\n", "# Add the `examples/adapters` directory to the Python path\n", "sys.path.append(\"EmbedAnything/examples/adapters\")\n", "print(\"✅ EmbedAnything cloned and adapter path added.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We will use OpenAI as the LLM in this RAG pipeline. You should prepare the [api key](https://platform.openai.com/docs/quickstart) `OPENAI_API_KEY` as an environment variable. "]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import os\n", "from openai import OpenAI\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-***********\"\n", "\n", "openai_client = OpenAI()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Build RAG\n", "### Initialize Milvus \n", "\n", "Before we embed any files, we need to prepare two components that interact with Mil<PERSON><PERSON>:\n", "\n", "1. `MilvusVectorAdapter` – This is the Milvus adapter for EmbedAnything, and is used **only for vector ingestion** (i.e., inserting embeddings and creating indexes). It currently does **not** support search operations.\n", "2. `MilvusClient` – This is the official client from `pymilvus`, which enables **full access** to Milvus capabilities including vector search, filtering, and collection management.\n", "\n", "To avoid confusion:\n", "- Think of `MilvusVectorAdapter` as your \"write-only\" tool for storing vectors.\n", "- Think of `MilvusClient` as your \"read-and-search\" engine to actually perform queries and retrieve documents for RAG."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Ok - Milvus DB connection established.\n", "Collection 'embed_anything_milvus_collection' created with index.\n"]}], "source": ["import embed_anything\n", "from embed_anything import (\n", "    WhichModel,\n", "    EmbeddingModel,\n", ")\n", "from milvus_db import MilvusVectorAdapter\n", "from pymilvus import MilvusClient\n", "\n", "# Official Milvus client for full operations\n", "milvus_client = MilvusClient(uri=\"./milvus.db\", token=\"\")\n", "\n", "# EmbedAnything adapter for pushing embeddings into Milvus\n", "index_name = \"embed_anything_milvus_collection\"\n", "milvus_adapter = MilvusVectorAdapter(\n", "    uri=\"./milvus.db\", token=\"\", collection_name=index_name\n", ")\n", "\n", "# Delete existing collection if it exists\n", "if milvus_client.has_collection(index_name):\n", "    milvus_client.drop_collection(index_name)\n", "\n", "# Create a new collection with dimension matching the embedding model later used\n", "milvus_adapter.create_index(dimension=384)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> As for the argument of `MilvusVectorAdapter` and `MilvusClient`:\n", "> - Setting the `uri` as a local file, e.g.`./milvus.db`, is the most convenient method, as it automatically utilizes [Milvus Lite](https://milvus.io/docs/milvus_lite.md) to store all data in this file.\n", "> - If you have large scale of data, say more than a million vectors, you can set up a more performant Milvus server on [Docker or Kubernetes](https://milvus.io/docs/quickstart.md). In this setup, please use the server address and port as your uri, e.g.`http://localhost:19530`. If you enable the authentication feature on Milvus, use \"<your_username>:<your_password>\" as the token, otherwise don't set the token.\n", "> - If you want to use [Zilliz Cloud](https://zilliz.com/cloud), the fully managed cloud service for Milvus, adjust the `uri` and `token`, which correspond to the [Public Endpoint and Api key](https://docs.zilliz.com/docs/on-zilliz-cloud-console#free-cluster-details) in Zilliz Cloud."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Initialize Embedding Model and Embed PDF Document\n", "\n", "Now we'll initialize the embedding model. We'll use the `all-MiniLM-L12-v2 model` from the sentence-transformers library, which is a lightweight yet powerful model for generating text embeddings. It produces 384-dimensional embeddings, so this aligns with our Milvus collection dimension being set to 384. This alignment is crucial and ensures compatibility between the vector dimensions stored in Milvus and those generated by the model.\n", "\n", "EmbedAnything supports a lot more embedding models. For more details, please refer to the [official documentation](https://github.com/StarlightSearch/EmbedAnything)."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# Initialize the embedding model\n", "model = EmbeddingModel.from_pretrained_hf(\n", "    WhichModel.Bert, model_id=\"sentence-transformers/all-MiniLM-L12-v2\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, let's embed a PDF file. EmbedAnything makes it easy to process PDF (and many more) documents and store their embeddings directly in Milvus."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Converted 12 embeddings for insertion.\n", "Successfully inserted 12 embeddings.\n"]}], "source": ["# Embed a PDF file\n", "data = embed_anything.embed_file(\n", "    \"./pdf_files/WhatisMilvus.pdf\",\n", "    embedder=model,\n", "    adapter=milvus_adapter,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Retrieve and Generate Response\n", "\n", "Again, the `MilvusVectorAdapter` from EmbedAnything currently is a lightweight abstraction for vector ingestion and indexing only. It **does not support search** or retrieval queries. Therefore, for search relevant documents to build our RAG pipeline, we must directly use the `MilvusClient` instance (`milvus_client`) to query our Milvus vector store."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Define a function to retrieve relevant documents from Milvus."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def retrieve_documents(question, top_k=3):\n", "    query_vector = list(\n", "        embed_anything.embed_query([question], embedder=model)[0].embedding\n", "    )\n", "    search_res = milvus_client.search(\n", "        collection_name=index_name,\n", "        data=[query_vector],\n", "        limit=top_k,\n", "        output_fields=[\"text\"],\n", "    )\n", "    docs = [(res[\"entity\"][\"text\"], res[\"distance\"]) for res in search_res[0]]\n", "    return docs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Define a function to generate a response using the retrieved documents in the RAG pipeline."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def generate_rag_response(question):\n", "    retrieved_docs = retrieve_documents(question)\n", "    context = \"\\n\".join([f\"Text: {doc[0]}\\n\" for doc in retrieved_docs])\n", "    system_prompt = (\n", "        \"You are an AI assistant. Provide answers based on the given context.\"\n", "    )\n", "    user_prompt = f\"\"\"\n", "    Use the following pieces of information to answer the question. If the information is not in the context, say you don't know.\n", "    \n", "    Context:\n", "    {context}\n", "    \n", "    Question: {question}\n", "    \"\"\"\n", "    response = openai_client.chat.completions.create(\n", "        model=\"gpt-4o-mini\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt},\n", "        ],\n", "    )\n", "    return response.choices[0].message.content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's test the RAG pipeline with a sample question."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Question: How does <PERSON><PERSON><PERSON><PERSON> search for similar documents?\n", "Answer: Milvus searches for similar documents primarily through Approximate Nearest Neighbor (ANN) search, which finds the top K vectors closest to a given query vector. It also supports various other types of searches, such as filtering search under specified conditions, range search within a specified radius, hybrid search based on multiple vector fields, and keyword search based on BM25. Additionally, it can perform reranking to adjust the order of search results based on additional criteria, refining the initial ANN search results.\n"]}], "source": ["question = \"How does <PERSON><PERSON><PERSON><PERSON> search for similar documents?\"\n", "answer = generate_rag_response(question)\n", "print(f\"Question: {question}\")\n", "print(f\"Answer: {answer}\")"]}], "metadata": {"kernelspec": {"display_name": "test", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}