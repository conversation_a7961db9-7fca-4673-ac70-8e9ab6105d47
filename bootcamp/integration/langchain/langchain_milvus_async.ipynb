{"cells": [{"cell_type": "markdown", "id": "53b860c7", "metadata": {}, "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/integration/langchain/langchain_milvus_async.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/integration/langchain/langchain_milvus_async.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>\n", "</a>\n"]}, {"cell_type": "markdown", "id": "8819ee2e-238c-4f46-a57b-1986ba0999af", "metadata": {}, "source": ["# Asynchronous Functions in LangChain Milvus Integration\n", "\n", "\n", "This tutorial explores how to leverage asynchronous functions in [langchain-milvus](https://github.com/langchain-ai/langchain-milvus) to build high-performance applications. By using async methods, you can significantly improve your application's throughput and responsiveness, especially when dealing with large-scale retrieval. Whether you're building a real-time recommendation system, implementing semantic search in your application, or creating a RAG (Retrieval-Augmented Generation) pipeline, async operations can help you handle concurrent requests more efficiently. The high-performance vector database Milvus combined with LangChain's powerful LLM abstractions can provide a robust foundation for building scalable AI applications.\n", "\n", "## Async API Overview\n", "\n", "langchain-milvus provides comprehensive asynchronous operation support, significantly improving performance in large-scale concurrent scenarios. The async API maintains consistent interface design with sync API.\n", "\n", "### Core Async Functions\n", "To use async operations in langchain-milvus, simply add an `a` prefix to method names. This allows for better resource utilization and improved throughput when handling concurrent retrieval requests.\n", "\n", "\n", "| Operation Type | Sync Method | Async Method | Description |\n", "|---------------|-------------|--------------|-------------|\n", "| Add Texts | `add_texts()` | `aadd_texts()` | Add texts to vector store |\n", "| Add Documents | `add_documents()` | `aadd_documents()` | Add documents to vector store |\n", "| Add Embeddings | `add_embeddings()` | `aadd_embeddings()` | Add embedding vectors |\n", "| Similarity Search | `similarity_search()` | `asimilarity_search()` | Semantic search by text |\n", "| Vector Search | `similarity_search_by_vector()` | `asimilarity_search_by_vector()` | Semantic search by vector |\n", "| Search with Score | `similarity_search_with_score()` | `asimilarity_search_with_score()` | Semantic search by text and return similarity scores |\n", "| Vector Search with Score | `similarity_search_with_score_by_vector()` | `asimilarity_search_with_score_by_vector()` | Semantic search by vector and return similarity scores |\n", "| Diversity Search | `max_marginal_relevance_search()` | `amax_marginal_relevance_search()` | MMR search (return similar ones while also optimizing for diversity) |\n", "| Vector Diversity Search | `max_marginal_relevance_search_by_vector()` | `amax_marginal_relevance_search_by_vector()` |  MMR search by vector |\n", "| Delete Operation | `delete()` | `adelete()` | Delete documents |\n", "| Upsert Operation | `upsert()` | `aupsert()` | Upsert (update if existing, otherwise insert) documents |\n", "| Metadata Search | `search_by_metadata()` | `asearch_by_metadata()` | Query with metadata filtering |\n", "| Get Primary Keys | `get_pks()` | `aget_pks()` | Get primary keys by expression |\n", "| Create from Texts | `from_texts()` | `afrom_texts()` | Create vector store from texts |\n", "\n", "For more detailed information about these functions, please refer to the [API Reference](https://python.langchain.com/api_reference/milvus/vectorstores/langchain_milvus.vectorstores.milvus.Milvus.html#milvus).\n", "\n", "### Performance Benefits\n", "\n", "Async operations provide significant performance improvements when handling large volumes of concurrent requests, particularly suitable for:\n", "- Batch document processing\n", "- High-concurrency search scenarios\n", "- Production RAG applications\n", "- Large-scale data import/export\n", "\n", "In this tutorial, we'll demonstrate these performance benefits through detailed comparisons of synchronous and asynchronous operations, showing you how to leverage async APIs for optimal performance in your applications."]}, {"cell_type": "markdown", "id": "3d6c3dbb", "metadata": {}, "source": ["\n", "## Before you begin\n", "\n", "Code snippets on this page require the following dependencies:"]}, {"cell_type": "code", "execution_count": 1, "id": "5b2dc820-63be-4f7e-b4ac-63368637acd5", "metadata": {}, "outputs": [], "source": ["! pip install -U pymilvus langchain-milvus langchain langchain-core langchain-openai langchain-text-splitters nest-asyncio"]}, {"cell_type": "markdown", "id": "774695e3-65f4-40b9-8fca-6df34c46fe91", "metadata": {}, "source": ["> If you are using Google Colab, to enable dependencies just installed, you may need to **restart the runtime** (click on the \"Runtime\" menu at the top of the screen, and select \"Restart session\" from the dropdown menu).\n", "\n", "We will use OpenAI models. You should prepare the [api key](https://platform.openai.com/docs/quickstart) `OPENAI_API_KEY` as an environment variable:"]}, {"cell_type": "code", "execution_count": 2, "id": "59fc4303-bfcd-4420-95c0-7fbd12f83c4c", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-***********\""]}, {"cell_type": "markdown", "id": "326581b7-bb5c-4731-a7ce-3beb5c894766", "metadata": {}, "source": ["If you are using Jupyter Notebook, you need to run this line of code before running the asynchronous code:"]}, {"cell_type": "code", "execution_count": 3, "id": "bfec8144-2a41-494f-bfee-38a67709588e", "metadata": {}, "outputs": [], "source": ["import nest_asyncio\n", "\n", "nest_asyncio.apply()"]}, {"cell_type": "markdown", "id": "e4db9cac-59fa-488c-b95d-3c415d5c6d56", "metadata": {}, "source": ["## Exploring Async APIs and Performance Comparison\n", "\n", "Now let's dive deeper into the performance comparison between synchronous and asynchronous operations with langchain-milvus.\n", "\n", "First, import the necessary libraries:"]}, {"cell_type": "code", "execution_count": 4, "id": "35e627b2-e224-42ec-bc41-cd89b939b56f", "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import random\n", "import time\n", "from langchain_core.documents import Document\n", "from langchain_openai import OpenAIEmbeddings\n", "from langchain_milvus import Milvus\n", "\n", "# Define the Milvus URI\n", "URI = \"http://localhost:19530\""]}, {"cell_type": "markdown", "id": "89bfc1b1-3536-4f76-aeaf-39a693547ebf", "metadata": {}, "source": ["### Setting up Test Functions\n", "\n", "Let's create helper functions to generate test data:"]}, {"cell_type": "code", "execution_count": 5, "id": "b74dc891-4fcf-4b84-8bc7-6749c060507a", "metadata": {}, "outputs": [], "source": ["def random_id():\n", "    \"\"\"Generate a random string ID\"\"\"\n", "    random_num_str = \"\"\n", "    for _ in range(16):\n", "        random_digit = str(random.randint(0, 9))\n", "        random_num_str += random_digit\n", "    return random_num_str\n", "\n", "\n", "def generate_test_documents(num_docs):\n", "    \"\"\"Generate test documents for performance testing\"\"\"\n", "    docs = []\n", "    for i in range(num_docs):\n", "        content = (\n", "            f\"This is test document {i} with some random content: {random.random()}\"\n", "        )\n", "        metadata = {\n", "            \"id\": f\"doc_{i}\",\n", "            \"score\": random.random(),\n", "            \"category\": f\"cat_{i % 5}\",\n", "        }\n", "        doc = Document(page_content=content, metadata=metadata)\n", "        docs.append(doc)\n", "    return docs"]}, {"cell_type": "markdown", "id": "e6ced2f4-08e2-4d4e-bb81-4f611edc64ab", "metadata": {}, "source": ["### Initialize the Vector Store\n", "\n", "Before we can run our performance tests, we need to set up a clean Milvus vector store. This function ensures we start with a fresh collection for each test, eliminating any interference from previous data:"]}, {"cell_type": "code", "execution_count": 6, "id": "cb99b988-282e-4fde-b6ca-438abd5cc753", "metadata": {}, "outputs": [], "source": ["def init_vector_store():\n", "    \"\"\"Initialize and return a fresh vector store for testing\"\"\"\n", "    return <PERSON><PERSON><PERSON><PERSON>(\n", "        embedding_function=OpenAIEmbeddings(),\n", "        collection_name=\"langchain_perf_test\",\n", "        connection_args={\"uri\": URI},\n", "        auto_id=True,\n", "        drop_old=True,  # Always start with a fresh collection\n", "    )"]}, {"cell_type": "markdown", "id": "752aa719-def9-4d45-bce0-105ba645bf67", "metadata": {}, "source": ["### Async vs Sync: Add Documents\n", "\n", "Now let's compare the performance of synchronous vs asynchronous document addition. These functions will help us measure how much faster async operations can be when adding multiple documents to the vector store. The async version creates tasks for each document addition and runs them concurrently, while the sync version processes documents one by one:"]}, {"cell_type": "code", "execution_count": 7, "id": "ab73b158-3e14-4702-9046-9e8fb1900823", "metadata": {}, "outputs": [], "source": ["async def async_add(milvus_store, num_adding):\n", "    \"\"\"Add documents asynchronously and measure the time\"\"\"\n", "    docs = generate_test_documents(num_adding)\n", "    start_time = time.time()\n", "    tasks = []\n", "    for doc in docs:\n", "        # Create tasks for each document addition\n", "        task = milvus_store.aadd_documents([doc])\n", "        tasks.append(task)\n", "    results = await asyncio.gather(*tasks)\n", "    end_time = time.time()\n", "    return end_time - start_time\n", "\n", "\n", "def sync_add(milvus_store, num_adding):\n", "    \"\"\"Add documents synchronously and measure the time\"\"\"\n", "    docs = generate_test_documents(num_adding)\n", "    start_time = time.time()\n", "    for doc in docs:\n", "        result = milvus_store.add_documents([doc])\n", "    end_time = time.time()\n", "    return end_time - start_time"]}, {"cell_type": "markdown", "id": "e2c91767-51a6-4477-acaf-0179ae9fc9a9", "metadata": {}, "source": ["Now let's execute our performance tests with different document counts to see the real-world performance differences. We'll test with varying loads to understand how async operations scale compared to their synchronous counterparts. The tests will measure execution time for both approaches and help demonstrate the performance benefits of asynchronous operations:"]}, {"cell_type": "code", "execution_count": 8, "id": "8e85d590-4ae1-4025-9ca9-6c46f7015f7f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-05 10:44:12,274 [DEBUG][_create_connection]: Created new connection using: dd5f77bb78964c079da42c2446b03bf6 (async_milvus_client.py:599)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Async add for 10 documents took 1.74 seconds\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-05 10:44:16,940 [DEBUG][_create_connection]: Created new connection using: 8b13404a78654cdd9b790371eb44e427 (async_milvus_client.py:599)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Async add for 100 documents took 2.77 seconds\n", "Sync add for 10 documents took 5.36 seconds\n", "Sync add for 100 documents took 65.60 seconds\n"]}], "source": ["add_counts = [10, 100]\n", "\n", "# Get the event loop\n", "loop = asyncio.get_event_loop()\n", "\n", "# Create a new vector store for testing\n", "milvus_store = init_vector_store()\n", "\n", "# Test async document addition\n", "for count in add_counts:\n", "\n", "    async def measure_async_add():\n", "        async_time = await async_add(milvus_store, count)\n", "        print(f\"Async add for {count} documents took {async_time:.2f} seconds\")\n", "        return async_time\n", "\n", "    loop.run_until_complete(measure_async_add())\n", "\n", "# Reset vector store for sync tests\n", "milvus_store = init_vector_store()\n", "\n", "# Test sync document addition\n", "for count in add_counts:\n", "    sync_time = sync_add(milvus_store, count)\n", "    print(f\"Sync add for {count} documents took {sync_time:.2f} seconds\")"]}, {"cell_type": "markdown", "id": "d06e5de5-367e-4e52-8755-aee15e94c1f3", "metadata": {}, "source": ["### Async vs Sync: Search\n", "\n", "For the search performance comparison, we'll need to populate the vector store first. The following functions will help us measure search performance by creating multiple concurrent search queries and comparing the execution time between synchronous and asynchronous approaches:"]}, {"cell_type": "code", "execution_count": 9, "id": "deb597ce-2f28-4d12-9c19-db74606bc228", "metadata": {}, "outputs": [], "source": ["def populate_vector_store(milvus_store, num_docs=1000):\n", "    \"\"\"Populate the vector store with test documents\"\"\"\n", "    docs = generate_test_documents(num_docs)\n", "    milvus_store.add_documents(docs)\n", "    return docs\n", "\n", "\n", "async def async_search(milvus_store, num_queries):\n", "    \"\"\"Perform async searches and measure the time\"\"\"\n", "    start_time = time.time()\n", "    tasks = []\n", "    for i in range(num_queries):\n", "        query = f\"test document {i % 50}\"\n", "        task = milvus_store.asimilarity_search(query=query, k=3)\n", "        tasks.append(task)\n", "    results = await asyncio.gather(*tasks)\n", "    end_time = time.time()\n", "    return end_time - start_time\n", "\n", "\n", "def sync_search(milvus_store, num_queries):\n", "    \"\"\"Perform sync searches and measure the time\"\"\"\n", "    start_time = time.time()\n", "    for i in range(num_queries):\n", "        query = f\"test document {i % 50}\"\n", "        result = milvus_store.similarity_search(query=query, k=3)\n", "    end_time = time.time()\n", "    return end_time - start_time"]}, {"cell_type": "markdown", "id": "ec47e15e-7bad-4e0c-8ab9-38a301083831", "metadata": {}, "source": ["Now let's run comprehensive search performance tests to see how async operations scale compared to synchronous ones. We'll test with different query volumes to demonstrate the performance benefits of asynchronous operations, especially as the number of concurrent operations increases:"]}, {"cell_type": "code", "execution_count": 10, "id": "089e22fe-a1b9-48e9-826f-c39badafd3db", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-05 10:45:28,131 [DEBUG][_create_connection]: Created new connection using: 851824591c64415baac843e676e78cdd (async_milvus_client.py:599)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Async search for 10 queries took 2.31 seconds\n", "Async search for 100 queries took 3.72 seconds\n", "Sync search for 10 queries took 6.07 seconds\n", "Sync search for 100 queries took 54.22 seconds\n"]}], "source": ["# Initialize and populate the vector store\n", "milvus_store = init_vector_store()\n", "populate_vector_store(milvus_store, 1000)\n", "\n", "query_counts = [10, 100]\n", "\n", "# Test async search\n", "for count in query_counts:\n", "\n", "    async def measure_async_search():\n", "        async_time = await async_search(milvus_store, count)\n", "        print(f\"Async search for {count} queries took {async_time:.2f} seconds\")\n", "        return async_time\n", "\n", "    loop.run_until_complete(measure_async_search())\n", "\n", "# Test sync search\n", "for count in query_counts:\n", "    sync_time = sync_search(milvus_store, count)\n", "    print(f\"Sync search for {count} queries took {sync_time:.2f} seconds\")"]}, {"cell_type": "markdown", "id": "22e20f31-4005-4266-aaa1-c3b7e0aef508", "metadata": {}, "source": ["### Async vs Sync: Delete\n", "\n", "Delete operations are another critical aspect where async operations can provide significant performance improvements. Let's create functions to measure the performance difference between synchronous and asynchronous delete operations. These tests will help demonstrate how async operations can handle batch deletions more efficiently:"]}, {"cell_type": "code", "execution_count": 11, "id": "77a9c2ee-1a32-4661-aafd-258ef6623920", "metadata": {}, "outputs": [], "source": ["async def async_delete(milvus_store, num_deleting):\n", "    \"\"\"Delete documents asynchronously and measure the time\"\"\"\n", "    start_time = time.time()\n", "    tasks = []\n", "    for i in range(num_deleting):\n", "        expr = f\"id == 'doc_{i}'\"\n", "        task = milvus_store.adelete(expr=expr)\n", "        tasks.append(task)\n", "    results = await asyncio.gather(*tasks)\n", "    end_time = time.time()\n", "    return end_time - start_time\n", "\n", "\n", "def sync_delete(milvus_store, num_deleting):\n", "    \"\"\"Delete documents synchronously and measure the time\"\"\"\n", "    start_time = time.time()\n", "    for i in range(num_deleting):\n", "        expr = f\"id == 'doc_{i}'\"\n", "        result = milvus_store.delete(expr=expr)\n", "    end_time = time.time()\n", "    return end_time - start_time"]}, {"cell_type": "markdown", "id": "706dba7d-de3e-4507-8a5b-a92112aadc3f", "metadata": {}, "source": ["Now let's execute the delete performance tests to quantify the performance difference. We'll start with a fresh vector store populated with test data, then perform delete operations using both synchronous and asynchronous approaches:"]}, {"cell_type": "code", "execution_count": 12, "id": "75d15e2e-ec37-4ecf-a88f-77f72c4c2ba6", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-05 10:46:57,211 [DEBUG][_create_connection]: Created new connection using: 504e9ce3be92411e87077971c82baca2 (async_milvus_client.py:599)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Async delete for 10 operations took 0.58 seconds\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-05 10:47:12,309 [DEBUG][_create_connection]: Created new connection using: 22c1513b444e4c40936e2176d7a1a154 (async_milvus_client.py:599)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Async delete for 100 operations took 0.61 seconds\n", "Sync delete for 10 operations took 2.82 seconds\n", "Sync delete for 100 operations took 29.21 seconds\n"]}], "source": ["delete_counts = [10, 100]\n", "\n", "# Initialize and populate the vector store\n", "milvus_store = init_vector_store()\n", "populate_vector_store(milvus_store, 1000)\n", "\n", "# Test async delete\n", "for count in delete_counts:\n", "\n", "    async def measure_async_delete():\n", "        async_time = await async_delete(milvus_store, count)\n", "        print(f\"Async delete for {count} operations took {async_time:.2f} seconds\")\n", "        return async_time\n", "\n", "    loop.run_until_complete(measure_async_delete())\n", "\n", "# Reset and repopulate the vector store for sync tests\n", "milvus_store = init_vector_store()\n", "populate_vector_store(milvus_store, 1000)\n", "\n", "# Test sync delete\n", "for count in delete_counts:\n", "    sync_time = sync_delete(milvus_store, count)\n", "    print(f\"Sync delete for {count} operations took {sync_time:.2f} seconds\")"]}, {"cell_type": "markdown", "id": "9a0bdc1c-2277-41da-9f17-bfde1fc35d52", "metadata": {}, "source": ["## Conclusion\n", "\n", "This tutorial demonstrated the significant performance advantages of using asynchronous operations with LangChain and Milvus. We compared the synchronous and asynchronous versions of add, search, and delete operations, showing how async operations can provide substantial speed improvements, especially for large batch operations.\n", "\n", "Key takeaways:\n", "1. Async operations deliver the most benefit when performing many individual operations that can run in parallel\n", "2. For workload that generates higher throughput, the performance gap between sync and async operations widens\n", "3. Async operations fully utilize the compute power of the machines\n", "\n", "When building production RAG applications with LangChain and Milvus, consider using the async API when performance is a concern, especially for concurrent operations."]}], "metadata": {"kernelspec": {"display_name": "py310", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}