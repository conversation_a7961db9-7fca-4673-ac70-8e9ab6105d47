{"cells": [{"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/integration/langchain/full_text_search_with_langchain.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/integration/langchain/full_text_search_with_langchain.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>\n", "</a>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Using Full-Text Search with <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>\n", "\n", "[Full-text search](https://milvus.io/docs/full-text-search.md#Full-Text-Search) is a traditional method for retrieving documents by matching specific keywords or phrases in the text. It ranks results based on relevance scores calculated from factors like term frequency. While semantic search is better at understanding meaning and context, full-text search excels at precise keyword matching, making it a useful complement to semantic search. The BM25 algorithm is widely used for ranking in full-text search and plays a key role in Retrieval-Augmented Generation (RAG).\n", "\n", "[Milvus 2.5](https://milvus.io/blog/introduce-milvus-2-5-full-text-search-powerful-metadata-filtering-and-more.md) introduces native full-text search capabilities using BM25. This approach converts text into sparse vectors that represent BM25 scores. You can simply input raw text and Milvus will automatically generate and store the sparse vectors, with no manual sparse embedding generation required.\n", " \n", "Lang<PERSON>hain's integration with Milvus has also introduced this feature, simplifying the process of incorporating full-text search into RAG applications. By combining full-text search with semantic search with dense vectors, you can achieve a hybrid approach that leverages both semantic context from dense embeddings and precise keyword relevance from word matching. This integration enhances the accuracy, relevance, and user experience of search systems.\n", "\n", "This tutorial will show how to use <PERSON><PERSON><PERSON><PERSON> and Mil<PERSON>s to implement full-text search in your application.\n", "\n", "> - Full-text search is currently available in Milvus Standalone, Milvus Distributed, and Zilliz Cloud, though not yet supported in Milvus Lite (which has this feature planned for future implementation). <NAME_EMAIL> for more information.\n", "> - Before proceeding with this tutorial, ensure you have a basic understanding of [full-text search](https://milvus.io/docs/full-text-search.md#Full-Text-Search) and the [basic usage](https://milvus.io/docs/basic_usage_langchain.md) of LangChain Milvus integration."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prerequisites\n", "\n", "Before running this notebook, make sure you have the following dependencies installed:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["! pip install --upgrade --quiet  langchain langchain-core langchain-community langchain-text-splitters langchain-milvus langchain-openai bs4 #langchain-voyageai"]}, {"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["> If you are using Google Colab, to enable dependencies just installed, you may need to **restart the runtime** (click on the \"Runtime\" menu at the top of the screen, and select \"Restart session\" from the dropdown menu)."]}, {"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["We will use the models from OpenAI. You should prepare the environment variables `OPENAI_API_KEY` from [OpenAI](https://platform.openai.com/docs/quickstart)."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-***********\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Specify your Milvus server `URI` (and optionally the `TOKEN`). For how to install and start the Milvus server following this [guide](https://milvus.io/docs/install_standalone-docker-compose.md). "]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["URI = \"http://localhost:19530\"\n", "# TOKEN = ..."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Prepare some examples documents:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from langchain_core.documents import Document\n", "\n", "docs = [\n", "    Document(page_content=\"I like this apple\", metadata={\"category\": \"fruit\"}),\n", "    Document(page_content=\"I like swimming\", metadata={\"category\": \"sport\"}),\n", "    Document(page_content=\"I like dogs\", metadata={\"category\": \"pets\"}),\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initialization with BM25 Function\n", "### Hybrid Search\n", "\n", "For full-text search Milvus VectorStore accepts a `builtin_function` parameter. Through this parameter, you can pass in an instance of the `BM25BuiltInFunction`. This is different than semantic search which usually passes dense embeddings to the `VectorStore`, \n", "\n", "Here is a simple example of hybrid search in Milvus with OpenAI dense embedding for semantic search and BM25 for full-text search:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"is_executing": true, "name": "#%%\n"}}, "outputs": [], "source": ["from langchain_milvus import Milvus, BM25BuiltInFunction\n", "from langchain_openai import OpenAIEmbeddings\n", "\n", "\n", "vectorstore = Milvus.from_documents(\n", "    documents=docs,\n", "    embedding=OpenAIEmbeddings(),\n", "    builtin_function=BM25BuiltInFunction(),\n", "    # `dense` is for OpenAI embeddings, `sparse` is the output field of BM25 function\n", "    vector_field=[\"dense\", \"sparse\"],\n", "    connection_args={\n", "        \"uri\": URI,\n", "    },\n", "    consistency_level=\"Strong\",\n", "    # drop_old=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In the code above, we define an instance of `BM25BuiltInFunction` and pass it to the `Milvus` object. `BM25BuiltInFunction` is a lightweight wrapper class for [`Function`](https://milvus.io/docs/manage-collections.md#Function) in Milvus.\n", "\n", "You can specify the input and output fields for this function in the parameters of the `BM25BuiltInFunction`:\n", "- `input_field_names` (str): The name of the input field, default is `text`. It indicates which field this function reads as input.\n", "- `output_field_names` (str): The name of the output field, default is `sparse`. It indicates which field this function outputs the computed result to.\n", "\n", "Note that in the Milvus initialization parameters mentioned above, we also specify `vector_field=[\"dense\", \"sparse\"]`. Since the `sparse` field is taken as the output field defined by the `BM25BuiltInFunction`, the other `dense` field will be automatically assigned to the output field of OpenAIEmbeddings.\n", "\n", "In practice, especially when combining multiple embeddings or functions, we recommend explicitly specifying the input and output fields for each function to avoid ambiguity.\n", "\n", "In the following example, we specify the input and output fields of `BM25BuiltInFunction` explicitly, making it clear which field the built-in function is for.\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["['dense1', 'dense2', 'sparse']"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# from langchain_voyageai import VoyageAIEmbeddings\n", "\n", "embedding1 = OpenAIEmbeddings(model=\"text-embedding-ada-002\")\n", "embedding2 = OpenAIEmbeddings(model=\"text-embedding-3-large\")\n", "# embedding2 = VoyageAIEmbeddings(model=\"voyage-3\")  # You can also use embedding from other embedding model providers, e.g VoyageAIEmbeddings\n", "\n", "\n", "vectorstore = Milvus.from_documents(\n", "    documents=docs,\n", "    embedding=[embedding1, embedding2],\n", "    builtin_function=BM25BuiltInFunction(\n", "        input_field_names=\"text\", output_field_names=\"sparse\"\n", "    ),\n", "    text_field=\"text\",  # `text` is the input field name of BM25BuiltInFunction\n", "    # `sparse` is the output field name of BM25BuiltInFunction, and `dense1` and `dense2` are the output field names of embedding1 and embedding2\n", "    vector_field=[\"dense1\", \"dense2\", \"sparse\"],\n", "    connection_args={\n", "        \"uri\": URI,\n", "    },\n", "    consistency_level=\"Strong\",\n", "    # drop_old=True,\n", ")\n", "\n", "vectorstore.vector_fields"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this example, we have three vector fields. Among them, `sparse` is used as the output field for `BM25BuiltInFunction`, while the other two, `dense1` and `dense2`, are automatically assigned as the output fields for the two `OpenAIEmbeddings` models (based on the order).  \n", "\n", "In this way, you can define multiple vector fields and assign different combinations of embeddings or functions to them, to implement hybrid search."]}, {"cell_type": "markdown", "metadata": {}, "source": ["When performing hybrid search, we just need to pass in the query text and optionally set the topK and reranker parameters. The `vectorstore` instance will automatically handle the vector embeddings and built-in functions and finally use a reranker to refine the results. The underlying implementation details of the searching process are hidden from the user."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'category': 'fruit', 'pk': 454646931479251897}, page_content='I like this apple')]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["vectorstore.similarity_search(\n", "    \"Do I like apples?\", k=1\n", ")  # , ranker_type=\"weighted\", ranker_params={\"weights\":[0.3, 0.3, 0.4]})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For more information about hybrid search, you can refer to the [Hybrid Search introduction](https://milvus.io/docs/multi-vector-search.md#Hybrid-Search) and this [<PERSON><PERSON><PERSON><PERSON> hybrid search tutorial](https://milvus.io/docs/milvus_hybrid_search_retriever.md) ."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### BM25 search without embedding\n", "\n", "If you want to perform only full-text search with BM25 function without using any embedding-based semantic search, you can set the embedding parameter to `None` and keep only the `builtin_function` specified as the BM25 function instance. The vector field only has \"sparse\" field. For example:  "]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["['sparse']"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["vectorstore = Milvus.from_documents(\n", "    documents=docs,\n", "    embedding=None,\n", "    builtin_function=BM25BuiltInFunction(\n", "        output_field_names=\"sparse\",\n", "    ),\n", "    vector_field=\"sparse\",\n", "    connection_args={\n", "        \"uri\": URI,\n", "    },\n", "    consistency_level=\"Strong\",\n", "    # drop_old=True,\n", ")\n", "\n", "vectorstore.vector_fields"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Customize analyzer\n", "\n", "Analyzers are essential in full-text search by breaking the sentence into tokens and performing lexical analysis like stemming and stop word removal. Analyzers are usually language-specific. You can refer to [this guide](https://milvus.io/docs/analyzer-overview.md#Analyzer-Overview) to learn more about analyzers in Milvus.\n", "\n", "Milvus supports two types of analyzers: **Built-in Analyzers** and **Custom Analyzers**. By default, the `BM25BuiltInFunction` will use the [standard built-in analyzer](https://milvus.io/docs/standard-analyzer.md), which is the most basic analyzer that tokenizes the text with punctuation. \n", "\n", "If you want to use a different analyzer or customize the analyzer, you can pass in the `analyzer_params` parameter in the `BM25BuiltInFunction` initialization.\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["analyzer_params_custom = {\n", "    \"tokenizer\": \"standard\",\n", "    \"filter\": [\n", "        \"lowercase\",  # Built-in filter\n", "        {\"type\": \"length\", \"max\": 40},  # Custom filter\n", "        {\"type\": \"stop\", \"stop_words\": [\"of\", \"to\"]},  # Custom filter\n", "    ],\n", "}\n", "\n", "\n", "vectorstore = Milvus.from_documents(\n", "    documents=docs,\n", "    embedding=OpenAIEmbeddings(),\n", "    builtin_function=BM25BuiltInFunction(\n", "        output_field_names=\"sparse\",\n", "        enable_match=True,\n", "        analyzer_params=analyzer_params_custom,\n", "    ),\n", "    vector_field=[\"dense\", \"sparse\"],\n", "    connection_args={\n", "        \"uri\": URI,\n", "    },\n", "    consistency_level=\"Strong\",\n", "    # drop_old=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can take a look at the schema of the Milvus collection and make sure the customized analyzer is set up correctly."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'auto_id': True, 'description': '', 'fields': [{'name': 'text', 'description': '', 'type': <DataType.VARCHAR: 21>, 'params': {'max_length': 65535, 'enable_match': True, 'enable_analyzer': True, 'analyzer_params': {'tokenizer': 'standard', 'filter': ['lowercase', {'type': 'length', 'max': 40}, {'type': 'stop', 'stop_words': ['of', 'to']}]}}}, {'name': 'pk', 'description': '', 'type': <DataType.INT64: 5>, 'is_primary': True, 'auto_id': True}, {'name': 'dense', 'description': '', 'type': <DataType.FLOAT_VECTOR: 101>, 'params': {'dim': 1536}}, {'name': 'sparse', 'description': '', 'type': <DataType.SPARSE_FLOAT_VECTOR: 104>, 'is_function_output': True}, {'name': 'category', 'description': '', 'type': <DataType.VARCHAR: 21>, 'params': {'max_length': 65535}}], 'enable_dynamic_field': False, 'functions': [{'name': 'bm25_function_de368e79', 'description': '', 'type': <FunctionType.BM25: 1>, 'input_field_names': ['text'], 'output_field_names': ['sparse'], 'params': {}}]}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["vectorstore.col.schema"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For more concept details, e.g., `analyzer`, `tokenizer`, `filter`, `enable_match`, `analyzer_params`, please refer to the [analyzer documentation](https://milvus.io/docs/analyzer-overview.md)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Using Hybrid Search and Reranking in RAG\n", "We have learned how to use the basic BM25 build-in function in LangChain and Milvus. Let's introduce an optimized RAG implementation with hybrid search and reranking.\n", "\n", "\n", "![](../../pics/advanced_rag/hybrid_and_rerank.png)\n", "\n", "This diagram shows the Hybrid Retrieve & Reranking process, combining BM25 for keyword matching and vector search for semantic retrieval. Results from both methods are merged, reranked, and passed to an LLM to generate the final answer.\n", "\n", "Hybrid search balances precision and semantic understanding, improving accuracy and robustness for diverse queries. It retrieves candidates with BM25 full-text search and vector search, ensuring both semantic, context-aware, and accurate retrieval.\n", "\n", "Let's get started with an example."]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "### Prepare the data\n", "\n", "We use the Langchain WebBaseLoader to load documents from web sources and split them into chunks using the RecursiveCharacterTextSplitter.\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["Document(metadata={'source': 'https://lilianweng.github.io/posts/2023-06-23-agent/'}, page_content='Fig. 1. Overview of a LLM-powered autonomous agent system.\\nComponent One: Planning#\\nA complicated task usually involves many steps. An agent needs to know what they are and plan ahead.\\nTask Decomposition#\\nChain of thought (CoT; <PERSON> et al. 2022) has become a standard prompting technique for enhancing model performance on complex tasks. The model is instructed to “think step by step” to utilize more test-time computation to decompose hard tasks into smaller and simpler steps. CoT transforms big tasks into multiple manageable tasks and shed lights into an interpretation of the model’s thinking process.\\nTree of Thoughts (<PERSON> et al. 2023) extends CoT by exploring multiple reasoning possibilities at each step. It first decomposes the problem into multiple thought steps and generates multiple thoughts per step, creating a tree structure. The search process can be BFS (breadth-first search) or DFS (depth-first search) with each state evaluated by a classifier (via a prompt) or majority vote.\\nTask decomposition can be done (1) by LLM with simple prompting like \"Steps for XYZ.\\\\n1.\", \"What are the subgoals for achieving XYZ?\", (2) by using task-specific instructions; e.g. \"Write a story outline.\" for writing a novel, or (3) with human inputs.\\nAnother quite distinct approach, LLM+P (<PERSON> et al. 2023), involves relying on an external classical planner to do long-horizon planning. This approach utilizes the Planning Domain Definition Language (PDDL) as an intermediate interface to describe the planning problem. In this process, LLM (1) translates the problem into “Problem PDDL”, then (2) requests a classical planner to generate a PDDL plan based on an existing “Domain PDDL”, and finally (3) translates the PDDL plan back into natural language. Essentially, the planning step is outsourced to an external tool, assuming the availability of domain-specific PDDL and a suitable planner which is common in certain robotic setups but not in many other domains.\\nSelf-Reflection#')"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["import bs4\n", "from langchain_community.document_loaders import WebBaseLoader\n", "from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "\n", "# Create a WebBaseLoader instance to load documents from web sources\n", "loader = WebBaseLoader(\n", "    web_paths=(\n", "        \"https://lilianweng.github.io/posts/2023-06-23-agent/\",\n", "        \"https://lilianweng.github.io/posts/2023-03-15-prompt-engineering/\",\n", "    ),\n", "    bs_kwargs=dict(\n", "        parse_only=bs4.SoupStrainer(\n", "            class_=(\"post-content\", \"post-title\", \"post-header\")\n", "        )\n", "    ),\n", ")\n", "# Load documents from web sources using the loader\n", "documents = loader.load()\n", "# Initialize a RecursiveCharacterTextSplitter for splitting text into chunks\n", "text_splitter = RecursiveCharacterTextSplitter(chunk_size=2000, chunk_overlap=200)\n", "\n", "# Split the documents into chunks using the text_splitter\n", "docs = text_splitter.split_documents(documents)\n", "\n", "# Let's take a look at the first document\n", "docs[1]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load the document into Milvus vector store\n", "As the introduction above, we initialize and load the prepared documents into Milvus vector store, which contains two vector fields: `dense` is for the OpenAI embedding and `sparse` is for the BM25 function."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["vectorstore = Milvus.from_documents(\n", "    documents=docs,\n", "    embedding=OpenAIEmbeddings(),\n", "    builtin_function=BM25BuiltInFunction(),\n", "    vector_field=[\"dense\", \"sparse\"],\n", "    connection_args={\n", "        \"uri\": URI,\n", "    },\n", "    consistency_level=\"Strong\",\n", "    # drop_old=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Build RAG chain\n", "We prepare the LLM instance and prompt, then conbine them into a RAG pipeline using the LangChain Expression Language."]}, {"cell_type": "code", "execution_count": 12, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["from langchain_core.runnables import RunnablePassthrough\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_openai import ChatOpenAI\n", "\n", "# Initialize the OpenAI language model for response generation\n", "llm = ChatOpenAI(model_name=\"gpt-4o\", temperature=0)\n", "\n", "# Define the prompt template for generating AI responses\n", "PROMPT_TEMPLATE = \"\"\"\n", "Human: You are an AI assistant, and provides answers to questions by using fact based and statistical information when possible.\n", "Use the following pieces of information to provide a concise answer to the question enclosed in <question> tags.\n", "If you don't know the answer, just say that you don't know, don't try to make up an answer.\n", "<context>\n", "{context}\n", "</context>\n", "\n", "<question>\n", "{question}\n", "</question>\n", "\n", "The response should be specific and use statistics or numbers when possible.\n", "\n", "Assistant:\"\"\"\n", "\n", "# Create a PromptTemplate instance with the defined template and input variables\n", "prompt = PromptTemplate(\n", "    template=PROMPT_TEMPLATE, input_variables=[\"context\", \"question\"]\n", ")\n", "# Convert the vector store to a retriever\n", "retriever = vectorstore.as_retriever()\n", "\n", "\n", "# Define a function to format the retrieved documents\n", "def format_docs(docs):\n", "    return \"\\n\\n\".join(doc.page_content for doc in docs)"]}, {"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["Use the LCEL(LangChain Expression Language) to build a RAG chain."]}, {"cell_type": "code", "execution_count": 13, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["# Define the RAG (Retrieval-Augmented Generation) chain for AI response generation\n", "rag_chain = (\n", "    {\"context\": retriever | format_docs, \"question\": RunnablePassthrough()}\n", "    | prompt\n", "    | llm\n", "    | StrOutputParser()\n", ")\n", "\n", "# rag_chain.get_graph().print_ascii()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Invoke the RAG chain with a specific question and retrieve the response"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["'PAL (Program-aided Language models) and PoT (Program of Thoughts prompting) are approaches that involve using language models to generate programming language statements to solve natural language reasoning problems. This method offloads the solution step to a runtime, such as a Python interpreter, allowing for complex computation and reasoning to be handled externally. PAL and PoT rely on language models with strong coding skills to effectively generate and execute these programming statements.'"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["query = \"What is PAL and PoT?\"\n", "res = rag_chain.invoke(query)\n", "res"]}, {"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["Congratulations! You have built a hybrid(dense vector + sparse bm25 function) search RAG chain powered by <PERSON><PERSON><PERSON><PERSON> and Lang<PERSON><PERSON>n."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 4}