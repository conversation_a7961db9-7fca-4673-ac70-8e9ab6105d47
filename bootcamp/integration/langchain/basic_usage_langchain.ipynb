{"cells": [{"cell_type": "markdown", "id": "683953b3", "metadata": {}, "source": ["# Use <PERSON><PERSON><PERSON><PERSON> as a LangChain Vector Store\n", "\n", "This notebook shows how to use functionality related to the [Milvus](https://milvus.io/docs/overview.md) as a [LangChain vector store](https://python.langchain.com/docs/integrations/vectorstores/).\n", "\n", "## Setup\n", "\n", "You'll need to install `langchain-milvus` with `pip install -qU langchain-milvus` to use this integration.\n"]}, {"cell_type": "code", "execution_count": null, "id": "a62cff8a-bcf7-4e33-bbbc-76999c2e3e20", "metadata": {"tags": []}, "outputs": [], "source": ["%pip install -qU  langchain_milvus"]}, {"cell_type": "markdown", "id": "633addc3", "metadata": {}, "source": ["The latest version of pymilvus comes with a local vector database Milvus Lite, good for prototyping. If you have large scale of data such as more than a million docs, we recommend setting up a more performant Milvus server on [docker or kubernetes](https://milvus.io/docs/install_standalone-docker.md#Start-Milvus).\n", "\n", "\n", "## Initialization\n", "\n"]}, {"cell_type": "code", "execution_count": 25, "id": "a7dd253f", "metadata": {}, "outputs": [], "source": ["from langchain_openai import OpenAIEmbeddings\n", "\n", "embeddings = OpenAIEmbeddings(model=\"text-embedding-3-large\")"]}, {"cell_type": "code", "execution_count": 28, "id": "dcf88bdf", "metadata": {"tags": []}, "outputs": [], "source": ["from langchain_milvus import Milvus\n", "\n", "# The easiest way is to use Milvus Lite where everything is stored in a local file.\n", "# If you have a Milvus server you can use the server URI such as \"http://localhost:19530\".\n", "URI = \"./milvus_example.db\"\n", "\n", "vector_store = Milvus(\n", "    embedding_function=embeddings,\n", "    connection_args={\"uri\": URI},\n", ")"]}, {"cell_type": "markdown", "id": "cae1a7d5", "metadata": {}, "source": ["### Compartmentalize the data with Milvus Collections\n", "\n", "You can store different unrelated documents in different collections within same Milvus instance to maintain the context"]}, {"cell_type": "markdown", "id": "c07cd24b", "metadata": {}, "source": ["Here's how you can create a new vector store collection from documents:"]}, {"cell_type": "code", "execution_count": 29, "id": "c6f4973d", "metadata": {}, "outputs": [], "source": ["from langchain_core.documents import Document\n", "\n", "vector_store_saved = Milvus.from_documents(\n", "    [Document(page_content=\"foo!\")],\n", "    embeddings,\n", "    collection_name=\"langchain_example\",\n", "    connection_args={\"uri\": URI},\n", ")"]}, {"cell_type": "markdown", "id": "3b12df8c", "metadata": {}, "source": ["And here is how you retrieve that stored collection"]}, {"cell_type": "code", "execution_count": 30, "id": "12817d16", "metadata": {}, "outputs": [], "source": ["vector_store_loaded = Milvus(\n", "    embeddings,\n", "    connection_args={\"uri\": URI},\n", "    collection_name=\"langchain_example\",\n", ")"]}, {"cell_type": "markdown", "id": "f1fc3818", "metadata": {}, "source": ["## Manage vector store\n", "\n", "Once you have created your vector store, we can interact with it by adding and deleting different items.\n", "\n", "### Add items to vector store\n", "\n", "We can add items to our vector store by using the `add_documents` function."]}, {"cell_type": "code", "execution_count": 31, "id": "3ced24f6", "metadata": {}, "outputs": [{"data": {"text/plain": ["['b0248595-2a41-4f6b-9c25-3a24c1278bb3',\n", " 'fa642726-5329-4495-a072-187e948dd71f',\n", " '9905001c-a4a3-455e-ab94-72d0ed11b476',\n", " 'eacc7256-d7fa-4036-b1f7-83d7a4bee0c5',\n", " '7508f7ff-c0c9-49ea-8189-634f8a0244d8',\n", " '2e179609-3ff7-4c6a-9e05-08978903fe26',\n", " 'fab1f2ac-43e1-45f9-b81b-fc5d334c6508',\n", " '1206d237-ee3a-484f-baf2-b5ac38eeb314',\n", " 'd43cbf9a-a772-4c40-993b-9439065fec01',\n", " '25e667bb-6f09-4574-a368-************']"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["from uuid import uuid4\n", "\n", "from langchain_core.documents import Document\n", "\n", "document_1 = Document(\n", "    page_content=\"I had chocalate chip pancakes and scrambled eggs for breakfast this morning.\",\n", "    metadata={\"source\": \"tweet\"},\n", ")\n", "\n", "document_2 = Document(\n", "    page_content=\"The weather forecast for tomorrow is cloudy and overcast, with a high of 62 degrees.\",\n", "    metadata={\"source\": \"news\"},\n", ")\n", "\n", "document_3 = Document(\n", "    page_content=\"Building an exciting new project with <PERSON><PERSON><PERSON><PERSON> - come check it out!\",\n", "    metadata={\"source\": \"tweet\"},\n", ")\n", "\n", "document_4 = Document(\n", "    page_content=\"Robbers broke into the city bank and stole $1 million in cash.\",\n", "    metadata={\"source\": \"news\"},\n", ")\n", "\n", "document_5 = Document(\n", "    page_content=\"Wow! That was an amazing movie. I can't wait to see it again.\",\n", "    metadata={\"source\": \"tweet\"},\n", ")\n", "\n", "document_6 = Document(\n", "    page_content=\"Is the new iPhone worth the price? Read this review to find out.\",\n", "    metadata={\"source\": \"website\"},\n", ")\n", "\n", "document_7 = Document(\n", "    page_content=\"The top 10 soccer players in the world right now.\",\n", "    metadata={\"source\": \"website\"},\n", ")\n", "\n", "document_8 = Document(\n", "    page_content=\"LangGraph is the best framework for building stateful, agentic applications!\",\n", "    metadata={\"source\": \"tweet\"},\n", ")\n", "\n", "document_9 = Document(\n", "    page_content=\"The stock market is down 500 points today due to fears of a recession.\",\n", "    metadata={\"source\": \"news\"},\n", ")\n", "\n", "document_10 = Document(\n", "    page_content=\"I have a bad feeling I am going to get deleted :(\",\n", "    metadata={\"source\": \"tweet\"},\n", ")\n", "\n", "documents = [\n", "    document_1,\n", "    document_2,\n", "    document_3,\n", "    document_4,\n", "    document_5,\n", "    document_6,\n", "    document_7,\n", "    document_8,\n", "    document_9,\n", "    document_10,\n", "]\n", "uuids = [str(uuid4()) for _ in range(len(documents))]\n", "\n", "vector_store.add_documents(documents=documents, ids=uuids)"]}, {"cell_type": "markdown", "id": "e23c22d8", "metadata": {}, "source": ["### Delete items from vector store"]}, {"cell_type": "code", "execution_count": 32, "id": "1f387fa8", "metadata": {}, "outputs": [{"data": {"text/plain": ["(insert count: 0, delete count: 1, upsert count: 0, timestamp: 0, success count: 0, err count: 0, cost: 0)"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["vector_store.delete(ids=[uuids[-1]])"]}, {"cell_type": "markdown", "id": "fb12fa75", "metadata": {}, "source": ["## Query vector store\n", "\n", "Once your vector store has been created and the relevant documents have been added you will most likely wish to query it during the running of your chain or agent. \n", "\n", "### Query directly\n", "\n", "#### Similarity search\n", "\n", "Performing a simple similarity search with filtering on metadata can be done as follows:"]}, {"cell_type": "code", "execution_count": 33, "id": "35801a55", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* Building an exciting new project with <PERSON><PERSON><PERSON><PERSON> - come check it out! [{'pk': '9905001c-a4a3-455e-ab94-72d0ed11b476', 'source': 'tweet'}]\n", "* LangGraph is the best framework for building stateful, agentic applications! [{'pk': '1206d237-ee3a-484f-baf2-b5ac38eeb314', 'source': 'tweet'}]\n"]}], "source": ["results = vector_store.similarity_search(\n", "    \"Lang<PERSON>hain provides abstractions to make working with LLMs easy\",\n", "    k=2,\n", "    expr='source == \"tweet\"',\n", "    # param=...  # Search params for the index type\n", ")\n", "for res in results:\n", "    print(f\"* {res.page_content} [{res.metadata}]\")"]}, {"cell_type": "markdown", "id": "35574409", "metadata": {}, "source": ["#### Similarity search with score\n", "\n", "You can also search with score:"]}, {"cell_type": "code", "execution_count": 22, "id": "c360af3d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* [SIM=21192.628906] bar [{'pk': '2', 'source': 'https://example.com'}]\n"]}], "source": ["results = vector_store.similarity_search_with_score(\n", "    \"Will it be hot tomorrow?\", k=1, expr='source == \"news\"'\n", ")\n", "for res, score in results:\n", "    print(f\"* [SIM={score:3f}] {res.page_content} [{res.metadata}]\")"]}, {"cell_type": "markdown", "id": "14db337f", "metadata": {}, "source": ["For a full list of all the search options available when using the `Milvus` vector store, you can visit the [API reference](https://python.langchain.com/api_reference/milvus/vectorstores/langchain_milvus.vectorstores.milvus.Milvus.html).\n", "\n", "### Query by turning into retriever\n", "\n", "You can also transform the vector store into a retriever for easier usage in your chains. "]}, {"cell_type": "code", "execution_count": 34, "id": "f6d9357c", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'pk': 'eacc7256-d7fa-4036-b1f7-83d7a4bee0c5', 'source': 'news'}, page_content='<PERSON><PERSON> broke into the city bank and stole $1 million in cash.')]"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["retriever = vector_store.as_retriever(search_type=\"mmr\", search_kwargs={\"k\": 1})\n", "retriever.invoke(\"Stealing from the bank is a crime\", filter={\"source\": \"news\"})"]}, {"cell_type": "markdown", "id": "8ac953f1", "metadata": {}, "source": ["## Usage for Retrieval-Augmented Generation\n", "\n", "For guides on how to use this vector store for retrieval-augmented generation (RAG), see this [RAG guide](https://milvus.io/docs/integrate_with_langchain.md)."]}, {"cell_type": "markdown", "id": "7fb27b941602401d91542211134fc71a", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["### Per-User Retrieval\n", "\n", "When building a retrieval app, you often have to build it with multiple users in mind. This means that you may be storing data not just for one user, but for many different users, and they should not be able to see eachother’s data.\n", "\n", "Milvus recommends using [partition_key](https://milvus.io/docs/multi_tenancy.md#Partition-key-based-multi-tenancy) to implement multi-tenancy, here is an example.\n", "> The feature of Partition key is now not available in Milvus Lite, if you want to use it, you need to start Milvus server from [docker or kubernetes](https://milvus.io/docs/install_standalone-docker.md#Start-Milvus)."]}, {"cell_type": "code", "execution_count": 2, "id": "acae54e37e7d407bbb7b55eff062a284", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["from langchain_core.documents import Document\n", "\n", "docs = [\n", "    Document(page_content=\"i worked at kensho\", metadata={\"namespace\": \"harrison\"}),\n", "    Document(page_content=\"i worked at facebook\", metadata={\"namespace\": \"ankush\"}),\n", "]\n", "vectorstore = Milvus.from_documents(\n", "    docs,\n", "    embeddings,\n", "    connection_args={\"uri\": URI},\n", "    # drop_old=True,\n", "    partition_key_field=\"namespace\",  # Use the \"namespace\" field as the partition key\n", ")"]}, {"cell_type": "markdown", "id": "9a63283cbaf04dbcab1f6479b197f3a8", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["To conduct a search using the partition key, you should include either of the following in the boolean expression of the search request:\n", "\n", "`search_kwargs={\"expr\": '<partition_key> == \"xxxx\"'}`\n", "\n", "`search_kwargs={\"expr\": '<partition_key> == in [\"xxx\", \"xxx\"]'}`\n", "\n", "Do replace `<partition_key>` with the name of the field that is designated as the partition key.\n", "\n", "Milvus changes to a partition based on the specified partition key, filters entities according to the partition key, and searches among the filtered entities.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "8dd0d8092fe74a7c96281538738b07e2", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["[Document(page_content='i worked at facebook', metadata={'namespace': 'ankush'})]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# This will only get documents for Ankus<PERSON>\n", "vectorstore.as_retriever(search_kwargs={\"expr\": 'namespace == \"ankush\"'}).invoke(\n", "    \"where did i work?\"\n", ")"]}, {"cell_type": "code", "execution_count": 4, "id": "72eea5119410473aa328ad9291626812", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["[Document(page_content='i worked at kensho', metadata={'namespace': 'harrison'})]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# This will only get documents for <PERSON>\n", "vectorstore.as_retriever(search_kwargs={\"expr\": 'namespace == \"harrison\"'}).invoke(\n", "    \"where did i work?\"\n", ")"]}, {"cell_type": "markdown", "id": "f1a873c5", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all __ModuleName__VectorStore features and configurations head to the API reference: https://python.langchain.com/api_reference/milvus/vectorstores/langchain_milvus.vectorstores.milvus.Milvus.html"]}], "metadata": {"kernelspec": {"display_name": "py310", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}