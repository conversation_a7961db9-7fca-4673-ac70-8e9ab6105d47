{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/integration/langchain/milvus_hybrid_search_retriever.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/integration/langchain/milvus_hybrid_search_retriever.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>\n", "</a>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Hybrid Search with <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>\n", "\n", "Hybrid search combines the strengths of different search paradigms to enhance retrieval accuracy and robustness. It leverages the capabilities of both dense vector search and sparse vector search, as well as combinations of multiple dense vector search strategies, ensuring comprehensive and precise retrieval for diverse queries.\n", "\n", "![](../../pics/advanced_rag/hybrid_and_rerank.png)\n", "\n", "This diagram illustrates the most common hybrid search scenario, which is the dense + sparse hybrid search. In this case, candidates are retrieved using both semantic vector similarity and precise keyword matching. Results from these methods are merged, reranked, and passed to an LLM to generate the final answer. This approach balances precision and semantic understanding, making it highly effective for diverse query scenarios.\n", "\n", "In addition to dense + sparse hybrid search, hybrid strategies can also combine multiple dense vector models. For instance, one dense vector model might specialize in capturing semantic nuances, while another focuses on contextual embeddings or domain-specific representations. By merging results from these models and reranking them, this type of hybrid search ensures a more nuanced and context-aware retrieval process.\n", "\n", "LangChain Milvus integration provides a flexible way to implement hybrid search, it supports any number of vector fields, and any custom dense or sparse embedding models, which allows LangChain Milvus to flexibly adapt to various hybrid search usage scenarios, and at the same time compatible with other capabilities of LangChain.\n", "\n", "In this tutorial, we will start with the most common dense + sparse case, and then introduce any number of general hybrid search usage approachs.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> Note: The [MilvusCollectionHybridSearchRetriever](https://api.python.langchain.com/en/latest/milvus/retrievers/langchain_milvus.retrievers.milvus_hybrid_search.MilvusCollectionHybridSearchRetriever.html), which is another implementation of hybrid search with Milvus and LangChain, **is about to be deprecated**. Please use the approach in this document to implement hybrid search because it is more flexible and compatible with LangChain."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prerequisites\n", "\n", "Before running this notebook, make sure you have the following dependencies installed:"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["! pip install --upgrade --quiet  langchain langchain-core langchain-community langchain-text-splitters langchain-milvus langchain-openai bs4 pymilvus[model] #langchain-voyageai"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> If you are using Google Colab, to enable dependencies just installed, you may need to **restart the runtime** (click on the \"Runtime\" menu at the top of the screen, and select \"Restart session\" from the dropdown menu)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["We will use the models from OpenAI. You should prepare the environment variables `OPENAI_API_KEY` from [OpenAI](https://platform.openai.com/docs/quickstart)."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-***********\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Specify your Milvus server `URI` (and optionally the `TOKEN`). For how to install and start the Milvus server following this [guide](https://milvus.io/docs/install_standalone-docker-compose.md). "]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["URI = \"http://localhost:19530\"\n", "# TOKEN = ..."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Prepare some example documents, which are fictional story summaries categorized by theme or genre."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["from langchain_core.documents import Document\n", "\n", "docs = [\n", "    Document(\n", "        page_content=\"In 'The Whispering Walls' by <PERSON>, a young journalist named <PERSON> uncovers a decades-old conspiracy hidden within the crumbling walls of an ancient mansion, where the whispers of the past threaten to destroy her own sanity.\",\n", "        metadata={\"category\": \"Mystery\"},\n", "    ),\n", "    Document(\n", "        page_content=\"In 'The Last Refuge' by <PERSON>, a group of survivors must band together to escape a post-apocalyptic wasteland, where the last remnants of humanity cling to life in a desperate bid for survival.\",\n", "        metadata={\"category\": \"Post-Apocalyptic\"},\n", "    ),\n", "    Document(\n", "        page_content=\"In 'The Memory Thief' by <PERSON>, a charismatic thief with the ability to steal and manipulate memories is hired by a mysterious client to pull off a daring heist, but soon finds themselves trapped in a web of deceit and betrayal.\",\n", "        metadata={\"category\": \"Heist/Thriller\"},\n", "    ),\n", "    Document(\n", "        page_content=\"In 'The City of Echoes' by <PERSON>, a brilliant detective must navigate a labyrinthine metropolis where time is currency, and the rich can live forever, but at a terrible cost to the poor.\",\n", "        metadata={\"category\": \"Science Fiction\"},\n", "    ),\n", "    Document(\n", "        page_content=\"In 'The Starlight Serenade' by <PERSON>, a shy astronomer discovers a mysterious melody emanating from a distant star, which leads her on a journey to uncover the secrets of the universe and her own heart.\",\n", "        metadata={\"category\": \"Science Fiction/Romance\"},\n", "    ),\n", "    Document(\n", "        page_content=\"In 'The Shadow Weaver' by <PERSON>, a young orphan discovers she has the ability to weave powerful illusions, but soon finds herself at the center of a deadly game of cat and mouse between rival factions vying for control of the mystical arts.\",\n", "        metadata={\"category\": \"Fantasy\"},\n", "    ),\n", "    Document(\n", "        page_content=\"In 'The Lost Expedition' by <PERSON><PERSON><PERSON>, a team of explorers ventures into the heart of the Amazon rainforest in search of a lost city, but soon finds themselves hunted by a ruthless treasure hunter and the treacherous jungle itself.\",\n", "        metadata={\"category\": \"Adventure\"},\n", "    ),\n", "    Document(\n", "        page_content=\"In 'The Clockwork Kingdom' by <PERSON>, a brilliant inventor discovers a hidden world of clockwork machines and ancient magic, where a rebellion is brewing against the tyrannical ruler of the land.\",\n", "        metadata={\"category\": \"Steampunk/Fantasy\"},\n", "    ),\n", "    Document(\n", "        page_content=\"In 'The Phantom Pilgrim' by <PERSON>, a charismatic smuggler is hired by a mysterious organization to transport a valuable artifact across a war-torn continent, but soon finds themselves pursued by deadly assassins and rival factions.\",\n", "        metadata={\"category\": \"Adventure/Thriller\"},\n", "    ),\n", "    Document(\n", "        page_content=\"In 'The Dreamwalker's Journey' by <PERSON><PERSON>, a young dreamwalker discovers she has the ability to enter people's dreams, but soon finds herself trapped in a surreal world of nightmares and illusions, where the boundaries between reality and fantasy blur.\",\n", "        metadata={\"category\": \"Fantasy\"},\n", "    ),\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Dense embedding + Sparse embedding\n", "### Option 1(Recommended): dense embedding + Milvus BM25 built-in function\n", "Use dense embedding + Milvus BM25 built-in function to assemble the hybrid retrieval vector store instance."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from langchain_milvus import Milvus, BM25BuiltInFunction\n", "from langchain_openai import OpenAIEmbeddings\n", "\n", "\n", "vectorstore = Milvus.from_documents(\n", "    documents=docs,\n", "    embedding=OpenAIEmbeddings(),\n", "    builtin_function=BM25BuiltInFunction(),  # output_field_names=\"sparse\"),\n", "    vector_field=[\"dense\", \"sparse\"],\n", "    connection_args={\n", "        \"uri\": URI,\n", "    },\n", "    consistency_level=\"Strong\",\n", "    # drop_old=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> When you use `BM25BuiltInFunction`, please note that full-text search is currently available in Milvus Standalone, Milvus Distributed, and Zilliz Cloud, though not yet supported in Milvus Lite (which has this feature planned for future implementation). <NAME_EMAIL> for more information.\n", "\n", "In the code above, we define an instance of `BM25BuiltInFunction` and pass it to the `Milvus` object. `BM25BuiltInFunction` is a lightweight wrapper class for [`Function`](https://milvus.io/docs/manage-collections.md#Function) in Milvus. We can use it with `OpenAIEmbeddings`  to initialize a dense + sparse hybrid search Milvus vector store instance.\n", "\n", "`BM25BuiltInFunction` does not require the client to pass corpus or training, all are automatically processed at the Milvus server's end, so users do not need to care about any vocabulary and corpus. In addition, users can also customize the [analyzer](https://milvus.io/docs/analyzer-overview.md#Analyzer-Overview) to implement the custom text processing in the BM25. \n", "\n", "For more information about `BM25BuiltInFunction`, please refer to the [Full-Text-Search](https://milvus.io/docs/full-text-search.md#Full-Text-Search) and [Using Full-Text Search with <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>](https://milvus.io/docs/full_text_search_with_langchain.md).\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Option 2: Use dense and customized LangChain sparse embedding\n", "\n", "You can inherit the class `BaseSparseEmbedding` from `langchain_milvus.utils.sparse`, and implement the `embed_query` and `embed_documents` methods to customize the sparse embedding process. This allows you to customize any sparse embedding method both based on term frequency statistics(e.g. [BM25](https://milvus.io/docs/embed-with-bm25.md#BM25)) or neural networks(e.g. [SPADE](https://milvus.io/docs/embed-with-splade.md#SPLADE)).\n", "\n", "Here is an example:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from typing import Dict, List\n", "from langchain_milvus.utils.sparse import BaseSparseEmbedding\n", "\n", "\n", "class MyCustomEmbedding(BaseSparseEmbedding):  # inherit from BaseSparseEmbedding\n", "    def __init__(self, model_path): ...  # code to init or load model\n", "\n", "    def embed_query(self, query: str) -> Dict[int, float]:\n", "        ...  # code to embed query\n", "        return {  # fake embedding result\n", "            1: 0.1,\n", "            2: 0.2,\n", "            3: 0.3,\n", "            # ...\n", "        }\n", "\n", "    def embed_documents(self, texts: List[str]) -> List[Dict[int, float]]:\n", "        ...  # code to embed documents\n", "        return [  # fake embedding results\n", "            {\n", "                1: 0.1,\n", "                2: 0.2,\n", "                3: 0.3,\n", "                # ...\n", "            }\n", "        ] * len(texts)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We have a demo class `BM25SparseEmbedding` inherited from `BaseSparseEmbedding` in `langchain_milvus.utils.sparse`.\n", "You can pass it into the initialization embedding list of the Milvus vector store instance just like other langchain dense embedding classes."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# BM25SparseEmbedding is inherited from BaseSparseEmbedding\n", "from langchain_milvus.utils.sparse import BM25SparseEmbedding\n", "\n", "embedding1 = OpenAIEmbeddings()\n", "\n", "corpus = [doc.page_content for doc in docs]\n", "embedding2 = BM25SparseEmbedding(\n", "    corpus=corpus\n", ")  # pass in corpus to initialize the statistics\n", "\n", "vectorstore = Milvus.from_documents(\n", "    documents=docs,\n", "    embedding=[embedding1, embedding2],\n", "    vector_field=[\"dense\", \"sparse\"],\n", "    connection_args={\n", "        \"uri\": URI,\n", "    },\n", "    consistency_level=\"Strong\",\n", "    # drop_old=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Although this is a way to use BM25, it requires users to manage the corpus for term frequency statistics. We recommend using the BM25 built-in function(Option 1) instead, as it handles everything on the Milvus server side. This eliminates the need for users to concern about managing the corpus or training a vocabulary. For more information, please refer to the [Using Full-Text Search with Lang<PERSON>hain and Milvus](https://milvus.io/docs/full_text_search_with_langchain.md)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define multiple arbitrary vector fields\n", "When initializing the Milvus vector store, you can pass in the list of embeddings (and will also list of build-in functions in the future) to implement multi-ways retrival, and then rerank these candidates.\n", "Here is an example:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["['dense1', 'dense2', 'sparse']"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# from langchain_voyageai import VoyageAIEmbeddings\n", "\n", "embedding1 = OpenAIEmbeddings(model=\"text-embedding-ada-002\")\n", "embedding2 = OpenAIEmbeddings(model=\"text-embedding-3-large\")\n", "# embedding3 = VoyageAIEmbeddings(model=\"voyage-3\")  # You can also use embedding from other embedding model providers, e.g VoyageAIEmbeddings\n", "\n", "\n", "vectorstore = Milvus.from_documents(\n", "    documents=docs,\n", "    embedding=[embedding1, embedding2],  # embedding3],\n", "    builtin_function=BM25BuiltInFunction(output_field_names=\"sparse\"),\n", "    # `sparse` is the output field name of BM25BuiltInFunction, and `dense1` and `dense2` are the output field names of embedding1 and embedding2\n", "    vector_field=[\"dense1\", \"dense2\", \"sparse\"],\n", "    connection_args={\n", "        \"uri\": URI,\n", "    },\n", "    consistency_level=\"Strong\",\n", "    # drop_old=True,\n", ")\n", "\n", "vectorstore.vector_fields"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this example, we have three vector fields. Among them, `sparse` is used as the output field for `BM25BuiltInFunction`, while the other two, `dense1` and `dense2`, are automatically assigned as the output fields for the two `OpenAIEmbeddings` models (based on the order).  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Specify the index params for multi-vector fields\n", "By default, the index types of each vector field will be automatically determined by the type of embedding or built-in function. However, you can also specify the index type for each vector field to optimize the search performance."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["['dense1', 'dense2', 'sparse']"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["dense_index_param_1 = {\n", "    \"metric_type\": \"COSINE\",\n", "    \"index_type\": \"HNSW\",\n", "}\n", "dense_index_param_2 = {\n", "    \"metric_type\": \"IP\",\n", "    \"index_type\": \"HNSW\",\n", "}\n", "sparse_index_param = {\n", "    \"metric_type\": \"BM25\",\n", "    \"index_type\": \"AUTOINDEX\",\n", "}\n", "\n", "vectorstore = Milvus.from_documents(\n", "    documents=docs,\n", "    embedding=[embedding1, embedding2],\n", "    builtin_function=BM25BuiltInFunction(output_field_names=\"sparse\"),\n", "    index_params=[dense_index_param_1, dense_index_param_2, sparse_index_param],\n", "    vector_field=[\"dense1\", \"dense2\", \"sparse\"],\n", "    connection_args={\n", "        \"uri\": URI,\n", "    },\n", "    consistency_level=\"Strong\",\n", "    # drop_old=True,\n", ")\n", "\n", "vectorstore.vector_fields"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> Please keep the order of list of index params consistent with the order of `vectorstore.vector_fields` to avoid confusion."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Rerank the candidates\n", "After the first stage of retrieval, we need to rerank the candidates to get a better result. You can choose [WeightedRanker](https://milvus.io/docs/reranking.md#Weighted-Scoring-WeightedRanker) or [RRFRanker](https://milvus.io/docs/reranking.md#Reciprocal-Rank-Fusion-RRFRanker) depending on your requirements. You can refer to the [Reranking](https://milvus.io/docs/reranking.md#Reranking) for more information.\n", "\n", "Here is an example for weighted reranking:"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'pk': 454646931479252186, 'category': 'Heist/Thriller'}, page_content=\"In 'The Memory Thief' by <PERSON>, a charismatic thief with the ability to steal and manipulate memories is hired by a mysterious client to pull off a daring heist, but soon finds themselves trapped in a web of deceit and betrayal.\")]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["vectorstore = Milvus.from_documents(\n", "    documents=docs,\n", "    embedding=OpenAIEmbeddings(),\n", "    builtin_function=BM25BuiltInFunction(),\n", "    vector_field=[\"dense\", \"sparse\"],\n", "    connection_args={\n", "        \"uri\": URI,\n", "    },\n", "    consistency_level=\"Strong\",\n", "    # drop_old=True,\n", ")\n", "\n", "query = \"What are the novels <PERSON> has written and what are their contents?\"\n", "\n", "vectorstore.similarity_search(\n", "    query, k=1, ranker_type=\"weighted\", ranker_params={\"weights\": [0.6, 0.4]}\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here is an example of RRF reranking:"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'category': 'Heist/Thriller', 'pk': 454646931479252186}, page_content=\"In 'The Memory Thief' by <PERSON>, a charismatic thief with the ability to steal and manipulate memories is hired by a mysterious client to pull off a daring heist, but soon finds themselves trapped in a web of deceit and betrayal.\")]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["vectorstore.similarity_search(query, k=1, ranker_type=\"rrf\", ranker_params={\"k\": 100})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you don't pass any parameters about rerank, the average weighted rerank strategy is used by default."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Using Hybrid Search and Reranking in RAG\n", "\n", "In the scenario of RAG, the most prevalent approach for hybrid search is dense + sparse retrieval, followed by reranking. The subsequent example demonstrates a straightforward end-to-end code."]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "### Prepare the data\n", "\n", "We use the Langchain WebBaseLoader to load documents from web sources and split them into chunks using the RecursiveCharacterTextSplitter.\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["Document(metadata={'source': 'https://lilianweng.github.io/posts/2023-06-23-agent/'}, page_content='Fig. 1. Overview of a LLM-powered autonomous agent system.\\nComponent One: Planning#\\nA complicated task usually involves many steps. An agent needs to know what they are and plan ahead.\\nTask Decomposition#\\nChain of thought (CoT; <PERSON> et al. 2022) has become a standard prompting technique for enhancing model performance on complex tasks. The model is instructed to “think step by step” to utilize more test-time computation to decompose hard tasks into smaller and simpler steps. CoT transforms big tasks into multiple manageable tasks and shed lights into an interpretation of the model’s thinking process.\\nTree of Thoughts (<PERSON> et al. 2023) extends CoT by exploring multiple reasoning possibilities at each step. It first decomposes the problem into multiple thought steps and generates multiple thoughts per step, creating a tree structure. The search process can be BFS (breadth-first search) or DFS (depth-first search) with each state evaluated by a classifier (via a prompt) or majority vote.\\nTask decomposition can be done (1) by LLM with simple prompting like \"Steps for XYZ.\\\\n1.\", \"What are the subgoals for achieving XYZ?\", (2) by using task-specific instructions; e.g. \"Write a story outline.\" for writing a novel, or (3) with human inputs.\\nAnother quite distinct approach, LLM+P (<PERSON> et al. 2023), involves relying on an external classical planner to do long-horizon planning. This approach utilizes the Planning Domain Definition Language (PDDL) as an intermediate interface to describe the planning problem. In this process, LLM (1) translates the problem into “Problem PDDL”, then (2) requests a classical planner to generate a PDDL plan based on an existing “Domain PDDL”, and finally (3) translates the PDDL plan back into natural language. Essentially, the planning step is outsourced to an external tool, assuming the availability of domain-specific PDDL and a suitable planner which is common in certain robotic setups but not in many other domains.\\nSelf-Reflection#')"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["import bs4\n", "from langchain_community.document_loaders import WebBaseLoader\n", "from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "\n", "# Create a WebBaseLoader instance to load documents from web sources\n", "loader = WebBaseLoader(\n", "    web_paths=(\n", "        \"https://lilianweng.github.io/posts/2023-06-23-agent/\",\n", "        \"https://lilianweng.github.io/posts/2023-03-15-prompt-engineering/\",\n", "    ),\n", "    bs_kwargs=dict(\n", "        parse_only=bs4.SoupStrainer(\n", "            class_=(\"post-content\", \"post-title\", \"post-header\")\n", "        )\n", "    ),\n", ")\n", "# Load documents from web sources using the loader\n", "documents = loader.load()\n", "# Initialize a RecursiveCharacterTextSplitter for splitting text into chunks\n", "text_splitter = RecursiveCharacterTextSplitter(chunk_size=2000, chunk_overlap=200)\n", "\n", "# Split the documents into chunks using the text_splitter\n", "docs = text_splitter.split_documents(documents)\n", "\n", "# Let's take a look at the first document\n", "docs[1]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load the document into Milvus vector store\n", "As the introduction above, we initialize and load the prepared documents into Milvus vector store, which contains two vector fields: `dense` is for the OpenAI embedding and `sparse` is for the BM25 function."]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["vectorstore = Milvus.from_documents(\n", "    documents=docs,\n", "    embedding=OpenAIEmbeddings(),\n", "    builtin_function=BM25BuiltInFunction(),\n", "    vector_field=[\"dense\", \"sparse\"],\n", "    connection_args={\n", "        \"uri\": URI,\n", "    },\n", "    consistency_level=\"Strong\",\n", "    # drop_old=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Build RAG chain\n", "We prepare the LLM instance and prompt, then conbine them into a RAG pipeline using the LangChain Expression Language."]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["from langchain_core.runnables import RunnablePassthrough\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_openai import ChatOpenAI\n", "\n", "# Initialize the OpenAI language model for response generation\n", "llm = ChatOpenAI(model_name=\"gpt-4o\", temperature=0)\n", "\n", "# Define the prompt template for generating AI responses\n", "PROMPT_TEMPLATE = \"\"\"\n", "Human: You are an AI assistant, and provides answers to questions by using fact based and statistical information when possible.\n", "Use the following pieces of information to provide a concise answer to the question enclosed in <question> tags.\n", "If you don't know the answer, just say that you don't know, don't try to make up an answer.\n", "<context>\n", "{context}\n", "</context>\n", "\n", "<question>\n", "{question}\n", "</question>\n", "\n", "The response should be specific and use statistics or numbers when possible.\n", "\n", "Assistant:\"\"\"\n", "\n", "# Create a PromptTemplate instance with the defined template and input variables\n", "prompt = PromptTemplate(\n", "    template=PROMPT_TEMPLATE, input_variables=[\"context\", \"question\"]\n", ")\n", "# Convert the vector store to a retriever\n", "retriever = vectorstore.as_retriever()\n", "\n", "\n", "# Define a function to format the retrieved documents\n", "def format_docs(docs):\n", "    return \"\\n\\n\".join(doc.page_content for doc in docs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Use the LCEL(LangChain Expression Language) to build a RAG chain."]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["# Define the RAG (Retrieval-Augmented Generation) chain for AI response generation\n", "rag_chain = (\n", "    {\"context\": retriever | format_docs, \"question\": RunnablePassthrough()}\n", "    | prompt\n", "    | llm\n", "    | StrOutputParser()\n", ")\n", "\n", "# rag_chain.get_graph().print_ascii()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Invoke the RAG chain with a specific question and retrieve the response"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["'PAL (Program-aided Language models) and PoT (Program of Thoughts prompting) are approaches that involve using language models to generate programming language statements to solve natural language reasoning problems. This method offloads the solution step to a runtime, such as a Python interpreter, allowing for complex computation and reasoning to be handled externally. PAL and PoT rely on language models with strong coding skills to effectively perform these tasks.'"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["query = \"What is PAL and PoT?\"\n", "res = rag_chain.invoke(query)\n", "res"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Congratulations! You have built a hybrid(dense vector + sparse bm25 function) search RAG chain powered by <PERSON><PERSON><PERSON><PERSON> and Lang<PERSON><PERSON>n."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 4}