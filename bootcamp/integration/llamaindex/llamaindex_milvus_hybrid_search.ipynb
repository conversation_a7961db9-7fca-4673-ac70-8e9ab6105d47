{"cells": [{"attachments": {}, "cell_type": "markdown", "id": "1496f9de", "metadata": {}, "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/integration/llamaindex/llamaindex_milvus_hybrid_search.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/integration/llamaindex/llamaindex_milvus_hybrid_search.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>\n", "</a>"]}, {"attachments": {}, "cell_type": "markdown", "id": "0b692c73", "metadata": {}, "source": ["# RAG using Hybrid Search with Milvus and LlamaIndex\n", "\n", "Hybrid search leverages the strengths of both semantic retrieval and keyword matching to deliver more accurate and contextually relevant results. By combining the advantages of semantic search and keyword matching, hybrid search is particularly effective in complex information retrieval tasks.\n", "\n", "This notebook demonstrates how to use Milvus for hybrid search in [LlamaIndex](https://www.llamaindex.ai/) RAG pipelines. We'll begin with the recommended default hybrid search (semantic + BM25) and then explore other alternative sparse embedding methods and customization of hybrid reranker."]}, {"attachments": {}, "cell_type": "markdown", "id": "f81e2c81", "metadata": {}, "source": ["## Prerequisites\n", "\n", "**Install dependencies**\n", "\n", "Before getting started, make sure you have the following dependencies installed:"]}, {"cell_type": "code", "execution_count": null, "id": "3e0c18ca", "metadata": {}, "outputs": [], "source": ["! pip install llama-index-vector-stores-milvus\n", "! pip install llama-index-embeddings-openai\n", "! pip install llama-index-llms-openai"]}, {"cell_type": "markdown", "id": "df3ddfc5", "metadata": {}, "source": ["> If you're using Google Colab, you may need to **restart the runtime** (Navigate to the \"Runtime\" menu at the top of the interface, and select \"Restart session\" from the dropdown menu.)"]}, {"cell_type": "markdown", "id": "0eacd974", "metadata": {}, "source": ["**Set up accounts**\n", "\n", "This tutorial uses OpenAI for text embeddings and answer generation. You need to prepare the [OpenAI API key](https://platform.openai.com/api-keys). "]}, {"cell_type": "code", "execution_count": null, "id": "0c9f4d21-145a-401e-95ff-ccb259e8ef84", "metadata": {}, "outputs": [], "source": ["import openai\n", "\n", "openai.api_key = \"sk-\""]}, {"cell_type": "markdown", "id": "1cc8cb78", "metadata": {}, "source": ["To use the Milvus vector store, specify your Milvus server `URI` (and optionally with the `TOKEN`). To start a Milvus server, you can set up a Milvus server by following the [Milvus installation guide](https://milvus.io/docs/install-overview.md) or simply trying [Zilliz Cloud](https://docs.zilliz.com/docs/register-with-zilliz-cloud) for free.\n", "\n", "> Full-text search is currently supported in Milvus Standalone, Milvus Distributed, and Zilliz Cloud, but not yet in Milvus Lite (planned for future implementation). <NAME_EMAIL> for more information."]}, {"cell_type": "code", "execution_count": null, "id": "18502d6b", "metadata": {}, "outputs": [], "source": ["URI = \"http://localhost:19530\"\n", "# TOKEN = \"\""]}, {"cell_type": "markdown", "id": "80e5cdda", "metadata": {}, "source": ["**Load example data**\n", "\n", "Run the following commands to download sample documents into the \"data/paul_graham\" directory:"]}, {"cell_type": "code", "execution_count": null, "id": "2a2e24d1", "metadata": {}, "outputs": [], "source": ["! mkdir -p 'data/paul_graham/'\n", "! wget 'https://raw.githubusercontent.com/run-llama/llama_index/main/docs/docs/examples/data/paul_graham/paul_graham_essay.txt' -O 'data/paul_graham/paul_graham_essay.txt'"]}, {"cell_type": "markdown", "id": "f77b94ac", "metadata": {}, "source": ["Then use `SimpleDirectoryReaderLoad` to load the essay \"What I Worked On\" by <PERSON>:"]}, {"cell_type": "code", "execution_count": null, "id": "68cbd239-880e-41a3-98d8-dbb3fab55431", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Example document:\n", " Doc ID: f9cece8c-9022-46d8-9d0e-f29d70e1dbbe\n", "Text: What I Worked On  February 2021  Before college the two main\n", "things I worked on, outside of school, were writing and programming. I\n", "didn't write essays. I wrote what beginning writers were supposed to\n", "write then, and probably still are: short stories. My stories were\n", "awful. They had hardly any plot, just characters with strong feelings,\n", "which I ...\n"]}], "source": ["from llama_index.core import SimpleDirectoryReader\n", "\n", "documents = SimpleDirectoryReader(\"./data/paul_graham/\").load_data()\n", "\n", "# Let's take a look at the first document\n", "print(\"Example document:\\n\", documents[0])"]}, {"cell_type": "markdown", "id": "bb4ba837", "metadata": {}, "source": ["## Hybrid Search with BM25\n", "\n", "This section shows how to perform a hybrid search using BM25. To get started, we will initialize the `MilvusVectorStore` and create an index for the example documents. The default configuration uses:\n", "\n", "- Dense embeddings from the default embedding model (OpenAI's `text-embedding-ada-002`)\n", "- BM25 for full-text search if enable_sparse is True\n", "- RRFRanker with k=60 for combining results if hybrid search is enabled"]}, {"cell_type": "code", "execution_count": null, "id": "c7569fbf", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-04-17 03:38:16,645 [DEBUG][_create_connection]: Created new connection using: cf0f4df74b18418bb89ec512063c1244 (async_milvus_client.py:547)\n", "Sparse embedding function is not provided, using default.\n", "Default sparse embedding function: BM25BuiltInFunction(input_field_names='text', output_field_names='sparse_embedding').\n"]}], "source": ["# Create an index over the documnts\n", "from llama_index.vector_stores.milvus import MilvusVectorStore\n", "from llama_index.core import StorageContext, VectorStoreIndex\n", "\n", "\n", "vector_store = MilvusVectorStore(\n", "    uri=URI,\n", "    # token=TOKEN,\n", "    dim=1536,  # vector dimension depends on the embedding model\n", "    enable_sparse=True,  # enable the default full-text search using BM25\n", "    overwrite=True,  # drop the collection if it already exists\n", ")\n", "storage_context = StorageContext.from_defaults(vector_store=vector_store)\n", "index = VectorStoreIndex.from_documents(documents, storage_context=storage_context)"]}, {"attachments": {}, "cell_type": "markdown", "id": "dd270925", "metadata": {}, "source": ["Here is more information about the arguments for configuring dense and sparse fields in the `MilvusVectorStore`:\n", "\n", "**dense field**\n", "- `enable_dense (bool)`: A boolean flag to enable or disable dense embedding. Defaults to True.\n", "- `dim (int, optional)`: The dimension of the embedding vectors for the collection.\n", "- `embedding_field (str, optional)`: The name of the dense embedding field for the collection, defaults to DEFAULT_EMBEDDING_KEY.\n", "- `index_config (dict, optional)`: The configuration used for building the dense embedding index. Defaults to None.\n", "- `search_config (dict, optional)`: The configuration used for searching the Mi<PERSON><PERSON><PERSON> dense index. Note that this must be compatible with the index type specified by `index_config`. Defaults to None.\n", "- `similarity_metric (str, optional)`: The similarity metric to use for dense embedding, currently supports IP, COSINE and L2.\n", "\n", "**sparse field**\n", "- `enable_sparse (bool)`: A boolean flag to enable or disable sparse embedding. Defaults to False.\n", "- `sparse_embedding_field (str)`: The name of sparse embedding field, defaults to DEFAULT_SPARSE_EMBEDDING_KEY.\n", "- `sparse_embedding_function (Union[BaseSparseEmbeddingFunction, BaseMilvusBuiltInFunction], optional)`: If enable_sparse is True, this object should be provided to convert text to a sparse embedding. If None, the default sparse embedding function (BM25BuiltInFunction) will be used, or use BGEM3SparseEmbedding given existing collection without built-in functions.\n", "- `sparse_index_config (dict, optional)`: The configuration used to build the sparse embedding index. Defaults to None."]}, {"cell_type": "markdown", "id": "5fe075f5", "metadata": {}, "source": ["To enable hybrid search during the querying stage, set `vector_store_query_mode` to \"hybrid\". This will combine and rerank search results from both semantic search and full-text search. Let's test with a sample query: \"What did the author learn at Viaweb?\":"]}, {"cell_type": "code", "execution_count": null, "id": "ba1558b3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The author learned about retail, the importance of user feedback, and the significance of growth\n", "rate as the ultimate test of a startup at Viaweb.\n"]}], "source": ["import textwrap\n", "\n", "query_engine = index.as_query_engine(\n", "    vector_store_query_mode=\"hybrid\", similarity_top_k=5\n", ")\n", "response = query_engine.query(\"What did the author learn at Viaweb?\")\n", "print(textwrap.fill(str(response), 100))"]}, {"cell_type": "markdown", "id": "4bfab85a", "metadata": {}, "source": ["### Customize text analyzer\n", "\n", "Analyzers play a vital role in full-text search by breaking sentences into tokens and performing lexical processing, such as stemming and stop-word removal. They are typically language-specific. For more details, refer to [Milvus Analyzer Guide](https://milvus.io/docs/analyzer-overview.md#Analyzer-Overview).\n", "\n", "Milvus supports two types of analyzers: **Built-in Analyzers** and **Custom Analyzers**. By default, if `enable_sparse` is set to True, `MilvusVectorStore` utilizes the `BM25BuiltInFunction` with default configurations, employing the standard built-in analyzer that tokenizes text based on punctuation.\n", "\n", "To use a different analyzer or customize the existing one, you can provide values to the `analyzer_params` argument when building the `BM25BuiltInFunction`. Then, set this function as the `sparse_embedding_function` in `MilvusVectorStore`."]}, {"cell_type": "code", "execution_count": null, "id": "5920a3e7", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-04-17 03:38:48,085 [DEBUG][_create_connection]: Created new connection using: 61afd81600cb46ee89f887f16bcbfe55 (async_milvus_client.py:547)\n"]}], "source": ["from llama_index.vector_stores.milvus.utils import BM25BuiltInFunction\n", "\n", "bm25_function = BM25BuiltInFunction(\n", "    analyzer_params={\n", "        \"tokenizer\": \"standard\",\n", "        \"filter\": [\n", "            \"lowercase\",  # Built-in filter\n", "            {\"type\": \"length\", \"max\": 40},  # Custom cap size of a single token\n", "            {\"type\": \"stop\", \"stop_words\": [\"of\", \"to\"]},  # Custom stopwords\n", "        ],\n", "    },\n", "    enable_match=True,\n", ")\n", "\n", "vector_store = MilvusVectorStore(\n", "    uri=URI,\n", "    # token=TOKEN,\n", "    dim=1536,\n", "    enable_sparse=True,\n", "    sparse_embedding_function=bm25_function,  # BM25 with custom analyzer\n", "    overwrite=True,\n", ")"]}, {"cell_type": "markdown", "id": "0b6a9f3e", "metadata": {}, "source": ["## Hybrid Search with Other Sparse Embedding\n", "\n", "Besides combining semantic search with BM25, Milvus also supports hybrid search using a sparse embedding function such as [BGE-M3](https://arxiv.org/abs/2402.03216). The following example uses the built-in `BGEM3SparseEmbeddingFunction` to generate sparse embeddings.\n", "\n", "First, we need to install the `FlagEmbedding` package:"]}, {"cell_type": "code", "execution_count": null, "id": "80abd763", "metadata": {}, "outputs": [], "source": ["! pip install -q FlagEmbedding"]}, {"cell_type": "markdown", "id": "c9dbf9c2", "metadata": {}, "source": ["Then let's build the vector store and index using the default OpenAI model for densen embedding and the built-in BGE-M3 for sparse embedding:"]}, {"cell_type": "code", "execution_count": null, "id": "b35c3c45", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Fetching 30 files: 100%|██████████| 30/30 [00:00<00:00, 68871.99it/s]\n", "2025-04-17 03:39:02,074 [DEBUG][_create_connection]: Created new connection using: ff4886e2f8da44e08304b748d9ac9b51 (async_milvus_client.py:547)\n", "Chunks: 100%|██████████| 1/1 [00:00<00:00,  1.07it/s]\n"]}], "source": ["from llama_index.vector_stores.milvus.utils import BGEM3SparseEmbeddingFunction\n", "\n", "vector_store = MilvusVectorStore(\n", "    uri=URI,\n", "    # token=TOKEN,\n", "    dim=1536,\n", "    enable_sparse=True,\n", "    sparse_embedding_function=BGEM3SparseEmbeddingFunction(),\n", "    overwrite=True,\n", ")\n", "\n", "storage_context = StorageContext.from_defaults(vector_store=vector_store)\n", "index = VectorStoreIndex.from_documents(documents, storage_context=storage_context)"]}, {"cell_type": "markdown", "id": "e6ce9b01", "metadata": {}, "source": ["Now let's perform a hybrid search query with a sample question:"]}, {"cell_type": "code", "execution_count": null, "id": "b8562b3f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Chunks: 100%|██████████| 1/1 [00:00<00:00, 17.29it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["The author learned about retail, the importance of user feedback, the value of growth rate in a\n", "startup, the significance of pricing strategy, the benefits of working on things that weren't\n", "prestigious, and the challenges and rewards of running a startup.\n"]}], "source": ["query_engine = index.as_query_engine(\n", "    vector_store_query_mode=\"hybrid\", similarity_top_k=5\n", ")\n", "response = query_engine.query(\"What did the author learn at Viaweb??\")\n", "print(textwrap.fill(str(response), 100))"]}, {"cell_type": "markdown", "id": "ce364cd9", "metadata": {}, "source": ["### Customize Sparse Embedding Function\n", "\n", "You can also customize the sparse embedding function as long as it inherits from `BaseSparseEmbeddingFunction`, including the following methods:\n", "\n", "- `encode_queries`: This method converts texts into list of sparse embeddings for queries.\n", "- `encode_documents`: This method converts text into list of sparse embeddings for documents.\n", "\n", "The output of each method should follow the format of the sparse embedding, which is a list of dictionaries. Each dictionary should have a key (an integer) representing the dimension, and a corresponding value (a float) representing the embedding's magnitude in that dimension (e.g., {1: 0.5, 2: 0.3}).\n", "\n", "For example, here's a custom sparse embedding function implementation using BGE-M3:"]}, {"cell_type": "code", "execution_count": null, "id": "2a2d7e0f", "metadata": {}, "outputs": [], "source": ["from FlagEmbedding import BGEM3FlagModel\n", "from typing import List\n", "from llama_index.vector_stores.milvus.utils import BaseSparseEmbeddingFunction\n", "\n", "\n", "class ExampleEmbeddingFunction(BaseSparseEmbeddingFunction):\n", "    def __init__(self):\n", "        self.model = BGEM3FlagModel(\"BAAI/bge-m3\", use_fp16=False)\n", "\n", "    def encode_queries(self, queries: List[str]):\n", "        outputs = self.model.encode(\n", "            queries,\n", "            return_dense=False,\n", "            return_sparse=True,\n", "            return_colbert_vecs=False,\n", "        )[\"lexical_weights\"]\n", "        return [self._to_standard_dict(output) for output in outputs]\n", "\n", "    def encode_documents(self, documents: List[str]):\n", "        outputs = self.model.encode(\n", "            documents,\n", "            return_dense=False,\n", "            return_sparse=True,\n", "            return_colbert_vecs=False,\n", "        )[\"lexical_weights\"]\n", "        return [self._to_standard_dict(output) for output in outputs]\n", "\n", "    def _to_standard_dict(self, raw_output):\n", "        result = {}\n", "        for k in raw_output:\n", "            result[int(k)] = raw_output[k]\n", "        return result"]}, {"cell_type": "markdown", "id": "465853f3", "metadata": {}, "source": ["## Customize hybrid reranker\n", "\n", "Milvus supports two types of [reranking strategies](https://milvus.io/docs/reranking.md): Reciprocal Rank Fusion (RRF) and Weighted Scoring. The default ranker in `MilvusVectorStore` hybrid search is RRF with k=60. To customize the hybrid ranker, modify the following parameters:\n", "\n", "- `hybrid_ranker (str)`: Specifies the type of ranker used in hybrid search queries. Currently only supports [\"RRFRanker\", \"WeightedRanker\"]. Defaults to \"RRFRanker\".\n", "- `hybrid_ranker_params (dict, optional)`: Configuration parameters for the hybrid ranker. The structure of this dictionary depends on the specific ranker being used:\n", "    - For \"RRFRanker\", it should include:\n", "        - \"k\" (int): A parameter used in Reciprocal Rank Fusion (RRF). This value is used to calculate the rank scores as part of the RRF algorithm, which combines multiple ranking strategies into a single score to improve search relevance. The default value is 60 if not specified.\n", "    - For \"WeightedRanker\", it expects:\n", "        - \"weights\" (list of float): A list of exactly two weights:\n", "            1. The weight for the dense embedding component.\n", "            2. The weight for the sparse embedding component.\n", "            These weights are used to balance the significance of the dense and sparse components of the embeddings in the hybrid retrieval process. The default weights are [1.0, 1.0] if not specified."]}, {"cell_type": "code", "execution_count": null, "id": "e5c465a7", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-04-17 03:44:00,419 [DEBUG][_create_connection]: Created new connection using: 09c051fb18c04f97a80f07958856587b (async_milvus_client.py:547)\n", "Sparse embedding function is not provided, using default.\n", "No built-in function detected, using BGEM3SparseEmbeddingFunction().\n", "Fetching 30 files: 100%|██████████| 30/30 [00:00<00:00, 136622.28it/s]\n", "Chunks: 100%|██████████| 1/1 [00:00<00:00,  1.07it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["The author learned several valuable lessons at Viaweb, including the importance of understanding\n", "growth rate as the ultimate test of a startup, the significance of user feedback in shaping the\n", "software, and the realization that web applications were the future of software development.\n", "Additionally, the experience at Viaweb taught the author about the challenges and rewards of running\n", "a startup, the value of simplicity in software design, and the impact of pricing strategies on\n", "attracting customers.\n"]}], "source": ["vector_store = MilvusVectorStore(\n", "    uri=URI,\n", "    # token=TOKEN,\n", "    dim=1536,\n", "    overwrite=False,  # Use the existing collection created in the previous example\n", "    enable_sparse=True,\n", "    hybrid_ranker=\"WeightedRanker\",\n", "    hybrid_ranker_params={\"weights\": [1.0, 0.5]},\n", ")\n", "index = VectorStoreIndex.from_vector_store(vector_store)\n", "query_engine = index.as_query_engine(\n", "    vector_store_query_mode=\"hybrid\", similarity_top_k=5\n", ")\n", "response = query_engine.query(\"What did the author learn at Viaweb?\")\n", "print(textwrap.fill(str(response), 100))"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3"}}, "nbformat": 4, "nbformat_minor": 5}