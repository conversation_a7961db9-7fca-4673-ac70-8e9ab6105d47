{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/integration/openai_agents_milvus.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/integration/openai_agents_milvus.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>\n", "</a>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Milvus Integration with OpenAI Agents: A Step-by-Step Guide\n", "\n", "This notebook shows how to create an agent that can query <PERSON><PERSON><PERSON><PERSON> using natural language through Function Calling. We'll combine OpenAI's Agents framework with Milvus's powerful vector search capabilities to create a nice search experience.\n", "\n", "## OpenAI Agents \n", "\n", "The OpenAI Agents SDK enables you to build agentic AI apps in a lightweight, easy-to-use package with very few abstractions. It's a production-ready upgrade of their previous experimentation for agents, Swarm. The Agents SDK has a very small set of primitives:\n", "\n", "* Agents, which are LLMs equipped with instructions and tools\n", "* Handoffs, which allow agents to delegate to other agents for specific tasks\n", "* Guardrails, which enable the inputs to agents to be validated\n", "\n", "In combination with Python, these primitives are powerful enough to express complex relationships between tools and agents, and allow you to build real-world applications without a steep learning curve. In addition, the SDK comes with built-in tracing that lets you visualize and debug your agentic flows, as well as evaluate them and even fine-tune models for your application.\n", "\n", "![](../pics/openai-agent.png)\n", "\n", "## <PERSON><PERSON><PERSON><PERSON>\n", "\n", "Milvus is a high-performance, highly scalable Open-Source vector database that runs efficiently across a wide range of environments, from a laptop to large-scale distributed systems. It is available as both open-source software and a [Cloud Offering](https://zilliz.com/).\n", "\n", "## Setup and Dependencies\n", "\n", "First, we need to set up our environment with the necessary libraries and initialize asyncio for Jupyter compatibility."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["! pip install openai pymilvus pydantic nest_asyncio"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> If you are using Google Colab, to enable dependencies just installed, you may need to **restart the runtime** (click on the \"Runtime\" menu at the top of the screen, and select \"Restart session\" from the dropdown menu).\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import nest_asyncio\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "\n", "nest_asyncio.apply()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We will use the models from OpenAI. You should prepare the [api key](https://platform.openai.com/docs/quickstart) `OPENAI_API_KEY` as an environment variable."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-***********\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Connecting to <PERSON><PERSON><PERSON><PERSON> and Creating a Schema\n", "\n", "Now we'll connect to our Milvus instance and create a schema for our collection. This schema will define the structure of our data, including:\n", "- An ID field as the primary key\n", "- A text field to store our document content\n", "- A sparse vector field to store the BM25 embeddings\n", "\n", "### Full-Text Search in Milvus 2.5\n", "* Unified system for both vector and keyword search (unified APIs)\n", "* Built-in sparse-BM25 algorithm (similar as Elasticsearch use but vector based)\n", "* No need to manually generate embeddings for keyword search\n", "\n", "<img src=\"https://milvus.io/docs/v2.5.x/assets/full-text-search.png\" width=\"70%\" alt=\"img\">"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON> Milvu<PERSON> with <PERSON><PERSON> \n", "\n", "Before running this example, make sure to install Milvus and start it with Docker, have a look at our documentation - https://milvus.io/docs/install_standalone-docker.md"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'auto_id': False, 'description': '', 'fields': [{'name': 'id', 'description': '', 'type': <DataType.INT64: 5>, 'is_primary': True, 'auto_id': True}, {'name': 'text', 'description': '', 'type': <DataType.VARCHAR: 21>, 'params': {'max_length': 1000, 'enable_analyzer': True}}, {'name': 'sparse', 'description': '', 'type': <DataType.SPARSE_FLOAT_VECTOR: 104>}], 'enable_dynamic_field': False}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from pymilvus import DataType, FunctionType, MilvusClient\n", "\n", "client = MilvusClient(uri=\"http://localhost:19530\")\n", "\n", "schema = client.create_schema()\n", "\n", "# Simple schema that handles both text and vectors\n", "schema.add_field(\n", "    field_name=\"id\", datatype=DataType.INT64, is_primary=True, auto_id=True\n", ")\n", "schema.add_field(\n", "    field_name=\"text\", datatype=DataType.VARCHAR, max_length=1000, enable_analyzer=True\n", ")\n", "schema.add_field(field_name=\"sparse\", datatype=DataType.SPARSE_FLOAT_VECTOR)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up BM25 for Full-Text Search\n", "\n", "Milvus supports full-text search through BM25 functions. Here we define a function that will automatically convert our text data into sparse vector representations optimized for text search."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'auto_id': False, 'description': '', 'fields': [{'name': 'id', 'description': '', 'type': <DataType.INT64: 5>, 'is_primary': True, 'auto_id': True}, {'name': 'text', 'description': '', 'type': <DataType.VARCHAR: 21>, 'params': {'max_length': 1000, 'enable_analyzer': True}}, {'name': 'sparse', 'description': '', 'type': <DataType.SPARSE_FLOAT_VECTOR: 104>, 'is_function_output': True}], 'enable_dynamic_field': False, 'functions': [{'name': 'text_bm25_emb', 'description': '', 'type': <FunctionType.BM25: 1>, 'input_field_names': ['text'], 'output_field_names': ['sparse'], 'params': {}}]}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from pymilvus import Function\n", "\n", "# Milvus handles tokenization and BM25 conversion\n", "bm25_function = Function(\n", "    name=\"text_bm25_emb\",  # Function name\n", "    input_field_names=[\"text\"],  # Name of the VARCHAR field containing raw text data\n", "    output_field_names=[\n", "        \"sparse\"\n", "    ],  # Name of the SPARSE_FLOAT_VECTOR field reserved to store generated embeddings\n", "    function_type=FunctionType.BM25,\n", ")\n", "\n", "schema.add_function(bm25_function)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating the Collection and Loading Sample Data\n", "\n", "Now we'll create our collection with the schema and index parameters, then load some sample data about information retrieval and Milvus."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'insert_count': 37, 'ids': [456486814660619140, 456486814660619141, 456486814660619142, 456486814660619143, 456486814660619144, 456486814660619145, 456486814660619146, 456486814660619147, 456486814660619148, 456486814660619149, 456486814660619150, 456486814660619151, 456486814660619152, 456486814660619153, 456486814660619154, 456486814660619155, 456486814660619156, 456486814660619157, 456486814660619158, 456486814660619159, 456486814660619160, 456486814660619161, 456486814660619162, 456486814660619163, 456486814660619164, 456486814660619165, 456486814660619166, 456486814660619167, 456486814660619168, 456486814660619169, 456486814660619170, 456486814660619171, 456486814660619172, 456486814660619173, 456486814660619174, 456486814660619175, 456486814660619176], 'cost': 0}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["index_params = client.prepare_index_params()\n", "\n", "index_params.add_index(field_name=\"sparse\", index_type=\"AUTOINDEX\", metric_type=\"BM25\")\n", "\n", "if client.has_collection(\"demo\"):\n", "    client.drop_collection(\"demo\")\n", "\n", "client.create_collection(\n", "    collection_name=\"demo\",\n", "    schema=schema,\n", "    index_params=index_params,\n", ")\n", "\n", "## 3. Loading Test Data\n", "client.insert(\n", "    \"demo\",\n", "    [\n", "        {\n", "            \"text\": \"Information retrieval helps users find relevant documents in large datasets.\"\n", "        },\n", "        {\n", "            \"text\": \"Search engines use information retrieval techniques to index and rank web pages.\"\n", "        },\n", "        {\n", "            \"text\": \"The core of IR is matching user queries with the most relevant content.\"\n", "        },\n", "        {\n", "            \"text\": \"Vector search is revolutionising modern information retrieval systems.\"\n", "        },\n", "        {\n", "            \"text\": \"Machine learning improves ranking algorithms in information retrieval.\"\n", "        },\n", "        {\n", "            \"text\": \"IR techniques include keyword-based search, semantic search, and vector search.\"\n", "        },\n", "        {\n", "            \"text\": \"Boolean retrieval is one of the earliest information retrieval methods.\"\n", "        },\n", "        {\"text\": \"TF-IDF is a classic method used to score document relevance in IR.\"},\n", "        {\n", "            \"text\": \"Modern IR systems integrate deep learning for better contextual understanding.\"\n", "        },\n", "        {\n", "            \"text\": \"Milvus is an open-source vector database designed for AI-powered search.\"\n", "        },\n", "        {\n", "            \"text\": \"Milvus enables fast and scalable similarity search on high-dimensional data.\"\n", "        },\n", "        {\n", "            \"text\": \"With Milvus, developers can build applications that support image, text, and video retrieval.\"\n", "        },\n", "        {\n", "            \"text\": \"Milvus integrates well with deep learning frameworks like PyTorch and TensorFlow.\"\n", "        },\n", "        {\n", "            \"text\": \"The core of Milvus is optimised for approximate nearest neighbour (ANN) search.\"\n", "        },\n", "        {\n", "            \"text\": \"Milvus supports hybrid search combining structured and unstructured data.\"\n", "        },\n", "        {\n", "            \"text\": \"Large-scale AI applications rely on Milvus for efficient vector retrieval.\"\n", "        },\n", "        {\"text\": \"<PERSON><PERSON><PERSON><PERSON> makes it easy to perform high-speed similarity searches.\"},\n", "        {\"text\": \"Cloud-native by design, <PERSON><PERSON><PERSON><PERSON> scales effortlessly with demand.\"},\n", "        {\n", "            \"text\": \"Milvus powers applications in recommendation systems, fraud detection, and genomics.\"\n", "        },\n", "        {\n", "            \"text\": \"The latest version of Milvus introduces faster indexing and lower latency.\"\n", "        },\n", "        {\"text\": \"Milvus supports HNSW, IVF_FLAT, and other popular ANN algorithms.\"},\n", "        {\n", "            \"text\": \"Vector embeddings from models like OpenAI’s CLIP can be indexed in Milvus.\"\n", "        },\n", "        {\n", "            \"text\": \"Milvus has built-in support for multi-tenancy in enterprise use cases.\"\n", "        },\n", "        {\n", "            \"text\": \"The Milvus community actively contributes to improving its performance.\"\n", "        },\n", "        {\n", "            \"text\": \"Milvus integrates with data pipelines like Apache Kafka for real-time updates.\"\n", "        },\n", "        {\n", "            \"text\": \"Using Milvus, companies can enhance search experiences with vector search.\"\n", "        },\n", "        {\n", "            \"text\": \"<PERSON><PERSON><PERSON><PERSON> plays a crucial role in powering AI search in medical research.\"\n", "        },\n", "        {\"text\": \"Milvus integrates with LangChain for advanced RAG pipelines.\"},\n", "        {\n", "            \"text\": \"Open-source contributors continue to enhance Milvus’ search performance.\"\n", "        },\n", "        {\n", "            \"text\": \"Multi-modal search in Milvus enables applications beyond text and images.\"\n", "        },\n", "        {\"text\": \"Milvus has an intuitive REST API for easy integration.\"},\n", "        {\"text\": \"Milvus’ FAISS and HNSW backends provide flexibility in indexing.\"},\n", "        {\n", "            \"text\": \"The architecture of Milvus ensures fault tolerance and high availability.\"\n", "        },\n", "        {\"text\": \"Milvus integrates seamlessly with LLM-based applications.\"},\n", "        {\"text\": \"Startups leverage Milvus to build next-gen AI-powered products.\"},\n", "        {\"text\": \"Milvus Cloud offers a managed solution for vector search at scale.\"},\n", "        {\n", "            \"text\": \"The future of AI search is being shaped by Milvus and similar vector databases.\"\n", "        },\n", "    ],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Defining Output Types for Structured Results\n", "\n", "To make our search results more structured and easier to work with, we'll define Pydantic models that specify the format of our search results."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel\n", "\n", "\n", "# Simplified output model for search results\n", "class MilvusSearchResult(BaseModel):\n", "    id: int\n", "    text: str\n", "\n", "\n", "class MilvusSearchResults(BaseModel):\n", "    results: list[MilvusSearchResult]\n", "    query: str"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating a Custom Search Tool\n", "\n", "Next, we'll create a custom function tool that our agent can use to search the Milvus database. This tool will:\n", "1. Accept a collection name, query text, and limit parameter\n", "2. Execute a BM25 search against the Milvus collection\n", "3. Return the results in a structured format"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import json\n", "from typing import Any\n", "from pymilvus import MilvusClient\n", "from agents import function_tool, RunContextWrapper\n", "\n", "\n", "@function_tool\n", "async def search_milvus_text(\n", "    ctx: RunContextWrapper[Any], collection_name: str, query_text: str, limit: int\n", ") -> str:\n", "    \"\"\"Search for text documents in a Milvus collection using full text search.\n", "\n", "    Args:\n", "        collection_name: Name of the <PERSON><PERSON><PERSON><PERSON> collection to search.\n", "        query_text: The text query to search for.\n", "        limit: Maximum number of results to return.\n", "    \"\"\"\n", "    try:\n", "        # Initialize Milvus client\n", "        client = MilvusClient()\n", "\n", "        # Prepare search parameters for BM25\n", "        search_params = {\"metric_type\": \"BM25\", \"params\": {\"drop_ratio_search\": 0.2}}\n", "\n", "        # Execute search with text query\n", "        results = client.search(\n", "            collection_name=collection_name,\n", "            data=[query_text],\n", "            anns_field=\"sparse\",\n", "            limit=limit,\n", "            search_params=search_params,\n", "            output_fields=[\"text\"],\n", "        )\n", "        return json.dumps(\n", "            {\"results\": results, \"query\": query_text, \"collection\": collection_name}\n", "        )\n", "\n", "    except Exception as e:\n", "        print(f\"Exception is: {e}\")\n", "        return f\"Error searching Milvus: {str(e)}\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Building the Agent\n", "\n", "Now we'll create an agent that can use our search tool. We'll give it instructions on how to handle search requests and specify that it should return results in our structured format."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from agents import Agent, Runner, WebSearchTool, trace\n", "\n", "\n", "async def main():\n", "    agent = Agent(\n", "        name=\"<PERSON><PERSON><PERSON><PERSON> Searcher\",\n", "        instructions=\"\"\"\n", "        You are a helpful agent that can search through Milvus vector database using full text search. Return the results in a structured format.\n", "        \"\"\",\n", "        tools=[\n", "            WebSearchTool(user_location={\"type\": \"approximate\", \"city\": \"New York\"}),\n", "            search_milvus_text,\n", "        ],\n", "        output_type=MilvusSearchResults,\n", "    )\n", "\n", "    with trace(\"Milvus search example\"):\n", "        result = await Runner.run(\n", "            agent,\n", "            \"Find documents in the 'demo' collection that are similar to this concept: 'information retrieval'\",\n", "        )\n", "        # print(result.final_output.results)\n", "        formatted_results = \"\\n\".join(\n", "            f\"{i+1}. ID: {res.id}, Text: {res.text}\"\n", "            for i, res in enumerate(result.final_output.results)\n", "        )\n", "        print(f\"Search results:\\n{formatted_results}\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Search results:\n", "1. ID: 456486814660619146, Text: Boolean retrieval is one of the earliest information retrieval methods.\n", "2. ID: 456486814660619144, Text: Machine learning improves ranking algorithms in information retrieval.\n", "3. ID: 456486814660619143, Text: Vector search is revolutionising modern information retrieval systems.\n", "4. ID: 456486814660619140, Text: Information retrieval helps users find relevant documents in large datasets.\n", "5. ID: 456486814660619141, Text: Search engines use information retrieval techniques to index and rank web pages.\n"]}], "source": ["asyncio.run(main())"]}, {"attachments": {"74d6c61c-cb1d-4882-868d-f7e9fbd4c021.png": {"image/png": "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"}, "a4abc7aa-3e70-4266-8ec8-169ffc83bc26.png": {"image/png": "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"}}, "cell_type": "markdown", "metadata": {}, "source": ["# ⭐️ <PERSON><PERSON><PERSON>\n", "We hope you liked this tutorial showcasing how to use Milvus with OpenAI Agents. If you liked it and our project, please give us a star on Github! ⭐\n", "\n", "![image.png](attachment:74d6c61c-cb1d-4882-868d-f7e9fbd4c021.png)\n", "\n", "# 🤝 Add me on Linkedin!\n", "If you have some questions related to <PERSON><PERSON><PERSON><PERSON>, GenAI, etc, I am <PERSON>, you can add me on LinkedIn and I'll gladly help you.\n", "\n", "![image.png](attachment:a4abc7aa-3e70-4266-8ec8-169ffc83bc26.png)\n", "\n", "# 💬 Join our Discord\n", "\n", "If you're interested in learning more about <PERSON><PERSON><PERSON><PERSON> or you wanna share some feedback, feel free to join our [Discord channel](https://zilliz.com/discord)."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 4}