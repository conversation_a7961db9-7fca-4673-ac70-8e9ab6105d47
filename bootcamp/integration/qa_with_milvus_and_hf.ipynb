{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/integration/qa_with_milvus_and_hf.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/integration/qa_with_milvus_and_hf.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>\n", "</a>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Question Answering Using <PERSON><PERSON><PERSON><PERSON> and Hugging Face\n", "\n", "A question answering system based on semantic search works by finding the most similar question from a dataset of question-answer pairs for a given query question. Once the most similar question is identified, the corresponding answer from the dataset is considered as the answer for the query. This approach relies on semantic similarity measures to determine the similarity between questions and retrieve relevant answers.\n", "\n", "This tutorial shows how to build a question answering system using [Hugging Face](https://huggingface.co) as the data loader & embedding generator for data processing and [Milvus](https://milvus.io) as the vector database for semantic search."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Before you begin\n", "\n", "You need to make sure all required dependencies are installed:\n", "\n", "- `pymilvus`: a python package works with the vector database service powered by Milvus or Zilliz Cloud.\n", "- `datasets`, `transformers`: Hugging Face packages manage data and utilize models.\n", "- `torch`: a powerful library provides efficient tensor computation and deep learning tools."]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["%pip install --upgrade pymilvus transformers datasets torch"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> If you are using Google Colab, to enable dependencies just installed, you may need to **restart the runtime** (click on the \"Runtime\" menu at the top of the screen, and select \"Restart session\" from the dropdown menu)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prepare data\n", "\n", "In this section, we will load example question-answer pairs from the Hugging Face Datasets. As a demo, we only take partial data from the validation split of [SQuAD](https://huggingface.co/datasets/raj<PERSON><PERSON>/squad)."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset({\n", "    features: ['title', 'question', 'answer'],\n", "    num_rows: 11\n", "})\n"]}], "source": ["from datasets import load_dataset\n", "\n", "\n", "DATASET = \"squad\"  # Name of dataset from HuggingFace Datasets\n", "INSERT_RATIO = 0.001  # Ratio of example dataset to be inserted\n", "\n", "data = load_dataset(DATASET, split=\"validation\")\n", "# Generates a fixed subset. To generate a random subset, remove the seed.\n", "data = data.train_test_split(test_size=INSERT_RATIO, seed=42)[\"test\"]\n", "# Clean up the data structure in the dataset.\n", "data = data.map(\n", "    lambda val: {\"answer\": val[\"answers\"][\"text\"][0]},\n", "    remove_columns=[\"id\", \"answers\", \"context\"],\n", ")\n", "\n", "# View summary of example data\n", "print(data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To generate embeddings for questions, you are able to select a text embedding model from Hugging Face Models. In this tutorial, we will use a small sentencce embedding model [all-MiniLM-L6-v2](https://huggingface.co/sentence-transformers/all-MiniLM-L6-v2) as example."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from transformers import AutoTokenizer, AutoModel\n", "import torch\n", "\n", "MODEL = (\n", "    \"sentence-transformers/all-MiniLM-L6-v2\"  # Name of model from HuggingFace Models\n", ")\n", "INFERENCE_BATCH_SIZE = 64  # Batch size of model inference\n", "\n", "# Load tokenizer & model from HuggingFace Hub\n", "tokenizer = AutoTokenizer.from_pretrained(MODEL)\n", "model = AutoModel.from_pretrained(MODEL)\n", "\n", "\n", "def encode_text(batch):\n", "    # Tokenize sentences\n", "    encoded_input = tokenizer(\n", "        batch[\"question\"], padding=True, truncation=True, return_tensors=\"pt\"\n", "    )\n", "\n", "    # Compute token embeddings\n", "    with torch.no_grad():\n", "        model_output = model(**encoded_input)\n", "\n", "    # Perform pooling\n", "    token_embeddings = model_output[0]\n", "    attention_mask = encoded_input[\"attention_mask\"]\n", "    input_mask_expanded = (\n", "        attention_mask.unsqueeze(-1).expand(token_embeddings.size()).float()\n", "    )\n", "    sentence_embeddings = torch.sum(\n", "        token_embeddings * input_mask_expanded, 1\n", "    ) / torch.clamp(input_mask_expanded.sum(1), min=1e-9)\n", "\n", "    # Normalize embeddings\n", "    batch[\"question_embedding\"] = torch.nn.functional.normalize(\n", "        sentence_embeddings, p=2, dim=1\n", "    )\n", "    return batch\n", "\n", "\n", "data = data.map(encode_text, batched=True, batch_size=INFERENCE_BATCH_SIZE)\n", "data_list = data.to_list()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Insert data\n", "\n", "Now we have question-answer pairs ready with question embeddings. The next step is to insert them into the vector database.\n", "\n", "We will first need to connect to Milvus service and create a Milvus collection."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from pymilvus import MilvusClient\n", "\n", "\n", "MILVUS_URI = \"./huggingface_milvus_test.db\"  # Connection URI\n", "COLLECTION_NAME = \"huggingface_test\"  # Collection name\n", "DIMENSION = 384  # Embedding dimension depending on model\n", "\n", "milvus_client = MilvusClient(MILVUS_URI)\n", "if milvus_client.has_collection(collection_name=COLLECTION_NAME):\n", "    milvus_client.drop_collection(collection_name=COLLECTION_NAME)\n", "milvus_client.create_collection(\n", "    collection_name=COLLECTION_NAME,\n", "    dimension=DIMENSION,\n", "    auto_id=True,  # Enable auto id\n", "    enable_dynamic_field=True,  # Enable dynamic fields\n", "    vector_field_name=\"question_embedding\",  # Map vector field name and embedding column in dataset\n", "    consistency_level=\"Strong\",  # To enable search with latest data\n", ")"]}, {"cell_type": "markdown", "source": ["> As for the argument of `MilvusClient`:\n", "> - Setting the `uri` as a local file, e.g.`./milvus.db`, is the most convenient method, as it automatically utilizes [Milvus Lite](https://milvus.io/docs/milvus_lite.md) to store all data in this file.\n", "> - If you have large scale of data, you can set up a more performant Milvus server on [docker or kubernetes](https://milvus.io/docs/quickstart.md). In this setup, please use the server uri, e.g.`http://localhost:19530`, as your `uri`.\n", "> - If you want to use [Zilliz Cloud](https://zilliz.com/cloud), the fully managed cloud service for Milvus, adjust the `uri` and `token`, which correspond to the [Public Endpoint and Api key](https://docs.zilliz.com/docs/on-zilliz-cloud-console#free-cluster-details) in Zilliz Cloud."], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "metadata": {}, "source": ["Insert all data into the collection:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'insert_count': 11,\n", " 'ids': [450072488481390592, 450072488481390593, 450072488481390594, 450072488481390595, 450072488481390596, 450072488481390597, 450072488481390598, 450072488481390599, 450072488481390600, 450072488481390601, 450072488481390602],\n", " 'cost': 0}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["milvus_client.insert(collection_name=COLLECTION_NAME, data=data_list)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Ask questions\n", "\n", "Once all the data is inserted into Milvus, we can ask questions and see what the closest answers are."]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Question: What is LGM?\n", "{'answer': 'Last Glacial Maximum', 'score': 0.956273078918457, 'original question': 'What does LGM stands for?'}\n", "{'answer': 'coordinate the response to the embargo', 'score': 0.2120140939950943, 'original question': 'Why was this short termed organization created?'}\n", "{'answer': '\"Reducibility Among Combinatorial Problems\"', 'score': 0.1945795714855194, 'original question': 'What is the paper written by <PERSON> in 1972 that ushered in a new era of understanding between intractability and NP-complete problems?'}\n", "\n", "\n", "Question: When did Massachusetts first mandate that children be educated in schools?\n", "{'answer': '1852', 'score': 0.9709997177124023, 'original question': 'In what year did Massachusetts first require children to be educated in schools?'}\n", "{'answer': 'several regional colleges and universities', 'score': 0.34164726734161377, 'original question': 'In 1890, who did the university decide to team up with?'}\n", "{'answer': '1962', 'score': 0.1931006908416748, 'original question': 'When were stromules discovered?'}\n", "\n", "\n"]}], "source": ["questions = {\n", "    \"question\": [\n", "        \"What is LGM?\",\n", "        \"When did Massachusetts first mandate that children be educated in schools?\",\n", "    ]\n", "}\n", "\n", "# Generate question embeddings\n", "question_embeddings = [v.tolist() for v in encode_text(questions)[\"question_embedding\"]]\n", "\n", "# Search across Milvus\n", "search_results = milvus_client.search(\n", "    collection_name=COLLECTION_NAME,\n", "    data=question_embeddings,\n", "    limit=3,  # How many search results to output\n", "    output_fields=[\"answer\", \"question\"],  # Include these fields in search results\n", ")\n", "\n", "# Print out results\n", "for q, res in zip(questions[\"question\"], search_results):\n", "    print(\"Question:\", q)\n", "    for r in res:\n", "        print(\n", "            {\n", "                \"answer\": r[\"entity\"][\"answer\"],\n", "                \"score\": r[\"distance\"],\n", "                \"original question\": r[\"entity\"][\"question\"],\n", "            }\n", "        )\n", "    print(\"\\n\")"]}], "metadata": {"kernelspec": {"display_name": "develop", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.12"}}, "nbformat": 4, "nbformat_minor": 2}