{"cells": [{"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/integration/haystack/rag_with_milvus_and_haystack.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/integration/haystack/rag_with_milvus_and_haystack.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>\n", "</a>\n"]}, {"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["# Retrieval-Augmented Generation (RAG) with Milvus and Haystack\n", "\n", "This guide demonstrates how to build a Retrieval-Augmented Generation (RAG) system using Haystack and Milvus.\n", "\n", "The RAG system combines a retrieval system with a generative model to generate new text based on a given prompt. The system first retrieves relevant documents from a corpus using Milvus, and then uses a generative model to generate new text based on the retrieved documents.\n", "\n", "[Haystack](https://haystack.deepset.ai/) is the open source Python framework by deepset for building custom apps with large language models (LLMs). [Milvus](https://milvus.io/) is the world's most advanced open-source vector database, built to power embedding similarity search and AI applications.\n", "\n", "\n", "\n", "## Prerequisites\n", "\n", "Before running this notebook, make sure you have the following dependencies installed:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["! pip install --upgrade --quiet pymilvus milvus-haystack markdown-it-py mdit_plain"]}, {"cell_type": "markdown", "metadata": {"collapsed": false}, "source": ["> If you are using Google Colab, to enable dependencies just installed, you may need to **restart the runtime** (click on the \"Runtime\" menu at the top of the screen, and select \"Restart session\" from the dropdown menu)."]}, {"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["We will use the models from OpenAI. You should prepare the [api key](https://platform.openai.com/docs/quickstart) `OPENAI_API_KEY` as an environment variable."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-***********\""]}, {"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Prepare the data\n", "\n", "We use an online content about [<PERSON>](https://www.gutenberg.org/cache/epub/7785/pg7785.txt) as a store of private knowledge for our RAG pipeline, which is a good data source for a simple RAG pipeline.\n", "\n", "Download it and save it as a local text file."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import os\n", "import urllib.request\n", "\n", "url = \"https://www.gutenberg.org/cache/epub/7785/pg7785.txt\"\n", "file_path = \"./davinci.txt\"\n", "\n", "if not os.path.exists(file_path):\n", "    urllib.request.urlretrieve(url, file_path)"]}, {"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Create the indexing Pipeline\n", "\n", "Create an indexing pipeline that converts the text into documents, splits them into sentences, and embeds them. The documents are then written to the Milvus document store."]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["from haystack import Pipeline\n", "from haystack.components.converters import MarkdownToDocument\n", "from haystack.components.embedders import OpenAIDocumentEmbedder, OpenAITextEmbedder\n", "from haystack.components.preprocessors import DocumentSplitter\n", "from haystack.components.writers import DocumentWriter\n", "from haystack.utils import Secret\n", "\n", "from milvus_haystack import MilvusDocumentStore\n", "from milvus_haystack.milvus_embedding_retriever import MilvusEmbeddingRetriever\n", "\n", "\n", "document_store = MilvusDocumentStore(\n", "    connection_args={\"uri\": \"./milvus.db\"},\n", "    # connection_args={\"uri\": \"http://localhost:19530\"},\n", "    # connection_args={\"uri\": YOUR_ZILLIZ_CLOUD_URI, \"token\": Secret.from_env_var(\"ZILLIZ_CLOUD_API_KEY\")},\n", "    # drop_old=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {"collapsed": false}, "source": ["> For the connection_args:\n", "> - Setting the `uri` as a local file, e.g.`./milvus.db`, is the most convenient method, as it automatically utilizes [Milvus Lite](https://milvus.io/docs/milvus_lite.md) to store all data in this file.\n", "> - If you have large scale of data, you can set up a more performant Milvus server on [docker or kubernetes](https://milvus.io/docs/quickstart.md). In this setup, please use the server uri, e.g.`http://localhost:19530`, as your `uri`.\n", "> - If you want to use [Zilliz Cloud](https://zilliz.com/cloud), the fully managed cloud service for Milvus, adjust the `uri` and `token`, which correspond to the [Public Endpoint and Api key](https://docs.zilliz.com/docs/on-zilliz-cloud-console#free-cluster-details) in Zilliz Cloud."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Converting markdown files to Documents: 100%|█| 1/\n", "Calculating embeddings: 100%|█| 9/9 [00:05<00:00, \n", "E20240516 10:40:32.945937 5309095 milvus_local.cpp:189] [SERVER][GetCollection][] Collecton HaystackCollection not existed\n", "E20240516 10:40:32.946677 5309095 milvus_local.cpp:189] [SERVER][GetCollection][] Collecton HaystackCollection not existed\n", "E20240516 10:40:32.946704 5309095 milvus_local.cpp:189] [SERVER][GetCollection][] Collecton HaystackCollection not existed\n", "E20240516 10:40:32.946725 5309095 milvus_local.cpp:189] [SERVER][GetCollection][] Collecton HaystackCollection not existed\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of documents: 277\n"]}], "source": ["indexing_pipeline = Pipeline()\n", "indexing_pipeline.add_component(\"converter\", MarkdownToDocument())\n", "indexing_pipeline.add_component(\n", "    \"splitter\", DocumentSplitter(split_by=\"sentence\", split_length=2)\n", ")\n", "indexing_pipeline.add_component(\"embedder\", OpenAIDocumentEmbedder())\n", "indexing_pipeline.add_component(\"writer\", DocumentWriter(document_store))\n", "indexing_pipeline.connect(\"converter\", \"splitter\")\n", "indexing_pipeline.connect(\"splitter\", \"embedder\")\n", "indexing_pipeline.connect(\"embedder\", \"writer\")\n", "indexing_pipeline.run({\"converter\": {\"sources\": [file_path]}})\n", "\n", "print(\"Number of documents:\", document_store.count_documents())"]}, {"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Create the retrieval pipeline\n", "\n", "Create a retrieval pipeline that retrieves documents from the Milvus document store using a vector similarity search engine."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["). The\n", "composition of this oil-painting seems to have been built up on the\n", "second cartoon, which he had made some eight years earlier, and which\n", "was apparently taken to France in 1516 and ultimately lost.\n", "----------\n", "\n", "This \"Baptism of Christ,\" which is now in the Accademia in Florence\n", "and is in a bad state of preservation, appears to have been a\n", "comparatively early work by <PERSON><PERSON><PERSON><PERSON><PERSON>, and to have been painted\n", "in 1480-1482, when <PERSON> would be about thirty years of age.\n", "\n", "To about this period belongs the superb drawing of the \"Warrior,\" now\n", "in the Malcolm Collection in the British Museum.\n", "----------\n", "\" Although he\n", "completed the cartoon, the only part of the composition which he\n", "eventually executed in colour was an incident in the foreground\n", "which dealt with the \"Battle of the Standard.\" One of the many\n", "supposed copies of a study of this mural painting now hangs on the\n", "south-east staircase in the Victoria and Albert Museum.\n", "----------\n"]}], "source": ["question = 'Where is the painting \"Warrior\" currently stored?'\n", "\n", "retrieval_pipeline = Pipeline()\n", "retrieval_pipeline.add_component(\"embedder\", OpenAITextEmbedder())\n", "retrieval_pipeline.add_component(\n", "    \"retriever\", MilvusEmbeddingRetriever(document_store=document_store, top_k=3)\n", ")\n", "retrieval_pipeline.connect(\"embedder\", \"retriever\")\n", "\n", "retrieval_results = retrieval_pipeline.run({\"embedder\": {\"text\": question}})\n", "\n", "for doc in retrieval_results[\"retriever\"][\"documents\"]:\n", "    print(doc.content)\n", "    print(\"-\" * 10)"]}, {"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Create the RAG pipeline\n", "\n", "Create a RAG pipeline that combines the MilvusEmbeddingRetriever and the OpenAIGenerator to answer the question using the retrieved documents."]}, {"cell_type": "code", "execution_count": 9, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RAG answer: The painting \"Warrior\" is currently stored in the Malcolm Collection in the British Museum.\n"]}], "source": ["from haystack.utils import Secret\n", "from haystack.components.builders import PromptBuilder\n", "from haystack.components.generators import OpenAIGenerator\n", "\n", "prompt_template = \"\"\"Answer the following query based on the provided context. If the context does\n", "                     not include an answer, reply with 'I don't know'.\\n\n", "                     Query: {{query}}\n", "                     Documents:\n", "                     {% for doc in documents %}\n", "                        {{ doc.content }}\n", "                     {% endfor %}\n", "                     Answer:\n", "                  \"\"\"\n", "\n", "rag_pipeline = Pipeline()\n", "rag_pipeline.add_component(\"text_embedder\", OpenAITextEmbedder())\n", "rag_pipeline.add_component(\n", "    \"retriever\", MilvusEmbeddingRetriever(document_store=document_store, top_k=3)\n", ")\n", "rag_pipeline.add_component(\"prompt_builder\", PromptBuilder(template=prompt_template))\n", "rag_pipeline.add_component(\n", "    \"generator\",\n", "    OpenAIGenerator(\n", "        api_key=Secret.from_token(os.getenv(\"OPENAI_API_KEY\")),\n", "        generation_kwargs={\"temperature\": 0},\n", "    ),\n", ")\n", "rag_pipeline.connect(\"text_embedder.embedding\", \"retriever.query_embedding\")\n", "rag_pipeline.connect(\"retriever.documents\", \"prompt_builder.documents\")\n", "rag_pipeline.connect(\"prompt_builder\", \"generator\")\n", "\n", "results = rag_pipeline.run(\n", "    {\n", "        \"text_embedder\": {\"text\": question},\n", "        \"prompt_builder\": {\"query\": question},\n", "    }\n", ")\n", "print(\"RAG answer:\", results[\"generator\"][\"replies\"][0])"]}, {"cell_type": "markdown", "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}, "source": ["For more information about how to use milvus-haystack, please refer to the [milvus-haystack Readme](https://github.com/milvus-io/milvus-haystack)."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 4}