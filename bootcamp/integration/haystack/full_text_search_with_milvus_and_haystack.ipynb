{"cells": [{"cell_type": "markdown", "id": "972fba7b-781d-441d-b678-8a713ae2a3ba", "metadata": {}, "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/integration/haystack/full_text_search_with_milvus_and_haystack.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/integration/haystack/full_text_search_with_milvus_and_haystack.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>\n", "</a>\n", "\n", "\n", "# Full-text search with <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>ck\n", "\n", "\n", "[Full-text search](https://milvus.io/docs/full-text-search.md#Full-Text-Search) is a traditional method for retrieving documents by matching specific keywords or phrases in the text. It ranks results based on relevance scores calculated from factors like term frequency. While semantic search is better at understanding meaning and context, full-text search excels at precise keyword matching, making it a useful complement to semantic search. The BM25 algorithm is widely used for ranking in full-text search and plays a key role in Retrieval-Augmented Generation (RAG).\n", "\n", "[Milvus 2.5](https://milvus.io/blog/introduce-milvus-2-5-full-text-search-powerful-metadata-filtering-and-more.md) introduces native full-text search capabilities using BM25. This approach converts text into sparse vectors that represent BM25 scores. You can simply input raw text and Milvus will automatically generate and store the sparse vectors, with no manual sparse embedding generation required.\n", " \n", "[Haystack](https://haystack.deepset.ai/) now supports this Milvus feature, making it easy to add full-text search to RAG applications. You can combine full-text search with dense vector semantic search for a hybrid approach that benefits from both semantic understanding and keyword matching precision. This combination improves search accuracy and delivers better results to users.\n", " \n", "This tutorial demonstrates how to implement full-text and hybrid search in your application using Haystack and Milvus.\n", "\n", "To use the Milvus vector store, specify your Milvus server `URI` (and optionally with the `TOKEN`). To start a Milvus server, you can set up a Milvus server by following the [Milvus installation guide](https://milvus.io/docs/install-overview.md) or simply [trying Zilliz Cloud](https://docs.zilliz.com/docs/register-with-zilliz-cloud)(fully managed Milvus) for free.\n", "\n", "> - Full-text search is currently available in Milvus Standalone, Milvus Distributed, and Zilliz Cloud, though not yet supported in Milvus Lite (which has this feature planned for future implementation). <NAME_EMAIL> for more information.\n", "> - Before proceeding with this tutorial, ensure you have a basic understanding of [full-text search](https://milvus.io/docs/full-text-search.md#Full-Text-Search) and the [basic usage](https://github.com/milvus-io/milvus-haystack/blob/main/README.md) of Haystack Milvus integration.\n", "\n", "## Prerequisites\n", "\n", "Before running this notebook, make sure you have the following dependencies installed:"]}, {"cell_type": "code", "execution_count": 1, "id": "8ada8197-f103-414b-b2cc-6d8abe4f8d2e", "metadata": {}, "outputs": [], "source": ["! pip install --upgrade --quiet pymilvus milvus-haystack"]}, {"cell_type": "markdown", "id": "efba2444-363a-403c-821c-ef451200eed9", "metadata": {}, "source": ["> If you are using Google Colab, to enable dependencies just installed, you may need to **restart the runtime** (click on the \"Runtime\" menu at the top of the screen, and select \"Restart session\" from the dropdown menu).\n", "\n", "We will use the models from OpenAI. You should prepare the [api key](https://platform.openai.com/docs/quickstart) `OPENAI_API_KEY` as an environment variable."]}, {"cell_type": "code", "execution_count": 2, "id": "80c39ac3-077f-439c-a38e-9e79ce109579", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-***********\""]}, {"cell_type": "markdown", "id": "7fe662ae-5d9c-4177-82f1-3685f18787c9", "metadata": {}, "source": ["### Prepare the data\n", "\n", "Import the necessary packages in this notebook. Then prepare some sample documents."]}, {"cell_type": "code", "execution_count": 14, "id": "69b0b979-10c8-4aef-8692-73f27a539d9e", "metadata": {}, "outputs": [], "source": ["from haystack import Pipeline\n", "from haystack.components.embedders import OpenAIDocumentEmbedder, OpenAITextEmbedder\n", "from haystack.components.writers import DocumentWriter\n", "from haystack.utils import Secret\n", "from milvus_haystack import MilvusDocumentStore, MilvusSparseEmbeddingRetriever\n", "from haystack.document_stores.types import DuplicatePolicy\n", "from milvus_haystack.function import BM25BuiltInFunction\n", "from milvus_haystack import MilvusDocumentStore\n", "from milvus_haystack.milvus_embedding_retriever import MilvusHybridRetriever\n", "\n", "from haystack.utils import Secret\n", "from haystack.components.builders import PromptBuilder\n", "from haystack.components.generators import OpenAIGenerator\n", "from haystack import Document\n", "\n", "documents = [\n", "    Document(content=\"<PERSON> likes this apple\", meta={\"category\": \"fruit\"}),\n", "    Document(content=\"<PERSON> likes swimming\", meta={\"category\": \"sport\"}),\n", "    Document(content=\"<PERSON> likes white dogs\", meta={\"category\": \"pets\"}),\n", "]"]}, {"cell_type": "markdown", "id": "5b3ff37f", "metadata": {}, "source": ["Integrating full-text search into a RAG system balances semantic search with precise and predictable keyword-based retrieval. You can also choose to only use full text search though it's recommended to combine full text search with semantic search for better search results. Here for demonstration purpose we will show full text search alone and hybrid search."]}, {"cell_type": "markdown", "id": "5c68f424-3f37-49d6-ae8e-b7ae51e59bfd", "metadata": {}, "source": ["## BM25 search without embedding\n", "### Create the indexing Pipeline\n", "\n", "For full-text search Milvus MilvusDocumentStore accepts a `builtin_function` parameter. Through this parameter, you can pass in an instance of the `BM25BuiltInFunction`, which implements the BM25 algorithm on the Milvus server side. Set the `builtin_function` specified as the BM25 function instance. For example:"]}, {"cell_type": "code", "execution_count": 4, "id": "2d775ec4-44a5-4ad5-80f7-2922945a1a45", "metadata": {}, "outputs": [], "source": ["connection_args = {\"uri\": \"http://localhost:19530\"}\n", "# connection_args = {\"uri\": YOUR_ZILLIZ_CLOUD_URI, \"token\": Secret.from_env_var(\"ZILLIZ_CLOUD_API_KEY\")}\n", "\n", "document_store = MilvusDocumentStore(\n", "    connection_args=connection_args,\n", "    sparse_vector_field=\"sparse_vector\",  # The sparse vector field.\n", "    text_field=\"text\",\n", "    builtin_function=[\n", "        BM25BuiltInFunction(  # The BM25 function converts the text into a sparse vector.\n", "            input_field_names=\"text\",\n", "            output_field_names=\"sparse_vector\",\n", "        )\n", "    ],\n", "    consistency_level=\"Strong\",  # Supported values are (`\"Strong\"`, `\"Session\"`, `\"Bounded\"`, `\"Eventually\"`).\n", "    # drop_old=True,  # Drop the old collection if it exists and recreate it.\n", ")"]}, {"cell_type": "markdown", "id": "8ebc8cc2-a162-4c39-bad1-ce668f0d0216", "metadata": {}, "source": ["> For the connection_args:\n", "> - You can set up a more performant Milvus server on [docker or kubernetes](https://milvus.io/docs/quickstart.md). In this setup, please use the server address, e.g.`http://localhost:19530`, as your `uri`.\n", "> - If you want to use [Zilliz Cloud](https://zilliz.com/cloud), the fully managed cloud service for Milvus, adjust the `uri` and `token`, which correspond to the [Public Endpoint and Api key](https://docs.zilliz.com/docs/on-zilliz-cloud-console#free-cluster-details) in Zilliz Cloud.\n"]}, {"cell_type": "markdown", "id": "c7d70167", "metadata": {}, "source": ["\n", "Build an indexing pipeline to write documents into the Milvus document store."]}, {"cell_type": "code", "execution_count": 5, "id": "b5883297-6d54-4ee0-a9d5-fcaa188e562b", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'writer': {'documents_written': 3}}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["writer = DocumentWriter(document_store=document_store, policy=DuplicatePolicy.NONE)\n", "\n", "indexing_pipeline = Pipeline()\n", "indexing_pipeline.add_component(\"writer\", writer)\n", "indexing_pipeline.run({\"writer\": {\"documents\": documents}})"]}, {"cell_type": "markdown", "id": "73928bc0-1885-4d45-9509-1e81a53d650f", "metadata": {}, "source": ["### Create the retrieval pipeline\n", "Create a retrieval pipeline that retrieves documents from the Milvus document store using `MilvusSparseEmbeddingRetriever`, which is a wrapper around `document_store`."]}, {"cell_type": "code", "execution_count": 6, "id": "fc632c8e-9f32-4ffd-8685-16aebcec2a98", "metadata": {}, "outputs": [{"data": {"text/plain": ["Document(id=bd334348dd2087c785e99b5a0009f33d9b8b8198736f6415df5d92602d81fd3e, content: '<PERSON> likes swimming', meta: {'category': 'sport'}, score: 1.2039074897766113)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["retrieval_pipeline = Pipeline()\n", "retrieval_pipeline.add_component(\n", "    \"retriever\", MilvusSparseEmbeddingRetriever(document_store=document_store)\n", ")\n", "\n", "question = \"Who likes swimming?\"\n", "\n", "retrieval_results = retrieval_pipeline.run({\"retriever\": {\"query_text\": question}})\n", "\n", "retrieval_results[\"retriever\"][\"documents\"][0]"]}, {"cell_type": "markdown", "id": "1f09726a-a81e-4d00-84ba-5a8f196be76f", "metadata": {}, "source": ["## Hybrid Search with semantic search and full-text search\n", "\n", "### Create the indexing Pipeline\n", "\n", "In the hybrid search, we use the BM25 function to perform full-text search, and specify the dense vector field `vector`, to perform semantic search."]}, {"cell_type": "code", "execution_count": 7, "id": "8ba26455-3d67-4b9e-a95e-3ca0e2e6c528", "metadata": {}, "outputs": [], "source": ["document_store = MilvusDocumentStore(\n", "    connection_args=connection_args,\n", "    vector_field=\"vector\",  # The dense vector field.\n", "    sparse_vector_field=\"sparse_vector\",  # The sparse vector field.\n", "    text_field=\"text\",\n", "    builtin_function=[\n", "        BM25BuiltInFunction(  # The BM25 function converts the text into a sparse vector.\n", "            input_field_names=\"text\",\n", "            output_field_names=\"sparse_vector\",\n", "        )\n", "    ],\n", "    consistency_level=\"Strong\",  # Supported values are (`\"Strong\"`, `\"Session\"`, `\"Bounded\"`, `\"Eventually\"`).\n", "    # drop_old=True,  # Drop the old collection and recreate it.\n", ")"]}, {"cell_type": "markdown", "id": "fd33a33b-0c81-49a4-ac70-1e9c01206adc", "metadata": {}, "source": ["Create an indexing pipeline that converts the documents into embeddings. The documents are then written to the Milvus document store."]}, {"cell_type": "code", "execution_count": 8, "id": "64eec3ad-1c08-4cf3-8ca8-2796949875af", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Calculating embeddings: 100%|██████████| 1/1 [00:01<00:00,  1.15s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of documents: 3\n"]}], "source": ["writer = DocumentWriter(document_store=document_store, policy=DuplicatePolicy.NONE)\n", "\n", "indexing_pipeline = Pipeline()\n", "indexing_pipeline.add_component(\"dense_doc_embedder\", OpenAIDocumentEmbedder())\n", "indexing_pipeline.add_component(\"writer\", writer)\n", "indexing_pipeline.connect(\"dense_doc_embedder\", \"writer\")\n", "indexing_pipeline.run({\"dense_doc_embedder\": {\"documents\": documents}})\n", "\n", "print(\"Number of documents:\", document_store.count_documents())"]}, {"cell_type": "markdown", "id": "45d0dd9c-a9a0-439b-98c8-956c8bbc4946", "metadata": {}, "source": ["### Create the retrieval pipeline\n", "\n", "Create a retrieval pipeline that retrieves documents from the Milvus document store using `MilvusHybridRetriever`, which contains the `document_store` and receives parameters about hybrid search."]}, {"cell_type": "code", "execution_count": 9, "id": "583b4452-fbb9-45c1-9d78-ddbf97926e47", "metadata": {}, "outputs": [{"data": {"text/plain": ["<haystack.core.pipeline.pipeline.Pipeline object at 0x3383ad990>\n", "🚅 Components\n", "  - dense_text_embedder: OpenAITextEmbedder\n", "  - retriever: <PERSON><PERSON><PERSON>sHybridRetriever\n", "🛤️ Connections\n", "  - dense_text_embedder.embedding -> retriever.query_embedding (List[float])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# from pymilvus import WeightedRanker\n", "retrieval_pipeline = Pipeline()\n", "retrieval_pipeline.add_component(\"dense_text_embedder\", OpenAITextEmbedder())\n", "retrieval_pipeline.add_component(\n", "    \"retriever\",\n", "    MilvusHybridRetriever(\n", "        document_store=document_store,\n", "        # top_k=3,\n", "        # reranker=WeightedRanker(0.5, 0.5),  # Default is RRFRanker()\n", "    ),\n", ")\n", "\n", "retrieval_pipeline.connect(\"dense_text_embedder.embedding\", \"retriever.query_embedding\")"]}, {"cell_type": "markdown", "id": "df11cd55-acee-4a51-abb6-b6d2ed7bdd3d", "metadata": {}, "source": ["When performing hybrid search using `MilvusHybridRetriever`, we can optionally set the topK and reranker parameters. It will automatically handle the vector embeddings and built-in functions and finally use a reranker to refine the results. The underlying implementation details of the searching process are hidden from the user.\n", "\n", "For more information about hybrid search, you can refer to the [Hybrid Search introduction](https://milvus.io/docs/multi-vector-search.md#Hybrid-Search)."]}, {"cell_type": "code", "execution_count": 10, "id": "0f2adaa5-c6a2-4621-9a18-054e93dfe43a", "metadata": {}, "outputs": [{"data": {"text/plain": ["Document(id=bd334348dd2087c785e99b5a0009f33d9b8b8198736f6415df5d92602d81fd3e, content: '<PERSON> likes swimming', meta: {'category': 'sport'}, score: 0.032786883413791656, embedding: vector of size 1536)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["question = \"Who likes swimming?\"\n", "\n", "retrieval_results = retrieval_pipeline.run(\n", "    {\n", "        \"dense_text_embedder\": {\"text\": question},\n", "        \"retriever\": {\"query_text\": question},\n", "    }\n", ")\n", "\n", "retrieval_results[\"retriever\"][\"documents\"][0]"]}, {"cell_type": "markdown", "id": "1dd0146d-51f1-4818-ab1e-d4a7ee3e6c72", "metadata": {}, "source": ["## Customize analyzer\n", "\n", "Analyzers are essential in full-text search by breaking the sentence into tokens and performing lexical analysis like stemming and stop word removal. Analyzers are usually language-specific. You can refer to [this guide](https://milvus.io/docs/analyzer-overview.md#Analyzer-Overview) to learn more about analyzers in Milvus.\n", "\n", "Milvus supports two types of analyzers: **Built-in Analyzers** and **Custom Analyzers**. By default, the `BM25BuiltInFunction` will use the [standard built-in analyzer](https://milvus.io/docs/standard-analyzer.md), which is the most basic analyzer that tokenizes the text with punctuation. \n", "\n", "If you want to use a different analyzer or customize the analyzer, you can pass in the `analyzer_params` parameter in the `BM25BuiltInFunction` initialization."]}, {"cell_type": "code", "execution_count": 11, "id": "f6db9eee-1279-4943-ade4-e2b925a0f6df", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Calculating embeddings: 100%|██████████| 1/1 [00:00<00:00,  1.39it/s]\n"]}, {"data": {"text/plain": ["{'dense_doc_embedder': {'meta': {'model': 'text-embedding-ada-002-v2',\n", "   'usage': {'prompt_tokens': 11, 'total_tokens': 11}}},\n", " 'writer': {'documents_written': 3}}"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["analyzer_params_custom = {\n", "    \"tokenizer\": \"standard\",\n", "    \"filter\": [\n", "        \"lowercase\",  # Built-in filter\n", "        {\"type\": \"length\", \"max\": 40},  # Custom filter\n", "        {\"type\": \"stop\", \"stop_words\": [\"of\", \"to\"]},  # Custom filter\n", "    ],\n", "}\n", "\n", "document_store = MilvusDocumentStore(\n", "    connection_args=connection_args,\n", "    vector_field=\"vector\",\n", "    sparse_vector_field=\"sparse_vector\",\n", "    text_field=\"text\",\n", "    builtin_function=[\n", "        BM25BuiltInFunction(\n", "            input_field_names=\"text\",\n", "            output_field_names=\"sparse_vector\",\n", "            analyzer_params=analyzer_params_custom,  # Custom analyzer parameters.\n", "            enable_match=True,  # Whether to enable match.\n", "        )\n", "    ],\n", "    consistency_level=\"Strong\",\n", "    # drop_old=True,\n", ")\n", "\n", "# write documents to the document store\n", "writer = DocumentWriter(document_store=document_store, policy=DuplicatePolicy.NONE)\n", "indexing_pipeline = Pipeline()\n", "indexing_pipeline.add_component(\"dense_doc_embedder\", OpenAIDocumentEmbedder())\n", "indexing_pipeline.add_component(\"writer\", writer)\n", "indexing_pipeline.connect(\"dense_doc_embedder\", \"writer\")\n", "indexing_pipeline.run({\"dense_doc_embedder\": {\"documents\": documents}})"]}, {"cell_type": "markdown", "id": "fbfbefa8-26eb-4624-a527-e4007c230d48", "metadata": {}, "source": ["We can take a look at the schema of the Milvus collection and make sure the customized analyzer is set up correctly."]}, {"cell_type": "code", "execution_count": 12, "id": "52d6ed3e-9dff-4691-9685-3d599c4b0864", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'auto_id': False, 'description': '', 'fields': [{'name': 'text', 'description': '', 'type': <DataType.VARCHAR: 21>, 'params': {'max_length': 65535, 'enable_match': True, 'enable_analyzer': True, 'analyzer_params': {'tokenizer': 'standard', 'filter': ['lowercase', {'type': 'length', 'max': 40}, {'type': 'stop', 'stop_words': ['of', 'to']}]}}}, {'name': 'id', 'description': '', 'type': <DataType.VARCHAR: 21>, 'params': {'max_length': 65535}, 'is_primary': True, 'auto_id': False}, {'name': 'vector', 'description': '', 'type': <DataType.FLOAT_VECTOR: 101>, 'params': {'dim': 1536}}, {'name': 'sparse_vector', 'description': '', 'type': <DataType.SPARSE_FLOAT_VECTOR: 104>, 'is_function_output': True}], 'enable_dynamic_field': True, 'functions': [{'name': 'bm25_function_7b6e15a4', 'description': '', 'type': <FunctionType.BM25: 1>, 'input_field_names': ['text'], 'output_field_names': ['sparse_vector'], 'params': {}}]}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["document_store.col.schema"]}, {"cell_type": "markdown", "id": "64cfee06-3bd3-417a-ab2a-ba6995de6317", "metadata": {}, "source": ["For more concept details, e.g., `analyzer`, `tokenizer`, `filter`, `enable_match`, `analyzer_params`, please refer to the [analyzer documentation](https://milvus.io/docs/analyzer-overview.md)."]}, {"cell_type": "markdown", "id": "02045007", "metadata": {}, "source": ["## Using Hybrid Search in RAG pipeline\n", "We have learned how to use the basic BM25 build-in function in Haystack and Milvus and prepared a loaded `document_store`. Let's introduce an optimized RAG implementation with hybrid search.\n", "\n", "\n", "![](../../pics/advanced_rag/hybrid_and_rerank.png)\n", "\n", "This diagram shows the Hybrid Retrieve & Reranking process, combining BM25 for keyword matching and dense vector search for semantic retrieval. Results from both methods are merged, reranked, and passed to an LLM to generate the final answer.\n", "\n", "Hybrid search balances precision and semantic understanding, improving accuracy and robustness for diverse queries. It retrieves candidates with BM25 full-text search and vector search, ensuring both semantic, context-aware, and accurate retrieval.\n", "\n", "Let's try an optimized RAG implementation with hybrid search.\n"]}, {"cell_type": "code", "execution_count": 13, "id": "6385fc0a-3871-4bc0-b267-a305073ccb4f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RAG answer: <PERSON> likes swimming.\n"]}], "source": ["prompt_template = \"\"\"Answer the following query based on the provided context. If the context does\n", "                     not include an answer, reply with 'I don't know'.\\n\n", "                     Query: {{query}}\n", "                     Documents:\n", "                     {% for doc in documents %}\n", "                        {{ doc.content }}\n", "                     {% endfor %}\n", "                     Answer:\n", "                  \"\"\"\n", "\n", "rag_pipeline = Pipeline()\n", "rag_pipeline.add_component(\"text_embedder\", OpenAITextEmbedder())\n", "rag_pipeline.add_component(\n", "    \"retriever\", MilvusHybridRetriever(document_store=document_store, top_k=1)\n", ")\n", "rag_pipeline.add_component(\"prompt_builder\", PromptBuilder(template=prompt_template))\n", "rag_pipeline.add_component(\n", "    \"generator\",\n", "    OpenAIGenerator(\n", "        api_key=Secret.from_token(os.getenv(\"OPENAI_API_KEY\")),\n", "        generation_kwargs={\"temperature\": 0},\n", "    ),\n", ")\n", "rag_pipeline.connect(\"text_embedder.embedding\", \"retriever.query_embedding\")\n", "rag_pipeline.connect(\"retriever.documents\", \"prompt_builder.documents\")\n", "rag_pipeline.connect(\"prompt_builder\", \"generator\")\n", "\n", "results = rag_pipeline.run(\n", "    {\n", "        \"text_embedder\": {\"text\": question},\n", "        \"retriever\": {\"query_text\": question},\n", "        \"prompt_builder\": {\"query\": question},\n", "    }\n", ")\n", "print(\"RAG answer:\", results[\"generator\"][\"replies\"][0])"]}, {"cell_type": "markdown", "id": "14d1d8ef-0dce-4d91-bdd3-0c3695a91ac9", "metadata": {}, "source": ["For more information about how to use milvus-haystack, please refer to the [milvus-haystack offical repository](https://github.com/milvus-io/milvus-haystack)."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}