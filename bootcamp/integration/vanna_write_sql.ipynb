{"cells": [{"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/integration/vanna_write_sql.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/integration/vanna_write_sql.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>\n", "</a>"]}, {"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["# Write SQL with <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>\n", "\n", "[<PERSON><PERSON>](https://vanna.ai/) is an open-source Python RAG (Retrieval-Augmented Generation) framework for SQL generation and related functionality. [<PERSON>lvus](https://milvus.io/) is the world's most advanced open-source vector database, built to power embedding similarity search and AI applications.\n", "\n", "<PERSON><PERSON> works in two easy steps - train a RAG \"model\" on your data, and then ask questions which will return SQL queries that can be set up to run on your database. This guide demonstrates how to use <PERSON><PERSON> to generate and execute SQL queries based on your data stored in a database.\n", "\n"]}, {"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Prerequisites\n", "\n", "Before running this notebook, make sure you have the following dependencies installed:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["! pip install \"vanna[milvus,openai]\""]}, {"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["> If you are using Google Colab, to enable dependencies just installed, you may need to **restart the runtime** (click on the \"Runtime\" menu at the top of the screen, and select \"Restart session\" from the dropdown menu)."]}, {"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["And you need set the `OPENAI_API_KEY` in your environment variables. You can get the API key from [OpenAI](https://platform.openai.com/docs/guides/production-best-practices/api-keys)."]}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-***********\""], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Data preparation\n", "\n", "First, we need to inherit from the `Milvus_VectorStore` and `OpenAI_Chat` classes from Vanna and define a new class `VannaMilvus` that combines capabilities from both."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["from pymilvus import MilvusClient, model\n", "from vanna.milvus import Milvus_VectorStore\n", "from vanna.openai import OpenAI_Chat\n", "\n", "\n", "class VannaMilvus(Milvus_VectorStore, OpenAI_Chat):\n", "    def __init__(self, config=None):\n", "        Milvus_VectorStore.__init__(self, config=config)\n", "        OpenAI_Chat.__init__(self, config=config)"]}, {"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["We initialize the `VannaMilvus` class with the necessary configuration parameters. We use a `milvus_client` instance to store embeddings and the `model.DefaultEmbeddingFunction()` initialized from [milvus_model](https://milvus.io/docs/embeddings.md) to generate embeddings.\n", "\n", "> As for the argument of `MilvusClient`:\n", "> - Setting the `uri` as a local file, e.g.`./milvus.db`, is the most convenient method, as it automatically utilizes [Milvus Lite](https://milvus.io/docs/milvus_lite.md) to store all data in this file.\n", "> - If you have large scale of data, you can set up a more performant Milvus server on [docker or kubernetes](https://milvus.io/docs/quickstart.md). In this setup, please use the server uri, e.g.`http://localhost:19530`, as your `uri`.\n", "> - If you want to use [Zilliz Cloud](https://zilliz.com/cloud), the fully managed cloud service for Milvus, adjust the `uri` and `token`, which correspond to the [Public Endpoint and Api key](https://docs.zilliz.com/docs/on-zilliz-cloud-console#free-cluster-details) in Zilliz Cloud."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["milvus_uri = \"./milvus_vanna.db\"\n", "\n", "milvus_client = MilvusClient(uri=milvus_uri)\n", "\n", "vn_milvus = VannaMilvus(\n", "    config={\n", "        \"api_key\": os.getenv(\"OPENAI_API_KEY\"),\n", "        \"model\": \"gpt-3.5-turbo\",\n", "        \"milvus_client\": milvus_client,\n", "        \"embedding_function\": model.DefaultEmbeddingFunction(),\n", "        \"n_results\": 2,  # The number of results to return from Milvus semantic search.\n", "    }\n", ")"]}, {"cell_type": "markdown", "source": ["This is a simple example with only a few sample of data, so we set `n_results` to 2 to make sure we search for the top 2 most similar results.\n", "In practice, you should set `n_results` to a higher value when dealing with larger training dataset."], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["We will use a sample SQLite database with few tables containing some sample data."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import sqlite3\n", "\n", "sqlite_path = \"./my-database.sqlite\"\n", "sql_connect = sqlite3.connect(sqlite_path)\n", "c = sql_connect.cursor()\n", "\n", "init_sqls = \"\"\"\n", "CREATE TABLE IF NOT EXISTS Customer (\n", "    ID INTEGER PRIMARY KEY AUTOINCREMENT,\n", "    Name TEXT NOT NULL,\n", "    Company TEXT NOT NULL,\n", "    City TEXT NOT NULL,\n", "    Phone TEXT NOT NULL\n", ");\n", "\n", "CREATE TABLE IF NOT EXISTS Company (\n", "    ID INTEGER PRIMARY KEY AUTOINCREMENT,\n", "    Name TEXT NOT NULL,\n", "    Industry TEXT NOT NULL,\n", "    Location TEXT NOT NULL,\n", "    EmployeeCount INTEGER NOT NULL\n", ");\n", "\n", "CREATE TABLE IF NOT EXISTS User (\n", "    ID INTEGER PRIMARY KEY AUTOINCREMENT,\n", "    Username TEXT NOT NULL UNIQUE,\n", "    Email TEXT NOT NULL UNIQUE\n", ");\n", "\n", "INSERT INTO Customer (Name, Company, City, Phone) \n", "VALUES ('<PERSON>', 'ABC Corp', 'New York', '************');\n", "\n", "INSERT INTO Customer (Name, Company, City, Phone) \n", "VALUES ('<PERSON>', 'XYZ Inc', 'Los Angeles', '************');\n", "\n", "INSERT INTO Company (Name, Industry, Location, EmployeeCount)\n", "VALUES ('ABC Corp', 'cutting-edge technology', 'New York', 100);\n", "\n", "INSERT INTO User (Username, Email)\n", "VALUES ('johndoe123', '<EMAIL>');\n", "\"\"\"\n", "\n", "for sql in init_sqls.split(\";\"):\n", "    c.execute(sql)\n", "\n", "sql_connect.commit()\n", "\n", "# Connect to the SQLite database\n", "vn_milvus.connect_to_sqlite(sqlite_path)"]}, {"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Train with data\n", "We can train the model on the DDL data of the SQLite database. We get the DDL data and feed it to the `train` function."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}, "pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Adding ddl: CREATE TABLE Customer (\n", "    ID INTEGER PRIMARY KEY AUTOINCREMENT,\n", "    Name TEXT NOT NULL,\n", "    Company TEXT NOT NULL,\n", "    City TEXT NOT NULL,\n", "    Phone TEXT NOT NULL\n", ")\n", "Adding ddl: CREATE TABLE sqlite_sequence(name,seq)\n", "Adding ddl: CREATE TABLE Company (\n", "    ID INTEGER PRIMARY KEY AUTOINCREMENT,\n", "    Name TEXT NOT NULL,\n", "    Industry TEXT NOT NULL,\n", "    Location TEXT NOT NULL,\n", "    EmployeeCount INTEGER NOT NULL\n", ")\n", "Adding ddl: CREATE TABLE User (\n", "    ID INTEGER PRIMARY KEY AUTOINCREMENT,\n", "    Username TEXT NOT NULL UNIQUE,\n", "    Email TEXT NOT NULL UNIQUE\n", ")\n"]}], "source": ["# If there exists training data, we should remove it before training.\n", "existing_training_data = vn_milvus.get_training_data()\n", "if len(existing_training_data) > 0:\n", "    for _, training_data in existing_training_data.iterrows():\n", "        vn_milvus.remove_training_data(training_data[\"id\"])\n", "\n", "# Get the DDL of the SQLite database\n", "df_ddl = vn_milvus.run_sql(\"SELECT type, sql FROM sqlite_master WHERE sql is not null\")\n", "\n", "# Train the model on the DDL data\n", "for ddl in df_ddl[\"sql\"].to_list():\n", "    vn_milvus.train(ddl=ddl)"]}, {"cell_type": "markdown", "source": ["Besides training on the DDL data, we can also train on the documentation and SQL queries of the database."], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Adding documentation....\n", "Adding documentation....\n", "Using model gpt-3.5-turbo for 65.0 tokens (approx)\n", "Question generated with sql: What are the details of the customer named <PERSON>? \n", "Adding SQL...\n"]}, {"data": {"text/plain": ["'595b185c-e6ad-47b0-98fd-0e93ef9b6a0a-sql'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Add documentation about your business terminology or definitions.\n", "vn_milvus.train(\n", "    documentation=\"ABC Corp specializes in cutting-edge technology solutions and innovation.\"\n", ")\n", "vn_milvus.train(\n", "    documentation=\"XYZ Inc is a global leader in manufacturing and supply chain management.\"\n", ")\n", "\n", "# You can also add SQL queries to your training data.\n", "vn_milvus.train(sql=\"SELECT * FROM Customer WHERE Name = '<PERSON>'\")"]}, {"cell_type": "markdown", "source": ["Let's take a look at the training data."], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>question</th>\n", "      <th>content</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>595b185c-e6ad-47b0-98fd-0e93ef9b6a0a-sql</td>\n", "      <td>What are the details of the customer named <PERSON><PERSON>...</td>\n", "      <td>SELECT * FROM Customer WHERE Name = '<PERSON>'</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>25f4956c-e370-4097-994f-996f22d145fa-ddl</td>\n", "      <td>None</td>\n", "      <td>CREATE TABLE Company (\\n    ID INTEGER PRIMARY...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>b95ecc66-f65b-49dc-a9f1-c1842ad230ff-ddl</td>\n", "      <td>None</td>\n", "      <td>CREATE TABLE Customer (\\n    ID INTEGER PRIMAR...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>fcc73d15-30a5-4421-9d73-b8c3b0ed5305-ddl</td>\n", "      <td>None</td>\n", "      <td>CREATE TABLE sqlite_sequence(name,seq)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>feae618c-5910-4f6f-8b4b-6cc3e03aec06-ddl</td>\n", "      <td>None</td>\n", "      <td>CREATE TABLE User (\\n    ID INTEGER PRIMARY KE...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>79a48db1-ba1f-4fd5-be99-74f2ca2eaeeb-doc</td>\n", "      <td>None</td>\n", "      <td>XYZ Inc is a global leader in manufacturing an...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>9f9df1b8-ae62-4823-ad28-d7e0f2d1f4c0-doc</td>\n", "      <td>None</td>\n", "      <td>ABC Corp specializes in cutting-edge technolog...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                         id  \\\n", "0  595b185c-e6ad-47b0-98fd-0e93ef9b6a0a-sql   \n", "0  25f4956c-e370-4097-994f-996f22d145fa-ddl   \n", "1  b95ecc66-f65b-49dc-a9f1-c1842ad230ff-ddl   \n", "2  fcc73d15-30a5-4421-9d73-b8c3b0ed5305-ddl   \n", "3  feae618c-5910-4f6f-8b4b-6cc3e03aec06-ddl   \n", "0  79a48db1-ba1f-4fd5-be99-74f2ca2eaeeb-doc   \n", "1  9f9df1b8-ae62-4823-ad28-d7e0f2d1f4c0-doc   \n", "\n", "                                            question  \\\n", "0  What are the details of the customer named <PERSON><PERSON>...   \n", "0                                               None   \n", "1                                               None   \n", "2                                               None   \n", "3                                               None   \n", "0                                               None   \n", "1                                               None   \n", "\n", "                                             content  \n", "0     SELECT * FROM Customer WHERE Name = '<PERSON>'  \n", "0  CREATE TABLE Company (\\n    ID INTEGER PRIMARY...  \n", "1  CREATE TABLE Customer (\\n    ID INTEGER PRIMAR...  \n", "2             CREATE TABLE sqlite_sequence(name,seq)  \n", "3  CREATE TABLE User (\\n    ID INTEGER PRIMARY KE...  \n", "0  XYZ Inc is a global leader in manufacturing an...  \n", "1  ABC Corp specializes in cutting-edge technolog...  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["training_data = vn_milvus.get_training_data()\n", "training_data"]}, {"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Generate SQLs and execute them\n", "As we have trained with the DDL data, the table structure is now available for generating SQL queries.\n", "\n", "Let's try a simple question."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SQL Prompt: [{'role': 'system', 'content': \"You are a SQLite expert. Please help to generate a SQL query to answer the question. Your response should ONLY be based on the given context and follow the response guidelines and format instructions. \\n===Tables \\nCREATE TABLE Customer (\\n    ID INTEGER PRIMARY KEY AUTOINCREMENT,\\n    Name TEXT NOT NULL,\\n    Company TEXT NOT NULL,\\n    City TEXT NOT NULL,\\n    Phone TEXT NOT NULL\\n)\\n\\nCREATE TABLE User (\\n    ID INTEGER PRIMARY KEY AUTOINCREMENT,\\n    Username TEXT NOT NULL UNIQUE,\\n    Email TEXT NOT NULL UNIQUE\\n)\\n\\n\\n===Additional Context \\n\\nABC Corp specializes in cutting-edge technology solutions and innovation.\\n\\nXYZ Inc is a global leader in manufacturing and supply chain management.\\n\\n===Response Guidelines \\n1. If the provided context is sufficient, please generate a valid SQL query without any explanations for the question. \\n2. If the provided context is almost sufficient but requires knowledge of a specific string in a particular column, please generate an intermediate SQL query to find the distinct strings in that column. Prepend the query with a comment saying intermediate_sql \\n3. If the provided context is insufficient, please explain why it can't be generated. \\n4. Please use the most relevant table(s). \\n5. If the question has been asked and answered before, please repeat the answer exactly as it was given before. \\n\"}, {'role': 'user', 'content': 'What are the details of the customer named John Doe?'}, {'role': 'assistant', 'content': \"SELECT * FROM Customer WHERE Name = 'John Doe'\"}, {'role': 'user', 'content': 'what is the phone number of John Doe?'}]\n", "Using model gpt-3.5-turbo for 367.25 tokens (approx)\n", "LLM Response: SELECT Phone FROM Customer WHERE Name = '<PERSON>'\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Phone</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>************</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          Phone\n", "0  ************"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["sql = vn_milvus.generate_sql(\"what is the phone number of <PERSON>?\")\n", "vn_milvus.run_sql(sql)"]}, {"cell_type": "markdown", "source": ["Here is a more complex question. The manufacturing corporation name information is in the document data, which is background information. The generated SQL query will retrieve the customer information based on the specific manufacturing corporation name."], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SQL Prompt: [{'role': 'system', 'content': \"You are a SQLite expert. Please help to generate a SQL query to answer the question. Your response should ONLY be based on the given context and follow the response guidelines and format instructions. \\n===Tables \\nCREATE TABLE Company (\\n    ID INTEGER PRIMARY KEY AUTOINCREMENT,\\n    Name TEXT NOT NULL,\\n    Industry TEXT NOT NULL,\\n    Location TEXT NOT NULL,\\n    EmployeeCount INTEGER NOT NULL\\n)\\n\\nCREATE TABLE Customer (\\n    ID INTEGER PRIMARY KEY AUTOINCREMENT,\\n    Name TEXT NOT NULL,\\n    Company TEXT NOT NULL,\\n    City TEXT NOT NULL,\\n    Phone TEXT NOT NULL\\n)\\n\\n\\n===Additional Context \\n\\nXYZ Inc is a global leader in manufacturing and supply chain management.\\n\\nABC Corp specializes in cutting-edge technology solutions and innovation.\\n\\n===Response Guidelines \\n1. If the provided context is sufficient, please generate a valid SQL query without any explanations for the question. \\n2. If the provided context is almost sufficient but requires knowledge of a specific string in a particular column, please generate an intermediate SQL query to find the distinct strings in that column. Prepend the query with a comment saying intermediate_sql \\n3. If the provided context is insufficient, please explain why it can't be generated. \\n4. Please use the most relevant table(s). \\n5. If the question has been asked and answered before, please repeat the answer exactly as it was given before. \\n\"}, {'role': 'user', 'content': 'What are the details of the customer named John Doe?'}, {'role': 'assistant', 'content': \"SELECT * FROM Customer WHERE Name = 'John Doe'\"}, {'role': 'user', 'content': 'which customer works for a manufacturing corporation?'}]\n", "Using model gpt-3.5-turbo for 384.25 tokens (approx)\n", "LLM Response: SELECT * \n", "FROM Customer \n", "WHERE Company = 'XYZ Inc'\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th>Name</th>\n", "      <th>Company</th>\n", "      <th>City</th>\n", "      <th>Phone</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2</td>\n", "      <td><PERSON></td>\n", "      <td>XYZ Inc</td>\n", "      <td>Los Angeles</td>\n", "      <td>************</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   ID        Name  Company         City         Phone\n", "0   2  <PERSON>Z Inc  Los Angeles  ************"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["sql = vn_milvus.generate_sql(\"which customer works for a manufacturing corporation?\")\n", "vn_milvus.run_sql(sql)"]}, {"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["Disconnect from the SQLite and Milvus and remove them to free up resources."]}, {"cell_type": "code", "execution_count": 10, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["sql_connect.close()\n", "milvus_client.close()\n", "\n", "os.remove(sqlite_path)\n", "if os.path.exists(milvus_uri):\n", "    os.remove(milvus_uri)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}}, "nbformat": 4, "nbformat_minor": 4}