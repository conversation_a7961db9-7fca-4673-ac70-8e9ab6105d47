{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<a href=\"https://colab.research.google.com/github/milvus-io/bootcamp/blob/master/integration/mistral_ocr_with_milvus.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>   <a href=\"https://github.com/milvus-io/bootcamp/blob/master/integration/mistral_ocr_with_milvus.ipynb\" target=\"_blank\">\n", "    <img src=\"https://img.shields.io/badge/View%20on%20GitHub-555555?style=flat&logo=github&logoColor=white\" alt=\"GitHub Repository\"/>\n", "</a>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Document Understanding with Mistral OCR and Milvus\n", "\n", "This tutorial demonstrates how to build a document understanding system using:\n", "\n", "## Mistral OCR\n", "A powerful optical character recognition service that:\n", "* Processes PDFs, images, and other document formats\n", "* Preserves document structure and formatting\n", "* Handles multi-page documents\n", "* Recognizes tables, lists, and other complex elements\n", "\n", "## Mistral Embeddings\n", "* Transforms text into numerical representations:\n", "* Converts text into 1024-dimensional vectors\n", "* Captures semantic relationships between concepts\n", "* Enables similarity matching based on meaning\n", "* Provides foundation for semantic search\n", "\n", "## Milvus Vector Database\n", "Specialized database for vector similarity search:\n", "* Open-Source\n", "* Performs efficient Vector Search\n", "* Scales to large document collections\n", "* Supports hybrid search (vector similarity + metadata filtering)\n", "* Optimized for AI applications\n", "\n", "By the end of this tutorial, you'll have a system that can:\n", "1. Process documents (PDFs/images) via URLs\n", "2. Extract text using OCR\n", "3. Store the text and vector embeddings in Milvus\n", "4. Perform semantic search across your document collection\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Dependencies\n", "\n", "First, let's install the required packages:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: mistralai in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (1.5.1)\n", "Requirement already satisfied: pymilvus in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (2.5.3)\n", "Requirement already satisfied: python-dotenv in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (1.0.1)\n", "Requirement already satisfied: eval-type-backport>=0.2.0 in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from mistralai) (0.2.2)\n", "Requirement already satisfied: httpx>=0.27.0 in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from mistralai) (0.28.1)\n", "Requirement already satisfied: jsonpath-python>=1.0.6 in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from mistralai) (1.0.6)\n", "Requirement already satisfied: pydantic>=2.9.0 in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from mistralai) (2.10.4)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from mistralai) (2.9.0.post0)\n", "Requirement already satisfied: typing-inspect>=0.9.0 in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from mistralai) (0.9.0)\n", "Requirement already satisfied: setuptools>69 in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from pymilvus) (75.6.0)\n", "Requirement already satisfied: grpcio<=1.67.1,>=1.49.1 in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from pymilvus) (1.67.1)\n", "Requirement already satisfied: protobuf>=3.20.0 in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from pymilvus) (5.29.2)\n", "Requirement already satisfied: ujson>=2.0.0 in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from pymilvus) (5.10.0)\n", "Requirement already satisfied: pandas>=1.2.4 in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from pymilvus) (2.2.3)\n", "Requirement already satisfied: milvus-lite>=2.4.0 in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from pymilvus) (2.4.11)\n", "Requirement already satisfied: anyio in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from httpx>=0.27.0->mist<PERSON><PERSON>) (4.7.0)\n", "Requirement already satisfied: certifi in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from httpx>=0.27.0->mistralai) (2024.2.2)\n", "Requirement already satisfied: httpcore==1.* in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from httpx>=0.27.0->mistralai) (1.0.7)\n", "Requirement already satisfied: idna in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from httpx>=0.27.0->mist<PERSON><PERSON>) (3.6)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from httpcore==1.*->httpx>=0.27.0->mist<PERSON>ai) (0.14.0)\n", "Requirement already satisfied: tqdm in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from milvus-lite>=2.4.0->pymilvus) (4.67.1)\n", "Requirement already satisfied: numpy>=1.26.0 in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from pandas>=1.2.4->pymilvus) (2.2.1)\n", "Requirement already satisfied: pytz>=2020.1 in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from pandas>=1.2.4->pymilvus) (2024.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from pandas>=1.2.4->pymilvus) (2024.2)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from pydantic>=2.9.0->mist<PERSON>ai) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from pydantic>=2.9.0->mist<PERSON><PERSON>) (2.27.2)\n", "Requirement already satisfied: typing-extensions>=4.12.2 in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from pydantic>=2.9.0->mist<PERSON><PERSON>) (4.12.2)\n", "Requirement already satisfied: six>=1.5 in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from python-dateutil>=2.8.2->mist<PERSON>ai) (1.17.0)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from typing-inspect>=0.9.0->mist<PERSON><PERSON>) (1.0.0)\n", "Requirement already satisfied: sniffio>=1.1 in /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages (from anyio->httpx>=0.27.0->mist<PERSON>ai) (1.3.1)\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m24.0\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["!pip install mistralai pymilvus python-dotenv"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Environment Setup\n", "\n", "You'll need:\n", "1. A Mistral API key (get one at https://console.mistral.ai/)\n", "2. Milvus running locally through [Docker](https://milvus.io/docs/install_standalone-docker.md) or with [Zilliz Cloud](https://zilliz.com) \n", "\n", "Let's set up our environment:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Connected to Mistral API and Milvus at http://localhost:19530\n"]}], "source": ["import json\n", "import os\n", "import re\n", "\n", "from dotenv import load_dotenv\n", "from mistralai import Mistral\n", "from pymilvus import CollectionSchema, DataType, FieldSchema, MilvusClient\n", "from pymilvus.client.types import LoadState\n", "\n", "# Load environment variables from .env file\n", "load_dotenv()\n", "\n", "# Initialize clients\n", "api_key = os.getenv(\"MISTRAL_API_KEY\")\n", "if not api_key:\n", "    api_key = input(\"Enter your Mistral API key: \")\n", "    os.environ[\"MISTRAL_API_KEY\"] = api_key\n", "\n", "client = Mistral(api_key=api_key)\n", "\n", "# Define models\n", "text_model = \"mistral-small-latest\"  # For chat interactions\n", "ocr_model = \"mistral-ocr-latest\"  # For OCR processing\n", "embedding_model = \"mistral-embed\"  # For generating embeddings\n", "\n", "# Connect to Milvus (default: localhost)\n", "milvus_uri = os.getenv(\"MILVUS_URI\", \"http://localhost:19530\")\n", "milvus_client = MilvusClient(uri=milvus_uri)\n", "\n", "# Milvus collection name\n", "COLLECTION_NAME = \"document_ocr\"\n", "\n", "print(f\"Connected to Mistral API and Milvus at {milvus_uri}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up Milvus Collection\n", "\n", "Now, let's create a Milvus collection to store our document data. The collection will have the following fields:\n", "- `id`: Primary key (auto-generated)\n", "- `url`: Source URL of the document\n", "- `page_num`: Page number within the document\n", "- `content`: Extracted text content\n", "- `embedding`: Vector representation of the content (1024 dimensions)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collection 'document_ocr' already exists.\n"]}], "source": ["def setup_milvus_collection():\n", "    \"\"\"Create Milvus collection if it doesn't exist.\"\"\"\n", "    # Check if collection already exists\n", "    if milvus_client.has_collection(COLLECTION_NAME):\n", "        print(f\"Collection '{COLLECTION_NAME}' already exists.\")\n", "        return\n", "\n", "    # Define collection schema\n", "    fields = [\n", "        FieldSchema(name=\"id\", dtype=DataType.INT64, is_primary=True, auto_id=True),\n", "        FieldSchema(name=\"url\", dtype=DataType.VARCHAR, max_length=500),\n", "        FieldSchema(name=\"page_num\", dtype=DataType.INT64),\n", "        FieldSchema(name=\"content\", dtype=DataType.VARCHAR, max_length=65535),\n", "        FieldSchema(name=\"embedding\", dtype=DataType.FLOAT_VECTOR, dim=1024),\n", "    ]\n", "\n", "    schema = CollectionSchema(fields=fields)\n", "\n", "    # Create collection\n", "    milvus_client.create_collection(\n", "        collection_name=COLLECTION_NAME,\n", "        schema=schema,\n", "    )\n", "\n", "    # Create index for vector search\n", "    index_params = milvus_client.prepare_index_params()\n", "    index_params.add_index(\n", "        field_name=\"embedding\",\n", "        index_type=\"IVF_FLAT\",  # Index type for approximate nearest neighbor search\n", "        metric_type=\"COSINE\",  # Similarity metric\n", "        params={\"nlist\": 128},  # Number of clusters\n", "    )\n", "\n", "    milvus_client.create_index(\n", "        collection_name=COLLECTION_NAME, index_params=index_params\n", "    )\n", "\n", "    print(f\"Collection '{COLLECTION_NAME}' created successfully with index.\")\n", "\n", "\n", "setup_milvus_collection()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Core Functionality\n", "\n", "Let's implement the core functions for our document understanding system:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Generate embeddings using Mistral\n", "def generate_embedding(text):\n", "    \"\"\"Generate embedding for text using Mistral embedding model.\"\"\"\n", "    response = client.embeddings.create(model=embedding_model, inputs=[text])\n", "    return response.data[0].embedding\n", "\n", "\n", "# Store OCR results in Mil<PERSON>s\n", "def store_ocr_in_milvus(url, ocr_result):\n", "    \"\"\"Process OCR results and store in Milvus.\"\"\"\n", "    # Extract pages from OCR result\n", "    pages = []\n", "    current_page = \"\"\n", "    page_num = 0\n", "\n", "    for line in ocr_result.split(\"\\n\"):\n", "        if line.startswith(\"### Page \"):\n", "            if current_page:\n", "                pages.append((page_num, current_page.strip()))\n", "            page_num = int(line.replace(\"### Page \", \"\"))\n", "            current_page = \"\"\n", "        else:\n", "            current_page += line + \"\\n\"\n", "\n", "    # Add the last page\n", "    if current_page:\n", "        pages.append((page_num, current_page.strip()))\n", "\n", "    # Prepare data for Milvus\n", "    entities = []\n", "    for page_num, content in pages:\n", "        # Generate embedding for the page content\n", "        embedding = generate_embedding(content)\n", "\n", "        # Create entity\n", "        entity = {\n", "            \"url\": url,\n", "            \"page_num\": page_num,\n", "            \"content\": content,\n", "            \"embedding\": embedding,\n", "        }\n", "        entities.append(entity)\n", "\n", "    # Insert into Milvus\n", "    if entities:\n", "        milvus_client.insert(collection_name=COLLECTION_NAME, data=entities)\n", "        print(f\"Stored {len(entities)} pages from {url} in Milvus.\")\n", "\n", "    return len(entities)\n", "\n", "\n", "# Define OCR function\n", "def perform_ocr(url):\n", "    \"\"\"Apply OCR to a URL (PDF or image).\"\"\"\n", "    try:\n", "        # Try PDF OCR first\n", "        response = client.ocr.process(\n", "            model=ocr_model, document={\"type\": \"document_url\", \"document_url\": url}\n", "        )\n", "    except Exception:\n", "        try:\n", "            # If PDF OCR fails, try Image OCR\n", "            response = client.ocr.process(\n", "                model=ocr_model, document={\"type\": \"image_url\", \"image_url\": url}\n", "            )\n", "        except Exception as e:\n", "            return str(e)  # Return error message\n", "\n", "    # Format the OCR results\n", "    ocr_result = \"\\n\\n\".join(\n", "        [\n", "            f\"### Page {i + 1}\\n{response.pages[i].markdown}\"\n", "            for i in range(len(response.pages))\n", "        ]\n", "    )\n", "\n", "    # Store in Milvus\n", "    store_ocr_in_milvus(url, ocr_result)\n", "\n", "    return ocr_result\n", "\n", "\n", "# Process URLs\n", "def process_document(url):\n", "    \"\"\"Process a document URL and return its contents.\"\"\"\n", "    print(f\"Processing document: {url}\")\n", "    ocr_result = perform_ocr(url)\n", "    return ocr_result"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Search Functionality\n", "\n", "Now, let's implement the search functionality to retrieve relevant document content:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def search_documents(query, limit=5):\n", "    \"\"\"Search Milvus for similar content to the query.\"\"\"\n", "    # Check if collection exists\n", "    if not milvus_client.has_collection(COLLECTION_NAME):\n", "        return \"No documents have been processed yet.\"\n", "\n", "    # Load collection if not already loaded\n", "    if milvus_client.get_load_state(COLLECTION_NAME) != LoadState.Loaded:\n", "        milvus_client.load_collection(COLLECTION_NAME)\n", "\n", "    print(f\"Searching for: {query}\")\n", "    query_embedding = generate_embedding(query)\n", "\n", "    search_params = {\"metric_type\": \"COSINE\", \"params\": {\"nprobe\": 10}}\n", "\n", "    search_results = milvus_client.search(\n", "        collection_name=COLLECTION_NAME,\n", "        data=[query_embedding],\n", "        anns_field=\"embedding\",\n", "        search_params=search_params,\n", "        limit=limit,\n", "        output_fields=[\"url\", \"page_num\", \"content\"],\n", "    )\n", "\n", "    results = []\n", "\n", "    if not search_results or not search_results[0]:\n", "        return \"No matching documents found.\"\n", "\n", "    for i, hit in enumerate(search_results[0]):\n", "        url = hit[\"entity\"][\"url\"]\n", "        page_num = hit[\"entity\"][\"page_num\"]\n", "        content = hit[\"entity\"][\"content\"]\n", "        score = hit[\"distance\"]\n", "\n", "        results.append(\n", "            {\n", "                \"rank\": i + 1,\n", "                \"score\": score,\n", "                \"url\": url,\n", "                \"page\": page_num,\n", "                \"content\": content[:500] + \"...\" if len(content) > 500 else content,\n", "            }\n", "        )\n", "\n", "    return results\n", "\n", "\n", "# Get statistics about stored documents\n", "def get_document_stats():\n", "    \"\"\"Get statistics about documents stored in Milvus.\"\"\"\n", "    if not milvus_client.has_collection(COLLECTION_NAME):\n", "        return \"No documents have been processed yet.\"\n", "\n", "    # Get collection stats\n", "    stats = milvus_client.get_collection_stats(COLLECTION_NAME)\n", "    row_count = stats[\"row_count\"]\n", "\n", "    # Get unique URLs\n", "    results = milvus_client.query(\n", "        collection_name=COLLECTION_NAME, filter=\"\", output_fields=[\"url\"], limit=10000\n", "    )\n", "\n", "    unique_urls = set()\n", "    for result in results:\n", "        unique_urls.add(result[\"url\"])\n", "\n", "    return {\n", "        \"total_pages\": row_count,\n", "        \"unique_documents\": len(unique_urls),\n", "        \"documents\": list(unique_urls),\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Demo: Processing Documents\n", "\n", "Let's process some example documents. You can replace these URLs with your own documents."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing document: https://arxiv.org/pdf/2310.06825.pdf\n", "Stored 9 pages from https://arxiv.org/pdf/2310.06825.pdf in Milvus.\n", "\n", "OCR Result Preview:\n", "====================\n", "### Page 1\n", "# Mistral 7B \n", "\n", "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>\n", "\n", "![img-0.jpeg](img-0.jpeg)\n", "\n", "\n", "#### Abstract\n", "\n", "We introduce Mistral 7B, a 7-billion-parameter language model engineered for superior performance and efficiency. Mistral 7B outperforms the best open 13B model (Llama 2) across all evaluated benchmarks, and the best released 34B model (Llama 1) in reasoning, mathematics, and code generation. Our model leverages grouped-query attention (GQA) for faster inference, coupled with sliding window attention (SWA) to effectively handle sequences of arbitrary length with a reduced inference cost. We also provide a model fine-tuned to follow instructions, Mistral 7B - Instruct, that surpasses Llama 2 13B - chat mod...\n"]}], "source": ["# Example PDF URL (Mistral AI paper)\n", "pdf_url = \"https://arxiv.org/pdf/2310.06825.pdf\"\n", "\n", "# Process the document\n", "ocr_result = process_document(pdf_url)\n", "\n", "# Display a preview of the OCR result\n", "print(\"\\nOCR Result Preview:\")\n", "print(\"====================\")\n", "print(ocr_result[:1000] + \"...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's process an image as well:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing document: https://s3.eu-central-1.amazonaws.com/readcoop.cis.public-assets.prod/hero/old-german-scripts.png\n", "Stored 1 pages from https://s3.eu-central-1.amazonaws.com/readcoop.cis.public-assets.prod/hero/old-german-scripts.png in Milvus.\n", "\n", "Image OCR Result:\n", "=================\n", "### Page 1\n", "![img-0.jpeg](img-0.jpeg)\n", "![img-1.jpeg](img-1.jpeg)\n", "![img-2.jpeg](img-2.jpeg)\n", "![img-3.jpeg](img-3.jpeg)\n", "![img-4.jpeg](img-4.jpeg)\n", "![img-5.jpeg](img-5.jpeg)\n", "![img-6.jpeg](img-6.jpeg)\n", "![img-7.jpeg](img-7.jpeg)\n", "![img-8.jpeg](img-8.jpeg)\n", "![img-9.jpeg](img-9.jpeg)\n", "![img-10.jpeg](img-10.jpeg)\n", "![img-11.jpeg](img-11.jpeg)\n", "![img-12.jpeg](img-12.jpeg)\n", "![img-13.jpeg](img-13.jpeg)\n", "![img-14.jpeg](img-14.jpeg)\n", "![img-15.jpeg](img-15.jpeg)\n", "![img-16.jpeg](img-16.jpeg)\n", "![img-17.jpeg](img-17.jpeg)\n", "![img-18.jpeg](img-18.jpeg)\n", "![img-19.jpeg](img-19.jpeg)\n", "![img-20.jpeg](img-20.jpeg)\n", "![img-21.jpeg](img-21.jpeg)\n", "![img-22.jpeg](img-22.jpeg)\n", "![img-23.jpeg](img-23.jpeg)\n", "![img-24.jpeg](img-24.jpeg)\n", "![img-25.jpeg](img-25.jpeg)\n", "![img-26.jpeg](img-26.jpeg)\n", "![img-27.jpeg](img-27.jpeg)\n", "![img-28.jpeg](img-28.jpeg)\n", "![img-29.jpeg](img-29.jpeg)\n", "![img-30.jpeg](img-30.jpeg)\n"]}], "source": ["# Example image URL (replace with your own)\n", "image_url = \"https://s3.eu-central-1.amazonaws.com/readcoop.cis.public-assets.prod/hero/old-german-scripts.png\"\n", "\n", "# Process the image\n", "try:\n", "    ocr_result = process_document(image_url)\n", "    print(\"\\nImage OCR Result:\")\n", "    print(\"=================\")\n", "    print(ocr_result)\n", "except Exception as e:\n", "    print(f\"Error processing image: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Demo: Searching Documents\n", "\n", "Now that we've processed some documents, let's search through them:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total pages stored: 58\n", "Unique documents: 3\n", "\n", "Processed documents:\n", "1. https://arxiv.org/pdf/2310.06825.pdf\n", "2. https://s3.eu-central-1.amazonaws.com/readcoop.cis.public-assets.prod/hero/old-german-scripts.png\n", "3. https://arxiv.org/pdf/2410.07073\n"]}], "source": ["# Get document statistics\n", "stats = get_document_stats()\n", "print(f\"Total pages stored: {stats['total_pages']}\")\n", "print(f\"Unique documents: {stats['unique_documents']}\")\n", "print(\"\\nProcessed documents:\")\n", "for i, url in enumerate(stats[\"documents\"]):\n", "    print(f\"{i + 1}. {url}\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Searching for: What is Mistral 7B?\n", "Search results for: 'What is Mistral 7B?'\n", "\n", "Result 1 (Score: 0.83)\n", "Source: https://arxiv.org/pdf/2310.06825.pdf (Page 2)\n", "Content: Mistral 7B is released under the Apache 2.0 license. This release is accompanied by a reference implementation ${ }^{1}$ facilitating easy deployment either locally or on cloud platforms such as AWS, GCP, or Azure using the vLLM [17] inference server and SkyPilot ${ }^{2}$. Integration with Hugging Face ${ }^{3}$ is also streamlined for easier integration. Moreover, Mistral 7B is crafted for ease of fine-tuning across a myriad of tasks. As a demonstration of its adaptability and superior perform...\n", "\n", "Result 2 (Score: 0.83)\n", "Source: https://arxiv.org/pdf/2310.06825.pdf (Page 2)\n", "Content: Mistral 7B is released under the Apache 2.0 license. This release is accompanied by a reference implementation ${ }^{1}$ facilitating easy deployment either locally or on cloud platforms such as AWS, GCP, or Azure using the vLLM [17] inference server and SkyPilot ${ }^{2}$. Integration with Hugging Face ${ }^{3}$ is also streamlined for easier integration. Moreover, Mistral 7B is crafted for ease of fine-tuning across a myriad of tasks. As a demonstration of its adaptability and superior perform...\n", "\n", "Result 3 (Score: 0.82)\n", "Source: https://arxiv.org/pdf/2310.06825.pdf (Page 1)\n", "Content: # Mistral 7B \n", "\n", "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>\n", "\n", "![img-0.jpeg](img-0.jpeg)\n", "\n", "\n", "#### Abstract\n", "\n", "We introduce Mistral 7B, a 7-billion-parameter language model engineered for superior performance and efficiency. Mistral 7...\n", "\n"]}], "source": ["# Search for information\n", "query = \"What is Mistral 7B?\"\n", "results = search_documents(query, limit=3)\n", "\n", "print(f\"Search results for: '{query}'\\n\")\n", "\n", "if isinstance(results, str):\n", "    print(results)\n", "else:\n", "    for result in results:\n", "        print(f\"Result {result['rank']} (Score: {result['score']:.2f})\")\n", "        print(f\"Source: {result['url']} (Page {result['page']})\")\n", "        print(f\"Content: {result['content']}\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Try another search query:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Searching for: What are the capabilities of Mi<PERSON>l's language models?\n", "Search results for: 'What are the capabilities of Mistral's language models?'\n", "\n", "Result 1 (Score: 0.85)\n", "Source: https://arxiv.org/pdf/2310.06825.pdf (Page 2)\n", "Content: Mistral 7B is released under the Apache 2.0 license. This release is accompanied by a reference implementation ${ }^{1}$ facilitating easy deployment either locally or on cloud platforms such as AWS, GCP, or Azure using the vLLM [17] inference server and SkyPilot ${ }^{2}$. Integration with Hugging Face ${ }^{3}$ is also streamlined for easier integration. Moreover, Mistral 7B is crafted for ease of fine-tuning across a myriad of tasks. As a demonstration of its adaptability and superior perform...\n", "\n", "Result 2 (Score: 0.85)\n", "Source: https://arxiv.org/pdf/2310.06825.pdf (Page 2)\n", "Content: Mistral 7B is released under the Apache 2.0 license. This release is accompanied by a reference implementation ${ }^{1}$ facilitating easy deployment either locally or on cloud platforms such as AWS, GCP, or Azure using the vLLM [17] inference server and SkyPilot ${ }^{2}$. Integration with Hugging Face ${ }^{3}$ is also streamlined for easier integration. Moreover, Mistral 7B is crafted for ease of fine-tuning across a myriad of tasks. As a demonstration of its adaptability and superior perform...\n", "\n", "Result 3 (Score: 0.84)\n", "Source: https://arxiv.org/pdf/2310.06825.pdf (Page 6)\n", "Content: | Model | Answer |\n", "| :--: | :--: |\n", "| Mistral 7B - Instruct with Mistral system prompt | To kill a Linux process, you can use the `kill' command followed by the process ID (PID) of the process you want to terminate. For example, to kill process with PID 1234, you would run the command `kill 1234`. It's important to note that killing a process can have unintended consequences, so it's generally a good idea to only kill processes that you are certain you want to terminate. Additionally, it's genera...\n", "\n"]}], "source": ["# Search for more specific information\n", "query = \"What are the capabilities of Mistral's language models?\"\n", "results = search_documents(query, limit=3)\n", "\n", "print(f\"Search results for: '{query}'\\n\")\n", "\n", "if isinstance(results, str):\n", "    print(results)\n", "else:\n", "    for result in results:\n", "        print(f\"Result {result['rank']} (Score: {result['score']:.2f})\")\n", "        print(f\"Source: {result['url']} (Page {result['page']})\")\n", "        print(f\"Content: {result['content']}\\n\")"]}, {"attachments": {"af7b18aa-753e-48a6-a608-b2c04ccc40a4.png": {"image/png": "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"}, "e0569552-6ae8-4687-bddb-639db76339da.png": {"image/png": "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"}}, "cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "In this tutorial, we've built a complete document understanding system using Mistral OCR and Milvus. This system can:\n", "\n", "1. Process documents from URLs\n", "2. Extract text using Mistral's OCR capabilities\n", "3. Generate vector embeddings for the content\n", "4. Store both text and vectors in Milvus\n", "5. Perform semantic search across all processed documents\n", "\n", "This approach enables powerful document understanding capabilities that go beyond simple keyword matching, allowing users to find information based on meaning rather than exact text matches.\n", "\n", "## ⭐️ <PERSON><PERSON><PERSON>\n", "We hope you liked this tutorial showcasing how to use Milvus with OpenAI Agents. If you liked it and our project, please give us a **[star on Github](https://github.com/milvus-io/milvus)**! ⭐\n", "\n", "![image.png](attachment:e0569552-6ae8-4687-bddb-639db76339da.png)\n", "\n", "## 🤝 Add me on Linkedin!\n", "If you have some questions related to <PERSON><PERSON><PERSON><PERSON>, GenAI, etc, I am <PERSON>, you can add me on LinkedIn and I'll gladly help you.\n", "\n", "![image.png](attachment:af7b18aa-753e-48a6-a608-b2c04ccc40a4.png)\n", "\n", "## 💬 Join our Discord\n", "If you're interested in learning more about <PERSON><PERSON><PERSON><PERSON> or you wanna share some feedback, feel free to **join our [Discord](https://zilliz.com/discord) channel**.\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 4}