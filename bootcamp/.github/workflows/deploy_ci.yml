name: Deploy CI Test

on:
  pull_request:
    branches:
      - master
    types: [ labeled ]

jobs:
  CI-Deploy-Test:
    # Example label: audio_similarity_search/quick_deploy
    if: contains(github.event.label.name, 'benchmark') || contains(github.event.label.name, '/quick_deploy') || contains(github.event.label.name, '/object_detection')
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@master
    - name: Setup Python3.7
      uses: actions/setup-python@master
      with:
        python-version: 3.7
    - uses: conda-incubator/setup-miniconda@v2
      with:
        auto-update-conda: true
        python-version: 3.7
    - name: Conda install RDKit
      if: github.event.label.name == 'molecular_similarity_search/quick_deploy'
      shell: bash -l {0}
      run: |
        conda info
        conda install -c rdkit rdkit -y

    - name: Install pip packeages
      shell: bash -l {0}
      run: |
        python -m pip install --upgrade pip
        pip install pytest
        pip install gdown
    - name: Start Milvus
      run: |
        wget https://github.com/milvus-io/milvus/releases/download/v2.0.0-rc7/milvus-standalone-docker-compose.yml -O docker-compose.yml
        docker-compose up -d

    - name: Install libsndfile
      if: github.event.label.name == 'audio_similarity_search/quick_deploy'
      run: sudo apt-get install -y libsndfile1
    - name: Install ffmpeg
      if: github.event.label.name == 'video_similarity_search/object_detection' || github.event.label.name == 'video_similarity_search/quick_deploy'
      run: sudo apt-get install -y ffmpeg
    - name: Start MySQL
      if: contains(github.event.label.name, '/quick_deploy') || contains(github.event.label.name, '/object_detection')
      run: docker run -p 3306:3306 -e MYSQL_ROOT_PASSWORD=123456 -d mysql:5.7
    
    - name: Test main server
      if: contains(github.event.label.name, '/quick_deploy') || contains(github.event.label.name, '/object_detection')
      shell: bash -l {0}
      run: |
        cd solutions/${{github.event.label.name}}/server
        pip install -r requirements.txt
        cd src && python -m pytest

    - name: Test benchmark
      if: github.event.label.name == 'benchmark'
      shell: bash -l {0}
      run: |
        cd benchmark_test/scripts
        pip install -r requirements.txt
        python -m pytest test_main.py
