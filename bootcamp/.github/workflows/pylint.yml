# This is a workflow to checkout code with pylint

name: Workflow for pylint
on:
  pull_request:
    branches:
      - main
jobs:
  Pylint-Check:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@master
    - name: Setup Python
      uses: actions/setup-python@master
      with:
        python-version: 3.8
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
    - name: Python pylint
      run: |
        pip install pylint==2.10.2
        pylint --rcfile=pylint.conf --output-format=colorized solutions
        pylint --rcfile=pylint.conf --output-format=colorized benchmark_test
