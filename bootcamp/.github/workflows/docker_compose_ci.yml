name: Docker Compose CI Test
on:
  pull_request:
    branches:
      - master
    types: [ labeled ]
    paths:
      - '**docker-compose.yaml'

jobs:
  Docker-Compose-Test:
    if: github.event.label.name == 'docker-compose'
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@master
    - name: Setup Python3.7
      uses: actions/setup-python@master
      with:
        python-version: 3.7
    - name: Install pip packeages
      run: |
        python -m pip install --upgrade pip
        pip install pytest
        pip install gdown

    - name: Get all added and modified files as CSV
      id: files
      uses: Ana06/get-changed-files@v2.0.0
      with:
        format: 'csv'
    - name: Run test with docker-compose
      run: |
        mapfile -d ',' -t added_modified_files < <(printf '%s,' '${{ steps.files.outputs.added_modified }}')
        for added_modified_file in "${added_modified_files[@]}"; do
          if [[ ${added_modified_file} = *docker-compose.yaml ]]; then
            mkdir -p `dirname ${added_modified_file}`/data
            docker-compose -f ${added_modified_file} up -d
            cd `dirname ${added_modified_file}`
            pip install -r server/requirements.txt
            python -m pytest test_docker_compose.py
          fi
        done
