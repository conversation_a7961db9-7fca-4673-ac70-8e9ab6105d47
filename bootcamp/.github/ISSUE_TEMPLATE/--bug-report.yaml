name: 🐞 Bug report
description: Create a report to help us resolve or improve
title: "[BUG]: "
body:
- type: markdown
  attributes:
    value: |
      Thanks for taking the time to fill out this bug report! Please fill the form in English!
- type: checkboxes
  attributes:
    label: Is there an existing issue for this?
    description: Please search to see if an issue already exists for the bug you encountered, and check the [FAQ](https://github.com/milvus-io/bootcamp/blob/master/bootcamp_faq.md).
    options:
    - label: I have searched the existing issues
      required: true
- type: textarea
  attributes:
    label: Current Behavior
    description: A clear and concise description of what the issue is. And you can take a look at the [announcement](https://github.com/milvus-io/bootcamp/issues/587)
    placeholder: |
      When I do <X>, <Y> happens and I see the error message attached below:
      ```...```
  validations:
    required: false
- type: textarea
  attributes:
    label: Expected Behavior
    description: A concise description of what you expected to happen.
    placeholder: When I do <X>, <Z> should happen instead.
  validations:
    required: false
- type: textarea
  attributes:
    label: Steps To Reproduce
    description: |
      Which solution are you running? Please post the link.
      Steps to reproduce the behavior(Docker or Source code):
    placeholder: |
      1. Go to '...'
      2. Click on '....'
      3. Scroll down to '....'
      4. See error
    render: markdown
  validations:
    required: false
- type: textarea
  attributes:
    label: Software version
    description: |
      Please complete the following information:
    value: |
      Milvus: [e.g. 2.0.0rc1]
      Server: [e.g. 2.0.0]
      Client: [e.g. 2.0.0]
    render: markdown
  validations:
    required: false
- type: textarea
  attributes:
    label: Anything else?
    description: |
      Links? References? Anything that will give us more context about the issue you are encountering!
  validations:
    required: false