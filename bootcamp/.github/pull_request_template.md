## 📌 Description

<!-- Describe what this PR does and why it's needed -->
Closes #[issue_number]

## ✅ Changes

- [ ] Feature added: ...
- [ ] Bug fixed: ...
- [ ] Refactor: ...
- [ ] Other (explain): ...

## 🔬 How to Test

<!-- Provide steps to manually test or describe how it was tested -->
1. ...
2. ...

## 📝 Checklist

- [ ] Code compiles and runs
- [ ] Linting passes (Check jupyter notebooks: `pip install "black[jupyter]"` > `black {file_or_directory}`)
- [ ] PR includes relevant docs or comments
- [ ] Rebased on the latest `main` (no merge commits or conflicts)

## 🧼 Commit Hygiene

✔️ Follow best practices:
- Small, focused commits with clear messages
- Group related changes together
- Avoid large monolithic commits
- Use meaningful commit messages (e.g., `fix:`, `feat:`, `refactor:`)

## 👀 Reviewer Notes

<!-- Any context or areas of focus for reviewers -->
- Please focus on ...
- Watch out for ...

---

### 📎 Related Issues, PRs, or Docs

- Related PR: #
- Issue: #

---
