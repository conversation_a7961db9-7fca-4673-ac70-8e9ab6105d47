version: '3.5'

services:
  etcd:
    container_name: milvus-etcd
    image: quay.io/coreos/etcd:v3.5.5
    networks:
      app_net:
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=4294967296
      - ETCD_SNAPSHOT_COUNT=50000
    volumes:
      - ${DOCKER_VOLUME_DIRECTORY:-.}/volumes/etcd:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd

  minio:
    container_name: milvus-minio
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    networks:
      app_net:
    environment:
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
    volumes:
      - ${DOCKER_VOLUME_DIRECTORY:-.}/volumes/minio:/minio_data
    command: minio server /minio_data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  standalone:
    container_name: milvus-standalone
    image: milvusdb/milvus:v2.2.10
    networks:
      app_net:
        ipv4_address: ************0
    command: ["milvus", "run", "standalone"]
    environment:
      ETCD_ENDPOINTS: etcd:2379
      MINIO_ADDRESS: minio:9000
    volumes:
      - ${DOCKER_VOLUME_DIRECTORY:-.}/volumes/milvus:/var/lib/milvus
    ports:
      - "19530:19530"
      - "9091:9091"
    depends_on:
      - "etcd"
      - "minio"

  mysql:
    container_name: img-search-mysql
    image: mysql:5.7
    networks:
      app_net:
        ipv4_address: ************1
    environment:
      - MYSQL_ROOT_PASSWORD=123456
    ports:
      - "3306:3306"

  webserver:
    container_name: img-search-webserver
    image: milvusbootcamp/img-search-server:2.2.10
    networks:
      app_net:
        ipv4_address: ************2
    environment:
      MILVUS_HOST: '************0'
      MYSQL_HOST: '************1'
    volumes:
      - ./data:/data
    restart: always
    depends_on:
      - standalone
      - mysql
    ports:
      - "5000:5000"

  webclient:
    container_name: img-search-webclient
    image: milvusbootcamp/img-search-client:2.2.10
    networks:
      app_net:
        ipv4_address: ************3
    environment:
      API_URL: 'http://127.0.0.1:5000'
    ports:
      - "8001:80"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://127.0.0.1:8001"]
      interval: 30s
      timeout: 20s
      retries: 3

networks:
  app_net:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: ************/24
          gateway: ************
