<img src="pics/logo.png" alt="milvus bootcamp banner">

<div class="column" align="middle">
  <a href="https://github.com/milvus-io/bootcamp/blob/master/LICENSE"><img height="20" src="https://img.shields.io/github/license/milvus-io/bootcamp" alt="license"/></a>
  <a href="https://milvus.io"><img src="https://img.shields.io/badge/milvus-blue" alt="milvus"/></a>
  <a href="https://cloud.zilliz.com/signup?utm_source=partner&utm_medium=referral&utm_campaign=2024-11-04_web_github-readme_global"><img src="https://img.shields.io/badge/zilliz-green" alt="fully-managed-milvus"/></a>
  <a href="https://discord.gg/mKc3R95yE5"><img height="20" src="https://img.shields.io/badge/Discord-%235865F2.svg?style=for-the-badge&logo=discord&logoColor=white" alt="discord"/></a>
  <a href="https://x.com/milvusio"><img src="https://img.shields.io/twitter/follow/milvusio" alt="twitter"/></a>
</div>

## :bird: What You Can Do

Begin an interactive journey to master [Milvus](https://milvus.io), enhancing your projects with seamless integration and optimization tools.

- **Explore Tutorials:** Dive into notebooks that walk you through diverse Milvus use cases.

- **Deploy Demos:** Build your own demo to see Milvus in action.

- **Discover Use Cases:** Learn how Milvus integrates with other tools and frameworks through practical examples.

- **Expand Your Skills:** Apply evaluation methods to test and optimize your applications.

## :pencil: Examples

You can explore a comprehensive [Tutorials Overview](https://milvus.io/docs/tutorials-overview.md) covering topics such as Retrieval-Augmented Generation (RAG), Semantic Search, Hybrid Search, Question Answering, Recommendation Systems, and various quick-start guides. These resources are designed to help you get started quickly and efficiently.

<table>
  <tr>
    <td width="30%">
      <a href="https://milvus.io/milvus-demos">
        <img src="https://assets.zilliz.com/image_search_59a64e4f22.gif" />
      </a>
    </td>
    <td width="30%">
<a href="https://milvus.io/milvus-demos">
<img src="https://assets.zilliz.com/qa_df5ee7bd83.gif" />
</a>
    </td>
    <td width="30%">
<a href="https://milvus.io/milvus-demos">
<img src="https://assets.zilliz.com/mole_search_76f8340572.gif" />
</a>
    </td>
  </tr>
  <tr>
    <th>
      <a href="https://milvus.io/milvus-demos">Image Search</a>
    </th>
    <th>
      <a href="https://milvus.io/milvus-demos">RAG</a>
    </th>
    <th>
      <a href="https://milvus.io/milvus-demos">Drug Discovery</a>
    </th>
  </tr>
</table>


Here is a selection of demos and tutorials to show how to build various types of AI applications made with Milvus:

| Tutorial | Use Case | Related Milvus Features |
| ----------- | -------- | -------- |
| [Build RAG with Milvus](https://milvus.io/docs/build-rag-with-milvus.md) |  RAG | vector search |
| [Advanced RAG Optimizations](https://milvus.io/docs/how_to_enhance_your_rag.md) | RAG | vector search, full text search |
| [Full Text Search with Milvus](https://milvus.io/docs/full_text_search_with_milvus.md) | Text Search | full text search |
| [Hybrid Search with Milvus](https://milvus.io/docs/hybrid_search_with_milvus.md) | Hybrid Search | hybrid search, multi vector, dense embedding, sparse embedding |
| [Image Search with Milvus](https://milvus.io/docs/image_similarity_search.md) | Semantic Search | vector search, dynamic field |
| [Multimodal Search using Multi Vectors](https://milvus.io/docs/multimodal_rag_with_milvus.md) | Semantic Search | multi vector, hybrid search |
| [Movie Recommendation with Milvus](https://milvus.io/docs/movie_recommendation_with_milvus.md) | Recommendation System | vector search |
| [Graph RAG with Milvus](https://milvus.io/docs/graph_rag_with_milvus.md) | RAG | graph search |
| [Use Milvus as a LangChain Vector Store](https://milvus.io/docs/basic_usage_langchain.md) | RAG | vector search |
| [RAG with Milvus and LlamaIndex](https://milvus.io/docs/integrate_with_llamaindex.md) | RAG | vector search |
| [Contextual Retrieval with Milvus](https://milvus.io/docs/contextual_retrieval_with_milvus.md) | Quickstart | vector search |
| [Vector Visualization](https://milvus.io/docs/vector_visualization.md) | Quickstart | vector search |
| [HDBSCAN Clustering with Milvus](https://milvus.io/docs/hdbscan_clustering_with_milvus.md) | Quickstart | vector search |
| [Use ColPali for Multi-Modal Retrieval with Milvus](https://milvus.io/docs/use_ColPali_with_milvus.md) | Quickstart | vector search |


## :fire: Community

Join the Milvus community on [Discord](https://discord.gg/8uyFbECzPX) to share your suggestions, advice, and questions with our engineering team.

To learn latest news about Milvus, follow us on social media:

- [X](https://twitter.com/milvusio)
- [LinkedIn](https://www.linkedin.com/company/the-milvus-project)
- [Youtube](https://www.youtube.com/channel/UCMCo_F7pKjMHBlfyxwOPw-g)
- [Medium](https://medium.com/@milvusio)

You can also check out our [FAQ page](https://milvus.io/docs/performance_faq.md) to discover solutions or answers to your issues or questions, and subscribe to Milvus mailing lists:

- [Technical Steering Committee](https://lists.lfai.foundation/g/milvus-tsc)
- [Technical Discussions](https://lists.lfai.foundation/g/milvus-technical-discuss)
- [Announcement](https://lists.lfai.foundation/g/milvus-announce)

Let's accelerate your Milvus journey together!