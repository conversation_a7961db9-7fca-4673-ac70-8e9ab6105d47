#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指标数据存储到Milvus数据库 - 混合检索版本
读取Excel文件中的指标数据，使用Qwen3-Embedding-0.6B模型生成密集向量，
同时使用BM25内置函数生成稀疏向量，支持混合检索
"""

import pandas as pd
import numpy as np
from sentence_transformers import SentenceTransformer
from pymilvus import MilvusClient, DataType, Function, FunctionType, AnnSearchRequest, RRFRanker, WeightedRanker, FieldSchema, CollectionSchema, utility
import json
import uuid
from datetime import datetime
from pymilvus.client.constants import ConsistencyLevel


class IndicatorsToMilvusHybrid:
    def __init__(self, model_path="/home/<USER>/.cache/modelscope/hub/models/Qwen/Qwen3-Embedding-0.6B", 
                 milvus_host="************", milvus_port="19530"):
        """
        初始化类
        
        Args:
            model_path: Qwen3-Embedding-0.6B模型路径
            milvus_host: Milvus服务器地址
            milvus_port: Milvus服务器端口
        """
        self.model_path = model_path
        self.milvus_host = milvus_host
        self.milvus_port = milvus_port
        self.collection_name = "indicators_qwen_hybrid"
        self.model = None
        self.client = None
        
    def load_model(self):
        """加载Qwen3-Embedding-0.6B模型"""
        print("正在加载Qwen3-Embedding-0.6B模型...")
        try:
            self.model = SentenceTransformer(self.model_path)
            print("Qwen3-Embedding-0.6B模型加载完成")
        except Exception as e:
            print(f"模型加载失败: {e}")
            raise
        
    def connect_milvus(self):
        """连接到Milvus数据库"""
        print(f"正在连接Milvus数据库 {self.milvus_host}:{self.milvus_port}...")
        self.client = MilvusClient(uri=f"http://{self.milvus_host}:{self.milvus_port}")
        print("Milvus连接成功")
        
    def create_collection(self):
        """创建支持混合检索的Milvus集合"""
        # 检查集合是否已存在
        if self.client.has_collection(self.collection_name):
            print(f"集合 {self.collection_name} 已存在，删除旧集合...")
            self.client.drop_collection(self.collection_name)
            
        # 创建schema
        schema = self.client.create_schema(
            auto_id=False,
            enable_dynamic_field=True,
        )
        analyzer_params={"type": "chinese"}

        # 添加字段
        schema.add_field(field_name="id", datatype=DataType.VARCHAR, max_length=100, is_primary=True)
        schema.add_field(field_name="indicator_id", datatype=DataType.VARCHAR, max_length=100)
        schema.add_field(field_name="indicator_name", datatype=DataType.VARCHAR, max_length=500)
        schema.add_field(field_name="measurement_object_type", datatype=DataType.VARCHAR, max_length=200)
        schema.add_field(field_name="indicator_meaning", datatype=DataType.VARCHAR, max_length=2000)
        schema.add_field(field_name="indicator_formula", datatype=DataType.VARCHAR, max_length=2000)
        schema.add_field(field_name="formula_description", datatype=DataType.VARCHAR, max_length=2000)
        schema.add_field(field_name="function_category", datatype=DataType.VARCHAR, max_length=500)
        schema.add_field(field_name="combined_text", datatype=DataType.VARCHAR, max_length=5000, enable_match=True, enable_analyzer=True,analyzer_params=analyzer_params)
        schema.add_field(field_name="embedding", datatype=DataType.FLOAT_VECTOR, dim=1024)
        schema.add_field(field_name="sparse_vector", datatype=DataType.SPARSE_FLOAT_VECTOR)
        
        # 创建索引参数
        index_params = self.client.prepare_index_params()

        bm25_function = Function(
            name="bm25",
            function_type=FunctionType.BM25,
            input_field_names=["combined_text"],
            output_field_names=["sparse_vector"],
        )
        schema.add_function(bm25_function)

        
        # 添加密集向量索引
        index_params.add_index(
            field_name="embedding",
            index_type="IVF_FLAT",
            metric_type="COSINE",
            params={"nlist": 128}
        )
        
        # 添加稀疏向量索引
        index_params.add_index(
            field_name="sparse_vector",
            index_type="SPARSE_INVERTED_INDEX",
            metric_type="BM25"
        )
        
        # 创建集合
        self.client.create_collection(
            collection_name=self.collection_name,
            schema=schema,
            index_params=index_params,
            consistency_level="Strong"
        )
        print(f"集合 {self.collection_name} 创建成功")
        
    def read_excel_data(self, excel_path):
        """读取Excel文件中的指标数据"""
        print(f"正在读取Excel文件: {excel_path}")
        
        # 读取第一个sheet
        df = pd.read_excel(excel_path, sheet_name=0)
        
        # 清理数据
        df = df.dropna(subset=['指标编号', '指标名称'])
        df = df.fillna('')
        
        print(f"读取到 {len(df)} 条有效数据")
        return df
        
    def generate_embeddings(self, texts):
        """生成文本嵌入向量"""
        print(f"正在生成 {len(texts)} 条文本的嵌入向量...")
        try:
            embeddings = self.model.encode(texts, prompt_name="passage", show_progress_bar=True)
        except:
            embeddings = self.model.encode(texts, show_progress_bar=True)
        return embeddings.tolist()
        
    def prepare_data(self, df):
        """准备要插入Milvus的数据"""
        print("正在准备数据...")
        
        data = []
        combined_texts = []
        
        for _, row in df.iterrows():
            # 生成唯一ID
            record_id = str(uuid.uuid4())
            
            # 组合文本用于生成嵌入向量和BM25
            combined_text = f"指标编号: {row['指标编号']}, 指标名称: {row['指标名称']}, 测量对象类型: {row['测量对象类型']}, 指标含义: {row['指标含义']}, 指标公式: {row['指标公式']}, 公式描述: {row['公式描述']}, 所属功能: {row['所属功能']}"
            combined_texts.append(combined_text)
            
            # 准备记录数据
            record = {
                "id": record_id,
                "indicator_id": str(row['指标编号']),
                "indicator_name": str(row['指标名称']),
                "measurement_object_type": str(row['测量对象类型']),
                "indicator_meaning": str(row['指标含义']),
                "indicator_formula": str(row['指标公式']),
                "formula_description": str(row['公式描述']),
                "function_category": str(row['所属功能']),
                "combined_text": combined_text
            }
            data.append(record)
            
        # 生成密集嵌入向量
        embeddings = self.generate_embeddings(combined_texts)
        
        # 将嵌入向量添加到数据中
        for i, record in enumerate(data):
            record["embedding"] = embeddings[i]
            # 注意：sparse_vector由BM25函数自动生成，不需要手动添加
            
        return data
        
    def insert_data(self, data):
        """将数据插入Milvus集合"""
        print(f"正在插入 {len(data)} 条数据到Milvus...")

        # 使用MilvusClient插入数据
        self.client.insert(
            collection_name=self.collection_name,
            data=data
        )
        print(f"数据插入成功，插入了 {len(data)} 条记录")
        
    def load_collection(self):
        """加载集合到内存以支持搜索"""
        print("正在加载集合到内存...")
        self.client.load_collection(self.collection_name)
        print("集合加载完成")
        
    def test_hybrid_search(self, query_text, top_k=5, use_rrf=False):
        """测试混合检索功能"""
        print(f"正在测试混合检索: '{query_text}'...")
        
        try:
            # 生成查询的密集向量
            try:
                query_embedding = self.model.encode([query_text], prompt_name="query").tolist()[0]
            except:
                query_embedding = self.model.encode([query_text]).tolist()[0]
            
            # 创建稀疏向量搜索请求（BM25）
            sparse_request = AnnSearchRequest(
                data=[query_text],  # BM25使用文本查询
                anns_field="sparse_vector",
                param={"metric_type": "BM25"},
                limit=top_k
            )
            
            # 创建密集向量搜索请求
            dense_request = AnnSearchRequest(
                data=[query_embedding],
                anns_field="embedding",
                param={"metric_type": "COSINE", "params": {"nprobe": 10}},
                limit=top_k
            )
            
            # 选择重排序器
            if use_rrf:
                ranker = RRFRanker()
                print("使用RRF重排序器")
            else:
                ranker = WeightedRanker(0.7, 0.3)  # 密集向量权重0.7，稀疏向量权重0.3
                print("使用加权重排序器")
            
            # 执行混合搜索
            results = self.client.hybrid_search(
                collection_name=self.collection_name,
                reqs=[sparse_request, dense_request],
                ranker=ranker,
                limit=top_k,
                output_fields=["indicator_id", "indicator_name", "measurement_object_type", 
                             "indicator_meaning", "indicator_formula", "formula_description", 
                             "function_category", "combined_text"],
                consistency_level=ConsistencyLevel.Bounded
            )
            
            return results
            
        except Exception as e:
            print(f"混合检索测试失败: {e}")
            return None
        
    def run(self, excel_path):
        """运行完整流程"""
        try:
            # 1. 加载模型
            self.load_model()
            
            # 2. 连接Milvus
            self.connect_milvus()
            
            # 3. 创建集合
            self.create_collection()
            
            # 4. 读取Excel数据
            df = self.read_excel_data(excel_path)
            
            # 5. 准备数据
            data = self.prepare_data(df)
            
            # 6. 插入数据
            self.insert_data(data)
            
            # 7. 加载集合
            self.load_collection()
            
            print("混合检索数据存储完成！")
            return True
            
        except Exception as e:
            print(f"处理过程中出现错误: {e}")
            return False


def main():
    """主函数"""
    excel_path = "tmpexport_1753788218285.xlsx"
    
    # 创建处理器实例
    processor = IndicatorsToMilvusHybrid()
    
    # 运行处理流程
    success = processor.run(excel_path)
    
    if success:
        print("\n=== 混合检索数据存储成功！现在可以进行混合检索测试 ===")
        
        # 混合检索示例
        print("\n=== 混合检索示例 ===")
        test_queries = [
            "调度",
            "QoS管理", 
            "时隙格式",
            "指标公式"
        ]
        
        for query in test_queries:
            print(f"\n混合检索查询: {query}")
            try:
                results = processor.test_hybrid_search(query, top_k=3)
                
                for i, hit in enumerate(results[0]):
                    print(f"  结果 {i+1} (混合分数: {hit.score:.4f}):")
                    print(f"    指标编号: {hit.entity.get('indicator_id')}")
                    print(f"    指标名称: {hit.entity.get('indicator_name')}")
                    print(f"    指标含义: {hit.entity.get('indicator_meaning')}")
                    print(f"    所属功能: {hit.entity.get('function_category')}")
                    print()
            except Exception as e:
                print(f"  混合检索测试失败: {e}")


if __name__ == "__main__":
    main()
