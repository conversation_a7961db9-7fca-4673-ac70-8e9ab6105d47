from sentence_transformers import SentenceTransformer
import torch

def get_text_embedding(text, model_path="/home/<USER>/Desktop/ragdb/text2vec-large-chinese"):
    """
    使用本地text2vec-large-chinese模型将文本转换为向量
    
    参数:
        text: 要向量化的文本
        model_path: 本地模型路径
    
    返回:
        文本的向量表示
    """
    # 加载本地模型
    model = SentenceTransformer(model_path)
    
    # 生成文本嵌入向量
    embedding = model.encode(text)
    print('embedding:', embedding)
    print('embedding type:', embedding,type(embedding),len(embedding))

    return embedding.tolist()  # 转为列表以便存储到Milvus
print(get_text_embedding("你好"))