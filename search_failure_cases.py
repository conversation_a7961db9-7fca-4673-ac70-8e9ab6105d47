#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
失败案例检索脚本
连接到Milvus数据库，搜索相似的失败案例
"""

from sentence_transformers import SentenceTransformer
from pymilvus import connections, Collection
import sys


class FailureCaseSearcher:
    def __init__(self, model_path="./text2vec-large-chinese", milvus_host="************", milvus_port="19530"):
        """
        初始化检索器
        
        Args:
            model_path: text2vec-large-chinese模型路径
            milvus_host: Milvus服务器地址
            milvus_port: Milvus服务器端口
        """
        self.model_path = model_path
        self.milvus_host = milvus_host
        self.milvus_port = milvus_port
        self.collection_name = "failure_analysis"
        self.model = None
        self.collection = None
        
    def initialize(self):
        """初始化模型和数据库连接"""
        print("正在初始化...")
        
        # 加载模型
        print("加载text2vec-large-chinese模型...")
        self.model = SentenceTransformer(self.model_path)
        
        # 连接Milvus
        print(f"连接Milvus数据库 {self.milvus_host}:{self.milvus_port}...")
        connections.connect("default", host=self.milvus_host, port=self.milvus_port)
        
        # 获取集合
        self.collection = Collection(self.collection_name)
        
        # 加载集合到内存
        print("加载集合到内存...")
        self.collection.load()
        
        print("初始化完成！")
        
    def search(self, query_text, top_k=5):
        """
        搜索相似的失败案例
        
        Args:
            query_text: 查询文本
            top_k: 返回最相似的前k个结果
            
        Returns:
            搜索结果列表
        """
        print(f"\n正在搜索与 '{query_text}' 相似的失败案例...")
        
        # 生成查询文本的嵌入向量
        query_embedding = self.model.encode([query_text]).tolist()
        
        # 搜索参数
        search_params = {"metric_type": "COSINE", "params": {"nprobe": 10}}
        
        # 执行搜索
        results = self.collection.search(
            data=query_embedding,
            anns_field="embedding",
            param=search_params,
            limit=top_k,
            output_fields=["task_id", "env_id", "branch_name", "timestamp", "failure_reason", "manual_operation", "reason_category"]
        )
        
        return results[0] if results else []
        
    def display_results(self, results, query_text):
        """显示搜索结果"""
        if not results:
            print("未找到相关结果")
            return
            
        print(f"\n查询: '{query_text}' 的搜索结果:")
        print("=" * 80)
        
        for i, hit in enumerate(results):
            print(f"\n结果 {i+1} (相似度分数: {hit.score:.4f})")
            print("-" * 60)
            print(f"任务ID: {hit.entity.get('task_id')}")
            print(f"环境ID: {hit.entity.get('env_id')}")
            print(f"分支名称: {hit.entity.get('branch_name')}")
            print(f"时间戳: {hit.entity.get('timestamp')}")
            print(f"失败原因: {hit.entity.get('failure_reason')}")
            print(f"人工操作: {hit.entity.get('manual_operation')}")
            print(f"原因分类: {hit.entity.get('reason_category')}")
            
    def interactive_search(self):
        """交互式搜索"""
        print("\n=== 交互式失败案例搜索 ===")
        print("输入查询文本来搜索相似的失败案例")
        print("输入 'quit' 或 'exit' 退出")

        while True:
            try:
                # 使用更安全的输入方式
                import sys
                print("\n请输入查询内容: ", end="", flush=True)
                query = sys.stdin.readline()

                if not query:  # EOF
                    print("\n检测到输入结束，退出搜索")
                    break

                query = query.strip()

                if query.lower() in ['quit', 'exit', '退出']:
                    print("退出搜索")
                    break

                if not query:
                    print("请输入有效的查询内容")
                    continue

                # 执行搜索
                results = self.search(query, top_k=5)

                # 显示结果
                self.display_results(results, query)

            except KeyboardInterrupt:
                print("\n\n搜索被中断")
                break
            except EOFError:
                print("\n\n检测到输入结束，退出搜索")
                break
            except Exception as e:
                print(f"搜索过程中出现错误: {e}")
                break
                
    def batch_search(self, queries):
        """批量搜索"""
        print("\n=== 批量搜索示例 ===")
        
        for query in queries:
            results = self.search(query, top_k=3)
            self.display_results(results, query)
            print("\n" + "="*80)


def main():
    """主函数"""
    # 创建搜索器
    searcher = FailureCaseSearcher()

    try:
        # 初始化
        searcher.initialize()

        # 检查命令行参数
        if len(sys.argv) > 1:
            # 如果提供了查询参数，直接搜索
            query = " ".join(sys.argv[1:])
            results = searcher.search(query, top_k=5)
            searcher.display_results(results, query)
        else:
            # 否则运行示例搜索，跳过交互式搜索以避免输入问题

            # 预定义的搜索示例
            example_queries = [
                "AAU重启问题",
                "版本故障导致失败",
                "业务不稳定需要复测",
                "系统问题需要重新开站",
                "流量峰值低"
            ]

            # 批量搜索示例
            searcher.batch_search(example_queries)

            print("\n" + "="*80)
            print("如需进行自定义查询，请使用以下命令:")
            print("python search_failure_cases.py '您的查询内容'")
            print("或者使用简化版本:")
            print("python simple_search.py '您的查询内容'")
            print("="*80)

    except Exception as e:
        print(f"程序运行出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
