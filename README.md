# 失败原因分析数据库系统

本系统将Excel中的失败原因分析数据存储到Milvus向量数据库中，并提供基于语义相似度的智能检索功能。

## 系统组件

### 1. 数据存储脚本 (`failure_analysis_to_milvus.py`)
- 读取Excel文件中的失败分析数据
- 使用text2vec-large-chinese模型生成文本嵌入向量
- 将数据存储到Milvus数据库
- 提供基本的检索示例

### 2. 检索脚本 (`search_failure_cases.py`)
- 独立的检索工具
- 支持命令行参数查询
- 支持交互式查询
- 提供批量查询示例

### 3. 文本嵌入模型 (`text2vec-large-chinese/`)
- 本地部署的中文文本嵌入模型
- 向量维度：1024
- 支持中文语义理解

## 数据库配置

- **Milvus服务器**: 10.230.55.23:19530
- **集合名称**: failure_analysis
- **向量维度**: 1024
- **相似度度量**: COSINE

## 数据字段

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | VARCHAR | 唯一标识符 |
| task_id | VARCHAR | 任务ID |
| env_id | VARCHAR | 环境ID |
| branch_name | VARCHAR | 分支名称 |
| timestamp | VARCHAR | 时间戳 |
| failure_reason | VARCHAR | 失败原因 |
| manual_operation | VARCHAR | 人工操作 |
| reason_category | VARCHAR | 原因分类 |
| combined_text | VARCHAR | 组合文本（用于检索） |
| embedding | FLOAT_VECTOR | 文本嵌入向量 |

## 使用方法

### 1. 数据存储
```bash
# 将Excel数据存储到Milvus数据库
python failure_analysis_to_milvus.py
```

### 2. 检索使用

#### 命令行查询（推荐）
```bash
# 使用主要检索脚本
python search_failure_cases.py "AAU重启问题"
python search_failure_cases.py "版本故障"
python search_failure_cases.py "业务不稳定"

# 使用简化版检索脚本（更稳定）
python simple_search.py "AAU重启问题"
python simple_search.py "核心网异常"
python simple_search.py "流量不足"
```

#### 批量示例查询
```bash
# 运行预定义的示例查询（不带参数）
python search_failure_cases.py
```

#### 故障排除
如果遇到检索报错，建议：
1. 优先使用 `simple_search.py` 脚本，它更稳定
2. 确保Milvus服务器可访问
3. 检查模型文件是否完整

## 检索示例

### 1. AAU相关问题
**查询**: "AAU重启问题"

**结果**:
- 任务ID: b96ef86c111248cb849bb230112485ff
- 失败原因: AC有值超门限，master分支可重启aau复测
- 人工操作: 重启AAU
- 原因分类: AAU异常

### 2. 版本故障问题
**查询**: "版本故障"

**结果**:
- 任务ID: 2b253edf24af4d36882e2ffc46142215
- 失败原因: 通感使用的SF耦联等配置随版本而回退，需要重配
- 人工操作: 修改网管配置
- 原因分类: 用例异常

### 3. 业务不稳定问题
**查询**: "业务不稳定"

**结果**:
- 任务ID: 69d600c18c734b818f62d3ce6474d680
- 失败原因: 大流量灌包不够稳定，需要抓现场分析
- 人工操作: 手动复测
- 原因分类: 业务不稳定

### 4. 流量相关问题
**查询**: "流量峰值低"

**结果**:
- 任务ID: bb0d3c34dd8b483f891434ef70d53886
- 失败原因: tcp灌包流量不足，人工复测通过
- 人工操作: 手动复测
- 原因分类: 业务不稳定

## 系统特点

1. **语义检索**: 基于文本语义相似度进行检索，而非简单的关键词匹配
2. **中文优化**: 使用专门的中文文本嵌入模型，对中文文本理解更准确
3. **高效存储**: 使用Milvus向量数据库，支持大规模向量检索
4. **灵活查询**: 支持自然语言查询，无需精确匹配关键词
5. **相似度评分**: 提供相似度分数，帮助判断结果的相关性

## 技术栈

- **Python 3.11**: 主要编程语言
- **sentence-transformers**: 文本嵌入模型框架
- **pymilvus**: Milvus数据库客户端
- **pandas**: 数据处理
- **openpyxl**: Excel文件读取

## 注意事项

1. 确保Milvus服务器正常运行并可访问
2. text2vec-large-chinese模型文件需要完整
3. Excel文件格式需要与预期一致
4. 首次运行会创建新的集合，如果集合已存在会被删除重建

## 扩展功能

系统支持以下扩展：
- 添加更多数据源
- 调整相似度阈值
- 增加过滤条件
- 导出检索结果
- 批量数据更新
