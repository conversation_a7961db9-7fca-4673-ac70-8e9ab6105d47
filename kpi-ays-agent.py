import os

# 代理服务器地址
proxy = "http://proxyhk.zte.com.cn:80"

# 设置 HTTP 和 HTTPS 代理
os.environ.pop('ALL_PROXY', None)
os.environ.pop('all_proxy', None)
os.environ.pop('SOCKS_PROXY', None)
os.environ.pop('socks_proxy', None)

os.environ['HTTPS_PROXY'] = ''
os.environ['https_proxy'] = ''
os.environ['HTTP_PROXY'] = ''
os.environ['http_proxy'] = ''

from agents import Agent
from agents import Runner
import asyncio
from agents import AsyncOpenAI
from agents import OpenAIChatCompletionsModel,ModelSettings

external_client = AsyncOpenAI(
    api_key="123",
    base_url="http://*************:15100/v1",
)

simple_agent = Agent(
    name="simple_agent",
    handoff_description="A helpful assistant.",
    instructions="You are responsible for responding to user requests.",
    model=OpenAIChatCompletionsModel(
        model="Qwen3-32b",
        openai_client=external_client,
    ),
    model_settings=ModelSettings(temperature=0.5),
)


async def main():
    result = await Runner.run(simple_agent, "你是什么模型")
    print(result.final_output)

if __name__ == "__main__":
    asyncio.run(main())
