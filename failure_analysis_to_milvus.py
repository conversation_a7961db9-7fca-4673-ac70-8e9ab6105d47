#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
失败原因分析数据存储到Milvus数据库
读取Excel文件中的失败分析数据，使用text2vec-large-chinese模型生成嵌入向量，存储到Milvus数据库
"""

import pandas as pd
import numpy as np
from sentence_transformers import SentenceTransformer
from pymilvus import connections, Collection, FieldSchema, CollectionSchema, DataType, utility
import json
import uuid
from datetime import datetime


class FailureAnalysisToMilvus:
    def __init__(self, model_path="./text2vec-large-chinese", milvus_host="************", milvus_port="19530"):
        """
        初始化类
        
        Args:
            model_path: text2vec-large-chinese模型路径
            milvus_host: Milvus服务器地址
            milvus_port: Milvus服务器端口
        """
        self.model_path = model_path
        self.milvus_host = milvus_host
        self.milvus_port = milvus_port
        self.collection_name = "failure_analysis"
        self.model = None
        self.collection = None
        
    def load_model(self):
        """加载text2vec-large-chinese模型"""
        print("正在加载text2vec-large-chinese模型...")
        self.model = SentenceTransformer(self.model_path)
        print("模型加载完成")
        
    def connect_milvus(self):
        """连接到Milvus数据库"""
        print(f"正在连接Milvus数据库 {self.milvus_host}:{self.milvus_port}...")
        connections.connect("default", host=self.milvus_host, port=self.milvus_port)
        print("Milvus连接成功")
        
    def create_collection(self):
        """创建Milvus集合"""
        # 检查集合是否已存在
        if utility.has_collection(self.collection_name):
            print(f"集合 {self.collection_name} 已存在，删除旧集合...")
            utility.drop_collection(self.collection_name)
            
        # 定义字段schema
        fields = [
            FieldSchema(name="id", dtype=DataType.VARCHAR, max_length=100, is_primary=True),
            FieldSchema(name="task_id", dtype=DataType.VARCHAR, max_length=100),
            FieldSchema(name="env_id", dtype=DataType.VARCHAR, max_length=200),
            FieldSchema(name="branch_name", dtype=DataType.VARCHAR, max_length=200),
            FieldSchema(name="timestamp", dtype=DataType.VARCHAR, max_length=100),
            FieldSchema(name="failure_reason", dtype=DataType.VARCHAR, max_length=2000),
            FieldSchema(name="manual_operation", dtype=DataType.VARCHAR, max_length=1000),
            FieldSchema(name="reason_category", dtype=DataType.VARCHAR, max_length=200),
            FieldSchema(name="combined_text", dtype=DataType.VARCHAR, max_length=3000),
            FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=1024)  # text2vec-large-chinese的向量维度是1024
        ]
        
        # 创建集合schema
        schema = CollectionSchema(fields, "失败原因分析数据集合")
        
        # 创建集合
        self.collection = Collection(self.collection_name, schema)
        print(f"集合 {self.collection_name} 创建成功")
        
        # 创建索引
        index_params = {
            "metric_type": "COSINE",
            "index_type": "IVF_FLAT",
            "params": {"nlist": 128}
        }
        self.collection.create_index("embedding", index_params)
        print("向量索引创建成功")
        
    def read_excel_data(self, excel_path):
        """读取Excel文件中的失败分析数据"""
        print(f"正在读取Excel文件: {excel_path}")
        
        # 读取失败原因分析sheet
        df = pd.read_excel(excel_path, sheet_name="失败原因分析")
        
        # 清理数据
        df = df.dropna(subset=['任务ID', '失败原因'])  # 删除关键字段为空的行
        df = df.fillna('')  # 其他字段的空值填充为空字符串
        
        print(f"读取到 {len(df)} 条有效数据")
        return df
        
    def generate_embeddings(self, texts):
        """生成文本嵌入向量"""
        print(f"正在生成 {len(texts)} 条文本的嵌入向量...")
        embeddings = self.model.encode(texts, show_progress_bar=True)
        return embeddings.tolist()
        
    def prepare_data(self, df):
        """准备要插入Milvus的数据"""
        print("正在准备数据...")
        
        data = []
        combined_texts = []
        
        for _, row in df.iterrows():
            # 生成唯一ID
            record_id = str(uuid.uuid4())
            
            # 组合文本用于生成嵌入向量
            combined_text = f"任务ID: {row['任务ID']}, 环境ID: {row['环境ID']}, 分支名称: {row['分支名称']}, 失败原因: {row['失败原因']}, 人工操作: {row['人工操作']}, 原因分类: {row['原因分类']}"
            combined_texts.append(combined_text)
            
            # 准备记录数据
            record = {
                "id": record_id,
                "task_id": str(row['任务ID']),
                "env_id": str(row['环境ID']),
                "branch_name": str(row['分支名称']),
                "timestamp": str(row['时间戳']),
                "failure_reason": str(row['失败原因']),
                "manual_operation": str(row['人工操作']),
                "reason_category": str(row['原因分类']),
                "combined_text": combined_text
            }
            data.append(record)
            
        # 生成嵌入向量
        embeddings = self.generate_embeddings(combined_texts)
        
        # 将嵌入向量添加到数据中
        for i, record in enumerate(data):
            record["embedding"] = embeddings[i]
            
        return data
        
    def insert_data(self, data):
        """将数据插入Milvus集合"""
        print(f"正在插入 {len(data)} 条数据到Milvus...")

        # 准备插入数据的格式 - 确保所有字段都是正确的类型
        insert_data = [
            [record["id"] for record in data],  # id字段
            [record["task_id"] for record in data],  # task_id字段
            [record["env_id"] for record in data],  # env_id字段
            [record["branch_name"] for record in data],  # branch_name字段
            [record["timestamp"] for record in data],  # timestamp字段
            [record["failure_reason"] for record in data],  # failure_reason字段
            [record["manual_operation"] for record in data],  # manual_operation字段
            [record["reason_category"] for record in data],  # reason_category字段
            [record["combined_text"] for record in data],  # combined_text字段
            [record["embedding"] for record in data]  # embedding字段
        ]

        # 插入数据
        mr = self.collection.insert(insert_data)
        print(f"数据插入成功，插入了 {len(mr.primary_keys)} 条记录")

        # 刷新集合以确保数据持久化
        self.collection.flush()
        print("数据刷新完成")
        
    def load_collection(self):
        """加载集合到内存以支持搜索"""
        print("正在加载集合到内存...")
        self.collection.load()
        print("集合加载完成")
        
    def search_similar(self, query_text, top_k=5):
        """搜索相似的失败案例"""
        print(f"正在搜索与 '{query_text}' 相似的失败案例...")
        
        # 生成查询文本的嵌入向量
        query_embedding = self.model.encode([query_text]).tolist()
        
        # 搜索参数
        search_params = {"metric_type": "COSINE", "params": {"nprobe": 10}}
        
        # 执行搜索
        results = self.collection.search(
            data=query_embedding,
            anns_field="embedding",
            param=search_params,
            limit=top_k,
            output_fields=["task_id", "env_id", "branch_name", "failure_reason", "manual_operation", "reason_category", "combined_text"]
        )
        
        return results
        
    def run(self, excel_path):
        """运行完整流程"""
        try:
            # 1. 加载模型
            self.load_model()
            
            # 2. 连接Milvus
            self.connect_milvus()
            
            # 3. 创建集合
            self.create_collection()
            
            # 4. 读取Excel数据
            df = self.read_excel_data(excel_path)
            
            # 5. 准备数据
            data = self.prepare_data(df)
            
            # 6. 插入数据
            self.insert_data(data)
            
            # 7. 加载集合
            self.load_collection()
            
            print("数据存储完成！")
            return True
            
        except Exception as e:
            print(f"处理过程中出现错误: {e}")
            return False


def main():
    """主函数"""
    excel_path = "六月失败原因及处理手段统计.xlsx"
    
    # 创建处理器实例
    processor = FailureAnalysisToMilvus()
    
    # 运行处理流程
    success = processor.run(excel_path)
    
    if success:
        print("\n=== 数据存储成功！现在可以进行检索测试 ===")
        
        # 检索示例
        print("\n=== 检索示例 ===")
        test_queries = [
            "AAU重启问题",
            "版本故障",
            "业务不稳定",
            "系统问题"
        ]
        
        for query in test_queries:
            print(f"\n查询: {query}")
            results = processor.search_similar(query, top_k=3)
            
            for i, hit in enumerate(results[0]):
                print(f"  结果 {i+1} (相似度: {hit.score:.4f}):")
                print(f"    任务ID: {hit.entity.get('task_id')}")
                print(f"    环境ID: {hit.entity.get('env_id')}")
                print(f"    失败原因: {hit.entity.get('failure_reason')}")
                print(f"    人工操作: {hit.entity.get('manual_operation')}")
                print(f"    原因分类: {hit.entity.get('reason_category')}")
                print()


if __name__ == "__main__":
    main()
