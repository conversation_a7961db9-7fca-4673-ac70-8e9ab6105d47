---
license: apache-2.0
language:
- zh
pipeline_tag: sentence-similarity
tags:
- text2vec
- feature-extraction
- sentence-similarity
- transformers
---

Based on the derivative model of https://huggingface.co/shibing624/text2vec-base-chinese, replace MacBERT with LERT, and keep other training conditions unchanged。

News

2024-06-25 [text2vec-large-chinese](https://huggingface.co/GanymedeNil/text2vec-large-chinese-onnx) onnxruntime version.

Talk to me: https://twitter.com/GanymedeNil