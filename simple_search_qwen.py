#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版失败案例检索脚本 - Qwen3-Embedding-0.6B版本
专门用于命令行查询，避免交互式输入问题
使用Qwen3-Embedding-0.6B模型进行向量检索
"""

from sentence_transformers import SentenceTransformer
from pymilvus import connections, Collection
import sys


def search_failure_cases(query_text, top_k=5):
    """
    搜索相似的失败案例
    
    Args:
        query_text: 查询文本
        top_k: 返回最相似的前k个结果
    """
    try:
        print(f"正在搜索与 '{query_text}' 相似的失败案例...")
        
        # 加载Qwen3-Embedding-0.6B模型
        print("加载Qwen3-Embedding-0.6B模型...")
        model_path = "/home/<USER>/.cache/modelscope/hub/models/Qwen/Qwen3-Embedding-0.6B"
        model = SentenceTransformer(model_path)
        
        # 连接Milvus
        print("连接Milvus数据库...")
        connections.connect("default", host="************", port="19530")
        
        # 获取集合 - 使用Qwen模型对应的集合
        collection = Collection("failure_analysis_qwen")
        collection.load()
        
        # 生成查询向量
        # 对于查询，尝试使用query prompt来提高效果
        try:
            query_embedding = model.encode([query_text], prompt_name="query").tolist()
            print("使用query prompt生成查询向量")
        except:
            # 如果不支持prompt_name，则使用默认方式
            query_embedding = model.encode([query_text]).tolist()
            print("使用默认方式生成查询向量")
        
        # 搜索参数
        search_params = {"metric_type": "COSINE", "params": {"nprobe": 10}}
        
        # 执行搜索
        results = collection.search(
            data=query_embedding,
            anns_field="embedding",
            param=search_params,
            limit=top_k,
            output_fields=["task_id", "env_id", "branch_name", "timestamp", "failure_reason", "manual_operation", "reason_category"]
        )
        
        # 显示结果
        if not results or not results[0]:
            print("未找到相关结果")
            return
            
        print(f"\n查询: '{query_text}' 的搜索结果 (使用Qwen3-Embedding-0.6B模型):")
        print("=" * 80)
        
        for i, hit in enumerate(results[0]):
            print(f"\n结果 {i+1} (相似度分数: {hit.score:.4f})")
            print("-" * 60)
            print(f"任务ID: {hit.entity.get('task_id')}")
            print(f"环境ID: {hit.entity.get('env_id')}")
            print(f"分支名称: {hit.entity.get('branch_name')}")
            print(f"时间戳: {hit.entity.get('timestamp')}")
            print(f"失败原因: {hit.entity.get('failure_reason')}")
            print(f"人工操作: {hit.entity.get('manual_operation')}")
            print(f"原因分类: {hit.entity.get('reason_category')}")
            
    except Exception as e:
        print(f"搜索过程中出现错误: {e}")
        return False
        
    return True


def search_both_models(query_text, top_k=5):
    """
    同时使用两个模型进行搜索，比较结果
    
    Args:
        query_text: 查询文本
        top_k: 返回最相似的前k个结果
    """
    try:
        print(f"正在使用两个模型搜索与 '{query_text}' 相似的失败案例...")
        
        # 连接Milvus
        print("连接Milvus数据库...")
        connections.connect("default", host="************", port="19530")
        
        # 搜索参数
        search_params = {"metric_type": "COSINE", "params": {"nprobe": 10}}
        
        # 1. 使用text2vec-large-chinese模型搜索
        print("\n=== 使用text2vec-large-chinese模型搜索 ===")
        try:
            model1 = SentenceTransformer("./text2vec-large-chinese")
            collection1 = Collection("failure_analysis")
            collection1.load()
            
            query_embedding1 = model1.encode([query_text]).tolist()
            results1 = collection1.search(
                data=query_embedding1,
                anns_field="embedding",
                param=search_params,
                limit=top_k,
                output_fields=["task_id", "failure_reason", "reason_category"]
            )
            
            if results1 and results1[0]:
                for i, hit in enumerate(results1[0]):
                    print(f"结果 {i+1} (相似度: {hit.score:.4f}): {hit.entity.get('failure_reason')}")
            else:
                print("未找到相关结果")
                
        except Exception as e:
            print(f"text2vec-large-chinese模型搜索失败: {e}")
        
        # 2. 使用Qwen3-Embedding-0.6B模型搜索
        print("\n=== 使用Qwen3-Embedding-0.6B模型搜索 ===")
        try:
            model_path = "/home/<USER>/.cache/modelscope/hub/models/Qwen/Qwen3-Embedding-0.6B"
            model2 = SentenceTransformer(model_path)
            collection2 = Collection("failure_analysis_qwen")
            collection2.load()
            
            try:
                query_embedding2 = model2.encode([query_text], prompt_name="query").tolist()
            except:
                query_embedding2 = model2.encode([query_text]).tolist()
                
            results2 = collection2.search(
                data=query_embedding2,
                anns_field="embedding",
                param=search_params,
                limit=top_k,
                output_fields=["task_id", "failure_reason", "reason_category"]
            )
            
            if results2 and results2[0]:
                for i, hit in enumerate(results2[0]):
                    print(f"结果 {i+1} (相似度: {hit.score:.4f}): {hit.entity.get('failure_reason')}")
            else:
                print("未找到相关结果")
                
        except Exception as e:
            print(f"Qwen3-Embedding-0.6B模型搜索失败: {e}")
            
    except Exception as e:
        print(f"搜索过程中出现错误: {e}")
        return False
        
    return True


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python simple_search_qwen.py '查询内容'           # 使用Qwen3-Embedding-0.6B模型搜索")
        print("  python simple_search_qwen.py '查询内容' --compare # 同时使用两个模型搜索并比较")
        print("\n示例:")
        print("  python simple_search_qwen.py 'AAU重启问题'")
        print("  python simple_search_qwen.py '核心网异常'")
        print("  python simple_search_qwen.py '业务不稳定' --compare")
        return
        
    # 获取查询内容和参数
    args = sys.argv[1:]
    compare_mode = "--compare" in args
    
    if compare_mode:
        args.remove("--compare")
    
    query = " ".join(args)
    
    if not query.strip():
        print("请提供查询内容")
        return
    
    # 执行搜索
    if compare_mode:
        search_both_models(query)
    else:
        search_failure_cases(query)


if __name__ == "__main__":
    main()
