#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Milvus混合检索的聊天代理
使用OpenAI Agents SDK和Qwen3-Embedding-0.6B模型，支持密集向量+BM25混合检索
"""
import os

# 代理服务器地址
proxy = "http://proxyhk.zte.com.cn:80"

# 设置 HTTP 和 HTTPS 代理
os.environ.pop('ALL_PROXY', None)
os.environ.pop('all_proxy', None)
os.environ.pop('SOCKS_PROXY', None)
os.environ.pop('socks_proxy', None)

os.environ['HTTPS_PROXY'] = ''
os.environ['https_proxy'] = ''
os.environ['HTTP_PROXY'] = ''
os.environ['http_proxy'] = ''

import json
from typing import Any, List, Dict
from sentence_transformers import SentenceTransformer
from pymilvus import MilvusClient, AnnSearchRequest, R<PERSON><PERSON><PERSON><PERSON><PERSON>, WeightedRanker
from pymilvus.client.constants import ConsistencyLevel
from agents import function_tool, RunContextWrapper, Agent, Runner, OpenAIChatCompletionsModel, set_tracing_disabled
from pydantic import BaseModel
from openai import AsyncOpenAI

set_tracing_disabled(True)
external_client = AsyncOpenAI(
    api_key="123", 
    base_url="http://10.239.212.61:15100/v1"
    # api_key="10334056", 
    # base_url="http://nebulacoder.dev.zte.com.cn:40081/v1"
)

model = OpenAIChatCompletionsModel( 
    model="Qwen3-32b",
    
    # model = 'nebulacoder-cot-v6.0',

    openai_client=external_client
)

class FailureCaseResult(BaseModel):
    """失败案例搜索结果模型"""
    task_id: str
    env_id: str
    branch_name: str
    timestamp: str
    failure_reason: str
    manual_operation: str
    reason_category: str
    hybrid_score: float
    search_type: str  # "hybrid", "dense", "sparse"

class MilvusHybridSearchResults(BaseModel):
    """Milvus混合搜索结果集合"""
    results: List[FailureCaseResult]
    query: str
    total_found: int
    search_method: str

class MilvusHybridSearchTool:
    """Milvus混合检索工具类"""
    
    def __init__(self):
        self.model = None
        self.client = None
        self.collection_name = "failure_analysis_qwen_hybrid"
        self._initialize()
    
    def _initialize(self):
        """初始化模型和数据库连接"""
        try:
            # 加载Qwen3-Embedding-0.6B模型
            model_path = "/home/<USER>/.cache/modelscope/hub/models/Qwen/Qwen3-Embedding-0.6B"
            self.model = SentenceTransformer(model_path)
            print("Qwen3-Embedding-0.6B模型加载成功")
            
            # 连接Milvus
            self.client = MilvusClient(uri="http://10.230.55.23:19530")
            
            # 加载collection
            self.client.load_collection(collection_name=self.collection_name)
            print(f"Collection {self.collection_name} 加载成功")
            print("Milvus连接成功")
            
        except Exception as e:
            print(f"初始化失败: {e}")
            raise

# 全局工具实例
milvus_hybrid_tool = MilvusHybridSearchTool()

@function_tool
async def search_failure_cases_hybrid(
    ctx: RunContextWrapper[Any], 
    query_text: str, 
    limit: int = 3,
    search_method: str = "hybrid"
) -> str:
    """使用混合检索搜索相似的失败案例
    
    Args:
        query_text: 查询文本，描述遇到的问题或故障
        limit: 返回结果数量限制，默认5个
        search_method: 搜索方法 ("hybrid", "dense", "sparse")
    
    Returns:
        JSON格式的搜索结果
    """
    try:
        print(f"正在使用{search_method}检索搜索与 '{query_text}' 相似的失败案例...")
        
        if search_method == "hybrid":
            results = await _hybrid_search(query_text, limit)
        elif search_method == "dense":
            results = await _dense_search(query_text, limit)
        elif search_method == "sparse":
            results = await _sparse_search(query_text, limit)
        else:
            results = await _hybrid_search(query_text, limit)
        
        return results
        
    except Exception as e:
        print(f"搜索失败: {e}")
        return f"搜索过程中出现错误: {str(e)}"

async def _hybrid_search(query_text: str, limit: int) -> str:
    """混合检索：密集向量 + BM25稀疏向量"""
    try:
        # 生成查询的密集向量
        try:
            query_embedding = milvus_hybrid_tool.model.encode([query_text], prompt_name="query").tolist()[0]
        except:
            query_embedding = milvus_hybrid_tool.model.encode([query_text]).tolist()[0]
        
        # 创建稀疏向量搜索请求（BM25）
        sparse_request = AnnSearchRequest(
            data=[query_text],
            anns_field="sparse_vector",
            param={"metric_type": "BM25"},
            limit=limit
        )
        
        # 创建密集向量搜索请求
        dense_request = AnnSearchRequest(
            data=[query_embedding],
            anns_field="embedding",
            param={"metric_type": "COSINE", "params": {"nprobe": 10}},
            limit=limit
        )
        
        # 使用加权重排序器
        ranker = WeightedRanker(0.7, 0.3)  # 密集向量权重0.7，稀疏向量权重0.3
        
        # 执行混合搜索
        results = milvus_hybrid_tool.client.hybrid_search(
            collection_name=milvus_hybrid_tool.collection_name,
            reqs=[sparse_request, dense_request],
            ranker=ranker,
            limit=limit,
            output_fields=["task_id", "env_id", "branch_name", "timestamp", 
                         "failure_reason", "manual_operation", "reason_category"],
            consistency_level="Bounded"
        )
        print('hybrid search results:', _format_results(results, query_text, "hybrid"))
        return _format_results(results, query_text, "hybrid")
        
    except Exception as e:
        print(f"混合检索失败: {e}")
        return f"混合检索过程中出现错误: {str(e)}"

async def _dense_search(query_text: str, limit: int) -> str:
    """密集向量检索"""
    try:
        # 生成查询向量
        try:
            query_embedding = milvus_hybrid_tool.model.encode([query_text], prompt_name="query").tolist()[0]
        except:
            query_embedding = milvus_hybrid_tool.model.encode([query_text]).tolist()[0]
        
        # 执行密集向量搜索
        results = milvus_hybrid_tool.client.search(
            collection_name=milvus_hybrid_tool.collection_name,
            data=[query_embedding],
            anns_field="embedding",
            search_params={"metric_type": "COSINE", "params": {"nprobe": 10}},
            limit=limit,
            output_fields=["task_id", "env_id", "branch_name", "timestamp", 
                         "failure_reason", "manual_operation", "reason_category"]
        )
        print('dense search results:', _format_results(results, query_text, "dense"))
        return _format_results(results, query_text, "dense")
        
    except Exception as e:
        print(f"密集向量检索失败: {e}")
        return f"密集向量检索过程中出现错误: {str(e)}"

async def _sparse_search(query_text: str, limit: int) -> str:
    """稀疏向量检索（BM25）"""
    try:
        # 执行BM25文本搜索
        results = milvus_hybrid_tool.client.search(
            collection_name=milvus_hybrid_tool.collection_name,
            data=[query_text],
            anns_field="sparse_vector",
            search_params={"metric_type": "BM25"},    
            limit=limit,
            output_fields=["task_id", "env_id", "branch_name", "timestamp", 
                         "failure_reason", "manual_operation", "reason_category"]
        )
        print('sparse search results:', _format_results(results, query_text, "sparse"))
        return _format_results(results, query_text, "sparse")
        
    except Exception as e:
        print(f"稀疏向量检索失败: {e}")
        return f"稀疏向量检索过程中出现错误: {str(e)}"

def _format_results(results, query_text: str, search_method: str) -> str:
    """格式化搜索结果"""
    failure_cases = []
    if results and len(results) > 0:
        for hit in results[0]:
            failure_case = FailureCaseResult(
                task_id=hit.get('entity', {}).get('task_id', ''),
                env_id=hit.get('entity', {}).get('env_id', ''),
                branch_name=hit.get('entity', {}).get('branch_name', ''),
                timestamp=hit.get('entity', {}).get('timestamp', ''),
                failure_reason=hit.get('entity', {}).get('failure_reason', ''),
                manual_operation=hit.get('entity', {}).get('manual_operation', ''),
                reason_category=hit.get('entity', {}).get('reason_category', ''),
                hybrid_score=float(hit.get('distance', 0)),
                search_type=search_method
            )
            failure_cases.append(failure_case)
    
    # 构建结果
    search_results = MilvusHybridSearchResults(
        results=failure_cases,
        query=query_text,
        total_found=len(failure_cases),
        search_method=search_method
    )
    
    return search_results.model_dump_json(indent=2)

def create_milvus_hybrid_agent():
    """创建Milvus混合检索聊天代理"""
    
    agent = Agent(
        model=model,
        name="5G高频一级CI冒烟测试故障分析助手（混合检索版）",
        instructions="""
        你是一个专业的5G高频一级CI冒烟测试故障分析助手，专门帮助分析和解决一级CI冒烟测试用例失败问题。
        
        你的能力包括：
        1. 使用混合检索（密集向量+BM25）搜索历史失败案例数据库
        2. 支持多种检索方式：混合检索、密集向量检索、稀疏向量检索
        3. 分析失败模式和趋势
        4. 提供基于历史数据的解决方案建议
        
        当用户描述问题时，请：
        1. 默认使用混合检索搜索相关案例，获得最佳检索效果
        2. 如果用户指定检索方式，则使用指定的方法
        3. 分析搜索结果中的模式和相似性
        4. 提供具体的解决建议和预防措施
        5. 解释不同检索方法的优势
        
        检索方法说明：
        - hybrid: 混合检索，结合语义相似性和关键词匹配，效果最佳
        - dense: 密集向量检索，基于语义相似性
        - sparse: 稀疏向量检索，基于BM25关键词匹配
        
        请用中文回复，保持专业和友好的语调。
        """,
        tools=[search_failure_cases_hybrid],
        output_type=str,
    )
    
    return agent

async def main():
    """主函数"""
    import sys
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python milvus_hybrid_chat_agent.py '你的问题' [检索方法]")
        print("\n示例:")
        print("  python milvus_hybrid_chat_agent.py 'AAU重启问题如何解决？'")
        print("  python milvus_hybrid_chat_agent.py '核心网异常的常见原因是什么？' hybrid")
        print("  python milvus_hybrid_chat_agent.py '版本故障' dense")
        print("  python milvus_hybrid_chat_agent.py '业务不稳定' sparse")
        return
    
    # 获取用户问题和检索方法
    user_question = sys.argv[1]
    search_method = sys.argv[2] if len(sys.argv) > 2 else "hybrid"
    
    # 如果指定了检索方法，将其添加到问题中
    if search_method in ["hybrid", "dense", "sparse"]:
        user_question_with_method = f"{user_question} [使用{search_method}检索方法]"
    else:
        user_question_with_method = user_question
        search_method = "hybrid"
    
    try:
        # 创建代理
        agent = create_milvus_hybrid_agent()
        
        print(f"用户问题: {user_question}")
        print(f"检索方法: {search_method}")
        print("=" * 80)
        
        # 运行代理
        runner = Runner()
        response = await runner.run(agent, user_question_with_method)
        
        print("助手回复:")
        print(response)
        
    except Exception as e:
        print(f"运行出错: {e}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())