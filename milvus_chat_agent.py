#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Milvus检索的聊天代理
使用OpenAI Agents SDK和Qwen3-Embedding-0.6B模型
"""
import os


# 代理服务器地址
proxy = "http://proxyhk.zte.com.cn:80"

# 设置 HTTP 和 HTTPS 代理
# 清除可能冲突的代理设置
os.environ.pop('ALL_PROXY', None)
os.environ.pop('all_proxy', None)
os.environ.pop('SOCKS_PROXY', None)
os.environ.pop('socks_proxy', None)

# 设置 HTTP 和 HTTPS 代理
os.environ['HTTPS_PROXY'] = ''
os.environ['https_proxy'] = ''
os.environ['HTTP_PROXY'] = ''
os.environ['http_proxy'] = ''


import json
from typing import Any, List, Dict
from sentence_transformers import SentenceTransformer
from pymilvus import MilvusClient
from agents import function_tool, <PERSON><PERSON>ontext<PERSON>rap<PERSON>, Agent, Runner, OpenAIChatCompletionsModel,set_tracing_disabled


from pydantic import BaseModel
from openai import AsyncOpenAI

set_tracing_disabled(True)
external_client = AsyncOpenAI(
    # api_key="123", 
    # base_url="http://10.239.212.61:15100/v1"
    api_key="10334056", 
    base_url="http://nebulacoder.dev.zte.com.cn:40081/v1"

)


model = OpenAIChatCompletionsModel( 
    #model="Qwen3-32b",
    
    model = 'nebulacoder-cot-v6.0',
    openai_client=external_client
)

class FailureCaseResult(BaseModel):
    """失败案例搜索结果模型"""
    task_id: str
    env_id: str
    branch_name: str
    timestamp: str
    failure_reason: str
    manual_operation: str
    reason_category: str
    similarity_score: float


class MilvusSearchResults(BaseModel):
    """Milvus搜索结果集合"""
    results: List[FailureCaseResult]
    query: str
    total_found: int


class MilvusSearchTool:
    """Milvus检索工具类"""
    
    def __init__(self):
        self.model = None
        self.client = None
        self.collection_name = "failure_analysis_qwen"
        self._initialize()
    
    def _initialize(self):
        """初始化模型和数据库连接"""
        try:
            # 加载Qwen3-Embedding-0.6B模型
            model_path = "/home/<USER>/.cache/modelscope/hub/models/Qwen/Qwen3-Embedding-0.6B"
            self.model = SentenceTransformer(model_path)
            print("Qwen3-Embedding-0.6B模型加载成功")
            
            # 连接Milvus
            self.client = MilvusClient(uri="http://10.230.55.23:19530")
            
            # 加载collection
            self.client.load_collection(collection_name=self.collection_name)
            print(f"Collection {self.collection_name} 加载成功")
            print("Milvus连接成功")
            
        except Exception as e:
            print(f"初始化失败: {e}")
            raise


# 全局工具实例
milvus_tool = MilvusSearchTool()


@function_tool
async def search_failure_cases(
    ctx: RunContextWrapper[Any], 
    query_text: str, 
    limit: int = 5
) -> str:
    """搜索相似的失败案例
    
    Args:
        query_text: 查询文本，描述遇到的问题或故障
        limit: 返回结果数量限制，默认5个
    
    Returns:
        JSON格式的搜索结果
    """
    try:
        print(f"正在搜索与 '{query_text}' 相似的失败案例...")
        
        # 生成查询向量
        try:
            query_embedding = milvus_tool.model.encode([query_text], prompt_name="query").tolist()[0]
            print("使用query prompt生成查询向量")
        except:
            query_embedding = milvus_tool.model.encode([query_text]).tolist()[0]
            print("使用默认方式生成查询向量")
        
        # 搜索参数
        search_params = {"metric_type": "COSINE", "params": {"nprobe": 10}}
        
        # 执行搜索
        results = milvus_tool.client.search(
            collection_name=milvus_tool.collection_name,
            data=[query_embedding],
            anns_field="embedding",
            limit=limit,
            search_params=search_params,
            output_fields=["task_id", "env_id", "branch_name", "timestamp", 
                         "failure_reason", "manual_operation", "reason_category"]
        )
        
        # 格式化结果
        failure_cases = []
        if results and len(results) > 0:
            for hit in results[0]:
                failure_case = FailureCaseResult(
                    task_id=hit.get('entity', {}).get('task_id', ''),
                    env_id=hit.get('entity', {}).get('env_id', ''),
                    branch_name=hit.get('entity', {}).get('branch_name', ''),
                    timestamp=hit.get('entity', {}).get('timestamp', ''),
                    failure_reason=hit.get('entity', {}).get('failure_reason', ''),
                    manual_operation=hit.get('entity', {}).get('manual_operation', ''),
                    reason_category=hit.get('entity', {}).get('reason_category', ''),
                    similarity_score=float(hit.get('distance', 0))
                )
                failure_cases.append(failure_case)
        
        # 构建结果
        search_results = MilvusSearchResults(
            results=failure_cases,
            query=query_text,
            total_found=len(failure_cases)
        )
        print('search_results.model_dump_json(indent=2)',search_results.model_dump_json(indent=2))
        return search_results.model_dump_json(indent=2)
        
    except Exception as e:
        print(f"搜索失败: {e}")
        return f"搜索过程中出现错误: {str(e)}"


def create_milvus_agent():
    """创建Milvus检索聊天代理"""
    
    agent = Agent(
        model= model,
        name="5G高频一级CI冒烟测试故障分析助手",
        instructions="""
        你是一个专业的5G高频一级CI冒烟测试故障分析助手，专门帮助分析和解决一级CI冒烟测试用例失败问题。
        
        你的能力包括：
        1. 搜索历史失败案例数据库
        2. 分析失败模式和趋势
        3. 提供基于历史数据的解决方案建议
        
        当用户描述问题时，请：
        1. 使用search_failure_cases工具搜索相关案例
        2. 分析搜索结果中的模式
        3. 提供具体的解决建议和预防措施
        
        请用中文回复，保持专业和友好的语调。
        """,
        tools=[search_failure_cases],
        output_type=str,
    )
    
    return agent


async def main():
    """主函数"""
    import sys
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python milvus_chat_agent.py '你的问题'")
        print("\n示例:")
        print("  python milvus_chat_agent.py 'AAU重启问题如何解决？'")
        print("  python milvus_chat_agent.py '核心网异常的常见原因是什么？'")
        return
    
    # 获取用户问题
    user_question = " ".join(sys.argv[1:])
    
    try:
        # 创建代理
        agent = create_milvus_agent()
        
        print(f"用户问题: {user_question}")
        print("=" * 80)
        
        # 运行代理
        runner = Runner()
        response = await runner.run(agent, user_question)
        
        print("助手回复:")
        print(response)
        
    except Exception as e:
        print(f"运行出错: {e}")


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
